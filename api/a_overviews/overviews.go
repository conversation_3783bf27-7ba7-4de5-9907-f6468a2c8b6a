package a_overviews

import (
	"fmt"
	"strconv"
	"time"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_asset_history"
	"git.gobies.org/foeye/foeye3/database/db_history_record"
	"git.gobies.org/foeye/foeye3/database/db_ip_histories"
	"git.gobies.org/foeye/foeye3/database/db_second_categories"
	"git.gobies.org/foeye/foeye3/database/db_tag"
	"git.gobies.org/foeye/foeye3/database/db_task"
	"git.gobies.org/foeye/foeye3/database/db_user"
	"git.gobies.org/foeye/foeye3/database/db_user_ability"
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_assets"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_compliance_monitor"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_subdomain"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_threats"
	"git.gobies.org/foeye/foeye3/packages/permission"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"git.gobies.org/foeye/foeye3/packages/util/timer"
	"git.gobies.org/foeye/foeye3/responses/r_asset"
	"git.gobies.org/foeye/foeye3/responses/r_asset_history"
	"git.gobies.org/foeye/foeye3/responses/r_common"
	"git.gobies.org/foeye/foeye3/store"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"
)

// OverViewAPIHandler interface for encapsulating http handles for assets.
type OverViewAPIHandler interface {
	GetOverviewsInfo(ctx *gin.Context)
	GetAssetsPie(ctx *gin.Context)
}

// OverViewAPI provides handlers for managing Over View.
type OverViewAPI struct {
	ComponentDB     database.GormSecondCategoriesDatabaseStore
	TagDB           database.GormTagDatabaseStore
	ES              es_asset.ESAssetDatabaseInter
	SubDomainES     elasticx.IndexSubDomainStore
	AssetElasticDB  elasticx.IndexAssetsStore
	ThreatElasticDB elasticx.IndexThreatsStore
	ViolationDB     elasticx.IndexComplianceMonitorStore
	History         *db_asset_history.GormAssetHistoryDatabase
	IpHistory       *db_ip_histories.GormIPHistoriesDatabase
	HistoryRecordDB *db_history_record.GormHistoryRecordDatabase
	TaskDB          *db_task.GormTaskDatabase
	UserDB          *db_user.GormUserDatabase
	UserAbilityDB   *db_user_ability.GormUserAbilityDatabase
	configure       *config.Configure
	storage         store.Factory
	databaseFactory database.Factory
}

// NewOverViewAPI creates a new NewOverViewAPI instance.
func NewOverViewAPI(DB *database.GormDatabase, ES *elasticx.ElasticDatabase, configure *config.Configure,
	storage store.Factory, databaseFactory database.Factory) (*OverViewAPI, error) {
	historyDB := new(db_asset_history.GormAssetHistoryDatabase)
	historyDB.Instance = DB

	if err := historyDB.UpdateItemIsNull(); err != nil {
		return nil, err
	}

	compoDb := new(db_second_categories.GormSecondCategoriesDatabase)
	compoDb.Instance = DB

	es := es_asset.NewESAssetDatabase(configure, ES, databaseFactory)
	es.Instance = ES
	tagDb := new(db_tag.GormTagDatabase)
	tagDb.Instance = DB

	assetsElasticDB := esi_assets.NewStore(ES)
	subdoaminEs := esi_subdomain.NewStore(ES)
	threatElasticDB := esi_threats.NewStore(ES)
	violationDB := esi_compliance_monitor.NewStore(ES)
	ipHistory := db_ip_histories.NewGormIPHistoriesDatabase(DB)
	taskDB := db_task.NewGormTaskDatabase(DB)

	historyRecordDB := db_history_record.NewGormHistoryRecordDatabase(DB)
	userDB := db_user.NewGormUserDatabase(configure, DB)

	userAbilityDB := db_user_ability.NewGormUserAbilityDatabase(DB)

	return &OverViewAPI{ES: es, TagDB: tagDb, ComponentDB: compoDb,
		History: historyDB, configure: configure, storage: storage,
		SubDomainES: subdoaminEs, AssetElasticDB: assetsElasticDB,
		ThreatElasticDB: threatElasticDB, ViolationDB: violationDB,
		IpHistory: ipHistory, HistoryRecordDB: historyRecordDB,
		TaskDB: taskDB, UserDB: userDB, UserAbilityDB: userAbilityDB}, nil
}

// GetOverviewsInfo 资产总览
// swagger:operation GET /v3/overviews 资产管理 GetOverviewsInfo
//
// 资产总览。
//
// ---
// responses:
//   200:
//     description: Ok
//     schema:
//       type: array
//       items:
//         $ref: "#/definitions/OverviewsInfo"
//   401:
//     description: 未认证
//     schema:
//         $ref: "#/definitions/Error"
func (api *OverViewAPI) GetOverviewsInfo(ctx *gin.Context) {
	// today := time.Now().Format("2006-01-02")
	query := &r_asset.QueryListAndKeyword{
		QueryList: &r_common.QueryList{
			Number: 0,
			Size:   0,
		},
		Search: r_asset.Search{
			CreatedTimeRange: fmt.Sprintf("%s,%s",
				timer.CurrentDateStr(),
				time.Now().AddDate(0, 0, 1).Format("2006-01-02")),
		},
	}

	/*---------------------------分域---------------------------------*/
	ipRangeManager, err := permission.CheckAssets(ctx, api.configure, api.storage.Redis().Client())
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.IpRangePermissionCheckError,
			statusx.IpRangePermissionCheckErrorOfMessage,
			err,
		)
		return
	}
	/*---------------------------分域---------------------------------*/

	// 计算网站总数
	/*---------------------------分域---------------------------------*/
	q := elastic.NewBoolQuery()
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		q.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	subDomainTotal, err := api.SubDomainES.CountSubdomainTotal(q)
	if err != nil {
		log.Println(err)
	}

	NewAddIp, _, _ := api.ES.GetAssetList(ctx, query, nil, ipRangeManager, false)
	// 用户自定义标签 key =>value
	tagMaps, tagsList, res, TotalComponents, TotalPorts, totalIp := api.GetCustomTags(ctx, ipRangeManager)

	// 计算业务标签种类数量
	businessAppTotal := 0
	for _, tag := range tagsList {
		if v, ok := tag.(map[string]interface{})["key"]; ok && v.(string) == "business_app" {
			if val, ok := tag.(map[string]interface{})["children"].([]map[string]interface{}); ok {
				businessAppTotal = len(val)
			}
		}
	}

	/*---------------------------分域---------------------------------*/
	// 修复漏洞
	repairedThreats, _ := api.ES.GetThreatsStatistics(ctx, true, tagMaps, ipRangeManager)
	// 未修复漏洞
	noRepairedThreats, _ := api.ES.GetThreatsStatistics(ctx, false, tagMaps, ipRangeManager)
	/*---------------------------分域---------------------------------*/
	var repairedPercent float64
	if noRepairedThreats.Total == 0 {
		repairedThreats.RepairedPercent = 100
	} else if repairedThreats.Total == 0 {
		repairedThreats.RepairedPercent = 0
	} else {
		aa := float64(repairedThreats.Total) / float64(repairedThreats.Total+noRepairedThreats.Total)
		repairedPercent, _ = strconv.ParseFloat(fmt.Sprintf("%0.2f", aa), 64)
		repairedThreats.RepairedPercent = float64(int(100 * repairedPercent))
	}

	/*---------------------------分域---------------------------------*/
	// 修复违规
	repairedViolations, _ := api.ES.GetViolationsStatistics(ctx, true, tagMaps, ipRangeManager)
	// 未修复违规
	noRepairedViolations, _ := api.ES.GetViolationsStatistics(ctx, false, tagMaps, ipRangeManager)
	/*---------------------------分域---------------------------------*/
	var violationsRepairedPercent float64
	if noRepairedViolations.Total == 0 {
		repairedViolations.RepairedPercent = 100
	} else if repairedViolations.Total == 0 {
		repairedViolations.RepairedPercent = 0
	} else {
		aa := float64(repairedViolations.Total) / float64(repairedViolations.Total+noRepairedViolations.Total)
		violationsRepairedPercent, _ = strconv.ParseFloat(fmt.Sprintf("%0.2f", aa), 64)
		repairedViolations.RepairedPercent = float64(int(100 * violationsRepairedPercent))
	}

	historyList, _ := api.History.GetAssetHistoryList(1, 10, true,
		[]interface{}{"ip_range_type = ?", "all_ips"}...)
	item := make(map[string]interface{})
	item["key"] = "asset_history"
	item["title"] = "资产变化趋势"
	if len(historyList) == 0 {
		item["children"] = nil
		res = append(res, item)
	} else {
		children := make([]*m_asset.Bucket, 0)
		reapiredThreat := make([]*m_asset.Bucket, 0)
		noReapiredThreat := make([]*m_asset.Bucket, 0)
		repairedCompliance := make([]*m_asset.Bucket, 0)
		noReapiredCompliance := make([]*m_asset.Bucket, 0)
		for _, history := range historyList {
			bucket := &m_asset.Bucket{Key: history.CalDate, Count: history.AssetCount}
			children = append(children, bucket)
			bucket1 := &m_asset.Bucket{Key: history.CalDate, Count: history.ThreatRepairedCount}
			reapiredThreat = append(reapiredThreat, bucket1)
			bucket2 := &m_asset.Bucket{Key: history.CalDate, Count: history.ThreatNorepairedCount}
			noReapiredThreat = append(noReapiredThreat, bucket2)
			bucket3 := &m_asset.Bucket{Key: history.CalDate, Count: history.ViolationRepairedCount}
			repairedCompliance = append(repairedCompliance, bucket3)
			bucket4 := &m_asset.Bucket{Key: history.CalDate, Count: history.ViolationNorepairedCount}
			noReapiredCompliance = append(noReapiredCompliance, bucket4)
		}
		item["children"] = children
		repairedThreats.Histories = reapiredThreat
		noRepairedThreats.Histories = noReapiredThreat
		repairedViolations.Histories = repairedCompliance
		noRepairedViolations.Histories = noReapiredCompliance
		res = append(res, item)
	}

	total := &r_asset_history.HeaderStatistics{
		TotalIP:          totalIp,
		TotalNewIp:       int(NewAddIp),
		TotalComponents:  TotalComponents,
		TotalThreat:      int(noRepairedThreats.Total),
		TotalPorts:       TotalPorts,
		TotalSubDomain:   subDomainTotal,
		TotalBusinessApp: businessAppTotal,
	}
	obj := map[string]interface{}{
		"header_statistics":          total,
		"asset_info":                 res,
		"repaired_threat_info":       repairedThreats,
		"norepaired_threat_info":     noRepairedThreats,
		"repaired_compliance_info":   repairedViolations,
		"norepaired_compliance_info": noRepairedViolations,
	}

	r_common.Success(ctx, obj)
}

func (api *OverViewAPI) GetCustomTags(ctx *gin.Context, ipRangeManager string) (map[string]string, []interface{}, []interface{}, int, int, int) {
	tags, _ := api.TagDB.GetList(0, 3, []interface{}{"mode = ?", m_tag.ModeOfCustomAddition, "ancestry = ?", 0}...)
	tagMaps := make(map[string]string)
	if len(tags.([]*m_tag.Tag)) > 0 {
		for _, item := range tags.([]*m_tag.Tag) {
			tagMaps[item.Realname] = item.Name
		}
	}
	components, _ := api.ComponentDB.GetSecondCategoriesList()
	query1 := &r_asset.QueryListAndKeyword{
		QueryList: &r_common.QueryList{
			Number: 0,
			Size:   0,
		},
	}
	tagsList := make([]interface{}, 0)
	res := make([]interface{}, 0)
	conditions, _ := api.ES.GetAssetStatistics(ctx, query1, tagMaps, components, true, ipRangeManager)
	TotalComponents, TotalPorts, totalIp := 0, 0, 0
	if categories, ok := conditions.([]map[string]interface{}); ok {
		for _, category := range categories {
			count := category["doc_count"].(int)
			item := make(map[string]interface{})
			for key, value := range category {
				if key == "key" && value.(string) == "ports" {
					TotalPorts = count
				} else if key == "key" && value.(string) == "rule_categories" {
					TotalComponents = count
				} else if key == "key" && value.(string) == "ip" {
					totalIp = count
				} else if key == "key" && value.(string) == "rules" {
					children := category["children"].([]*m_asset.Bucket)
					item["key"] = "rules"
					item["title"] = category["title"]
					if len(children) > 9 {
						item["children"] = children[:10]
					} else {
						item["children"] = children
					}
					res = append(res, item)
				} else if key == "key" && value.(string) == "rule_companies" {
					children := category["children"].([]*m_asset.Bucket)
					item["key"] = "rule_companies"
					item["title"] = category["title"]
					if len(children) > 9 {
						item["children"] = children[:10]
					} else {
						item["children"] = children
					}
					res = append(res, item)
				} else if key == "key" && value.(string) == "tags" {
					item["key"] = "tags"
					item["title"] = category["title"]
					item["children"] = category["children"]
					res = append(res, item)
					tagsList = category["children"].([]interface{})
				}
			}
		}
	}
	return tagMaps, tagsList, res, TotalComponents, TotalPorts, totalIp
}
