package a_poc

import (
	"net/http"
	"strings"

	"git.gobies.org/foeye/foeye3/api/a_common"
	"git.gobies.org/foeye/foeye3/database/db_poc"
	"git.gobies.org/foeye/foeye3/model/m_poc"
	"git.gobies.org/foeye/foeye3/packages/gormx"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"git.gobies.org/foeye/foeye3/responses/r_common"
	"git.gobies.org/foeye/foeye3/responses/r_poc"

	"github.com/gin-gonic/gin"
)

// GetPocList 获取PoC列表。
// swagger:operation GET /v3/pocs PoC管理 GetPocList
//
// 获取PoC列表。
//
// ---
// consumes: [application/json]
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// parameters:
//   - name: number
//     in: query
//     description: 分页数据的页码.
//     required: false
//     type: integer
//   - name: size
//     in: query
//     description: 分页数据的每页记录数.
//     required: false
//     type: integer
//   - name: keyword
//     in: query
//     description: 该接口可以通过关键字查询.
//     required: false
//     type: string
//   - name: sort
//     in: query
//     description: 排序字段
//     required: false
//     type: string
//   - name: ids
//     in: query
//     description: poc记录ID。
//     required: false
//     type: array
//     items:
//     type: integer
//   - name: level
//     in: query
//     description: 高级筛选-漏洞等级。
//     required: false
//     type: array
//     items:
//     type: integer
//   - name: state
//     in: query
//     description: 高级筛选-漏洞状态。
//     required: false
//     type: array
//     items:
//     type: integer
//   - name: vul_type
//     in: query
//     description: 高级筛选-漏洞类型。
//     required: false
//     type: array
//     items:
//     type: string
//   - name: group_id
//     in: query
//     description: 高级筛选-Poc分组。
//     required: false
//     type: array
//     items:
//     type: integer
//
// responses:
//
//	200:
//	  description: Ok
//	  schema:
//	    type: array
//	    items:
//	      $ref: "#/definitions/PocBase"
//	400:
//	  description: Bad Request
//	  schema:
//	      $ref: "#/definitions/Error"
//	401:
//	  description: Unauthorized
//	  schema:
//	      $ref: "#/definitions/Error"
//	500:
//	  description: Internal Error
//	  schema:
//	      $ref: "#/definitions/Error"
func (api *PocAPI) GetPocList(ctx *gin.Context) {
	QL := new(r_poc.QueryListAndSearch)
	if err := ctx.ShouldBindQuery(&QL); err != nil {
		r_common.BadRequestNoData(
			ctx,
			statusx.PocStatusFailedWithBindParamNumber,
			statusx.PocStatusFailedWithBindParamMessage,
			err,
		)
		return
	}
	where := searchConditions(QL)
	orders := setOrder(QL)
	var list []*m_poc.PocPlus
	var err error
	if len(where) > 1 && len(orders) == 0 {
		orders = []string{"pocs.created_at DESC"}
	}

	if len(orders) > 0 {
		list, err = api.DB.GetListSort(QL.Number, QL.Size, orders, where...)
	} else {
		list, err = api.DB.GetList(QL.Number, QL.Size, where...)
	}

	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordStatusFailedWithQueryList,
			statusx.PocWeakPasswordStatusFailedWithQueryListOfMessage,
			err,
		)
		return
	}

	// 根据查询参数返回指定类型的数据集
	c, err := api.DB.CountPoc(where...)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordStatusFailedWithQueryListCount,
			statusx.PocWeakPasswordStatusFailedWithQueryListCountOfMessage,
			err,
		)
		return
	}

	var baseList []*m_poc.PocFieldBase
	for _, poc := range list {
		baseList = append(baseList, m_poc.ConvertPocPlusToPocFieldBase(poc))
	}

	data := r_common.ResponsePaginationList{
		Pagination: r_common.Pagination{
			Number: QL.Number,
			Size:   QL.Size,
			Total:  c,
		},
		Data: baseList,
	}

	r_common.SuccessReaderJSON(ctx, http.StatusOK, statusx.StatusOkOfGetMessage, data)
}

// GetPoc 通过ID获取PoC信息。
// swagger:operation GET /v3/pocs/{id} PoC管理 GetPoc
//
// 通过ID获取PoC信息。
//
// ---
// consumes: [application/json]
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// parameters:
//   - name: id
//     in: path
//     description: the poc id
//     required: true
//     type: integer
//
// responses:
//
//	200:
//	  description: Ok
//	  schema:
//	      $ref: "#/definitions/PocBase"
//	400:
//	  description: Bad Request
//	  schema:
//	      $ref: "#/definitions/Error"
//	401:
//	  description: Unauthorized
//	  schema:
//	      $ref: "#/definitions/Error"
//	404:
//	  description: Not Found
//	  schema:
//	      $ref: "#/definitions/Error"
func (api *PocAPI) GetPoc(ctx *gin.Context) {
	a_common.WithParamForNumber(ctx, "id", func(id uint) {
		poc, err := api.DB.GetPocByID(id)
		if err != nil {
			r_common.InternalServerError(
				ctx,
				statusx.PocStatusFailedWithNoPocNumber,
				statusx.PocStatusFailedWithNoPocMessage,
				err,
			)
			return
		}
		if poc != nil {
			pocFileBase := m_poc.ConvertPocToPocFieldBase(poc)
			r_common.SuccessReaderJSON(ctx, http.StatusOK, statusx.StatusOkOfGetMessage, pocFileBase)
		} else {
			r_common.StatusNotFound(
				ctx,
				statusx.PocStatusFailedWithNoPocNumber,
				statusx.PocStatusFailedWithNoPocMessage,
			)
		}
	})
}

// GetVulTypes 批量修改poc状态。
// swagger:operation GET /v3/pocs/:id/vul_types PoC管理 GetVulTypes
//
// 获取PoC漏洞类型列表。
//
// ---
// consumes: [application/json]
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// responses:
//
//	200:
//	  description: Ok
//	  schema:
//	      ["弱口令", "命令执行"]
//	400:
//	  description: Bad Request
//	  schema:
//	      $ref: "#/definitions/Error"
//	401:
//	  description: Unauthorized
//	  schema:
//	      $ref: "#/definitions/Error"
//	500:
//	  description: Internal Error
//	  schema:
//	      $ref: "#/definitions/Error"
func (api *PocAPI) GetVulTypes(ctx *gin.Context) {
	list := api.DB.GetTags()
	r_common.SuccessReaderJSON(ctx, http.StatusOK, "获取漏洞类型列表成功", list)
}

// searchConditions 设置搜索条件
func searchConditions(searches *r_poc.QueryListAndSearch) []interface{} {
	where := make([]interface{}, 0)
	if searches.Ids != nil && len(searches.Ids) > 0 {
		where = append(where,
			gormx.GormQuery{
				Query:    "`pocs`.`id` IN ?",
				Arg:      searches.Ids,
				Operator: "and",
			})
	}

	if searches.Category != "" {
		if searches.Category == "common" {
			where = append(where,
				gormx.GormQuery{
					Query:    "`pocs`.`vulType` != ?",
					Arg:      "弱口令",
					Operator: "and",
				})
		} else if searches.Category == "weak_password" {
			where = append(where,
				gormx.GormQuery{
					Query:    "`pocs`.`vulType` = ?",
					Arg:      "弱口令",
					Operator: "and",
				})
		}
	}

	if searches.Level != nil && len(searches.Level) > 0 {
		where = append(where,
			gormx.GormQuery{
				Query:    "pocs.level IN ?",
				Arg:      searches.Level,
				Operator: "and",
			})
	}

	if searches.State != nil && len(searches.State) > 0 {
		where = append(where,
			gormx.GormQuery{
				Query:    "pocs.state IN ?",
				Arg:      searches.State,
				Operator: "and",
			})
	}

	if searches.VulType != nil && len(searches.VulType) > 0 {
		var placeholders string

		for i, tag := range searches.VulType {
			if tag == "信创" {
				where = append(where,
					gormx.GormQuery{
						Query:    "FIND_IN_SET(?, `pocs`.`tags`)",
						Arg:      "信创",
						Operator: "and",
					})
				continue
			}

			if tag == "两高一弱" {
				where = append(where,
					gormx.GormQuery{
						Query:    "FIND_IN_SET(?, `pocs`.`tags`)",
						Arg:      "两高一弱",
						Operator: "and",
					})
				continue
			}

			if tag == "热点" {
				where = append(where,
					gormx.GormQuery{
						Query:    "FIND_IN_SET(?, `pocs`.`tags`)",
						Arg:      "热点",
						Operator: "and",
					})
				continue
			}

			if i == 0 {
				placeholders += "FIND_IN_SET(?, tags) OR "
			} else {
				placeholders += "FIND_IN_SET( '" + tag + "', tags) OR "
			}
		}

		if placeholders != "" {
			placeholders = strings.TrimSuffix(placeholders, " OR ")

			where = append(where,
				gormx.GormQuery{
					Query:    placeholders,
					Arg:      searches.VulType[0],
					Operator: "and",
				})
		}
	}

	if searches.Keyword != "" {
		where = append(where, gormx.GormQuery{
			Query:    "concat_ws(',', pocs.name, pocs.cveId, pocs.vulNum) LIKE ?",
			Arg:      "%%" + searches.Keyword + "%%",
			Operator: "and",
		})
	}

	if searches.GroupId != nil && len(searches.GroupId) > 0 {
		where = append(where,
			gormx.GormQuery{
				Query:    "`pocs`.`id` IN (select DISTINCT(pocs_scan_templates.poc_id) from pocs_scan_templates where pocs_scan_templates.`scan_template_id` = ?)",
				Arg:      searches.GroupId,
				Operator: "and",
			})
	}

	if searches.HasEXP != "" {
		exp := 0
		if searches.HasEXP == "yes" {
			exp = 1
		}
		where = append(where,
			gormx.GormQuery{
				Query:    "`pocs`.`has_exp` = ?",
				Arg:      exp,
				Operator: "and",
			})
	}

	return where
}

// searchConditions 设置搜索条件
func setOrder(search *r_poc.QueryListAndSearch) []string {
	orders := make([]string, 0)

	if search.Sort != "" {
		orders = append(orders, search.Sort)
	}

	return orders
}

// GetPocsCount 计算POC总数。
// swagger:operation GET /v3/pocs/count PoC管理 GetPocsCount
//
// 计算POC总数。
//
// ---
// consumes: [application/json]
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// responses:
//
//	200:
//	  description: Ok
//	  schema:
//	    type: array
//	    items:
//	      $ref: "#/definitions/PocsCountNumber"
//	400:
//	  description: Bad Request
//	  schema:
//	      $ref: "#/definitions/Error"
//	401:
//	  description: Unauthorized
//	  schema:
//	      $ref: "#/definitions/Error"
//	500:
//	  description: Internal Error
//	  schema:
//	      $ref: "#/definitions/Error"
func (api *PocAPI) GetPocsCount(ctx *gin.Context) {
	pocCount, err := api.DB.CountPoc()
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordStatusFailedWithQueryListCount,
			statusx.PocWeakPasswordStatusFailedWithQueryListCountOfMessage,
			err,
		)
	}

	pocOffCount, err := api.DB.CountPoc([]interface{}{"`pocs`.`state` = 5"}...)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordCountFailedWithNotWeakPasswdCount,
			statusx.PocWeakPasswordCountFailedWithNotWeakPasswdCountOfMessage,
			err,
		)
	}

	groupCount, err := api.GroupStore.CountItems()
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordCountFailedWithPocGroupCountErr,
			statusx.PocWeakPasswordCountFailedWithPocGroupCountErrOfMessage,
			err,
		)
	}

	weakPasswordCount, err := api.DB.CountPoc([]interface{}{"FIND_IN_SET(?, `pocs`.`tags`)", "弱口令"}...)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.WeakPasswordCountFailedWithPocErr,
			statusx.WeakPasswordCountFailedWithPocErrOfMessage,
			err,
		)
	}

	twoHighAndOneWeakCount, err := api.DB.CountPoc([]interface{}{"FIND_IN_SET(?, `pocs`.`tags`)", "两高一弱"}...)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.TwoHighAndOneWeakCountFailedWithPocErr,
			statusx.TwoHighAndOneWeakCountFailedWithPocErrOfMessage,
			err,
		)
	}

	xcCount, err := api.DB.CountPoc([]interface{}{"FIND_IN_SET(?, `pocs`.`tags`)", "信创"}...)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.XCCountFailedWithPocErr,
			statusx.XCCountFailedWithPocErrOfMessage,
			err,
		)
	}

	expCount, err := api.DB.CountPoc([]interface{}{"`pocs`.`has_exp` = 1"}...)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.EXPCountFailedWithPocErr,
			statusx.EXPCountFailedWithPocErrOfMessage,
			err,
		)
	}

	customPocCount, err := api.DB.CountCustomPoc()
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.CustomPocStatusFailedWithCountList,
			statusx.CustomPocStatusFailedWithCountListOfMessage,
			err,
		)
		return
	}

	data := &m_poc.PocsCountNumber{
		PocCount:          pocCount,
		PocOffCount:       pocOffCount,
		GroupCount:        groupCount,
		WeakPasswordCount: weakPasswordCount,
		TwoHighAndOneWeak: twoHighAndOneWeakCount,
		XCCount:           xcCount,
		EXPCount:          expCount,
		CustomPocCount:    customPocCount,
		HotspotPocCount:   int64(len(db_poc.HotspotPocs)),
	}
	r_common.Success(ctx, data)
}
