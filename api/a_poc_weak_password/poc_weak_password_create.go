package a_poc_weak_password

import (
	"bufio"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye/foeye3/model/m_poc_weak_password"
	"git.gobies.org/foeye/foeye3/model/m_trace"
	"git.gobies.org/foeye/foeye3/packages/fsx"
	"git.gobies.org/foeye/foeye3/packages/gormx"
	"git.gobies.org/foeye/foeye3/packages/secure/token"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"git.gobies.org/foeye/foeye3/packages/tracex"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

// PostPocWeakPasswordFile 生成密码-上传文件。
// swagger:operation Post /v3/weak_pwd_pocs/weak_pwd_entries/file PoC弱口令管理 PostPocWeakPasswordFile
//
// 生成密码-上传文件。
//
// ---
// consumes: [multipart/form-data]
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// parameters:
//   - name: uploadFile
//     in: formData
//     description: from—data表单上传 key为uploadFile，value是需要上传的文件
//     required: true
//     type: file
//
// responses:
//
//	200:
//	  description: Ok
//	  schema:
//	      $ref: "#/definitions/Success"
//	400:
//	  description: Bad Request
//	  schema:
//	      $ref: "#/definitions/Error"
//	401:
//	  description: Unauthorized
//	  schema:
//	      $ref: "#/definitions/Error"
//	500:
//	  description: Internal Error
//	  schema:
//	      $ref: "#/definitions/Error"
func (api *PocWeakPasswordAPI) PostPocWeakPasswordFile(ctx *gin.Context) {
	file, err := ctx.FormFile("uploadFile")
	if err != nil {
		r_common.BadRequestNoData(
			ctx,
			statusx.PocWeakPasswordParamFailedWithBadRequest,
			statusx.PocWeakPasswordParamFailedWithBadRequestOfMessage,
			err,
		)
		/*--------------------------日志-------------------------------*/
		tracex.AddTrace(ctx, tracex.MethodNamePocWeakPasswordImport, tracex.Operate, fmt.Sprintf(tracex.DesPocWeakPasswordImport, ""))
		/*--------------------------日志-------------------------------*/
		return
	}

	if !strings.HasSuffix(file.Filename, ".txt") {
		r_common.BadRequestNoData(
			ctx,
			statusx.PocWeakPasswordUploadFileFailedWithBadRequest,
			statusx.PocWeakPasswordUploadFileFailedWithBadRequestOfMessage,
			errors.New(statusx.PocWeakPasswordUploadFileFailedWithBadRequestOfMessage),
		)
	}

	/*--------------------------日志-------------------------------*/
	defer tracex.AddTrace(ctx, tracex.MethodNamePocWeakPasswordImport, tracex.Operate, fmt.Sprintf(tracex.DesPocWeakPasswordImport, file.Filename))
	/*--------------------------日志-------------------------------*/

	if file.Size > UploadPocWeakPasswordTxtFileDefaultSize {
		r_common.BadRequestNoData(
			ctx,
			statusx.PocWeakPasswordFileSizeFailedWithBadRequest,
			statusx.PocWeakPasswordFileSizeFailedWithBadRequestOfMessage,
			errors.New(statusx.PocWeakPasswordFileSizeFailedWithBadRequestOfMessage),
		)
		return
	}

	openFile, err := file.Open()
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordCountFailedOpenFile,
			statusx.PocWeakPasswordCountFailedOpenFileMessage,
			err,
		)
		return
	}
	scanner := bufio.NewScanner(openFile)
	var fileLineCount int
	for scanner.Scan() {
		fileLineCount++
	}
	if err = scanner.Err(); err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordCountFailedLineCount,
			statusx.PocWeakPasswordCountFailedLineCountMessage,
			err,
		)
		return
	}
	if fileLineCount > 1000 {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordCountFailedLineCount,
			statusx.PocWeakPasswordCountFailedLineCountMessage,
			err,
		)
		return
	}

	filePath, err := fsx.GetUploadSavePathForTxtOfPocWeakPassword(api.baseDir)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordFilePathFailedWithBadRequest,
			statusx.PocWeakPasswordFilePathFailedWithBadRequestOfMessage,
			err,
		)
		return
	}

	filePath = filePath + fmt.Sprintf("/%s_%s", strconv.FormatInt(time.Now().UnixNano(), 10), file.Filename)

	// The uploaded file is saved to the server
	if err = ctx.SaveUploadedFile(file, filePath); err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordFileSaveFailedWithBadRequest,
			statusx.PocWeakPasswordFileSaveFailedWithBadRequestOfMessage,
			err,
		)
		return
	}

	instance, lines, err := api.Srv.PocWeakPasswords().UploadFileDataConvertToDatabase(api.Store, filePath)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordStatusFailedWithUploadFileGeneratePasswordValidateFailed,
			statusx.PocWeakPasswordStatusFailedWithUploadFileGeneratePasswordValidateFailedOfMessage+" "+err.Error(),
			err,
		)
		return
	}

	trace, ok := instance.(map[string]int)
	if !ok {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordStatusFailedWithUploadFileGeneratePasswordFailed,
			statusx.PocWeakPasswordStatusFailedWithUploadFileGeneratePasswordFailedOfMessage,
			err,
		)
		return
	}
	conditions := make([]interface{}, 0)
	conditions = append(conditions, gormx.GormQuery{
		Query:    "`weak_pwd_entries`.`state` = ?",
		Arg:      1,
		Operator: "and",
	})

	list, err := api.Store.GetList(0, 0, conditions...)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordStatusFailedWithQueryList,
			statusx.PocWeakPasswordStatusFailedWithQueryListOfMessage,
			err,
		)
		return
	}
	if err = api.Srv.PocWeakPasswords().InstanceConvertToDatabase(api.Store, lines.([]string), api.baseDir, list); err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordSaveFailedWithPost,
			statusx.PocWeakPasswordSaveFailedWithPostOfMessage,
			err,
		)
		return
	}

	claims, err := token.GetUserInfo(ctx, api.configure, api.storage.Redis().Client())
	if err != nil {
		r_common.StatusUnauthorized(ctx, err)
		return
	}

	descs := fmt.Sprintf("共导入 %d 条弱口令, 重复 %d 条, 已存在 %d 条, 失败0条", trace["export_count"], trace["repeat_count"], trace["exists_count"])
	t := new(m_trace.Trace)
	t.MethodName = "WeakPwdEntry#save_passwords"
	t.Result = true
	t.Descs = descs
	t.UserID = int(claims.ID)
	t.Category = "operate"
	t.Username = claims.Username

	if err = api.TraceStore.CreateTrace(t); err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordTraceFailedWithBadRequest,
			statusx.PocWeakPasswordTraceFailedWithBadRequestOfMessage,
			err,
		)
		return
	}
	r_common.OK(ctx)
}

// PostPocWeakPassword 生成密码-自定义设置。
// swagger:operation POST /v3/weak_pwd_pocs/weak_pwd_entries PoC弱口令管理 PostPocWeakPassword
//
//	生成密码-自定义设置。
//
// ---
// consumes: [application/json]
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// parameters:
//   - name: body
//     in: body
//     description: 生成密码-自定义设置
//     required: true
//     schema:
//     $ref: "#/definitions/PocWeakPasswordGenerateAddition"
//
// responses:
//
//	200:
//	  description: Ok
//	  schema:
//	      $ref: "#/definitions/Success"
//	400:
//	  description: Bad Request
//	  schema:
//	      $ref: "#/definitions/Error"
//	401:
//	  description: Unauthorized
//	  schema:
//	      $ref: "#/definitions/Error"
//	500:
//	  description: Internal Error
//	  schema:
//	      $ref: "#/definitions/Error"
func (api *PocWeakPasswordAPI) PostPocWeakPassword(ctx *gin.Context) {
	var body *m_poc_weak_password.PocWeakPasswordGenerateAddition
	if err := ctx.ShouldBindJSON(&body); err != nil {
		r_common.BadRequestNoData(
			ctx,
			statusx.PocWeakPasswordParamFailedWithBadRequest,
			statusx.PocWeakPasswordParamFailedWithBadRequestOfMessage,
			err,
		)
		/*--------------------------日志-------------------------------*/
		tracex.AddTrace(ctx, tracex.MethodNamePocWeakPasswordSelf, tracex.Operate, fmt.Sprintf(tracex.DesPocWeakPasswordSelf, ""))
		/*--------------------------日志-------------------------------*/
		return
	}

	/*--------------------------日志-------------------------------*/
	defer tracex.AddTrace(ctx, tracex.MethodNamePocWeakPasswordSelf, tracex.Operate, fmt.Sprintf(tracex.DesPocWeakPasswordSelf, body))
	/*--------------------------日志-------------------------------*/

	lines, err := api.Srv.PocWeakPasswords().ConvertInstanceToStringList(body)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordStatusFailedWithUploadFileGeneratePasswordValidateFailed,
			statusx.PocWeakPasswordStatusFailedWithUploadFileGeneratePasswordValidateFailedOfMessage+" "+err.Error(),
			err,
		)
		return
	}

	if lines == nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordStatusFailedWithUploadFileGeneratePasswordFailed,
			statusx.PocWeakPasswordStatusFailedWithUploadFileGeneratePasswordFailedOfMessage,
			err,
		)
		return
	}
	conditions := make([]interface{}, 0)
	conditions = append(conditions, gormx.GormQuery{
		Query:    "`weak_pwd_entries`.`state` = ?",
		Arg:      1,
		Operator: "and",
	})

	list, err := api.Store.GetList(0, 0, conditions...)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordStatusFailedWithQueryList,
			statusx.PocWeakPasswordStatusFailedWithQueryListOfMessage,
			err,
		)
		return
	}
	if err = api.Srv.PocWeakPasswords().InstanceConvertToDatabase(api.Store, lines.([]string), api.baseDir, list); err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.PocWeakPasswordSaveFailedWithPost,
			statusx.PocWeakPasswordSaveFailedWithPostOfMessage,
			err,
		)
		return
	}

	r_common.OK(ctx)
}
