package a_user

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye-dependencies/logger"
	"git.gobies.org/foeye/foeye3/api/a_common"
	"git.gobies.org/foeye/foeye3/model/m_ip_range"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/model/m_user"
	"git.gobies.org/foeye/foeye3/model/m_user_ability"
	"git.gobies.org/foeye/foeye3/model/m_visible_column"
	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/license"
	"git.gobies.org/foeye/foeye3/packages/permission"
	"git.gobies.org/foeye/foeye3/packages/secure/secret"
	"git.gobies.org/foeye/foeye3/packages/secure/token"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"git.gobies.org/foeye/foeye3/packages/stringsx"
	"git.gobies.org/foeye/foeye3/packages/tracex"
	"git.gobies.org/foeye/foeye3/packages/util"
	"git.gobies.org/foeye/foeye3/responses/r_common"
	"git.gobies.org/foeye/foeye3/responses/r_user"
)

// GetUsers 获取用户列表。
// swagger:operation GET /v3/users 用户管理 GetUsers
//
// 获取用户列表。
//
// ---
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// parameters:
//   - name: keyword
//     in: query
//     description: 模糊匹配关键词
//     required: false
//     type: string
//   - name: number
//     in: query
//     description: 分页数据的页码。
//     required: false
//     type: integer
//   - name: size
//     in: query
//     description: 分页数据的每页记录数。
//     required: false
//     type: integer
//
// responses:
//
//	200:
//	  description: Ok
//	  schema:
//	      $ref: "#/definitions/UserListResp"
func (api *UserAPI) GetUsers(ctx *gin.Context) {
	QL := new(r_user.QueryListAndKeyword)
	if err := ctx.ShouldBindQuery(&QL); err != nil {
		r_common.BadRequestNoData(ctx, statusx.InvalidQueryParams, statusx.InvalidQueryParamsMessage, err)
		return
	}

	user, err := token.GetUserInfo(ctx, api.configure, api.storage.Redis().Client())

	options := make([]interface{}, 0)
	if len(QL.Keyword) > 0 {
		lk := strings.Join([]string{"%%", QL.Keyword, "%%"}, "")
		options = append(options, "username LIKE ? or email LIKE ? or tel LIKE ?", lk, lk, lk)
	}

	var userId uint

	if user.Username != "admin" {
		userId = user.ID
		options = append(options, "id = ?", user.ID)
	}

	users, err := api.DB.GetUserByConditions(QL.Number, QL.Size, QL.Keyword, userId)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.InternalServerCode, statusx.InternalServerMessage, err)
		return
	}

	var resp = &m_user.UserListResp{}
	for _, user := range users {
		userAbility, _ := api.UserAbilityDB.GetUserAbilityByUserID(int(user.ID))
		ipRangeContent := api.GetIpRangeContent(user)
		resp.List = append(resp.List, api.buildItem(user, userAbility, ipRangeContent))
	}
	// 获取用户数量
	total, err := api.DB.CountUser(options...)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.UserStatusFailedWithCountUser, statusx.UserStatusFailedWithCountUserOfMessage, err)
		return
	}
	// 获取冻结用户数量
	options = append(options, "locked_at is not null")
	locked, err := api.DB.CountUser(options...)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.UserStatusFailedWithCountUser, statusx.UserStatusFailedWithCountUserOfMessage, err)
		return
	}
	resp.Total = uint(total)
	resp.Locked = uint(locked)

	if resp == nil {
		resp = &m_user.UserListResp{}
	}

	r_common.Success(
		ctx, r_common.ResponsePaginationList{
			Pagination: r_common.Pagination{
				Number: QL.Number,
				Size:   QL.Size,
				Total:  int64(resp.Total),
			},
			Data: resp,
		},
	)
}

func (api *UserAPI) buildItem(user *m_user.User, userAbility *m_user_ability.UserAbility, ipRangeContent []string) *m_user.UserListItem {
	var status uint8
	if user.LockedAt != nil {
		status = 1
	}

	isReset := false
	if (user.ResetAt == nil) || (user.ResetAt != nil && time.Now().Sub(*user.ResetAt).Hours() > float64(api.configure.Server.ResetPasswdHours)) {
		isReset = true
	}

	ipRanges, err := api.ipRangeDB.GetIpRangeConditionsVals("User", user.ID, user.IPRangeType)
	if err != nil {
		return nil
	}

	// 如果没有绑定过IP段，返回空数组，代表所有IP权限
	if ipRanges == nil {
		ipRanges = []uint{}
	}

	return &m_user.UserListItem{
		ID:            user.ID,
		Logins:        uint(user.SignInCount),
		Admin:         user.IsAdmin(),
		Username:      user.Username,
		Email:         user.Email,
		Tel:           user.Tel,
		IpRanges:      ipRanges,
		IpRangeType:   user.IPRangeType,
		IpContextList: ipRangeContent,
		Permission: map[string]bool{
			"asset_scan": user.CanScanAsset(userAbility.AssetTask),
			"vul_scan":   user.CanScanVul(userAbility.ThreatTask),
			"compliance": user.CanDoCompliance(userAbility.ComplianceMonitoring),
		},
		CreatedAt: fmt.Sprintf("%v", user.CreatedAt),
		Status:    status,
		Key:       user.Key,
		// 返回给前端的字段is_reset,使用reset_at字段做的判断
		IsReset: isReset,
	}
}

func (api *UserAPI) GetIpRangeContent(user *m_user.User) []string {
	ipRangeContent := make([]string, 0)
	ipRangeConditionsRaw, _ := api.ipRangeDB.GetIpRangeConditions("User", user.IPRangeType, user.ID)
	if ipRangeConditionsRaw != nil {
		ipRangeConditions := ipRangeConditionsRaw.([]m_ip_range.IPRangeConditions)
		for _, ipRangeCondition := range ipRangeConditions {
			if ipRangeCondition.Category == "ip_ranges" {
				if id, err := strconv.Atoi(ipRangeCondition.ValId); err == nil {
					ipRangeRaw, err := api.ipRangeDB.GetItemByID(uint(id))
					if err != nil {
						continue
					}

					if ipRange, ok := ipRangeRaw.(*m_ip_range.IpRange); ok {
						ipRangeContent = append(ipRangeContent, ipRange.IpRange)
					}
				}

			} else {
				if id, err := strconv.Atoi(ipRangeCondition.ValId); err == nil {
					tagRaw, err := api.tagDB.GetItemByID(uint(id))
					if err != nil {
						continue
					}
					if tag, ok := tagRaw.(*m_tag.Tag); ok {
						ipRangeContent = append(ipRangeContent, tag.Name)
					}
				}
			}
		}
	}
	return ipRangeContent
}

// CreateUser 添加用户。
// swagger:operation POST /v3/users 用户管理 CreateUser
//
// 添加用户。
//
// ---
// parameters:
//   - name: body
//     in: body
//     description:
//     required: true
//     schema:
//     $ref: "#/definitions/CreateParams"
//
// responses:
//
//	200:
//	  description: OK
func (api *UserAPI) CreateUser(ctx *gin.Context) {
	params := new(m_user.CreateParams)
	if err := ctx.ShouldBind(&params); err != nil {
		r_common.BadRequest(ctx, err)
		/*--------------------------日志-------------------------------*/
		tracex.AddTrace(
			ctx,
			tracex.MethodNamePortCreate,
			tracex.Operate,
			fmt.Sprintf(tracex.DesPortCreate, ""))
		/*--------------------------日志-------------------------------*/
		return
	}

	// 如果手机号不为空的时候，增加正则校验
	if params.Tel != "" && !util.Mobile(params.Tel) {
		r_common.BadRequestNoData(ctx, statusx.BadRequestPhoneVeryErr, statusx.BadRequestPhoneVeryErrMessage, errors.New(statusx.BadRequestPhoneVeryErrMessage))
		return
	}

	/*--------------------------日志-------------------------------*/
	defer tracex.AddTrace(
		ctx,
		tracex.MethodNameUserCreate,
		tracex.Operate,
		fmt.Sprintf(tracex.DesUserCreate, params.Username))
	/*--------------------------日志-------------------------------*/

	keyword, err := secret.DecryptByAes(params.Password)
	if err != nil {
		logger.Warnw("CreateUser", "err", err)
		r_common.InternalServerError(ctx, statusx.UserStatusParamsInternalErr, statusx.UserStatusParamsInternalErrMessage, err)
		return
	}
	params.Password = string(keyword)

	// 判断模块权限
	isLicense, err := license.CheckoutLicenseUser(api.configure, api.storage.Redis().Client(),
		*params.AssetScan, *params.VulScan, *params.Compliance)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.UserStatusFailedWithCheckoutLicenseUser, statusx.UserStatusFailedWithCheckoutLicenseUserOfMessage, err)
		return
	}
	if !isLicense {
		r_common.ForbiddenNoData(ctx, statusx.UserStatusFailedWithIsLicense,
			statusx.UserStatusFailedWithIsLicenseOfMessage, errors.New(statusx.UserStatusFailedWithIsLicenseOfMessage))
		return
	}

	if err := api.DB.CreateUser(params); err != nil {
		if err.Error() != "" {
			r_common.InternalServerError(ctx, statusx.UserStatusFailedWithCheckoutLicenseUser, err.Error(), err)
			return
		}
		r_common.InternalServerError(ctx, statusx.UserStatusFailedWithCheckoutLicenseUser, statusx.UserStatusFailedWithCheckoutLicenseUserOfMessage, err)
		return
	}

	roles := make([]string, 0)
	if *params.AssetScan {
		roles = append(roles, permission.AssetScan)
	}
	if *params.VulScan {
		roles = append(roles, permission.VulScan)
	}
	if *params.Compliance {
		roles = append(roles, permission.Compliance)
	}
	if err = permission.GrantRole(params.Username, roles...); err != nil {
		r_common.BadRequest(ctx, err)
		return
	}

	user, err := api.DB.GetUserByUsername(params.Username)
	if err != nil {
		r_common.BadRequest(ctx, err)
		return
	}
	data := []*m_visible_column.VisibleColumn{
		{
			UserId:       user.ID,
			GroupName:    m_threat.DefaultGroupName,
			ItemType:     m_threat.AssetType,
			Content:      m_threat.DefaultAssetFields,
			LastSelected: m_threat.DefaultLastSelected,
		}, {
			UserId:       user.ID,
			GroupName:    m_threat.DefaultGroupName,
			ItemType:     m_threat.PocTypeThreat,
			Content:      m_threat.DefaultFields,
			LastSelected: m_threat.DefaultLastSelected,
		}, {
			UserId:       user.ID,
			GroupName:    m_threat.DefaultGroupName,
			ItemType:     m_threat.PocTypeUnFixed,
			Content:      m_threat.DefaultFields,
			LastSelected: m_threat.DefaultLastSelected,
		}, {
			UserId:       user.ID,
			GroupName:    m_threat.DefaultGroupName,
			ItemType:     m_threat.PocTypeFixed,
			Content:      m_threat.DefaultFields,
			LastSelected: m_threat.DefaultLastSelected,
		},
	}

	_ = api.visibleColumnDB.CreateItems(data)

	// 创建任务模板,只读用户不需要创建常规模板
	//if *params.AssetScan || *params.VulScan || *params.Compliance {
	//	err = api.dbStore.Task().TaskTemplateCommonDefault(ctx, int(user.ID))
	//	if err != nil {
	//		log.Println(err)
	//		r_common.BadRequest(ctx, err)
	//		return
	//	}
	//}
	r_common.OK(ctx)
}

// SetState 更新用户状态。
// swagger:operation POST /v3/users/set_state 用户管理 SetState
//
// 更新用户状态。
//
// ---
// consumes: [application/json]
// parameters:
//   - name: body
//     in: body
//     description: ids=用户ID数组，state=要设置的状态，取值：(lock=冻结, unlock=解冻)
//     required: true
//     schema:
//     $ref: "#/definitions/SetStateParams"
//
// responses:
//
//	200:
//	  schema:
//	    example: {}
func (api *UserAPI) SetState(ctx *gin.Context) {
	var params m_user.SetStateParams

	if err := ctx.Bind(&params); err != nil {
		r_common.BadRequest(ctx, err)
		/*--------------------------日志-------------------------------*/
		tracex.AddTrace(
			ctx,
			tracex.MethodNameUserStateUpdate,
			tracex.Operate,
			fmt.Sprintf(tracex.DesUserStateUpdate, ""))
		/*--------------------------日志-------------------------------*/
		return
	}

	/*--------------------------日志-------------------------------*/
	defer tracex.AddTrace(
		ctx,
		tracex.MethodNameUserStateUpdate,
		tracex.Operate,
		fmt.Sprintf(tracex.DesUserStateUpdate, params))
	/*--------------------------日志-------------------------------*/

	cond, err := ProtectUsersCondition(ctx, api.DB, api.configure, api.storage.Redis().Client())
	if err != nil {
		r_common.BadRequest(ctx, err)
		return
	}

	idsList, _ := api.GetIdsByKeyword(params.Keyword)
	if len(params.IDs) > 0 {
		params.IDs = stringsx.UintIntersect(idsList, params.IDs)
		if len(params.IDs) == 0 {
			r_common.OK(ctx)
			return
		}
	} else {
		params.IDs = idsList
	}

	err = api.DB.SetState(params.IDs, params.State, cond)
	if err != nil {
		r_common.BadRequest(ctx, err)
		return
	}
	if params.State == "lock" {
		api.destroyUserToken(params.IDs)
	}

	r_common.OK(ctx)
}

func (api *UserAPI) destroyUserToken(ids []uint) {
	users, _ := api.DB.GetUserByIDS(ids)
	for _, user := range users {
		t, err := token.ReadUserToken(api.storage.Redis().Client(), user.Username, user.CurrentSignInIP)
		if t == "" || err != nil {
			continue
		}
		errInfo := token.DestroyUserToken(api.configure, api.storage.Redis().Client(), t, user.CurrentSignInIP)
		if errInfo != nil {
			continue
		}
	}
}

// UpdateKey 更新当前用户Key。
// swagger:operation POST /v3/users/update_key 用户管理 UpdateKey
//
// 更新当前用户Key。
// ---
// responses:
//
//	200:
//	  schema:
//	    example: {"key": "2094454b9a9a8b29541c4998695dabb2"}
func (api *UserAPI) UpdateKey(ctx *gin.Context) {
	userId := CurrentUserId(ctx, api.configure, api.storage.Redis().Client())
	userInfo, err := api.DB.GetUserByID(userId)
	if err != nil {
		r_common.InternalServerError(
			ctx,
			statusx.UserStatusFailedWithUserInfo,
			statusx.UserStatusFailedWithUserInfoOfMessage,
			err,
		)
		return
	}

	// 管理员只能更新邮箱和电话号码
	if userInfo.RoleName != "administrator" {
		r_common.ForbiddenNoData(
			ctx,
			statusx.ForbiddenWithUpdateKey,
			statusx.ForbiddenWithUpdateKeyOfMessage,
			errors.New(statusx.ForbiddenWithUpdateKeyOfMessage),
		)
		return
	}

	newKey, err := api.DB.UpdateKey(userId)
	if err != nil {
		/*--------------------------日志-------------------------------*/
		tracex.AddTrace(
			ctx,
			tracex.MethodNameUserKeyUpdate,
			tracex.Operate,
			fmt.Sprintf(tracex.DesNameUserKeyUpdate, ""))
		/*--------------------------日志-------------------------------*/
		r_common.InternalServerError(ctx, statusx.InternalServerCode, statusx.InternalServerMessage, err)
		return
	}

	/*--------------------------日志-------------------------------*/
	defer tracex.AddTrace(
		ctx,
		tracex.MethodNameUserKeyUpdate,
		tracex.Operate,
		fmt.Sprintf(tracex.DesNameUserKeyUpdate, newKey))
	/*--------------------------日志-------------------------------*/

	constant.ApiToken = constant.ApiUserInfo{
		ID:       userInfo.ID,
		Username: userInfo.Username,
		Token:    newKey,
	}
	r_common.Success(ctx, map[string]string{"key": newKey})
}

// UpdateUser 更新用户信息。
// swagger:operation PUT /v3/users/{id} 用户管理 UpdateUser
//
// 更新用户信息。
//
// ---
// parameters:
//   - name: id
//     in: path
//     description: 用户ID
//     required: true
//     type: integer
//   - name: body
//     in: body
//     description: 请求参数
//     required: true
//     schema:
//     $ref: "#/definitions/UpdateParams"
//
// responses:
//
//	200:
//	  description: Ok
//	  type: array
//	  schema:
//	    $ref: "#/definitions/Success"
//	400:
//	  description: Bad Request
//	  schema:
//	      $ref: "#/definitions/Error"
//	401:
//	  description: Unauthorized
//	  schema:
//	      $ref: "#/definitions/Error"
//	404:
//	  description: Not Found
//	  schema:
//	      $ref: "#/definitions/Error"
//	500:
//	  description: Internal Error
//	  schema:
//	      $ref: "#/definitions/Error"
func (api *UserAPI) UpdateUser(ctx *gin.Context) {
	a_common.WithParamForNumber(ctx, "id", func(id uint) {
		params := &m_user.UpdateParams{}
		if err := ctx.ShouldBind(params); err != nil {
			r_common.BadRequestNoData(ctx, statusx.ComplianceMonitorStatusFailedUpdateParamsBind,
				statusx.ComplianceMonitorStatusFailedUpdateParamsBindOfMessage, err,
			)
			/*--------------------------日志-------------------------------*/
			tracex.AddTrace(
				ctx,
				tracex.MethodNameUserUpdate,
				tracex.Operate,
				fmt.Sprintf(tracex.DesUserUpdate, ""))
			/*--------------------------日志-------------------------------*/
			return
		}

		// 如果手机号不为空的时候，增加正则校验
		if params.Tel != "" && !util.Mobile(params.Tel) {
			r_common.BadRequestNoData(ctx, statusx.BadRequestPhoneVeryErr, statusx.BadRequestPhoneVeryErrMessage, errors.New(statusx.BadRequestPhoneVeryErrMessage))
			return
		}

		/*--------------------------日志-------------------------------*/
		defer tracex.AddTrace(
			ctx,
			tracex.MethodNameUserUpdate,
			tracex.Operate,
			fmt.Sprintf(tracex.DesUserUpdate, params))
		/*--------------------------日志-------------------------------*/

		userInfo, err := api.DB.GetUserByID(id)
		if err != nil {
			r_common.InternalServerError(ctx, statusx.UserStatusFailedWithUpdateUser, statusx.UserStatusFailedWithUpdateUserOfMessage, err)
			return
		}

		// 管理员只能更新邮箱和电话号码
		if userInfo.RoleName == "administrator" {
			if err := api.DB.UpdateAdministratorUser(params, id); err != nil {
				r_common.InternalServerError(ctx, statusx.UserStatusFailedWithUpdateUser, statusx.UserStatusFailedWithUpdateUserOfMessage, err)
				return
			}
			r_common.OK(ctx)
			return
		}

		err = api.DB.UpdateUserByInstance(params, id)
		if err != nil {
			r_common.StatusConflict(ctx, statusx.UserStatusFailedWithUpdateUser, statusx.UserStatusFailedWithUpdateUserOfMessage, err)
			return
		}

		roles := make([]string, 0)
		if *params.VulScan {
			roles = append(roles, permission.VulScan)
		}
		if *params.AssetScan {
			roles = append(roles, permission.AssetScan)
		}
		if *params.Compliance {
			roles = append(roles, permission.Compliance)
		}
		permission.UpdateGrantRole(userInfo.Username, params.Username, roles...)

		r_common.OK(ctx)
	})
}
