package a_threat

import (
	"errors"
	"strings"

	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/model/m_visible_column"
	"git.gobies.org/foeye/foeye3/packages/secure/token"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"git.gobies.org/foeye/foeye3/responses/r_common"

	"github.com/gin-gonic/gin"
)

// CreateCustomField 漏洞管理-添加自定义模版。
// swagger:operation POST /v3/threats/fields 漏洞管理 CreateCustomField
//
//  漏洞管理-添加自定义模版。
//
// ---
// consumes: [application/json]
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// parameters:
// - name: body
//   in: body
//   description: 添加自定义模版.
//   required: true
//   schema:
//      $ref: "#/definitions/RequestCreateFieldTemplate"
// responses:
//   200:
//     description: Ok
//     schema:
//         $ref: "#/definitions/Success"
//   400:
//     description: Bad Request
//     schema:
//         $ref: "#/definitions/Error"
//   401:
//     description: Unauthorized
//     schema:
//         $ref: "#/definitions/Error"
//   500:
//     description: Internal Error
//     schema:
//         $ref: "#/definitions/Error"
func (api *ThreatAPI) CreateCustomField(ctx *gin.Context) {
	var body m_threat.RequestCreateFieldTemplate
	if err := ctx.ShouldBindJSON(&body); err != nil {
		r_common.BadRequestNoData(ctx, statusx.BadRequestForVerificationParameterCode, statusx.BadRequestForVerificationParameterMessage, err)
		return
	}

	if !strings.Contains(body.Content, "ip,common_title") || strings.HasSuffix(body.Content, ",") {
		r_common.BadRequestNoData(ctx,
			statusx.BadRequestForVerificationParameterCode, statusx.BadRequestForVerificationParameterMessage,
			errors.New(statusx.BadRequestForVerificationParameterMessage),
		)
		return
	}

	claims, err := token.GetUserInfo(ctx, api.configure, api.storage.Redis().Client())
	if err != nil {
		r_common.StatusUnauthorized(ctx, err)
	}

	has, err := api.ThreatStore.GetCustomFiledList(claims.ID, m_threat.PocTypeThreat)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.ThreatStatusFailedWithGetCustomFieldList, statusx.ThreatStatusFailedWithGetCustomFieldListOfMessage, err)
		return
	}

	items, ok := has.([]*m_visible_column.VisibleColumn)
	if !ok {
		r_common.InternalServerError(ctx, statusx.ThreatStatusFailedWithGetCustomFieldList,
			statusx.ThreatStatusFailedWithGetCustomFieldListOfMessage,
			errors.New(statusx.ThreatStatusFailedWithGetCustomFieldListOfMessage),
		)
		return
	}

	if len(items) >= 3 {
		r_common.ForbiddenNoData(ctx, statusx.ThreatStatusFailedWithCustomMaxNumber,
			statusx.ThreatStatusFailedWithCustomMaxNumberOfMessage,
			errors.New(statusx.ThreatStatusFailedWithCustomMaxNumberOfMessage),
		)
		return
	}

	instance := new(m_visible_column.VisibleColumn)
	instance.ItemType = body.ItemType
	instance.GroupName = body.Name
	instance.Content = body.Content
	instance.LastSelected = m_threat.DefaultLastSelected
	instance.UserId = claims.ID

	if err = api.ThreatStore.CreateCustomFieldItem(instance); err != nil {
		r_common.InternalServerError(ctx, statusx.ThreatStatusFailedWithCreateCustomField, statusx.ThreatStatusFailedWithCreateCustomFieldOfMessage, err)
		return
	}
	r_common.OK(ctx)
}
