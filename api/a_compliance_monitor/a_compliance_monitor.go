package a_compliance_monitor

import (
	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_asset_rule"
	"git.gobies.org/foeye/foeye3/database/db_compliance_monitor"
	"git.gobies.org/foeye/foeye3/database/db_ip_range"
	"git.gobies.org/foeye/foeye3/database/db_mail_server"
	"git.gobies.org/foeye/foeye3/database/db_scan_port/db_scan_port"
	"git.gobies.org/foeye/foeye3/database/db_tag"
	"git.gobies.org/foeye/foeye3/database/db_user"
	"git.gobies.org/foeye/foeye3/database/db_user_rules"
	"git.gobies.org/foeye/foeye3/database/db_violation_repair_records"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_assets"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_compliance_monitor"
	"git.gobies.org/foeye/foeye3/service"
	"git.gobies.org/foeye/foeye3/store"
)

// ComplianceAPIHandler interface for encapsulating handlers access.
type ComplianceAPIHandler interface {
	GetComplianceMonitorItems(ctx *gin.Context)

	GetComplianceMonitorList(ctx *gin.Context)
	CreateComplianceMonitor(ctx *gin.Context)
	DeleteComplianceMonitor(ctx *gin.Context)
	UpdateComplianceMonitor(ctx *gin.Context)
	UpdateComplianceMonitorStatus(ctx *gin.Context)
}

// UserAPI provides handlers for managing compliance monitor.
type ComplianceMonitorAPI struct {
	DB           database.GormComplianceDatabaseStore
	UserDB       database.UserDatabaseStore
	MailServerDB database.GormEmailServerDatabaseStore
	ElasticDB    elasticx.IndexComplianceMonitorStore
	ES           es_asset.ESAssetDatabaseInter

	ElasticFactory  elasticx.Factory
	DatabaseFactory database.Factory
	BaseDir         string

	IpRange                database.GormIPRangeDatabaseStore
	Protocols              database.GormProtocolDatabaseStore
	UserRule               db_user_rules.GormUserRuleDatabaseInter
	AssetElasticDB         elasticx.IndexAssetsStore
	ViolationRepairRecords database.GormViolationRepairRecordsDatabaseStore
	ScanPort               database.GormScanPortDatabaseStore
	PortsTemplates         database.GormPortsTemplatesDatabaseStore
	Tag                    database.GormTagDatabaseStore
	AssetRuleCount         database.GormAssetRuleCountDatabaseStore

	srv             service.Factory
	databaseFactory database.Factory
	configure       *config.Configure
	storage         store.Factory
}

// NewComplianceMonitorAPI
func NewComplianceMonitorAPI(
	GormDatabase *database.GormDatabase,
	elastic *elasticx.ElasticDatabase,
	configure *config.Configure,
	storage store.Factory,
	srv service.Factory,
	databaseFactory database.Factory,
	ElasticFactory elasticx.Factory,
) *ComplianceMonitorAPI {
	db := db_compliance_monitor.NewGormComplianceDatabase(GormDatabase)

	// User database operator.
	userDB := db_user.NewGormUserDatabase(configure, GormDatabase)

	// User database operator.
	MailServerDB := db_mail_server.NewGormEmailServerDatabase(GormDatabase)

	// ElasticSearch database operator.
	monitorDatabase := esi_compliance_monitor.NewStore(elastic)
	assetDatabase := esi_assets.NewStore(elastic)
	ipRange := &db_ip_range.GormIPRangeDatabase{Instance: GormDatabase}
	protocols := &db_scan_port.GormProtocolDatabase{Instance: GormDatabase}
	userRule := &db_user_rules.GormUserRuleDatabase{Instance: GormDatabase}
	violationRepairRecords := db_violation_repair_records.NewGormViolationRepairRecordsDatabase(GormDatabase)
	assetRuleCount := db_asset_rule.NewGormAssetRuleCountDatabase(GormDatabase)
	tag := db_tag.NewGormTagDatabase(GormDatabase)

	scanPort := db_scan_port.NewGormScanPortDatabase(GormDatabase)
	portsTemplates := &db_scan_port.GormPortsTemplatesDatabase{Instance: GormDatabase}

	es := es_asset.NewESAssetDatabase(configure, elastic, databaseFactory)
	es.Instance = elastic

	c := &ComplianceMonitorAPI{
		DB:           db,
		UserDB:       userDB,
		MailServerDB: MailServerDB,
		ElasticDB:    monitorDatabase,
		BaseDir:      "",
		ES:           es,

		AssetElasticDB:         assetDatabase,
		IpRange:                ipRange,
		Protocols:              protocols,
		UserRule:               userRule,
		ViolationRepairRecords: violationRepairRecords,
		ScanPort:               scanPort,
		PortsTemplates:         portsTemplates,
		Tag:                    tag,
		AssetRuleCount:         assetRuleCount,

		srv:             srv,
		databaseFactory: databaseFactory,
		DatabaseFactory: databaseFactory,
		ElasticFactory:  ElasticFactory,
		configure:       configure,
		storage:         storage,
	}

	return c
}
