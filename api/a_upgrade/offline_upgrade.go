package a_upgrade

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/database/db_user_rules"
	"git.gobies.org/foeye/foeye3/model/m_second_categories"
	"git.gobies.org/foeye/foeye3/model/m_upgrade"
	"git.gobies.org/foeye/foeye3/model/m_user_rules"
	"git.gobies.org/foeye/foeye3/packages/fsx"
	"git.gobies.org/foeye/foeye3/packages/jsoner"
	"git.gobies.org/foeye/foeye3/packages/license"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"git.gobies.org/foeye/foeye3/packages/systemx"
	"git.gobies.org/foeye/foeye3/packages/tracex"
	"git.gobies.org/foeye/foeye3/responses/r_common"
	"git.gobies.org/foeye/foeye3/responses/r_user_rules"
)

// OfflineUpgrade 上传程序升级包。
// swagger:operation POST /v3/systems/upgrade_upload 升级管理 OfflineUpgrade
//
// 上传程序升级包。
//
// ---
// consumes: [multipart/form-data]
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// parameters:
//   - name: file
//     in: formData
//     description: from—data表单上传 key为file，value是需要上传的文件
//     required: true
//     type: file
//
// responses:
//
//	200:
//	  description: Ok
//	  schema:
//	      $ref: "#/definitions/Success"
//	400:
//	  description: Bad Request
//	  schema:
//	      $ref: "#/definitions/Error"
//	401:
//	  description: Unauthorized
//	  schema:
//	      $ref: "#/definitions/Error"
//	500:
//	  description: Internal Error
//	  schema:
//	      $ref: "#/definitions/Error"
func (api *UpgradeAPI) OfflineUpgrade(ctx *gin.Context) {
	// 获取升级包内容
	file, err := ctx.FormFile("file")
	if err != nil {
		r_common.BadRequestNoData(ctx, statusx.UpgradeStatusFailedWithGetFileContent, statusx.UpgradeStatusFailedWithGetFileContentOfMessage, err)
		/*--------------------------日志-------------------------------*/
		tracex.AddTrace(
			ctx,
			tracex.MethodNameOfflineUpgrade,
			tracex.Operate,
			fmt.Sprintf(tracex.DesOfflineUpgrade, ""))
		/*--------------------------日志-------------------------------*/
		return
	}

	/*--------------------------日志-------------------------------*/
	defer tracex.AddTrace(
		ctx,
		tracex.MethodNameOfflineUpgrade,
		tracex.Operate,
		fmt.Sprintf(tracex.DesOfflineUpgrade, file.Filename))
	/*--------------------------日志-------------------------------*/

	// 判断服务到期时间
	// 获取激活状态，到期时间，资产上限
	// 读取production.yml
	productionPath := fsx.ProductionYmlFilePath.String()
	production, err := systemx.ReadProductionYamlToStruct(productionPath)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.LicenseStatusFailedWithParseProductionYaml, statusx.LicenseStatusFailedWithParseProductionYamlOfMessage, err)
		return
	}
	systemInfo, err := systemx.GetSystemInfo(production.AssetScanUrl)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.LicenseStatusFailedWithParseProductionYaml, statusx.LicenseStatusFailedWithParseProductionYamlOfMessage, err)
		return
	}

	if !systemInfo.Data.LicenseState {
		r_common.InternalServerError(ctx, statusx.UpgradeStatusFailedWithLicenseStateExpired, statusx.UpgradeStatusFailedWithLicenseStateExpiredOfMessage, err)
		return
	}

	// 任何时候，都要判断服务是否到期，如果服务到期则提示 无法升级服务到期
	upgradeTime, err := time.ParseInLocation("2006-01-02", systemInfo.Data.UpgradeLimitDate, time.Local)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.UpgradeStatusFailedWithParseInLocation, statusx.UpgradeStatusFailedWithParseInLocationOfMessage, err)
		return
	}
	subTime := upgradeTime.Sub(time.Now()).Seconds()
	if subTime <= 0 {
		r_common.ForbiddenNoData(ctx, statusx.UpgradeStatusFailedWithUpgradeTimeExpired,
			statusx.UpgradeStatusFailedWithUpgradeTimeExpiredOfMessage,
			errors.New(statusx.UpgradeStatusFailedWithUpgradeTimeExpiredOfMessage),
		)
		return
	}

	// 判断上传文件名称是否符合规则升级
	ext := path.Ext(file.Filename)
	if ext != ".dat" {
		r_common.BadRequestNoData(ctx, statusx.UpgradeStatusFailedWithFilename, statusx.UpgradeStatusFailedWithFilenameOfMessage, err)
		return
	}

	// 获取运行中任务数量
	// runningNum, err := api.DB.HaveTaskingCount()
	task, err := api.DB.TaskExists()
	if err != nil {
		r_common.InternalServerError(ctx, statusx.UpgradeStatusFailedWithHaveTaskingCount, statusx.UpgradeStatusFailedWithHaveTaskingCountOfMessage, err)
		return
	}

	// 判断是否有任务正在运行
	if task != nil && len(task) > 0 {
		r_common.ForbiddenNoData(ctx, statusx.UpgradeStatusFailedWithHaveRunningTask,
			statusx.UpgradeStatusFailedWithHaveRunningTaskOfMessage,
			errors.New(statusx.UpgradeStatusFailedWithHaveRunningTaskOfMessage),
		)
		return
	}

	// 判断是否有下载任务正在运行
	//conditions := make([]interface{}, 0)
	//conditions = append(conditions, "down_load_records.state = ?", m_down_load_record.StateGenerating)
	//count, err := api.DownLoadRecord.CountItems(conditions...)
	//if count > 0 || err != nil {
	//	r_common.SuccessOrAbortWithCustomStatusCode(err, r_common.SuccessOrAbortInfo{
	//		CTX:        ctx,
	//		Code:       http.StatusInternalServerError,
	//		CustomCode: statusx.UpgradeStatusFailedWithHaveRunningDownload,
	//		Error:      errors.New(statusx.UpgradeStatusFailedWithHaveRunningDownloadOfMessage),
	//	})
	//	return
	//}

	// 保存升级包
	upgradePath, _ := fsx.GetUploadSavePathOfUpgrade(api.configure, "")
	upgradePathMd5 := filepath.Join(upgradePath, "md5.txt")
	upgradePathTmp := filepath.Join(upgradePath, "updatefoeye_tmp.dat")
	err = ctx.SaveUploadedFile(file, upgradePathTmp) // foeye3的升级还没做
	if err != nil {
		r_common.InternalServerError(ctx, statusx.UpgradeStatusFailedWithSaveUpgradeFile, statusx.UpgradeStatusFailedWithSaveUpgradeFileOfMessage, err)
		return
	}
	// 获取文件md5
	fileMd5, err := fsx.GetFileMd5(upgradePathTmp)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.UpgradeStatusFailedWithGetFileMd5, statusx.UpgradeStatusFailedWithGetFileMd5OfMessage, err)
		return
	}
	// 写入文件
	err = ioutil.WriteFile(upgradePathMd5, []byte(fileMd5), os.ModePerm)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.UpgradeStatusFailedWithWriteFile, statusx.UpgradeStatusFailedWithWriteFileOfMessage, err)
		return
	}

	upgradePathReal := filepath.Join(upgradePath, "updatefoeye.dat")
	err = os.Rename(upgradePathTmp, upgradePathReal)
	if err != nil {
		r_common.InternalServerError(ctx, statusx.UpgradeStatusFailedWithRenameUpgradeFile, statusx.UpgradeStatusFailedWithRenameUpgradeFileOfMessage, err)
		return
	}

	systemx.UpgradeHandle(api.configure, api.storage.Redis().Client())

	//如果是神通数据库则备份我的自定义规则（解决神通数据库升级丢失我的规则数据的问题）
	if api.configure.Database.Type == "OSCAR" {
		go api.BackUpMyRules()
	}

	r_common.OK(ctx)
}

// GetUpgradeProgress 获取程序升级进度。
// swagger:operation GET /v3/systems/upgrade_upload 升级管理 GetUpgradeProgress
//
// 获取程序升级进度。
//
// ---
// consumes: [multipart/form-data]
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// responses:
//
//	200:
//	  description: Ok
//	  schema:
//	      $ref: "#/definitions/UpgradeProcessResponse"
//	400:
//	  description: Bad Request
//	  schema:
//	      $ref: "#/definitions/Error"
//	401:
//	  description: Unauthorized
//	  schema:
//	      $ref: "#/definitions/Error"
//	500:
//	  description: Internal Error
//	  schema:
//	      $ref: "#/definitions/Error"
func (api *UpgradeAPI) GetUpgradeProgress(ctx *gin.Context) {
	var data m_upgrade.UpgradeProcessResponse
	// 从指定文件获取升级状态
	upgradePath, _ := fsx.GetUploadSavePathOfUpgrade(api.configure, "")
	lineOne := strings.Trim(systemx.ReadContent(upgradePath, "updatefoeye.log"), "\n")
	// 从redis中获取升级状态
	rdsStatusRaw, _ := api.storage.Redis().Client().Get("upgrade_status").Bytes()
	rdsStatus := string(rdsStatusRaw)
	// 升级进度
	progress := strings.Trim(systemx.ReadContent(upgradePath, "progress.txt"), "\n")
	// 是否升级成功
	success := true
	// 升级失败信息
	message := ""
	fmt.Println("lineone:", lineOne)
	fmt.Println("progress:", progress)
	fmt.Println(systemx.IsContain([]string{"2", "3"}, lineOne))
	if systemx.IsContain([]string{"2", "3"}, lineOne) || systemx.IsContain([]string{"2", "3"}, rdsStatus) {
		api.storage.Redis().Client().Del(".keep.in.upgrade")
		if lineOne == "2" || rdsStatus == "2" {
			success = true
		}
		systemx.SaveContent(upgradePath, "updatefoeye.log", "")
		systemx.SaveContent(upgradePath, "progress.txt", "")
		if lineOne == "3" || rdsStatus == "3" {
			success = false
			failurePath := filepath.Join(upgradePath, "failure.txt")
			if failurePath != "" && systemx.PathExist(failurePath) {
				messageRaw, _ := ioutil.ReadFile(failurePath)
				message = string(messageRaw)
			}
		}
	}
	if rdsStatus != "" && lineOne != rdsStatus {
		lineOne = rdsStatus
	}
	message = systemx.StringFy(message)

	// 从内部到外部
	progressInt, err := strconv.Atoi(progress)
	if err != nil {
		data.Progress = progress
	} else if progressInt >= 1 {
		data.Progress = strconv.Itoa(progressInt - 1)
	} else {
		data.Progress = progress
	}
	data.State = lineOne
	data.NeedUpdateHistoryData = false
	data.Success = success
	data.Message = message
	if data.State == "2" {
		data.Progress = "100"
		license.FlushLicenseInfo(api.storage.Redis().Client())
		//如果是神通数据库则恢复备份我的自定义规则（解决神通数据库升级丢失我的规则数据的问题）
		if api.configure.Database.Type == "OSCAR" {
			go api.RestoreMyRules()
		}
	}
	r_common.Success(ctx, r_common.ResponseList{Data: data})
}

// ClearUpgradeUpdateFoeyeLog 清除升级标识，防止升级问题。
func (api *UpgradeAPI) ClearUpgradeUpdateFoeyeLog(ctx *gin.Context) {
	logFile := "./tmp/update/updatefoeye.log"
	file, err := os.OpenFile(logFile, os.O_WRONLY, 0666)
	if err != nil {
		r_common.InternalServerError(ctx,
			statusx.PocWeakPasswordCountFailedOpenFile,
			statusx.PocWeakPasswordCountFailedOpenFileMessage,
			err,
		)
		return
	}
	defer file.Close()

	err = file.Truncate(0)
	if err != nil {
		r_common.InternalServerError(ctx,
			statusx.PocWeakPasswordCountFailedOpenFile,
			statusx.PocWeakPasswordCountFailedOpenFileMessage,
			err,
		)
		return
	}

	r_common.OK(ctx)
}

func (api *UpgradeAPI) BackUpMyRules() {
	log.Info("[BackUpMyRules] start")

	//获取用户自定义的规则 仅备份1000条
	myRuleList, err := api.UserRulesDB.GetMyRuleList(r_user_rules.QueryListAndKeyword{
		QueryList: &r_common.QueryList{
			Size:   1000,
			Number: 1,
		},
	})

	if err != nil {
		log.Errorf("[BackUpMyRules] GetMyRuleList error:%v", err)
	}

	ruleIds := make([]uint, len(myRuleList))
	for i, rule := range myRuleList {
		ruleIds[i] = rule.ID
	}

	//获取用户自定义的规则对应的分类
	secondCategoryRules, err := api.UserRulesDB.GetSecondCategoryRulesByRuleIds(ruleIds)
	if err != nil {
		log.Errorf("[BackUpMyRules] GetSecondCategoryRulesByRuleIds error:%v", err)
	}

	//myRuleList写入json文件
	if err = jsoner.JsonToJsonFile(db_user_rules.MyRulesJsonPath, myRuleList); err != nil {
		log.Errorf("[BackUpMyRules]  write file  my_rules.json error:%v", err)
	}

	//secondCategoryRules写入json文件
	if err = jsoner.JsonToJsonFile(db_user_rules.MyRulesSecondCategoryJsonPath, secondCategoryRules); err != nil {
		log.Errorf("[BackUpMyRules] write file my_rules_second_category.json error:%v", err)
	}

	log.Info("[BackUpMyRules] end")

}

func (api *UpgradeAPI) RestoreMyRules() {
	log.Info("[RestoreMyRules] start")

	myRules := jsoner.ReadJsonFileToString(db_user_rules.MyRulesJsonPath)

	var mRList []m_user_rules.UserRule
	err := json.Unmarshal([]byte(myRules), &mRList)
	if err != nil {
		log.Errorf("[RestoreMyRules] json.Unmarshal error:%v", err)
	}

	log.Infof("[RestoreMyRules] myRules info:%+v", myRules)

	err = api.UserRulesDB.CreateUserRules(mRList)
	if err != nil {
		log.Errorf("[RestoreMyRules] CreateUserRules error:%v", err)
	}

	myRuleCategoryies := jsoner.ReadJsonFileToString(db_user_rules.MyRulesSecondCategoryJsonPath)
	var myRuleCateList []m_second_categories.SecondCategoryRule
	err = json.Unmarshal([]byte(myRuleCategoryies), &myRuleCateList)

	if err != nil {
		log.Errorf("[RestoreMyRules] json.Unmarshal error:%v", err)
	}

	log.Infof("[RestoreMyRules] myRuleCategoryies info:%+v", myRuleCategoryies)

	err = api.UserRulesDB.CreateUserRuleCategories(myRuleCateList)
	if err != nil {
		log.Errorf("[RestoreMyRules] CreateUserRuleCategories error:%v", err)
	}

	log.Info("[RestoreMyRules] end")
}
