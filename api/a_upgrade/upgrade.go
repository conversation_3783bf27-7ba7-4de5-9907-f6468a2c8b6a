package a_upgrade

import (
	"git.gobies.org/foeye/foeye3/api/a_user_rules"
	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_down_load_record"
	"git.gobies.org/foeye/foeye3/database/db_network"
	"git.gobies.org/foeye/foeye3/database/db_system"
	"git.gobies.org/foeye/foeye3/database/db_task"
	"git.gobies.org/foeye/foeye3/database/db_user_rules"
	"git.gobies.org/foeye/foeye3/store"

	"github.com/gin-gonic/gin"
)

// UpgradeAPIHandler 升级管理操作接口
type UpgradeAPIHandler interface {
	OfflineUpgrade(ctx *gin.Context)
	GetUpgradeProgress(ctx *gin.Context)
	ClearUpgradeUpdateFoeyeLog(ctx *gin.Context)
}

// UpgradeAPI 升级管理操作实例
type UpgradeAPI struct {
	DB             database.GormTaskDatabaseStore
	System         database.GormSystemDatabaseStore
	Network        database.GormSystemNetworkDatabaseStore
	DownLoadRecord database.GormDownLoadRecordDatabaseStore
	UserRulesDB    a_user_rules.UserRulesDatabaseInter
	ComponentDB    database.GormSecondCategoriesDatabaseStore

	configure *config.Configure
	storage   store.Factory
}

// NewUpgradeAPI 创建一个API实例
func NewUpgradeAPI(DB *database.GormDatabase, configure *config.Configure, storage store.Factory) (*UpgradeAPI, error) {
	db := &db_task.GormTaskDatabase{Instance: DB}
	systemDB := db_system.NewGormSystemDatabase(DB)
	networkDB := &db_network.GormSystemNetworkDatabase{Instance: DB}
	downLoadRecord := db_down_load_record.NewGormDownLoadRecordDatabase(DB)

	userRulesDB := new(db_user_rules.GormUserRuleDatabase)
	userRulesDB.Instance = DB

	//return &OfflineUpgradeAPI{DB: db}, nil
	c := &UpgradeAPI{
		DB:             db,
		System:         systemDB,
		Network:        networkDB,
		DownLoadRecord: downLoadRecord,
		UserRulesDB:    userRulesDB,

		configure: configure,
		storage:   storage,
	}

	return c, nil
}
