package a_overview_xc

import (
	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_asset_history"
	"git.gobies.org/foeye/foeye3/database/db_history_record"
	"git.gobies.org/foeye/foeye3/database/db_ip_histories"
	"git.gobies.org/foeye/foeye3/database/db_scan_port/db_scan_port"
	"git.gobies.org/foeye/foeye3/database/db_scan_port/db_scan_port_template"
	"git.gobies.org/foeye/foeye3/database/db_second_categories"
	"git.gobies.org/foeye/foeye3/database/db_tag"
	"git.gobies.org/foeye/foeye3/database/db_task"
	"git.gobies.org/foeye/foeye3/database/db_user"
	"git.gobies.org/foeye/foeye3/database/db_user_ability"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_assets"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_compliance_monitor"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_subdomain"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_threats"
	"git.gobies.org/foeye/foeye3/store"

	"github.com/gin-gonic/gin"
)

// OverViewAPIHandler interface for encapsulating http handles for assets.
type OverViewAPIHandler interface {
	GetOverviewsInfo(ctx *gin.Context)
	GetAssetsPie(ctx *gin.Context)
}

// OverViewAPI provides handlers for managing Over View.
type OverViewAPI struct {
	ComponentDB        database.GormSecondCategoriesDatabaseStore
	TagDB              database.GormTagDatabaseStore
	ES                 es_asset.ESAssetDatabaseInter
	SubDomainES        elasticx.IndexSubDomainStore
	AssetElasticDB     elasticx.IndexAssetsStore
	ThreatElasticDB    elasticx.IndexThreatsStore
	ViolationDB        elasticx.IndexComplianceMonitorStore
	History            *db_asset_history.GormAssetHistoryDatabase
	IpHistory          *db_ip_histories.GormIPHistoriesDatabase
	HistoryRecordDB    *db_history_record.GormHistoryRecordDatabase
	Store              database.GormScanPortDatabaseStore
	ScanPortTemplateDB *db_scan_port_template.GormScanPortTemplateDatabase
	TaskDB             *db_task.GormTaskDatabase
	UserDB             *db_user.GormUserDatabase
	UserAbilityDB      *db_user_ability.GormUserAbilityDatabase
	configure          *config.Configure
	storage            store.Factory
}

// NewOverViewXcAPI creates a new NewOverViewAPI instance.
func NewOverViewXcAPI(DB *database.GormDatabase, ES *elasticx.ElasticDatabase, configure *config.Configure,
	storage store.Factory, databaseFactory database.Factory) (*OverViewAPI, error) {
	historyDB := new(db_asset_history.GormAssetHistoryDatabase)
	historyDB.Instance = DB

	if err := historyDB.UpdateItemIsNull(); err != nil {
		return nil, err
	}

	compoDb := new(db_second_categories.GormSecondCategoriesDatabase)
	compoDb.Instance = DB

	es := es_asset.NewESAssetDatabase(configure, ES, databaseFactory)
	es.Instance = ES
	tagDb := new(db_tag.GormTagDatabase)
	tagDb.Instance = DB

	assetsElasticDB := esi_assets.NewStore(ES)
	subdoaminEs := esi_subdomain.NewStore(ES)
	threatElasticDB := esi_threats.NewStore(ES)
	violationDB := esi_compliance_monitor.NewStore(ES)
	ipHistory := db_ip_histories.NewGormIPHistoriesDatabase(DB)
	taskDB := db_task.NewGormTaskDatabase(DB)

	historyRecordDB := db_history_record.NewGormHistoryRecordDatabase(DB)

	scanPortTemplateDB := db_scan_port_template.NewGormScanPortTemplateDatabase(DB)

	userDB := db_user.NewGormUserDatabase(configure, DB)

	userAbilityDB := db_user_ability.NewGormUserAbilityDatabase(DB)
	store := db_scan_port.NewGormScanPortDatabase(DB)

	return &OverViewAPI{ES: es, TagDB: tagDb, ComponentDB: compoDb,
		History: historyDB, configure: configure, storage: storage,
		SubDomainES: subdoaminEs, AssetElasticDB: assetsElasticDB,
		ThreatElasticDB: threatElasticDB, ViolationDB: violationDB,
		IpHistory: ipHistory, HistoryRecordDB: historyRecordDB,
		TaskDB: taskDB, UserDB: userDB, UserAbilityDB: userAbilityDB, ScanPortTemplateDB: scanPortTemplateDB, Store: store}, nil
}
