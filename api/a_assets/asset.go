package a_assets

import (
	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_asset_rule"
	"git.gobies.org/foeye/foeye3/database/db_down_load_record"
	"git.gobies.org/foeye/foeye3/database/db_ip_histories"
	"git.gobies.org/foeye/foeye3/database/db_scan_port/db_scan_port"
	"git.gobies.org/foeye/foeye3/database/db_second_categories"
	"git.gobies.org/foeye/foeye3/database/db_tag"
	"git.gobies.org/foeye/foeye3/database/db_task"
	"git.gobies.org/foeye/foeye3/database/db_threat"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_assets"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_threats"
	"git.gobies.org/foeye/foeye3/service"
	"git.gobies.org/foeye/foeye3/store"
)

// AssetAPIHandler interface for encapsulating http handles for assets.
type AssetAPIHandler interface {
	GetAssetList(ctx *gin.Context)
	GetAssetIpList(ctx *gin.Context)
	GetAssetBusinessList(ctx *gin.Context)
	GetAssetDomainList(ctx *gin.Context)
	ExportAssets(ctx *gin.Context)
	GetAssetStatistics(ctx *gin.Context)
	GetAssetFilterCondition(ctx *gin.Context)
	UpdateTableView(ctx *gin.Context)
	DeleteAssets(ctx *gin.Context)
	UpdateAssetTags(ctx *gin.Context)
	GetAssetDetails(ctx *gin.Context)
	GetAssetOverview(ctx *gin.Context)
}

// AssetAPI provides handlers for managing assets.
type AssetAPI struct {
	DownLoadRecordDB database.GormDownLoadRecordDatabaseStore
	ComponentDB      database.GormSecondCategoriesDatabaseStore
	TagDB            database.GormTagDatabaseStore
	IpHistoriesDB    database.GormIPHistoriesDatabaseStore
	AssetRuleCountDB database.GormAssetRuleCountDatabaseStore
	ScanPortDB       database.GormScanPortDatabaseStore
	TaskDB           database.GormTaskDatabaseStore
	ThreatStore      database.GormThreatStore
	ES               es_asset.ESAssetDatabaseInter
	AssetElasticDB   elasticx.IndexAssetsStore
	ThreatElasticDB  elasticx.IndexThreatsStore
	Srv              service.Factory
	configure        *config.Configure
	storage          store.Factory
	mysqlFactory     database.Factory
	elasticFactory   elasticx.Factory
	baseDir          string
}

// NewAssetAPI creates a new AssetAPI instance.
func NewAssetAPI(DB *database.GormDatabase, ES *elasticx.ElasticDatabase, configure *config.Configure, storage store.Factory,
	service service.Factory, mysqlFactory database.Factory, elasticFactory elasticx.Factory) (*AssetAPI, error) {
	compoDb := db_second_categories.NewGormSecondCategoriesDatabase(DB)
	ipHistoryDB := db_ip_histories.NewGormIPHistoriesDatabase(DB)
	assetRuleCountDB := db_asset_rule.NewGormAssetRuleCountDatabase(DB)
	scanPortDB := db_scan_port.NewGormScanPortDatabase(DB)
	threatStore := db_threat.NewGormThreatDatabase(DB)
	taskDB := db_task.NewGormTaskDatabase(DB)
	tagDb := db_tag.NewGormTagDatabase(DB)
	downLoadRecordDB := db_down_load_record.NewGormDownLoadRecordDatabase(DB)

	es := es_asset.NewESAssetDatabase(configure, ES, mysqlFactory)
	es.Instance = ES

	assetES := esi_assets.NewStore(ES)
	threatES := esi_threats.NewStore(ES)

	return &AssetAPI{
		ES:               es,
		DownLoadRecordDB: downLoadRecordDB,
		TaskDB:           taskDB,
		TagDB:            tagDb,
		IpHistoriesDB:    ipHistoryDB,
		AssetRuleCountDB: assetRuleCountDB,
		ComponentDB:      compoDb,
		ScanPortDB:       scanPortDB,
		ThreatStore:      threatStore,
		AssetElasticDB:   assetES,
		ThreatElasticDB:  threatES,
		Srv:              service,
		configure:        configure,
		storage:          storage,
		baseDir:          "./",
		mysqlFactory:     mysqlFactory,
		elasticFactory:   elasticFactory,
	}, nil
}
