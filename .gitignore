# Project file
.idea
config.*.toml
!config.example.toml
!config.upgrade.toml
*.sh
logs/*

api/a_network/foeye.*
static/assets/txt/*
source.out
coverage.out
coverage.html
coverage-report.html
junit.xml
poc_new.json
nohup.out
api/risk/danger/danger/static/download/excel/asset/

# Temp file
tmp/runner-build
tmp/update/*
tmp/dcc/*
tmp/protocol/*
!tmp/update/.keep
!tmp/dcc/.keep
*.tmp.*
*.tmp*
zipx.zip
main
*.bak
*.log
*.swp
test/testfile/tem/*

# Depends package
vendor/*
!vendor/golang.org/
!vendor/vendor.json
!vendor/modules.txt

# Static file
static/*
!static/template/
static/assets/excel/202*.xlsx
static/assets/download/*
!static/assets/download/.keep
static/assets/upload/*
!static/assets/upload/.keep
!static/assets/
!static/upgrade/
static/bin/
!static/fonts/
!static/region/json
static/rules
!static/download/report/.keep
!static/backup/.keep
!static/restore/.keep
!static/settings/.keep
!static/secret
!static/download/excel/compliance_monitor/.keep
!static/download/excel/ip_range/ip_information_import_template.xlsx
!static/download/manual/user_manual.pdf
!static/download/manual/api_manual.pdf

# Binary file
FOEYE3
foeye3
cmd/foeyectl/foeyectl
cmd/decryptctl/decryptctl

# Swagger related files.
docs/spec.json
internal/mux/docs/spec.json
vendor

# Temp files.
*mosso.txt
*mosso.json
*mosso.log
*template_with_notification.html
*_TEST_*
*.DS_Store
test_data
fresh.conf
!tools/scripts/*
tmp/payload
build

internal/mux/api/a_backup_restore/static/
a_backup_restore/static/
packages/fsx.zip
api/a_download_log/static/download/zip/log/es_all_logs.zip
api/a_backup_restore/static/
dump.rdb
api/a_ip_range/static/

# Unit test files.
packages/etchosts/etc
foeye3.linux.amd64
foeye3.darwin.amd64

# Dependence packages
packages/riskrecords
.vscode/
.air.toml
__debug_bin
__debug_bin.exe
VERSION
POC_VERSION

service/static/
internal/mux/api/a_upgrade/tmp/update/progress.txt
internal/mux/api/a_upgrade/tmp/update/updatefoeye.dat
service/static/download/excel/asset/

public/task_data_leakage
api/risk/data_leak/public/
service/svc_risk/static/

api/a_threat/static/

coverage.txt
sorted_coverage.txt
