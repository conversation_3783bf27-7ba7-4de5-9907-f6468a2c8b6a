package m_asset

import "git.gobies.org/foeye/foeye3/model/m_ip_histories"

// ResponseAssetDetail  Model
//
// 数据模型 ResponseAssetDetail 。
//
// swagger:model ResponseAssetDetail
type ResponseAssetDetail struct {
	// 自定义标签.
	//
	// required: true
	// read only: false
	CustomFields interface{} `json:"custom_fields"`

	// 资产详情.
	//
	// required: true
	// read only: false
	Asset AssetDetail `json:"asset"`

	// 端口信息-端口/协议/组件.
	//
	// required: true
	// read only: false
	PortList []*PortList `json:"port_list"`

	// 组件信息-组件分层(五层).
	//
	// required: true
	// read only: false
	RuleInfos []*RuleInfos `json:"rule_infos"`

	Changes    []*m_ip_histories.Changes `json:"changes"`
	AulDetails []*AssetAulDetail         `json:"aul_details"`
	Violations []*AssetViolation         `json:"violations"`

	// IP画像-组件分层(五层).
	//
	// required: true
	// read only: false
	IpPortrait []*IpPortrait `json:"ip_portrait"`
}

type AssetDetail struct {
	// IP地址.
	//
	// required: true
	// read only: false
	// example: "************"
	IP string `json:"ip"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: "重要资产"
	AssetLevel string `json:"asset_level"`

	// 硬件类型.
	//
	// required: true
	// read only: false
	// example: "交换机"
	HardTypeName string `json:"hard_type_name"`

	// Mac地址.
	//
	// required: true
	// read only: false
	// example: "00:0c:29:82:14:43"
	Mac string `json:"mac"`

	// 厂商名称.
	//
	// required: true
	// read only: false
	// example: "XX业务系统"
	BusinessApp string `json:"business_app"`

	// 资产负责人.
	//
	// required: true
	// read only: false
	// example: "张三"
	Username string `json:"username"`

	// 机房信息.
	//
	// required: true
	// read only: false
	// example: "北京机房"
	ComputerRoom string `json:"computer_room"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: "XX科技有限公司"
	Company string `json:"company"`

	// 操作系统.
	//
	// required: true
	// read only: false
	// example: "Linux/3.2 - 4.9"
	Os string `json:"os"`

	// 地理位置.
	//
	// required: true
	// read only: false
	// example: "东城区"
	City string `json:"city"`

	// 地理位置.
	//
	// required: true
	// read only: false
	// example: "北京市"
	Province string `json:"province"`

	// 域名.
	//
	// required: true
	// read only: false
	Hosts []string `json:"hosts"`

	// 备注信息.
	//
	// required: true
	// read only: false
	// example: "网络资产测绘及风险分析系统"
	Name string `json:"name"`

	// RDP截图.
	//
	// required: true
	// read only: false
	// example: images/rdp/************-3389.png
	RdpImage string `json:"rdp_image"`

	// 自定义标签.
	//
	// required: true
	// read only: false
	CustomFields map[string]string `json:"custom_tag"`

	// 主机名称(新加).
	//
	// required: true
	// read only: false
	// example: kitty
	Hostname string `json:"hostname"`

	// 网站标题.
	//
	// required: true
	// read only: false
	// example: 网络资产与安全风险综合管控平台
	WebsiteTitle string `json:"website_title"`

	// 纬度.
	//
	// required: true
	// read only: false
	// example: 39.9289
	Latitude float64 `json:"latitude"`

	// 经度.
	//
	// required: true
	// read only: false
	// example: 116.3883
	Longitude float64 `json:"longitude"`

	// 域信息
	Asn AssetAsn `json:"asn"`
}

// AssetAsn  Model
//
// 数据模型 AssetAsn 。
//
// swagger:model AssetAsn
type AssetAsn struct {
	// as_number.
	//
	// required: true
	// read only: false
	// example: 4837
	Number int `json:"as_number"`

	// as_organization.
	//
	// required: true
	// read only: false
	// example: CHINA UNICOM China169 Backbone
	Organization string `json:"as_organization"`
}

// AssetAulDetail  Model
//
// 资产漏洞数据模型 AssetAulDetail 。
//
// swagger:model AssetAulDetail
type AssetAulDetail struct {

	// 漏洞名称.
	//
	// required: true
	// read only: false
	// example: "NFS协议未授权访问"
	CommonTitle string `json:"common_title"`

	// 漏洞等级.
	//
	// required: true
	// read only: false
	// example: 1
	Level int `json:"level"`

	// 漏洞类型.
	//
	// required: true
	// read only: false
	// example: "未授权访问"
	VulType string `json:"vulType"`

	// cve编号.
	//
	// required: true
	// read only: false
	// example: "CVE-2021-22205"
	CveID string `json:"cveId"`

	// 备注信息
	//
	// required: true
	// read only: false
	// example: "test"
	Name string `json:"name"`

	// 漏洞描述.
	//
	// required: true
	// read only: false
	// example: "张三"
	Description string `json:"common_description"`

	// 漏洞危害.
	//
	// required: true
	// read only: false
	// example: "张三"
	Impact string `json:"common_impact"`

	// 解决方案.
	//
	// required: true
	// read only: false
	// example: "张三"
	Recommendation string `json:"recommandation"`

	// 状态(未修复 : 1,2).
	//
	// required: true
	// read only: false
	// example: 2
	State int `json:"state"`

	// 创建时间.
	//
	// required: true
	// read only: false
	// example: 2021-12-1 09:00:15
	CreateTime string `json:"createtime"`

	// 修复状态（整合后).
	//
	// required: true
	// read only: false
	// example: (unfixed：未修复， fixed：已修复)
	FixStatus string `json:"fix_status"`

	// 组件信息.
	//
	// required: true
	// read only: false
	// example: [{"icon":"NGINX","title":"NGINX"},{"icon":"Oracle-MySQL","title":"Oracle-MySQL/5.7"}]
	AulModule []AulModule `json:"aul_module"`

	VulFile string `json:"-"`
}

// AssetViolation  Model
//
// 资产违规数据模型 AssetViolation 。
//
// swagger:model AssetViolation
type AssetViolation struct {

	// IP地址.
	//
	// required: true
	// read only: false
	// example: ***********
	IP string `json:"ip"`

	// 端口信息.
	//
	// required: true
	// read only: false
	// example: IP被占用
	Content string `json:"content"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: 2022-04-15 13:54:01
	Createtime string `json:"createtime"`

	// 通报时间.
	//
	// required: true
	// read only: false
	// example: 2022-04-15 13:54:01
	NoticedAt string `json:"noticed_at"`

	// 核查时间.
	//
	// required: true
	// read only: false
	// example: 2022-04-15 13:54:01
	InspectedAt string `json:"inspected_at"`

	// 修复情况.
	//
	// required: true
	// read only: false
	// example: not_repair
	Status string `json:"status"`

	// 检测类型.
	//
	// required: true
	// read only: false
	// example: ip_occupied
	MonitoringType string `json:"monitoring_type"`

	// swagger:ignore
	ViolationType string `json:"violation_type,omitempty"`

	//  监测项目前缀类型和Content 拼接成违规项.
	//
	// required: true
	// read only: false
	// example: 重要IP资源被占用监测
	ContentPre string `json:"content_pre"`
}

// IpPortrait  Model
//
// 资产违规数据模型 IpPortrait IP画像数据结构。
//
// swagger:model IpPortrait
type IpPortrait struct {
	// 协议名称.
	//
	// required: true
	// read only: false
	// example: http
	Protocol string `json:"protocol"`

	// 端口名称.
	//
	// required: true
	// read only: false
	// example: 80
	Port int `json:"port"`

	// 组件分层(五层).
	//
	// required: true
	// read only: false
	RuleInfos []*RuleInfos `json:"rule_infos"`
}

// ResponseDomainDetail 域名画像
type ResponseDomainDetail struct {
	// 漏洞详情
	AulDetails []*AssetAulDetail `json:"aul_details"`
	// 域名解析

}
