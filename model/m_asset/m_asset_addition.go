package m_asset

import (
	"strings"
)

// ResultAssetList Model.
//
// 数据模型 ResultAssetList 资产数据管理-卡片维度。
//
// swagger:model ResultAssetList
type ResultAssetList struct {
	// 列表相关信息.
	//
	// required: true
	// read only: false
	AssetListInfo []*AssetListInfo `json:"info"`

	// 自定义标签.
	//
	// required: true
	// read only: false
	CustomFields interface{} `json:"custom_fields"`
}

type AssetListInfo struct {
	UID string `json:"uid"`

	// 记录ID.
	//
	// required: true
	// read only: false
	// example: "************"
	ID string `json:"id"`

	// IP地址.
	//
	// required: true
	// read only: false
	// example: "************"
	IP string `json:"ip"`

	// 在线状态(在线:1,离线:0).
	//
	// required: true
	// read only: false
	// example: 1
	State int `json:"state"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: "重要资产"
	AssetLevel string `json:"asset_level"`

	// 备注信息.
	//
	// required: true
	// read only: false
	// example: "网络资产测绘及风险分析系统"
	Name string `json:"name"`

	// Mac地址.
	//
	// required: true
	// read only: false
	// example: "00:0c:29:82:14:43"
	Mac string `json:"mac"`

	// 地理位置.
	//
	// required: true
	// read only: false
	// example: "东城区"
	City string `json:"city"`

	// 地理位置.
	//
	// required: true
	// read only: false
	// example: "北京市"
	Province string `json:"province"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: "XX科技有限公司"
	Company string `json:"company"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: "XX业务系统"
	BusinessApp string `json:"business_app"`

	// 资产负责人.
	//
	// required: true
	// read only: false
	// example: "张三"
	Username string `json:"username"`

	// 机房信息.
	//
	// required: true
	// read only: false
	// example: "北京机房"
	ComputerRoom string `json:"computer_room"`

	// 自定义标签.
	//
	// required: true
	// read only: false
	CustomFields interface{} `json:"custom_fields"`

	// 开放端口数量.
	//
	// required: true
	// read only: false
	// example: 10
	PortSize int `json:"port_size"`

	// 发现方式(流量发现：flow).
	//
	// required: true
	// read only: false
	// example: "flow"
	AddWay string `json:"add_way"`

	// 卡片的背景图片.
	//
	// required: true
	// read only: false
	// example: "路由器"
	IconBackground string `json:"icon_background"`

	// RDP截图.
	//
	// required: true
	// read only: false
	// example: images/rdp/************-3389.png
	RdpImage string `json:"rdp_image"`

	// 组件分层(五层).
	//
	// required: true
	// read only: false
	RuleInfos []*RuleInfos `json:"rule_infos"`

	// 危险资产统计.
	//
	// required: true
	// read only: false
	ThreatStatus *ThreatStatus `json:"threat_status"`

	// 主机名称.
	//
	// required: true
	// read only: false
	// example: kitty
	Hostname string `json:"hostname"`

	// 网站截图.
	//
	// required: true
	// read only: false
	// example: images/website/10.10.10.10_22.png
	WebsiteImage string `json:"website_image"`

	// 网站标题.
	//
	// required: true
	// read only: false
	// example: 网络资产与安全风险综合管控平台
	WebsiteTitle string `json:"website_title"`

	// 是否是蜜罐.
	//
	// required: true
	// read only: false
	// example: true
	IsHoneypot bool `json:"is_honeypot"`

	// 是否是垃圾网站.
	//
	// required: true
	// read only: false
	// example: true
	IsFraud bool `json:"is_fraud"`

	// 是否是信创资产.
	//
	// required: true
	// read only: false
	// example: 1
	IsXc int `json:"is_xc"`
}

type RuleInfos struct {
	// 组件层级.
	//
	// required: true
	// read only: false
	// example: "支撑层"
	Key string `json:"key"`

	// 组件相应描述.
	//
	// required: true
	// read only: false
	RuleDesc []*RuleDesc `json:"rule_desc"`

	// 此字段用于排序,无任何意义.
	//
	// required: true
	// read only: false
	// example: 1
	Sort int `json:"sort"`
}

// RuleInfosPoc 验证规则组件是否危险
type RuleInfosPoc []*RuleInfos

// CheckPocs 检查poc
func (r *RuleInfosPoc) CheckPocs(pocs []string) {
	for _, ruleInfo := range *r {
		for _, ruleDesc := range ruleInfo.RuleDesc {
			for _, product := range pocs {
				if strings.Contains(strings.ToLower(ruleDesc.Name), product) {
					ruleDesc.IsThreat = true
					break
				}
			}
		}
	}
}

// RuleDesc Model.
//
// 数据模型 RuleDesc 资产数据管理-卡片维度-组件详情。
//
// swagger:model RuleDesc
type RuleDesc struct {
	// 组件图标.
	//
	// required: true
	// read only: false
	// example: "OpenSSH"
	Icon string `json:"icon"`

	// 组件名称.
	//
	// required: true
	// read only: false
	// example: "OpenSSH"
	Name string `json:"name"`

	// 组件详情.
	//
	// required: true
	// read only: false
	// example: "软件 / 支撑系统 / 其他支撑系统 /n 其他"
	Description string `json:"description"`

	// 是否存在漏洞.
	//
	// required: true
	// read only: false
	// example: false
	IsThreat bool `json:"is_threat"`

	// 信创类型.
	//
	// required: true
	// read only: false
	// example: 1
	IsXc int `json:"is_xc"`

	// 是否存在版本号.
	//
	// required: true
	// read only: false
	// example: 1
	IsVersion bool `json:"is_version"`

	// 分层编码
	LevelCode int `json:"level_code"`

	// 版本号
	Version string `json:"version"`

	// 数据库信息.
	//
	// required: false
	// read only: false
	// example: "Mysql"
	Database string `json:"database"`
}

// RuleDescPoc 验证规则组件是否危险
type RuleDescPoc []*RuleDesc

// CheckPocs 检查poc
func (r *RuleDescPoc) CheckPocs(pocs []string) {
	for _, ruleDesc := range *r {
		for _, key := range pocs {
			if strings.Contains(strings.ToLower(ruleDesc.Name), key) {
				ruleDesc.IsThreat = true
				break
			}
		}
	}
}

// ResultAssetIpList Model.
//
// 数据模型 ResultAssetIpList 资产数据管理-列表维度。
//
// swagger:model ResultAssetIpList
type ResultAssetIpList struct {
	// 列表相关信息.
	//
	// required: true
	// read only: false
	AssetIpListInfo []*AssetIpListInfo `json:"info"`

	// 自定义标签.
	//
	// required: true
	// read only: false
	CustomFields interface{} `json:"custom_fields"`

	// 字段列表.
	//
	// required: true
	// read only: false
	// example: ["ip","url"]
	Fields []string `json:"fields"`
}

type AssetIpListInfo struct {
	UID string `json:"uid"`

	// 记录ID.
	//
	// required: true
	// read only: false
	// example: "************"
	ID string `json:"id"`

	// IP地址.
	//
	// required: true
	// read only: false
	// example: "************"
	IP string `json:"ip"`

	// ipv6原始输入.
	//
	// required: true
	// read only: false
	// example: ::1
	Ipv6Raw string `json:"ipv6_raw"`

	// 在线状态(在线:1,离线:0).
	//
	// required: true
	// read only: false
	// example: 1
	State int `json:"state"`

	// 操作系统.
	//
	// required: true
	// read only: false
	// example: "Linux/3.2 - 4.9"
	Os string `json:"os"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: "重要资产"
	AssetLevel string `json:"asset_level"`

	// PortList Model
	//
	// 数据模型 PortList 资产数据管理-列表模式-端口/协议/组件。
	//
	// swagger:model PortList
	PortList []*PortList `json:"port_list"`

	// 域名信息.
	//
	// required: true
	// read only: false
	// example: "XXXXXX"
	Domain []string `json:"domain"`

	// 主机名称.
	//
	// required: true
	// read only: false
	// example: kitty
	Hostname string `json:"hostname"`

	// 网站截图.
	//
	// required: true
	// read only: false
	// example: images/website/10.10.10.10_22.png
	WebsiteImage string `json:"website_image"`

	// 网站标题.
	//
	// required: true
	// read only: false
	// example: 网络资产与安全风险综合管控平台
	WebsiteTitle string `json:"website_title"`

	// RDP截图.
	//
	// required: true
	// read only: false
	// example: images/rdp/************-3389.png
	RdpImage string `json:"rdp_image"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: "2020-03-06 17:29:50"
	Createtime string `json:"createtime"`

	// 上次扫描时间.
	//
	// required: true
	// read only: false
	// example: "2020-03-06 17:29:50"
	Lastupdatetime string `json:"lastupdatetime"`

	// Mac地址.
	//
	// required: true
	// read only: false
	// example: "00:0c:29:82:14:43"
	Mac string `json:"mac"`

	// IP类型(ipv4:true).
	//
	// required: true
	// read only: false
	// example: true
	IsIpv6 bool `json:"is_ipv6"`

	// 域名信息.
	//
	// required: true
	// read only: false
	// example: true
	Hosts []string `json:"hosts"`

	// 备注信息.
	//
	// required: true
	// read only: false
	// example: "网络资产测绘及风险分析系统"
	Name string `json:"name"`

	// 地理位置.
	//
	// required: true
	// read only: false
	// example: "东城区"
	City string `json:"city"`

	// 厂商名称.
	//
	// required: true
	// read only: false
	// example: "XX业务系统"
	BusinessApp string `json:"business_app"`

	// 资产负责人.
	//
	// required: true
	// read only: false
	// example: "张三"
	Username string `json:"username"`

	// 机房信息.
	//
	// required: true
	// read only: false
	// example: "北京机房"
	ComputerRoom string `json:"computer_room"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: "XX科技有限公司"
	Company string `json:"company"`

	// 资产负责人邮箱.
	//
	// required: true
	// read only: false
	// example: "***********"
	ManagerEmail string `json:"manager_email"`

	// 资产负责人电话.
	//
	// required: true
	// read only: false
	// example: "<EMAIL>"
	ManagerMobile string `json:"manager_mobile"`

	// 自定义标签.
	//
	// required: true
	// read only: false
	CustomFields map[string]string `json:"custom_fields"`

	ThreatStatus *ThreatStatus `json:"threat_status"`

	// 是否是蜜罐.
	//
	// required: true
	// read only: false
	// example: true
	IsHoneypot bool `json:"is_honeypot"`

	// 是否是垃圾网站.
	//
	// required: true
	// read only: false
	// example: true
	IsFraud bool `json:"is_fraud"`

	// 是否是信创资产.
	//
	// required: true
	// read only: false
	// example: 1
	IsXc int `json:"is_xc"`
}

type PortList struct {
	// 协议名称.
	//
	// required: true
	// read only: false
	// example: http
	Protocol string `json:"protocol"`

	// 端口名称.
	//
	// required: true
	// read only: false
	// example: 80
	Port int `json:"port"`

	RuleInfo []*RuleDesc `json:"rule_info"`

	// Banner信息.
	//
	// required: true
	// read only: false
	// example: GT-AX11000 login:
	Banner string `json:"banner"`

	// 网站标题.
	//
	// required: true
	// read only: false
	// example: Not Found
	Title string `json:"title"`

	// Host.
	//
	// required: true
	// read only: false
	// example: 127.0.0.1
	Host string `json:"host"`

	// 证书相关信息.
	//
	// required: false
	// read only: false
	Certs interface{} `json:"certs"`

	// 证书相关信息(字符串).
	//
	// required: false
	// read only: false
	CertString string `json:"cert_string"`

	// 网站截图.
	//
	// required: true
	// read only: false
	// example: images/website/10.10.10.10_22.png
	WebsiteImage string `json:"website_image"`

	// 链接地址
	//
	// required: true
	// read only: false
	LinkUrl string `json:"link_url"`
}

// ThreatStatus Model
//
// 数据模型 ThreatStatus 资产数据管理-危险属性。
//
// swagger:model ThreatStatus
type ThreatStatus struct {
	// 有重要漏洞的IP.
	//
	// required: true
	// read only: false
	// example: ["SSH弱口令"]
	ThreatIp []*ThreatIp `json:"threat_ip"`

	// 安装代理软件的IP.
	//
	// required: true
	// read only: false
	// example: [{"filename":"smb_ms17_010_rce.json","level":3,"name":"MS17-010 SMB 远程溢出","type":"命令执⾏"}]
	ProxySoftWare []string `json:"proxy_software"`

	// 集成工具.
	//
	// required: true
	// read only: false
	// example: ["aaa","bbb"]
	IntegrationTools []string `json:"integration_tools"`

	// 扫描器.
	//
	// required: true
	// read only: false
	// example: ["aaa","bbb"]
	Scanners []string `json:"scanners"`

	// 远程运维.
	//
	// required: true
	// read only: false
	// example: ["aaa","bbb"]
	RemoteOps []string `json:"remote_ops"`

	// 非标端口.
	//
	// required: true
	// read only: false
	// example: "{"no_std_ports":[{"key":1099,"value":"java-rmi"}]}"
	NoStdPorts []*NoStdPorts `json:"no_std_ports"`

	// 开放高危端口以及服务的IP.
	//
	// required: true
	// read only: false
	// example: "{"no_std_ports":[{"key":1099,"value":"java-rmi"}]}"
	RiskyPortAndServer []*NoStdPorts `json:"risky_port_and_server"`
}

type NoStdPorts struct {
	Key   int    `json:"key"`
	Value string `json:"value"`
}

type EsResultRuleInfos struct {
	Hosts     []string     `json:"hosts"`
	Ports     []string     `json:"ports"`
	Os        string       `json:"os"`
	OsVersion string       `json:"os_version"`
	RuleTags  []string     `json:"rule_tags"`
	RuleInfos []RuleDetail `json:"rule_infos"` // 提取为独立类型
	PortList  []PortDetail `json:"port_list"`  // 提取为独立类型
	TitleList []TitleInfo  `json:"title_list"` // 提取为独立类型
	Vuls      []VulInfo    `json:"vuls"`       // 提取为独立类型
}

// RuleDetail 描述规则详细信息
type RuleDetail struct {
	SoftHardCode int               `json:"soft_hard_code"` // 0-软编码，1-硬编码
	SecondCatTag string            `json:"second_cat_tag"` // 二级分类标签
	FirstCatTag  string            `json:"first_cat_tag"`  // 一级分类标签
	Company      string            `json:"company"`        // 厂商名称
	LevelCode    int               `json:"level_code"`     // 风险等级（1-低，2-中，3-高）
	Ports        []int             `json:"ports"`          // 关联端口
	Title        string            `json:"title"`          // 规则标题
	Version      string            `json:"version"`        // 规则版本
	IsXc         int               `json:"is_xc"`          // 是否可信（0-否，1-是）
	RuleVersion  map[string]string `json:"rule_version"`   // 规则版本详情
}

// PortDetail 描述端口信息
type PortDetail struct {
	Protocol string      `json:"protocol"`  // 协议类型（TCP/UDP）
	Port     int         `json:"port"`      // 端口号
	RdpImage string      `json:"rdp_image"` // RDP关联镜像
	Banner   string      `json:"banner"`    // 端口Banner信息
	Cert     interface{} `json:"cert"`      // 证书信息（可进一步细化类型）
}

// TitleInfo 描述标题信息
type TitleInfo struct {
	Port         int    `json:"port"`          // 端口号
	Host         string `json:"host"`          // 主机地址
	Title        string `json:"title"`         // 标题内容
	WebsiteImage string `json:"website_image"` // 网站快照路径
}

// VulInfo 描述漏洞信息
type VulInfo struct {
	Name     string `json:"name"`     // 漏洞名称
	Filename string `json:"filename"` // 关联文件名
	Level    int    `json:"level"`    // 漏洞等级（1-低，2-中，3-高）
}

// AdvancedScreenResponse Model.
//
// 数据模型 AdvancedScreenResponse 漏洞返回信息-高级筛选。
//
// swagger:model AdvancedScreenResponse
type AdvancedScreenResponse struct {
	// 发现方式(主动探测:self_defined,流量发现:flow).
	//
	// required: true
	// read only: false
	// example: {"key":2,"value":"全部"}
	AddWay []*SystemPreset `json:"add_way"`

	// Ip类型(Ipv4:false Ipv6:true).
	//
	// required: true
	// read only: false
	// example: {"key":2,"value":"全部"}
	IpType []*SystemPreset `json:"ip_type"`

	// 资产状态(在线:1 离线:0).
	//
	// required: true
	// read only: false
	// example: {"key":2,"value":"全部"}
	State []*SystemPreset `json:"state"`

	// 开放端口.
	//
	// required: true
	// read only: false
	// example: ["80","81"]
	Ports []string `json:"ports"`

	// 开放服务.
	//
	// required: true
	// read only: false
	// example: ["http","https"]
	Protocols []string `json:"protocols"`

	// 厂商品牌.
	//
	// required: true
	// read only: false
	// example: ["AAA","BBB"]
	CompanyTitle []string `json:"company_title"`

	// 组件名称.
	//
	// required: true
	// read only: false
	// example: ["http","https"]
	Rules []string `json:"rules"`

	// IP地址段.
	//
	// required: true
	// read only: false
	// example: "**********/24"
	IpRange string `json:"ip_range"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: ["南京分公司","北京分公司"]
	Company []string `json:"company"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: ["客服系统","客服系统"]
	BusinessApp []string `json:"business_app"`

	// 负责人.
	//
	// required: true
	// read only: false
	// example: ["张三","李四"]
	Username []string `json:"username"`

	// 机房信息.
	//
	// required: true
	// read only: false
	// example: "南京机房"
	ComputerRoom []string `json:"computer_room"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-12,2022-02-18"
	FindTime string `json:"find_time"`

	CustomFields []*CustomFields `json:"custom_fields"`
}

// CustomFields Model.
//
// 数据模型 CustomFields 高级筛选-自定义标签。
//
// swagger:model CustomFields
type CustomFields struct {
	// 英文名称.
	//
	// required: true
	// read only: false
	// example: "guojia"
	Key string `json:"key"`

	// 中文名称.
	//
	// required: true
	// read only: false
	// example: "国家"
	Value string `json:"value"`

	// 数据集合.
	//
	// required: true
	// read only: false
	// example:["中国","美国"]
	List []string `json:"list"`
}

// SystemPreset Model.
//
// 数据模型 SystemPreset 高级筛选-自定义标签。
//
// swagger:model SystemPreset
type SystemPreset struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type SystemBucket struct {
	Key      string `json:"key"`
	DocCount int    `json:"doc_count"`
}

type SystemBucketBool struct {
	Key         int    `json:"key"`
	KeyAsString string `json:"key_as_string"`
	DocCount    int    `json:"doc_count"`
}

// ThreatIp Model.
//
// 数据模型 ThreatIp 危险资产-重要漏洞IP。
//
// swagger:model ThreatIp
type ThreatIp struct {
	Name     string `json:"name"`
	Filename string `json:"filename"`
	Level    int    `json:"level"`
	// Type     string `json:"type"`
}

// ResultBusinessParentData Model.
//
// 数据模型 ResultBusinessParentData 资产数据管理-业务维度。
//
// swagger:model ResultBusinessParentData
type ResultBusinessParentData struct {
	// 记录id.
	//
	// required: true
	// read only: false
	// example: 1
	Id int `json:"id"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: "重要资产"
	Remark string `json:"remark"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: "客服系统"
	BusinessName string `json:"business_name"`

	// 域名数量.
	//
	// required: true
	// read only: false
	// example: 10
	DomainCount int64 `json:"domain_quantity"`

	// 关联IP数量.
	//
	// required: true
	// read only: false
	// example: 10
	IpCount int64 `json:"relation_quantity"`

	// 漏洞数量.
	//
	// required: true
	// read only: false
	// example: 10
	VulnerabilityCount int64 `json:"loophole_quantity"`

	// 弱口令数量
	//
	// required: true
	// read only: false
	// example: 10
	PocQuantity int64 `json:"poc_quantity"`
}

// BusinessData es返回模型
type BusinessData struct {
	State          int               `json:"state"`
	PortSize       int               `json:"port_size"`
	OsVersion      string            `json:"os_version"`
	Os             string            `json:"os"`
	City           string            `json:"city"`
	IP             string            `json:"ip"`
	BusinessApp    string            `json:"business_app"`
	ManagerMobile  string            `json:"manager_mobile"`
	Mac            string            `json:"mac"`
	ComputerRoom   string            `json:"computer_room"`
	Name           string            `json:"name"`
	Company        string            `json:"company"`
	ManagerEmail   string            `json:"manager_email"`
	Username       string            `json:"username"`
	AssetLevel     string            `json:"asset_level"`
	RuleTags       []string          `json:"rule_tags"`
	Protocols      []string          `json:"protocols"`
	Createtime     string            `json:"createtime"`
	Lastupdatetime string            `json:"lastupdatetime"`
	CustomFields   map[string]string `json:"custom_fields"`
}

// ResultDomainData Model.
//
// 数据模型 ResultDomainData 资产数据管理-域名维度。
//
// swagger:model ResultDomainData
type ResultDomainData struct {
	UID string `json:"uid"`

	// 域名.
	//
	// required: true
	// read only: false
	// example: "www.baidu.com"
	Domain string `json:"domain"`

	// IP地址.
	//
	// required: true
	// read only: false
	// example: "************"
	Ip string `json:"ip"`

	// IP数量(前端展示使用).
	//
	// required: true
	// read only: false
	// example: 5
	IpCount int `json:"ip_count"`

	// 在线状态(在线:1,离线:0).
	//
	// required: true
	// read only: false
	// example: 1
	State int `json:"state"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: "重要资产"
	AssetLevel string `json:"asset_level"`

	// 端口数量.
	//
	// required: true
	// read only: false
	// example: 10
	PortQuantity int `json:"port_quantity"`

	// 协议数量.
	//
	// required: true
	// read only: false
	// example: 10
	ProtocolQuantity int `json:"protocol_quantity"`

	// 组件数量.
	//
	// required: true
	// read only: false
	// example: 10
	RulesQuantity int `json:"rules_quantity"`

	// 操作系统.
	//
	// required: true
	// read only: false
	// example: "Linux/3.2 - 4.9"
	Os string `json:"os"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: "2020-03-06 17:29:50"
	FindTime string `json:"find_time"`

	// 上次扫描时间.
	//
	// required: true
	// read only: false
	// example: "2020-03-06 17:29:50"
	PreTime string `json:"pre_time"`

	// 危险资产统计.
	//
	// required: true
	// read only: false
	ThreatStatus *ThreatStatus `json:"threat_status"`

	// 是否是蜜罐.
	//
	// required: true
	// read only: false
	// example: true
	IsHoneypot bool `json:"is_honeypot"`

	// 是否是垃圾网站.
	//
	// required: true
	// read only: false
	// example: true
	IsFraud bool `json:"is_fraud"`

	// 是否是信创资产.
	//
	// required: true
	// read only: false
	// example: 1
	IsXc int `json:"is_xc"`
}

// BaseResult es temp model.
type BaseResult struct {
	Ip           string            `json:"ip"`
	Hosts        []string          `json:"hosts"`
	ComputerRoom string            `json:"computer_room"`
	Company      string            `json:"company"`
	UserName     string            `json:"username"`
	CustomFields map[string]string `json:"custom_fields"`
}

type TaskAssetCountDataInfoValue struct {
	Value float64 `json:"value"`
}

type TaskAssetCountDataInfo struct {
	IPCount        TaskAssetCountDataInfoValue `json:"ip_count"`
	PortCount      TaskAssetCountDataInfoValue `json:"port_count"`
	ProtocolCount  TaskAssetCountDataInfoValue `json:"protocol_count"`
	RuleInfosCount TaskAssetCountDataInfoValue `json:"rule_info_count"`
}

type TaskAssetCountData struct {
	LatestData TaskAssetCountDataInfo `json:"last_data"`
	LastData   TaskAssetCountDataInfo `json:"latest_data"`
}

type ResultNewDomainData struct {

	// 域名.
	Domain string `json:"domain"`

	// 管理单元
	Company string `json:"company"`

	// 机房
	ComputerRoom string `json:"computer_room"`

	// 责任人
	Username string `json:"username"`

	// 责任人联系方式
	ManagerMobile string `json:"manager_mobile"`

	// 责任人邮箱
	ManagerEmail string `json:"manager_email"`

	// 资产等级
	AssetLevel string `json:"asset_Level"`

	// 业务系统
	BusinessApp string `json:"business_app"`

	// 解析结果
	ResolutionInfo []Resolution `json:"resolution"`

	// 创建时间
	CreateTime string `json:"createtime"`

	// 最后解析时间
	LastUpdateTime string `json:"lastupdatetime"`
}
