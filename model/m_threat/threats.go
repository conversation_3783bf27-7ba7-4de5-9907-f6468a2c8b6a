package m_threat

const (
	PocTypeUnFixed      = "unfixed" // 未修复漏洞
	PocTypeFixed        = "fixed"
	AssetType           = "Asset"
	DefaultAssetFields  = "ip,port,protocol,modules"                                                                                                                                                             // 已修复漏洞
	PocTypeThreat       = "Threat"                                                                                                                                                                               // 漏洞管理自定义列表默认类型
	DefaultFields       = "ip,common_title,cveId,vulType,level,url,createtime,lastupdatetime,notice_time,name,mac,province,company,username,manager_mobile,manager_email,computer_room,business_app,asset_level" // 系统默认字段
	DefaultGroupName    = "current"                                                                                                                                                                              // 系统默认名称
	DefaultLastSelected = 1                                                                                                                                                                                      // 最后一次选择计为1                                                                                                                                                          // 最后选择的模版
	DefaultUserId       = 1                                                                                                                                                                                      // 系统默认用户ID
)

type Buckets struct {
	Buckets []Bucket `json:"buckets"`
}

type Bucket struct {
	Key      int `json:"key"`
	DocCount int `json:"doc_count"`
}

type SystemBucket struct {
	Key      string `json:"key"`
	DocCount int    `json:"doc_count"`
}

type SystemBuckets struct {
	SystemBucket []SystemBucket `json:"buckets"`
}

// ResultVulnerabilityDataByDefault Model.
//
// 数据模型 ResultVulnerabilityDataByDefault 漏洞返回信息-默认维度。
//
// swagger:model ResultVulnerabilityDataByDefault
type ResultVulnerabilityDataByDefault struct {
	VulCount                *VulCount                  `json:"count"`
	ResultVulnerabilityInfo []*ResultVulnerabilityInfo `json:"info"`
	CustomFields            interface{}                `json:"custom_fields"`

	// 字段列表.
	//
	// required: true
	// read only: false
	// example: ["ip","url"]
	Fields []string `json:"fields"`
}

// ResultVulnerabilityDataByIp Model.
//
// 数据模型 ResultVulnerabilityDataByIp 漏洞返回信息-IP维度。
//
// swagger:model ResultVulnerabilityDataByIp
type ResultVulnerabilityDataByIp struct {
	ResultVulnerabilityTotalData *ResultVulnerabilityTotalData `json:"count"`
	IpParentData                 []*IpParentData               `json:"info"`
}

// ResultVulnerabilityDataByPoc Model.
//
// 数据模型 ResultVulnerabilityDataByPoc 漏洞返回信息-漏洞维度。
//
// swagger:model ResultVulnerabilityDataByPoc
type ResultVulnerabilityDataByPoc struct {
	ResultVulnerabilityTotalData *ResultVulnerabilityTotalData `json:"count"`
	PocParentData                []*PocParentData              `json:"info"`
}

// AdvancedScreenResponse Model.
//
// 数据模型 AdvancedScreenResponse 漏洞返回信息-高级筛选。
//
// swagger:model AdvancedScreenResponse
type AdvancedScreenResponse struct {
	// 漏洞等级.
	//
	// required: true
	// read only: false
	// example: {"key":1,"value":"严重"}
	Level []*SystemPreset `json:"level"`

	// 漏洞验证(0 不支持 1 支持 2全部).
	//
	// required: true
	// read only: false
	// example: {"key":2,"value":"全部"}
	Verity []*SystemPreset `json:"verity"`

	// 漏洞类型.
	//
	// required: true
	// read only: false
	// example: ["未授权访问","远程漏洞"]
	VulType []string `json:"vul_type"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: ["重要资产","一般资产"]
	AssetLevel []string `json:"asset_level"`

	// 资产状态
	Online []*SystemPreset `json:"online"`

	// 漏洞状态
	Status []*SystemPreset `json:"status"`

	// IP地址段.
	//
	// required: true
	// read only: false
	// example: "**********/24"
	IpRange string `json:"ip_range"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: ["南京分公司","北京分公司"]
	Company []string `json:"company"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: ["客服系统","客服系统"]
	BusinessApp []string `json:"business_app"`

	// 负责人.
	//
	// required: true
	// read only: false
	// example: ["张三","李四"]
	Username []string `json:"username"`

	// 机房信息.
	//
	// required: true
	// read only: false
	// example: "南京机房"
	ComputerRoom []string `json:"computer_room"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-12,2022-02-18"
	FindTime string `json:"find_time"`

	CustomFields []*CustomFields `json:"custom_fields"`
}

// SystemPreset Model.
//
// 数据模型 SystemPreset 高级筛选-自定义标签。
//
// swagger:model SystemPreset
type SystemPreset struct {
	Key   int    `json:"key"`
	Value string `json:"value"`
}

// CustomFields Model.
//
// 数据模型 CustomFields 高级筛选-自定义标签。
//
// swagger:model CustomFields
type CustomFields struct {
	// 英文名称.
	//
	// required: true
	// read only: false
	// example: "guojia"
	Key string `json:"key"`

	// 中文名称.
	//
	// required: true
	// read only: false
	// example: "国家"
	Value string `json:"value"`

	// 数据集合.
	//
	// required: true
	// read only: false
	// example:["中国","美国"]
	List []string `json:"list"`
}

// VulCount Model.
//
// 数据模型 VulCount 漏洞数量统计。
//
// swagger:model VulCount
type VulCount struct {
	// 严重漏洞数量.
	//
	// required: true
	// read only: false
	// example: 1
	VeryHighVulCount int `json:"very_high_vul_count"`

	// 高危漏洞数量.
	//
	// required: true
	// read only: false
	// example: 1
	HighVulCount int `json:"high_vul_count"`

	// 中危漏洞数量.
	//
	// required: true
	// read only: false
	// example: 1
	ModerateVulCount int `json:"moderate_vul_count"`

	// 低危漏洞数量.
	//
	// required: true
	// read only: false
	// example: 1
	LowVulCount int `json:"low_vul_count"`
}

// ResultVulnerabilityInfo Model.
//
// 数据模型 ResultVulnerabilityInfo 漏洞返回信息概览。
//
// swagger:model ResultVulnerabilityInfo
type ResultVulnerabilityInfo struct {
	UID string `json:"uid"`

	// 记录ID.
	//
	// required: true
	// read only: false
	// example: "b5b100c4937f0806c0796053b7cc3542"
	ID string `json:"id"`

	// 漏洞名称.
	//
	// required: true
	// read only: false
	// example: "NFS协议未授权访问"
	CommonTitle string `json:"common_title"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-06 12:03:58"
	CreateTime string `json:"createtime"`

	// 上次扫描.
	//
	// required: true
	// read only: false
	// example: "2022-01-06 12:03:58"
	LastUpdateTime string `json:"lastupdatetime"`

	// 漏洞等级.
	//
	// required: true
	// read only: false
	// example: 1
	Level int `json:"level"`

	// 漏洞类型.
	//
	// required: true
	// read only: false
	// example: "未授权访问"
	VulType string `json:"vulType"`

	// IP地址.
	//
	// required: true
	// read only: false
	// example: "127.0.0.1"
	IP string `json:"ip"`

	// cve编号.
	//
	// required: true
	// read only: false
	// example: "CVE-2021-22205"
	CveID string `json:"cveId"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: "客服系统"
	BusinessApp string `json:"business_app"`

	// 电话.
	//
	// required: true
	// read only: false
	// example: "***********"
	ManagerMobile string `json:"manager_mobile"`

	// mac.
	//
	// required: true
	// read only: false
	// example: "00:0c:29:c0:09:23"

	Mac string `json:"mac"`

	// url.
	//
	// required: true
	// read only: false
	// example: "127.0.0.1"
	URL string `json:"url"`

	// 漏洞验证地址.
	//
	// required: true
	// read only: false
	// example: "127.0.0.1"
	HostInfo string `json:"hostinfo"`

	// 机房信息.
	//
	// required: true
	// read only: false
	// example: "南京机房"
	ComputerRoom string `json:"computer_room"`

	// 地理位置.
	//
	// required: true
	// read only: false
	// example: "南京机房"
	Province string `json:"province"`

	// 是否有漏洞响应结果(1 有)
	//
	// required: true
	// read only: false
	// example: 1
	HasExp int `json:"has_exp"`

	// 备注信息
	//
	// required: true
	// read only: false
	// example: "test"
	Name string `json:"name"`

	// 通报时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-06 12:03:58"
	NoticeTime string `json:"notice_time"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: "南京分公司"
	Company string `json:"company"`

	// 邮箱.
	//
	// required: true
	// read only: false
	// example: "<EMAIL>"
	ManagerEmail string `json:"manager_email"`

	// 漏洞响应结果.
	//
	// required: true
	// read only: false
	// example: "$4200\r\n# Server\r\nredis_version:6.2."
	LastResponse string `json:"last_response"`

	// 负责人.
	//
	// required: true
	// read only: false
	// example: "张三"
	Username string `json:"username"`

	// 漏洞描述.
	//
	// required: true
	// read only: false
	// example: "张三"
	Description string `json:"common_description"`

	// 漏洞危害.
	//
	// required: true
	// read only: false
	// example: "张三"
	Impact string `json:"common_impact"`

	// 解决方案.
	//
	// required: true
	// read only: false
	// example: "张三"
	Recommendation string `json:"recommandation"`

	// 自定义标签.
	//
	// required: true
	// read only: false
	// example: "AAAA"
	CustomFields interface{} `json:"custom_fields"`

	// 漏洞验证(true 可以验证).
	//
	// required: true
	// read only: false
	// example: true
	Verity bool `json:"verity"`

	// 城市.
	//
	// required: true
	// read only: false
	// example: "石家庄市"
	City string `json:"city"`

	// 状态(未修复：2).
	//
	// required: true
	// read only: false
	// example: 2
	State int `json:"state"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: "重要资产"
	AssetLevel string `json:"asset_level"`

	// 是否是信创漏洞.
	//
	// required: true
	// read only: false
	// example: 1
	IsXc int `json:"is_xc"`

	// 漏洞端口.
	//
	// required: true
	// read only: false
	// example: 3306
	Port int `json:"port"`

	// 资产状态
	Online int `json:"online"`

	// 漏洞状态
	Status int `json:"status"`

	// 备注
	Comment string `json:"comment"`

	KeywordTags string `json:"keyword_tags"`

	// 修复核查时间
	LastCheckTime string `json:"lastchecktime"`
}

// IpParentData Model.
//
// 数据模型 IpParentData 漏洞返回信息-IP维度概览。
//
// swagger:model IpParentData
type IpParentData struct {
	UID string `json:"uid"`

	// 记录ID.
	//
	// required: true
	// read only: false
	// example: "b5b100c4937f0806c0796053b7cc3542"
	IP string `json:"ip"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: "重要资产"
	AssetLevel string `json:"asset_level"`

	// 漏洞数量.
	//
	// required: true
	// read only: false
	// example: 3
	DocCount int `json:"doc_count"`

	// 备注信息
	//
	// required: true
	// read only: false
	// example: "test"
	Name string `json:"name"`

	// 地理位置.
	//
	// required: true
	// read only: false
	// example: "河北省"
	Province string `json:"province"`

	// 城市.
	//
	// required: true
	// read only: false
	// example: "石家庄市"
	City string `json:"city"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: "南京分公司"
	Company string `json:"company"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: "客服系统"
	BusinessApp string `json:"business_app"`

	// mac.
	//
	// required: true
	// read only: false
	// example: "00:0c:29:c0:09:23"
	Mac string `json:"mac"`

	// 负责人.
	//
	// required: true
	// read only: false
	// example: "张三"
	Username string `json:"username"`

	// 电话.
	//
	// required: true
	// read only: false
	// example: "***********"
	ManagerMobile string `json:"manager_mobile"`

	// 邮箱.
	//
	// required: true
	// read only: false
	// example: "<EMAIL>"
	ManagerEmail string `json:"manager_email"`

	// 状态(未修复：2).
	//
	// required: true
	// read only: false
	// example: 2
	State int `json:"state"`

	// 是否是信创漏洞.
	//
	// required: true
	// read only: false
	// example: 1
	IsXc int `json:"is_xc"`

	// 资产在线状态
	Online int `json:"online"`

	//IpChildData []*IpChildData `json:"ip_child_data"`
}

// IpChildData Model.
//
// 数据模型 IpChildData 漏洞返回信息-IP维度-嵌套数据。
//
// swagger:model IpChildData
type IpChildData struct {
	// 记录ID.
	//
	// required: true
	// read only: false
	// example: "b5b100c4937f0806c0796053b7cc3542"
	ID string `json:"id"`

	// 漏洞名称.
	//
	// required: true
	// read only: false
	// example: "NFS协议未授权访问"
	CommonTitle string `json:"common_title"`

	// cve编号.
	//
	// required: true
	// read only: false
	// example: "CVE-2021-22205"
	CveID string `json:"cveId"`

	// 漏洞等级.
	//
	// required: true
	// read only: false
	// example: 1
	Level int `json:"level"`

	// url.
	//
	// required: true
	// read only: false
	// example: "127.0.0.1"
	URL string `json:"url"`

	// 漏洞验证地址.
	//
	// required: true
	// read only: false
	// example: "127.0.0.1"
	HostInfo string `json:"hostinfo"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-06 12:03:58"
	CreateTime string `json:"createtime"`

	// 通报时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-06 12:03:58"
	NoticeTime string `json:"notice_time"`

	// 漏洞验证(true 可以验证).
	//
	// required: true
	// read only: false
	// example: true
	Verity bool `json:"verity"`

	// 漏洞描述.
	//
	// required: true
	// read only: false
	// example: "张三"
	Description string `json:"common_description"`

	// 漏洞危害.
	//
	// required: true
	// read only: false
	// example: "张三"
	Impact string `json:"common_impact"`

	// 解决方案.
	//
	// required: true
	// read only: false
	// example: "张三"
	Recommendation string `json:"recommandation"`

	// 是否有漏洞响应结果(1 有)
	//
	// required: true
	// read only: false
	// example: 1
	HasExp int `json:"has_exp"`
}

// PocParentData Model.
//
// 数据模型 PocParentData 漏洞返回信息-漏洞维度概览。
//
// swagger:model PocParentData
type PocParentData struct {
	// 漏洞名称.
	//
	// required: true
	// read only: false
	// example: "NFS协议未授权访问"
	CommonTitle string `json:"common_title"`

	// 漏洞等级.
	//
	// required: true
	// read only: false
	// example: 1
	Level int `json:"level"`

	// cve编号.
	//
	// required: true
	// read only: false
	// example: "CVE-2021-22205"
	CveID string `json:"cveId"`

	// 漏洞数量.
	//
	// required: true
	// read only: false
	// example: 3
	DocCount int `json:"doc_count"`

	// 漏洞描述.
	//
	// required: true
	// read only: false
	// example: "张三"
	Description string `json:"common_description"`

	// 漏洞危害.
	//
	// required: true
	// read only: false
	// example: "张三"
	Impact string `json:"common_impact"`

	// 解决方案.
	//
	// required: true
	// read only: false
	// example: "张三"
	Recommendation string `json:"recommandation"`

	// 是否是信创漏洞.
	//
	// required: true
	// read only: false
	// example: 1
	IsXc int `json:"is_xc"`

	//PocChildData []*PocChildData `json:"poc_child_data"`
}

// PocChildData Model.
//
// 数据模型 PocChildData 漏洞返回信息-漏洞维度嵌套数据。
//
// swagger:model PocChildData
type PocChildData struct {
	UID string `json:"uid"`

	// 记录ID.
	//
	// required: true
	// read only: false
	// example: "b5b100c4937f0806c0796053b7cc3542"
	ID string `json:"id"`

	// IP地址.
	//
	// required: true
	// read only: false
	// example: "127.0.0.1"
	IP string `json:"ip"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: "重要资产"
	AssetLevel string `json:"asset_level"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-06 12:03:58"
	CreateTime string `json:"createtime"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: "客服系统"
	BusinessApp string `json:"business_app"`

	// 电话.
	//
	// required: true
	// read only: false
	// example: "***********"
	ManagerMobile string `json:"manager_mobile"`

	// mac.
	//
	// required: true
	// read only: false
	// example: "00:0c:29:c0:09:23"
	Mac string `json:"mac"`

	// url.
	//
	// required: true
	// read only: false
	// example: "127.0.0.1"
	URL string `json:"url"`

	// 漏洞验证地址.
	//
	// required: true
	// read only: false
	// example: "127.0.0.1"
	HostInfo string `json:"hostinfo"`

	// 地理位置.
	//
	// required: true
	// read only: false
	// example: "河北省"
	Province string `json:"province"`

	// 地理位置.
	//
	// required: true
	// read only: false
	// example: "石家庄市"
	City string `json:"city"`

	// 是否有漏洞响应结果(1 有)
	//
	// required: true
	// read only: false
	// example: 1
	HasExp int `json:"has_exp"`

	// 备注信息
	//
	// required: true
	// read only: false
	// example: "test"
	Name string `json:"name"`

	// 通报时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-06 12:03:58"
	NoticeTime string `json:"notice_time"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: "南京分公司"
	Company string `json:"company"`

	// 邮箱.
	//
	// required: true
	// read only: false
	// example: "<EMAIL>"
	ManagerEmail string `json:"manager_email"`

	// 负责人.
	//
	// required: true
	// read only: false
	// example: "张三"
	Username string `json:"username"`

	// 漏洞验证(true 可以验证).
	//
	// required: true
	// read only: false
	// example: true
	Verity bool `json:"verity"`

	// 状态(未修复：2).
	//
	// required: true
	// read only: false
	// example: 2
	State int `json:"state"`

	// 是否是信创漏洞.
	//
	// required: true
	// read only: false
	// example: 1
	IsXc int `json:"is_xc"`
}

// ResultVulnerabilityTotalData Model.
//
// 数据模型 ResultVulnerabilityTotalData 漏洞数量统计。
//
// swagger:model ResultVulnerabilityTotalData
type ResultVulnerabilityTotalData struct {
	// 全部漏洞数量.
	//
	// required: true
	// read only: false
	// example: 5
	AllVeryHighVulCount int64 `json:"all_very_high_vul_count"`

	// 严重漏洞数量.
	//
	// required: true
	// read only: false
	// example: 1
	VeryHighVulCount int `json:"very_high_vul_count"`

	// 高危漏洞数量.
	//
	// required: true
	// read only: false
	// example: 1
	HighVulCount int `json:"high_vul_count"`

	// 中危漏洞数量.
	//
	// required: true
	// read only: false
	// example: 1
	ModerateVulCount int `json:"moderate_vul_count"`

	// 低危漏洞数量.
	//
	// required: true
	// read only: false
	// example: 1
	LowVulCount int `json:"low_vul_count"`
}

type ResponseManagerEmail struct {
	ResultVulnerabilityInfo []*ResultVulnerabilityInfo `json:"info"`
}
