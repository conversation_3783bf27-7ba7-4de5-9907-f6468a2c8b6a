[cors]
accesscontrolalloworigin = "localhost:3000,127.0.0.1:3000,http://0.0.0.0:8000"

[database]
defaultpassword = "foeye_password"
defaultstrength = 10
defaultusername = "foeye_username"
defaultserviceusername = "service"# 服务管理账号（三权分立，初始化用户）
defaultservicepassword = "service_password"# 服务管理账号（三权分立，初始化密码）
host = "mysql link address, like: localhost"
iscreatedefaultuser = true
isdebugmode = true # 打印sql语句
name = "database_name"
password = "XXXXXXXX"
port = 3306
setmaxidleconns = 10
setmaxopenconns = 100
type = "mysql"
user = "mysql_user_name"

[elastic]
host = "elastic link address, like: http://127.0.0.1:9200"
password = "XXXXXXXX"
username = "elasic_user"

[fofa]
baseuri = "/api/v1"
host = "https://fofa.info"
hosturi = "/host"
myuri = "/info/my"
searchuri = "/search/all"
timeout = 3

[goscanner]
dictpath = "/data/docker-env/web/goscanner"
godserverport = "8080"
path = "/data/docker-env/web/goscanner/goscanner"
pocpath = "/data/docker-env/web/goscanner/exploits"

[log]
level = "debug"
path = "./logs/production.log"

[likexc]
path = "/data/docker-env/web/foeye3/like_xc_ids.dict"

[microkernel]
host = "http://127.0.0.1:61234"
systeminfouri = "/api/v1/system/infos"

[nats]
host = "nats://127.0.0.1:4222"

[redis]
apiservertaskpause = "task_pause"
apiservertaskrecover = "task_recover"
apiservertaskstop = "task_stopping"
database = 0
host = "redis link address, like: 127.0.0.1:6379"
idletimeout = 200
password = "XXXXXXXX"

[rpc]
address = "127.0.0.1:30000"

[server]
allowshost = ["Localruby1", "localhost", "127.0.0.1", "127.0.0.1:3000", "*************"]
applicationname = "网络资产测绘及风险分析系统"
captchatimeout = 60
host = "0.0.0.0"
httpsport = 8080
ignorerisk = false  # 忽略风险管理模块控制
ignoresafetypoints = [] # The endpoint used to ignore security checks.
isbackup = false # true开启定时备份数据库
isclosecaptcha = false # true关闭验证码
isclosecompliancemonitor = false # true关闭合规检测定时任务
iscloseiprange = false # true关闭IP范围校验
iscloselicense = false # true关闭激活状态和战略探针的校验
isclosepermission = false # true关闭权限校验
isignoresafety = false # Whether to enable security verification. default is true.
isshowplatform = false
mode = "release" # debug release 改为debug影响修改密码功能（修改后无法退出）
port = 3000
productionpath = "./static/settings/production.yml"
readtimeout = 60
reportpath = "/var/www/foeye_front/dist/reports"
scantype = "intranet" # extranet|intranet
serverapidocumenttitle = "FOEYE3 API Interface Document."
servername = "apiservice"
taskmaximumlimit = 100 # 任务最大限制
tcpdumperrorlogpath = "./tmp/cap_error_log"
tcpdumppath = "./tmp/capture_data"
writetimeout = 60
ischannel = false # true表示渠道版,默认标品不配置这个
# 用户使用手册名称
#user_manual:标品用户手册
#user_manual_xc:信创用户手册
#user_manual_oem:标品OEM版用户手册
#user_manual_channel:渠道版用户手册
#user_manual_channel_oem:渠道OEM版用户手册
#user_manual_xc:信创版用户手册
ManualType = "user_manual"

[sso]
htmlpath = "/var/www/foeye_front/dist/single-sign-on.html"

[token]
duration = 20
iselasticexpires = true
secret = "application token"
unit = "minute"

[worker]
assetscanqueue = "asset_scan_worker"
database = 0
host = "localhost:6379"
pool = 10
processunique = "handle-xlsx-file-1"
