package automigrate

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"gorm.io/gorm"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_second_categories"
	"git.gobies.org/foeye/foeye3/database/db_user_rules"
	"git.gobies.org/foeye/foeye3/model/m_second_categories"
	"git.gobies.org/foeye/foeye3/model/m_user_rules"
)

var WithExportRulesCommand = &cobra.Command{
	Use:   "export_rules",
	Short: "export rules, run this command with the root permission.",
	Run: func(cmd *cobra.Command, args []string) {
		rules := &Rules{}
		rules.UserRulesPath = "./static/rules/user_rules.json"
		rules.ExportedRulesPath = "./static/rules/exported_rules.json"
		err := rules.ExportRules(ctlGormDb)
		//err := rules.ImportRules(ctlGormDb)

		if err != nil {
			log.Println(err.Error())
			os.Exit(1)
		}

		log.Println("Export rules Successful...")
	},
}

// Rules 规则导出结构体
type Rules struct {
	Rule               *m_user_rules.UserRule                  `json:"rule"`
	SecondCategory     *m_second_categories.SecondCategories   `json:"second_category"`
	SecondCategoryRule *m_second_categories.SecondCategoryRule `json:"second_category_rule"`
	UserRulesPath      string
	ExportedRulesPath  string
}

// GenerateHash 生成规则数据的唯一哈希值
func (r *Rules) GenerateHash() string {
	// 使用规则ID、产品名称、公司名称生成唯一哈希
	data := fmt.Sprintf("%d_%s_%s_%s_%s_%d_%d_%d", r.Rule.ID, r.Rule.Product, r.Rule.ProductUrl, r.Rule.Rule, r.Rule.Company, r.Rule.IsXc, r.Rule.LevelCode, r.Rule.SoftHardCode)
	hash := md5.Sum([]byte(data))
	return hex.EncodeToString(hash[:])
}

func (r *Rules) ExportRules(store *database.GormDatabase) error {
	rulesData := make(map[string]*Rules)

	ruleModel := &db_user_rules.GormUserRuleDatabase{Instance: store}
	secondCategoriesModel := &db_second_categories.GormSecondCategoriesDatabase{Instance: store}
	secondCategoryRuleModel := &db_second_categories.GormSecondCategoryRuleDatabase{Instance: store}

	list, err := ruleModel.GetList(0, 0, []interface{}{"taged = ?", 1}...)
	if err != nil {
		return err
	}

	log.Infof("len(list) =========%d", len(list))
	for _, rule := range list {

		secondCategoryRule, err := secondCategoryRuleModel.GetItemByRuleID(rule.ID)
		if err != nil {
			log.Infof("【GetItemByRuleID】rule.Id =========%d,err =========%v", rule.ID, err)
			continue
		}

		secondCategory, err := secondCategoriesModel.GetRuleCategoryByRuleId(secondCategoryRule.SecondCategoryID)
		if err != nil {
			log.Infof("【GetRuleCategoryByRuleId】rule.Id =========%d,err =========%v", rule.ID, err)
			continue
		}

		// 为每个规则创建新的 Rules 实例，避免所有规则指向同一个对象
		ruleData := &Rules{
			SecondCategoryRule: secondCategoryRule,
			SecondCategory:     secondCategory,
			Rule:               rule,
		}

		hash := ruleData.GenerateHash()
		rulesData[hash] = ruleData
	}

	// 将数据转换为JSON并写入文件
	return writeExportDataToFile(rulesData, r.ExportedRulesPath)
}

func (r *Rules) ImportRules(store *database.GormDatabase) {
	ruleModel := &db_user_rules.GormUserRuleDatabase{Instance: store}
	secondCategoriesModel := &db_second_categories.GormSecondCategoriesDatabase{Instance: store}
	secondCategoryRuleModel := &db_second_categories.GormSecondCategoryRuleDatabase{Instance: store}

	var userRuleHashValues []string

	// 第一次调用该user_rules.json文件不存在，不存在则userRuleHashValues变量为数据库数据的值
	if userData, err := os.ReadFile(r.UserRulesPath); err == nil {
		err = json.Unmarshal(userData, &userRuleHashValues)
		if err != nil {
			log.Errorf("failed to unmarshal user rules data, treating as empty: %v", err)
			return
		}
	} else {
		list, err := ruleModel.GetList(0, 0, []interface{}{"taged = ?", 1}...)
		if err != nil {
			log.Errorf("failed to ruleModel.GetList: %v", err)
			return
		}

		for _, rule := range list {
			// 为每个规则创建临时实例来生成哈希值
			tempRule := &Rules{Rule: rule}
			userRuleHashValues = append(userRuleHashValues, tempRule.GenerateHash())
		}
	}

	// 获取最新规则数据
	exportedData, err := os.ReadFile(r.ExportedRulesPath)
	if err != nil {
		log.Errorf("failed to read exported rules file %s: %v", r.ExportedRulesPath, err)
		return
	}

	// 将最新规则数据赋值到rulesData上
	rulesData := make(map[string]*Rules)
	err = json.Unmarshal(exportedData, &rulesData)
	if err != nil {
		log.Errorf("failed to unmarshal exported rules data: %v", err)
		return
	}

	// 取出rulesData上所有key赋值到变量ruleHashValues
	ruleHashValues := make([]string, 0, len(rulesData))
	for hash := range rulesData {
		ruleHashValues = append(ruleHashValues, hash)
	}

	// 使用ruleHashValues与userRuleHashValues取差集
	diffHashes := getDifference(ruleHashValues, userRuleHashValues)

	if len(diffHashes) == 0 {
		log.Info("No new rules to import")
		return
	}

	log.Infof("diffHashes ====== %v", diffHashes)

	// 取完差集后将ruleHashValues写入到user_rules.json文件中
	err = writeUserRulesHashesToFile(ruleHashValues, r.UserRulesPath)
	if err != nil {
		log.Errorf("failed to write user rules hashes: %v", err)
		return
	}

	// 循环差集取出rulesData上key对应的Rules值并处理数据库操作
	for _, hash := range diffHashes {
		err = r.processRuleImport(rulesData[hash], ruleModel, secondCategoriesModel, secondCategoryRuleModel)

		if err != nil {
			log.Errorf("failed to import rule with hash %s: %v", hash, err)
			continue
		}
	}

	log.Infof("Successfully imported %d new rules", len(diffHashes))
	return
}

// getDifference 计算两个字符串切片的差集 (slice1 - slice2)
func getDifference(slice1, slice2 []string) []string {
	// 创建map用于快速查找
	existsMap := make(map[string]bool)
	for _, item := range slice2 {
		existsMap[item] = true
	}

	// 找出slice1中不在slice2中的元素
	var diff []string
	for _, item := range slice1 {
		if !existsMap[item] {
			diff = append(diff, item)
		}
	}

	return diff
}

// writeUserRulesHashesToFile 将用户规则哈希值写入文件
func writeUserRulesHashesToFile(hashes []string, filePath string) error {
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	// 将哈希值转换为JSON
	jsonData, err := json.MarshalIndent(hashes, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal hashes to JSON: %w", err)
	}

	// 写入文件
	err = os.WriteFile(filePath, jsonData, 0755)
	if err != nil {
		return fmt.Errorf("failed to write hashes to file %s: %w", filePath, err)
	}

	return nil
}

// processRuleImport 处理单个规则的导入
func (r *Rules) processRuleImport(
	rule *Rules,
	ruleModel *db_user_rules.GormUserRuleDatabase,
	secondCategoriesModel *db_second_categories.GormSecondCategoriesDatabase,
	secondCategoryRuleModel *db_second_categories.GormSecondCategoryRuleDatabase,
) error {
	// 1. 处理UserRule - 根据Rule.ID判断数据库是否存在数据存在则更新不存在创建
	if rule.Rule != nil {
		existingRule, err := ruleModel.GetItemByID(rule.Rule.ID)
		if err != nil && err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to check existing user rule: %w", err)
		}

		if existingRule != nil {
			// 更新现有规则
			err = ruleModel.UpdateItemByInstance(rule.Rule)
			if err != nil {
				return fmt.Errorf("failed to update user rule ID %d: %w", rule.Rule.ID, err)
			}
			log.Debugf("Updated user rule ID: %d", rule.Rule.ID)
		} else {
			// 创建新规则
			_, err = ruleModel.CreateItemByInstance(rule.Rule)
			if err != nil {
				return fmt.Errorf("failed to create user rule ID %d: %w", rule.Rule.ID, err)
			}
			log.Debugf("Created user rule ID: %d", rule.Rule.ID)
		}
	}

	// 2. 处理SecondCategory - 根据SecondCategory.ID判断数据库是否存在数据存在则更新不存在创建
	if rule.SecondCategory != nil {
		// 检查分类是否存在
		existingCategory, err := secondCategoriesModel.GetRuleCategoryByRuleId(rule.SecondCategory.ID)
		if err != nil && err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to check existing second category: %w", err)
		}

		if existingCategory != nil && existingCategory.ID == rule.SecondCategory.ID {
			// 更新现有分类 - 使用原生SQL更新
			err = secondCategoriesModel.Instance.DB.Model(rule.SecondCategory).Where("id = ?", rule.SecondCategory.ID).Updates(rule.SecondCategory).Error
			if err != nil {
				return fmt.Errorf("failed to update second category ID %d: %w", rule.SecondCategory.ID, err)
			}
			log.Debugf("Updated second category ID: %d", rule.SecondCategory.ID)
		} else {
			// 创建新分类
			err = secondCategoriesModel.Instance.DB.Create(rule.SecondCategory).Error
			if err != nil {
				return fmt.Errorf("failed to create second category ID %d: %w", rule.SecondCategory.ID, err)
			}
			log.Debugf("Created second category ID: %d", rule.SecondCategory.ID)
		}
	}

	// 3. 处理SecondCategoryRule - 根据SecondCategoryRule.ID判断数据库是否存在数据存在则更新不存在创建
	if rule.SecondCategoryRule != nil {
		existingCategoryRule, err := secondCategoryRuleModel.GetItemByID(rule.SecondCategoryRule.ID)
		if err != nil && err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to check existing second category rule: %w", err)
		}

		if existingCategoryRule != nil && existingCategoryRule.ID == rule.SecondCategoryRule.ID {
			// 更新现有分类规则关联 - 使用原生SQL更新
			err = secondCategoryRuleModel.Instance.DB.Model(rule.SecondCategoryRule).Where("id = ?", rule.SecondCategoryRule.ID).Updates(rule.SecondCategoryRule).Error
			if err != nil {
				return fmt.Errorf("failed to update second category rule ID %d: %w", rule.SecondCategoryRule.ID, err)
			}
			log.Debugf("Updated second category rule ID: %d", rule.SecondCategoryRule.ID)
		} else {
			// 创建新分类规则关联
			err = secondCategoryRuleModel.CreateItemByInstance(rule.SecondCategoryRule)
			if err != nil {
				return fmt.Errorf("failed to create second category rule ID %d: %w", rule.SecondCategoryRule.ID, err)
			}
			log.Debugf("Created second category rule ID: %d", rule.SecondCategoryRule.ID)
		}
	}

	return nil
}

// writeExportDataToFile 将导出数据写入文件
func writeExportDataToFile(exportData map[string]*Rules, outputPath string) error {
	// 将数据转换为JSON
	jsonData, err := json.MarshalIndent(exportData, "", "  ")
	if err != nil {
		log.Errorf("failed to marshal export data to JSON: %v", err)
		return fmt.Errorf("failed to marshal export data to JSON: %v", err)
	}

	// 写入文件
	err = os.WriteFile(outputPath, jsonData, 0755)
	if err != nil {
		log.Errorf("failed to write export data to file %s: %v", outputPath, err)
		return fmt.Errorf("failed to write export data to file %s: %v", outputPath, err)
	}

	return nil
}
