package main

import (
	"github.com/spf13/cobra"

	"git.gobies.org/foeye/foeye3/cmd/foeyectl/automigrate"
)

func init() {
	// Disable commands auto sorting.
	cobra.EnableCommandSorting = false

	// addition commands with initialize.
	rootCmd.AddCommand(automigrate.WithImportPocCommand)
	rootCmd.AddCommand(automigrate.WithImportScanPortCommand)
	rootCmd.AddCommand(automigrate.WithImportPocWeakPasswordCommand)
	rootCmd.AddCommand(automigrate.WithRiskStatisticsCommand)
	rootCmd.AddCommand(automigrate.WithImportIpMatrixCommand)
	rootCmd.AddCommand(automigrate.WithTagAssetLevelCommand)
	rootCmd.AddCommand(automigrate.WithImportProductInspectionCommand)
	rootCmd.AddCommand(automigrate.WithDataFlushCommand)
	rootCmd.AddCommand(automigrate.WithEsSnapshotCommand)
	rootCmd.AddCommand(automigrate.WithIpConfigCommand)
	rootCmd.AddCommand(automigrate.WithUserAbilityCommand)
	rootCmd.AddCommand(automigrate.WithCreateTableCommand)
	rootCmd.AddCommand(automigrate.WithUnlockCommand)
	rootCmd.AddCommand(automigrate.WithUnlockSysAdminCommand)
	rootCmd.AddCommand(automigrate.WithResetSysAdminPasswdCommand)
	rootCmd.AddCommand(automigrate.WithDelThreatNoTaskCommand)
	rootCmd.AddCommand(automigrate.WithDomainDataMigrateCommand)
	rootCmd.AddCommand(automigrate.WithObtainDifferentialDataCommand)
	rootCmd.AddCommand(automigrate.WithDelDefaultRouterCommand)

	automigrate.CtlRun()
}
