[![pipeline status](https://git.gobies.org/foeye/foeye3/badges/master/pipeline.svg)](https://git.gobies.org/foeye/foeye3/-/commits/master})

[![coverage report](https://git.gobies.org/foeye/foeye3/badges/master/coverage.svg?key_text=master)](https://git.gobies.org/foeye/foeye3/-/commits/master)

[![coverage report](https://git.gobies.org/foeye/foeye3/badges/develop/coverage.svg?key_text=develop)](https://git.gobies.org/foeye/foeye3/-/commits/develop)

[![coverage report](https://git.gobies.org/foeye/foeye3/badges/testing/X3.3.10/coverage.svg?key_text=当前版本)](https://git.gobies.org/foeye/foeye3/-/commits/testing/X3.3.10)

[![coverage api_interface](https://foeye.fofa.info/api-interface-coverage-img/coverage.svg)](https://foeye.fofa.info/api-interface-coverage-img/coverage.svg)



# FOEYE3 Server

# FOEYE3 禁止说明 
1.Foeye所有项目里，禁止使用gorm里的Preload。

## [代码review规范]
### 一个合并请求，总代码行数不能超过500行。
### 一个方法函数的代码行数不能超过100行。
### 自已不能合并自己提交的合并请求。

## [编码规范](doc/spec.md)

***所有参与开发者必须严格遵守该规范，否则不予合并！！！***

## [分权分域](doc/permission.md)

## 起步

### 克隆项目到本地

```shell script
$ git clone ssh://******************:22703/foeye/foeye3.git
```

### 添加系统环境变量

该项目配置文件依赖一个名为 `APPLICATION_MODE` 的系统环境变量，如果您是Mac系统，则可以添加到您的Home目录下的`~/.bashrc` 文件，如果是使用了 `item2` 则可以添加到 `~/.zshrc` 文件。如果您是windows请到系统高级添加环境变量。 

```.bashrc
export APPLICATION_MODE=develop
```

该项目 `swagger` 文档依赖一个名为 `SWAGGER_MODE` 的系统环境变量，如果您是Mac系统，则可以添加到您的Home目录下的`~/.bashrc` 文件，如果是使用了 `item2` 则可以添加到 `~/.zshrc` 文件。如果您是windows请到系统高级添加环境变量。

```.bashrc
export SWAGGER_MODE=1
```

### 本地环境启动项目

到项目目录下执行以下命令，运行该项目。

```shell script
$ go run main.go
```

## 项目开发热编译

在项目开发阶段推荐使用[air](https://github.com/cosmtrek/air)工具进行项目高效编译。

### 安装

请参考官方Github项目[air](https://github.com/cosmtrek/air)进行安装。

### 使用

使用方式非常简单，安装fresh工具后，在项目目录下执行 `fresh` 命令即可实时编译、实时监控Go文件变化自动编译。

## 项目模块管理

使用 Go Module，中国区官方地址为[https://goproxy.cn/](https://goproxy.cn/)

## 项目API文档

项目使用Swagger作为API接口文档提供方案~

### 安装

请参数官方文档进行安装[https://goswagger.io/](https://goswagger.io/)

### 使用

#### 生成API文档

```shell
$  swagger generate spec --scan-models --exclude=./internal/mux -o docs/spec.json
$ swagger generate spec --work-dir=internal/mux --scan-models -o internal/mux/docs/spec.json
```

#### 访问API文档

```text
http://项目主机地址/docs
```

线上开启项目文档
```shell
 /etc/profile.d/fofaee.sh
 export SWAGGER_MODE = 1 
```

## 引入私有项目

1. 添加环境变量`GOPRIVATE`
```shell
go env -w GOPRIVATE=git.gobies.org
```
2. 添加git配置
```shell
git config --global url.ssh://******************:22703.insteadof=https://git.gobies.org
```

## 检查复杂度
```shell
gocyclo -over 30 .
```

## 本地开发

1. 需要在请求Header中加入如下参数，可以忽略token.
2. 配置文件 `[server]`.`mode` = `debug`

| 名称             | 值           |
|----------------|-------------|
| X-Access-Token | foeye-debug |


## 安装说明
### docker安装：
````azure
直接下载： docker pull harbor.fofa.info/foeye/fofaee:v1
离线安装： 到10.11.12.38上下载安装包/root/foeye_auto_docker.tar.gz
````
### 硬件安装
请参考售后安装手册。

