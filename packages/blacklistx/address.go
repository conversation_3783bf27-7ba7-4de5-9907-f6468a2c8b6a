package blacklistx

import (
	"sync"
	"time"

	"git.gobies.org/foeye-dependencies/address"
	"git.gobies.org/foeye-dependencies/logger"

	"git.gobies.org/foeye/foeye3/packages/helpers"
)

// ContainsAllBlacklist Check whether the IP address segment is in the blacklist.
func ContainsAllBlacklist(blacklist, section []string) bool {
	sets := &sync.Map{}
	allCIDR := address.ConvertIPsToCIDR(blacklist)
	allRanges := allCIDR.ToIPRanges()

	time1 := time.Now()
	sectionCIDR := address.ConvertIPsToCIDRContainDomain(section)
	time3 := time.Now()
	sectionRange := sectionCIDR.ToIPRanges()
	time2 := time.Now()
	duration := time2.Sub(time1)
	duration2 := time2.Sub(time3)
	logger.Infof("[ContainsAllBlacklist] sectionCIDR.ToIPRanges time use:", "duration", duration)
	logger.Infof("[ContainsAllBlacklist] sectionCIDR.ToIPRanges time use:", "duration2", duration2)

	if sectionCIDR.IsEmpty() {
		return false
	}

	// Store
	for _, current := range sectionRange {
		sets.Store(current, helpers.InArray(allRanges, current))
	}

	var isContains = true
	sets.Range(func(key, value interface{}) bool {
		exists := value.(bool)
		//if exists {
		isContains = isContains && exists
		//}

		return isContains
	})

	return isContains
}
