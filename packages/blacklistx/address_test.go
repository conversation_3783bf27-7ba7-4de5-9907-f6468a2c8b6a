package blacklistx

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestAddressSuite(t *testing.T) {
	suite.Run(t, &AddressSuite{})
}

type AddressSuite struct {
	suite.Suite
}

func (suite *AddressSuite) Test_ContainsAllBlacklist() {
	grids := []struct {
		all      []string
		section  []string
		expected bool
	}{
		{
			all:      []string{"**********", "**********", "**********", "**********", "**********"},
			section:  []string{"**********"},
			expected: true,
		},
		{
			all:      []string{"**********", "**********", "**********", "**********", "**********"},
			section:  []string{"**********", "**********"},
			expected: true,
		},
		{
			all:      []string{"**********", "**********", "**********", "**********", "**********"},
			section:  []string{"**********", "**********00"},
			expected: false,
		},
		{
			all:      []string{"**********", "**********", "**********", "**********", "**********"},
			section:  []string{"**********/24"},
			expected: false,
		},
		{
			all:      []string{"**********", "**********", "**********", "**********", "**********"},
			section:  []string{"2408:8607:500::2/120"},
			expected: false,
		},
		{
			all: []string{
				"12:3456:78:90ab:cd:ef01:23:30",
				"12:3456:78:90ab:cd:ef01:23:31",
				"12:3456:78:90ab:cd:ef01:23:32",
			},
			section:  []string{"12:3456:78:90ab:cd:ef01:23:30/125"},
			expected: false,
		},
	}

	for _, g := range grids {
		actual := ContainsAllBlacklist(g.all, g.section)
		assert.Equal(suite.T(), g.expected, actual)
	}
}
