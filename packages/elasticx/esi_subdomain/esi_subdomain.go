package esi_subdomain

import (
	"sync"

	"git.gobies.org/foeye/foeye3/packages/elasticx"
)

type Store struct {
	Instance  *elasticx.ElasticDatabase
	IndexName string
	TypeName  string
	fileLock  sync.Mutex
}

func NewStore(elasticdatabase *elasticx.ElasticDatabase) *Store {
	return &Store{
		Instance:  elasticdatabase,
		IndexName: elasticx.IndexNameOfSubDomain(),
		TypeName:  elasticx.TypeNameOfSubDomain,
	}
}
