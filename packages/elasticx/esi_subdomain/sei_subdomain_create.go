package esi_subdomain

import (
	"context"

	"git.gobies.org/foeye/foeye3/model/m_product_inspection"
	"git.gobies.org/foeye/foeye3/packages/ipx"

	"github.com/olivere/elastic"
)

// CreateBulkAssetFeature 批量插入资产特征
func (e *Store) CreateBulkAssetFeature(data []m_product_inspection.Subdomain) error {
	bulk := e.Instance.Client.Bulk()
	count := 0
	for _, val := range data {
		count++
		tmp := m_product_inspection.CreateSubdomain{
			Ip:        ipx.GenIpAddr(),
			Port:      ipx.GenPort(),
			Protocol:  "unknown",
			Fid:       val.Dom.Ehash,
			Subdomain: val,
		}
		req := elastic.NewBulkIndexRequest().Index(e.IndexName).Type(e.TypeName).Doc(tmp)
		bulk.Add(req)
		if count >= 1000 {
			_, err := bulk.Do(context.Background())
			if err != nil {
				return err
			}
			bulk.Reset()
			count = 0
		}
	}
	if bulk.NumberOfActions() == 0 {
		return nil
	}
	_, err := bulk.Do(context.Background())
	return err
}

// CreateBulkFeatureID 批量插入特征ID
func (e *Store) CreateBulkFeatureID(data []m_product_inspection.FeatureID) error {
	bulk := e.Instance.Client.Bulk()
	count := 0
	for _, val := range data {
		count++
		tmp := m_product_inspection.CreateSubdomainFeatureID{
			Ip:        ipx.GenIpAddr(),
			Port:      ipx.GenPort(),
			Fid:       val.Dom.Ehash,
			FeatureID: val,
		}
		req := elastic.NewBulkIndexRequest().Index(e.IndexName).Type(e.TypeName).Doc(tmp)
		bulk.Add(req)
		if count >= 1000 {
			_, err := bulk.Do(context.Background())
			if err != nil {
				return err
			}
			bulk.Reset()
			count = 0
		}
	}
	if bulk.NumberOfActions() == 0 {
		return nil
	}
	_, err := bulk.Do(context.Background())
	return err
}
