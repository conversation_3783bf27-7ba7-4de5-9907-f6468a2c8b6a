package esi_subdomain

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"testing"

	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/backup_restore"
	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye-dependencies/fsfire"
)

func TestElasticIndexAssetsSuite(t *testing.T) {
	suite.Run(t, new(ElasticIndexAssetsSuite))
}

type ElasticIndexAssetsSuite struct {
	suite.Suite
	ins             *Store
	configure       *config.Configure
	elasticdatabase *elasticx.ElasticDatabase
	afterDropTables bool
	baseDir         string
	testDataDir     string
}

func (suite *ElasticIndexAssetsSuite) BeforeSuite() {
	gomonkey.ApplyFunc(elasticx.IndexNameOfAsset, func() string {
		return "fofaee_assets_esi_subdomain_test"
	})
	assert.Equal(suite.T(), "fofaee_assets_esi_subdomain_test", elasticx.IndexNameOfAsset())

	gomonkey.ApplyFunc(elasticx.IndexNameOfNetinfo, func() string {
		return "fofaee_netinfos_esi_subdomain_test"
	})
	assert.Equal(suite.T(), "fofaee_netinfos_esi_subdomain_test", elasticx.IndexNameOfNetinfo())

	gomonkey.ApplyFunc(elasticx.IndexNameOfService, func() string {
		return "fofaee_service_esi_subdomain_test"
	})
	assert.Equal(suite.T(), "fofaee_service_esi_subdomain_test", elasticx.IndexNameOfService())

	gomonkey.ApplyFunc(elasticx.IndexNameOfTaskAsset, func() string {
		return "fofaee_task_assets_esi_subdomain_test"
	})
	assert.Equal(suite.T(), "fofaee_task_assets_esi_subdomain_test", elasticx.IndexNameOfTaskAsset())

	gomonkey.ApplyFunc(elasticx.IndexNameOfSubDomain, func() string {
		return "fofaee_subdomain_esi_subdomain_test"
	})
	assert.Equal(suite.T(), "fofaee_subdomain_esi_subdomain_test", elasticx.IndexNameOfSubDomain())

	gomonkey.ApplyFunc(elasticx.IndexNameOfThreat, func() string {
		return "fofaee_threats_esi_subdomain_test"
	})
	assert.Equal(suite.T(), "fofaee_threats_esi_subdomain_test", elasticx.IndexNameOfThreat())

	gomonkey.ApplyFunc(elasticx.IndexNameOfViolation, func() string {
		return "fofaee_violations_esi_subdomain_test"
	})
	assert.Equal(suite.T(), "fofaee_violations_esi_subdomain_test", elasticx.IndexNameOfViolation())

	gomonkey.ApplyFunc(elasticx.IndexNameOfSiteurl, func() string {
		return "fofaee_siteurl_esi_subdomain_test"
	})
	assert.Equal(suite.T(), "fofaee_siteurl_esi_subdomain_test", elasticx.IndexNameOfSiteurl())

	gomonkey.ApplyGlobalVar(&es_asset.Indexes, map[string]string{
		"asset":     elasticx.IndexNameOfAsset(),
		"service":   elasticx.IndexNameOfService(),
		"subdomain": elasticx.IndexNameOfSubDomain(),
	})
}

func (suite *ElasticIndexAssetsSuite) SetupSuite() {
	suite.BeforeSuite()
	var err error
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../../../"),
	)
	assert.NotNil(suite.T(), suite.configure)

	suite.baseDir = "./../../../"
	backup_restore.BaseDir = suite.baseDir
	suite.testDataDir, err = fsfire.GetFilePathWithFileSystemPath(suite.baseDir, fsfire.WithSpecificFileSystemPath("test"))
	assert.NoError(suite.T(), err)

	suite.elasticdatabase, err = elasticx.NewElasticDatabase(suite.configure)
	assert.NoError(suite.T(), err)

	suite.ins = NewStore(suite.elasticdatabase)
	assert.NotNil(suite.T(), suite.ins)

	suite.createIndex()

	err = suite.defaultCreateContent(suite.elasticdatabase, suite.ins.IndexName, suite.ins.TypeName)
	assert.NoError(suite.T(), err)

	suite.afterDropTables = true
}

func (suite *ElasticIndexAssetsSuite) createIndex() {
	backup_restore.InitVar()
	suite.elasticdatabase.Client.CreateIndex(elasticx.IndexNameOfSubDomain()).BodyString(
		fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`,
			backup_restore.SubdomainSettings,
			backup_restore.SubdomainMappings)).Do(context.Background())
	suite.elasticdatabase.Client.IndexPutTemplate(elasticx.IndexNameOfSubDomain()).BodyString(
		fmt.Sprintf(`{ "index_patterns": %s, "settings": %s, "mappings": %s }`,
			"fofaee_subdomain*",
			backup_restore.SubdomainSettings,
			backup_restore.SubdomainMappings)).Do(context.Background())

	suite.elasticdatabase.Client.CreateIndex(elasticx.IndexNameOfService()).BodyString(
		fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`,
			backup_restore.ServiceESSettings,
			backup_restore.ServiceESMappings)).Do(context.Background())
	suite.elasticdatabase.Client.IndexPutTemplate(elasticx.IndexNameOfService()).BodyString(
		fmt.Sprintf(`{ "index_patterns": %s, "settings": %s, "mappings": %s }`,
			"fofaee_service*",
			backup_restore.ServiceESSettings,
			backup_restore.ServiceESMappings)).Do(context.Background())

}

func (suite *ElasticIndexAssetsSuite) defaultCreateContent(elasticdatabase *elasticx.ElasticDatabase, indexName, typeName string) error {
	fileName := "subdomain.json"
	filePath := filepath.Join(suite.testDataDir, "data", fileName)

	res, err := ioutil.ReadFile(filePath)
	if err != nil {
		return err
	}
	tmp := make([]map[string]interface{}, 0)
	err = json.Unmarshal(res, &tmp)
	if err != nil {
		return err
	}
	for _, v := range tmp {
		_, err = elasticdatabase.Client.Index().Index(indexName).Type(typeName).BodyJson(v).Refresh("true").Do(context.Background())
		if err != nil {
			return err
		}
	}
	return nil
}

func (suite *ElasticIndexAssetsSuite) TearDownSuite() {
	if suite.afterDropTables {
		suite.elasticdatabase.Client.DeleteIndex(
			elasticx.IndexNameOfSubDomain(),
			elasticx.IndexNameOfService(),
		).Do(context.Background())
	}
}
