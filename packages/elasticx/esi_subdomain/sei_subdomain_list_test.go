package esi_subdomain

import (
	"context"
	"time"

	"github.com/stretchr/testify/assert"
)

func (suite *ElasticIndexAssetsSuite) Test_GetListAllWriteToFile() {
	actual, err := suite.ins.GetListAllWriteToFile(
		context.TODO(),
		"test_data/subdomain/subdomain_2020_0000024.txt",
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *ElasticIndexAssetsSuite) Test_GetSubdomainByFofa() {
	actual, err := suite.ins.GetSubdomainByFofa(`body="html"`, true)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	//mosso.DebugShowContentWithJSON(actual)
}

func (suite *ElasticIndexAssetsSuite) Test_GetSubdomainByRuleId() {
	time.Sleep(time.Second * 5)
	actual, err := suite.ins.GetSubdomainByRuleId(209)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	//mosso.DebugShowContentWithJSON(actual)
}

func (suite *ElasticIndexAssetsSuite) Test_GetSubdomainFeatureID() {
	actual, err := suite.ins.GetSubdomainFeatureID()
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	//mosso.DebugShowContentWithJSON(actual)
}
func (suite *ElasticIndexAssetsSuite) Test_GetSubdomainAssetFeature() {
	actual, err := suite.ins.GetSubdomainAssetFeature()
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
	//mosso.DebugShowContentWithJSON(actual)
}
