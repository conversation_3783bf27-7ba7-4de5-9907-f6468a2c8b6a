package esi_subdomain

import (
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
)

func (suite *ElasticIndexAssetsSuite) Test_GetServiceSubdomainInfo() {
	searchSource := elastic.NewSearchSource().
		From(1).<PERSON>ze(10).Sort("lastupdatetime", false)
	actual, total, err := suite.ins.GetServiceSubdomainInfo(searchSource)
	assert.NoError(suite.T(), err)
	assert.NotZero(suite.T(), total)
	assert.GreaterOrEqual(suite.T(), len(actual), int(3))
}

func (suite *ElasticIndexAssetsSuite) Test_GetServiceSubdomainAllInfo() {
	searchSource := elastic.NewSearchSource().Query(elastic.NewMatchAllQuery()).
		Sort("lastupdatetime", false)
	actual, total, err := suite.ins.GetServiceSubdomainAllInfo(searchSource)
	assert.NoError(suite.T(), err)
	assert.NotZero(suite.T(), total)
	assert.GreaterOrEqual(suite.T(), len(actual), int(4))
}
