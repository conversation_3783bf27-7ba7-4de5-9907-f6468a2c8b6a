package esi_subdomain

import (
	"encoding/json"

	"git.gobies.org/foeye/foeye3/model/m_product_inspection"

	"github.com/stretchr/testify/assert"
)

func (suite *ElasticIndexAssetsSuite) Test_CreateBulkAssetFeature() {
	dom := m_product_inspection.Dom{}
	err := json.Unmarshal([]byte(`{
    "dom":{
        "p":{
            "44":"0",
            "45":"0",
            "46":"1",
            "47":"1"
        },
        "tag_len":135,
        "nhash":"1904664954807811676",
        "ehash":"f513679b909d9d02226fa7489595e9ad",
        "tag_count":27,
        "foid":"Sv1z3xEq7eGIzmp0AG7r42xYiInvbqA1",
        "shash_bit":"110111101000100011011101011000",
        "sim_hash":"-2411434186488204972",
        "fhash":"4672018609249322461"
    },
    "header":"HTTP/1.1 404 Not Found\r\nConnection: close",
    "body":" <!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\"></body>\n</html>",
    "title":"404-最新网站速度测试结果-Boce.com"
}`), &dom)
	assert.NoError(suite.T(), err)
	data := []m_product_inspection.Subdomain{
		{
			Title:  "404-最新网站速度测试结果-Boce.com",
			Body:   "<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\"></body>\\n</html>",
			Header: "HTTP/1.1 404 Not Found\\r\\nConnection: close",
			Dom:    dom,
		},
	}
	err = suite.ins.CreateBulkAssetFeature(data)
	assert.NoError(suite.T(), err)
}

func (suite *ElasticIndexAssetsSuite) Test_CreateBulkFeatureID() {
	dom := m_product_inspection.Dom{}
	err := json.Unmarshal([]byte(`{
    "dom":{
        "p":{
            "44":"0",
            "45":"0",
            "46":"1",
            "47":"1"
        },
        "tag_len":135,
        "nhash":"1904664954807811676",
        "ehash":"f513679b909d9d02226fa7489595e9ad",
        "tag_count":27,
        "foid":"Sv1z3xEq7eGIzmp0AG7r42xYiInvbqA1",
        "shash_bit":"110111101000100011011101011000",
        "sim_hash":"-2411434186488204972",
        "fhash":"4672018609249322461"
    },
    "title":"404-最新网站速度测试结果-Boce.com"
}`), &dom)
	assert.NoError(suite.T(), err)
	data := []m_product_inspection.FeatureID{
		{
			Title: "404-最新网站速度测试结果-Boce.com",
			Dom:   dom,
		},
	}
	err = suite.ins.CreateBulkFeatureID(data)
	assert.NoError(suite.T(), err)
}
