package esi_subdomain

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/api/risk/asset/exclusive"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
)

// GetServiceSubdomainInfo 获取service和subdomain信息用于管理登录页
func (e *Store) GetServiceSubdomainInfo(searchSource *elastic.SearchSource) ([]*exclusive.RiskLoginListData, int, error) {
	data := make([]*exclusive.RiskLoginListData, 0)
	sourceFetch := elastic.NewFetchSourceContext(true).Include("ip", "port", "body", "header")
	sr, err := e.Instance.Client.
		Search(e.IndexName, elasticx.IndexNameOfService()).
		FetchSourceContext(sourceFetch).
		SearchSource(searchSource).
		Do(context.Background())
	if err != nil {
		return data, 0, err
	}
	for _, v := range sr.Hits.Hits {
		tmp := new(exclusive.RiskLoginListData)
		err = json.Unmarshal(*v.Source, &tmp)
		if err != nil {
			return nil, 0, err
		}
		tmp.ID = v.Id
		data = append(data, tmp)
	}
	return data, int(sr.Hits.TotalHits), nil
}

// GetServiceSubdomainAllInfo 获取service和subdomain信息用于管理登录页的所有信息
func (e *Store) GetServiceSubdomainAllInfo(searchSource *elastic.SearchSource) ([]*exclusive.RiskLoginListData, int, error) {
	data := make([]*exclusive.RiskLoginListData, 0)
	sourceFetch := elastic.NewFetchSourceContext(true).Include("ip", "port", "body", "header")
	ss := e.Instance.Client.
		Scroll(e.IndexName, elasticx.IndexNameOfService()).
		FetchSourceContext(sourceFetch).
		SearchSource(searchSource).Size(1000)
	total := 0
	for {
		sr, err := ss.Do(context.Background())
		if err == io.EOF {
			break
		}
		if err != nil {
			return data, 0, err
		}
		total = int(sr.Hits.TotalHits)
		for _, v := range sr.Hits.Hits {
			tmp := new(exclusive.RiskLoginListData)
			err = json.Unmarshal(*v.Source, &tmp)
			if err != nil {
				return nil, 0, err
			}
			tmp.ID = v.Id
			data = append(data, tmp)
		}
	}

	// 协议为http、https的数据，将协议与IP、端口拼接
	for _, v := range data {
		if v.Protocol == "http" || v.Protocol == "https" {
			v.UrlLink = fmt.Sprintf("%s://%s:%d", v.Protocol, v.IP, v.Port)
		}
	}

	return data, total, nil
}
