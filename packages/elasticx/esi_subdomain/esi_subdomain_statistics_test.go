package esi_subdomain

import (
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
)

func (suite *ElasticIndexAssetsSuite) Test_CountSubdomainTotal() {
	qTotal := elastic.NewMatchAllQuery()
	actual, err := suite.ins.CountSubdomainTotal(qTotal)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 4, actual)
}

func (suite *ElasticIndexAssetsSuite) Test_AssetOverviewCountSubdomain() {
	data, err := suite.ins.AssetOverviewCountSubdomain("10.10.10.20-100")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *ElasticIndexAssetsSuite) Test_AssetOverviewCountDomain() {
	data, err := suite.ins.AssetOverviewCountDomain("10.10.10.20-100")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}
