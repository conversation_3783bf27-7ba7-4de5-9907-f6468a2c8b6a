package esi_subdomain

import (
	"context"
	"encoding/json"
	"io"
	"time"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"

	"github.com/olivere/elastic"
)

func (e *Store) CountSubdomainTotal(query elastic.Query) (int, error) {
	total, err := e.Instance.Client.Count(e.IndexName).Type(e.TypeName).Query(query).Do(context.Background())
	if err != nil && err != io.EOF {
		return 0, err
	}

	return int(total), nil
}

// AssetOverviewCountSubdomain 资产概览计算网站总数
func (e *Store) AssetOverviewCountSubdomain(ipRangeManager string) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)
	tmpData["total"] = 0
	tmpData["add"] = 0

	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	newSubdomain := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("lastupdatetime").
			Gte(time.Now().Format("2006-01-02") + " 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))

	serv, err := e.Instance.Client.Search().
		Index(e.IndexName).
		Type(e.TypeName).
		Query(query).
		Size(0).
		Pretty(true).
		Aggregation("new_subdomain", newSubdomain).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	tmpData["total"] = int(serv.Hits.TotalHits)
	for key, rawMessage := range serv.Aggregations {
		switch key {
		case "new_subdomain":
			tmp := new(m_asset.IntBuckets)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.DocCount
		}
	}
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// AssetOverviewCountDomain 资产概览计算域名总数
func (e *Store) AssetOverviewCountDomain(ipRangeManager string) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)
	tmpData["total"] = 0
	tmpData["add"] = 0

	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	query.Must(elastic.NewExistsQuery("domain"))
	query.MustNot(elastic.NewTermQuery("domain", ""))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	domain := elastic.NewCardinalityAggregation().Field("domain")

	newDomain := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("lastupdatetime").
			Gte(time.Now().Format("2006-01-02")+" 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02")+" 00:00:00")).
		SubAggregation("domain", domain)

	serv, err := e.Instance.Client.Search().
		Index(e.IndexName).
		Type(e.TypeName).
		Query(query).
		Size(0).
		Pretty(true).
		Aggregation("new_domain", newDomain).
		Aggregation("domain", domain).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	for key, rawMessage := range serv.Aggregations {
		switch key {
		case "new_domain":
			tmp := new(m_asset.DomainCardinalityAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.Domain.Value
		case "domain":
			tmp := new(m_asset.CardinalityAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["total"] = tmp.Value
		}
	}
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}
