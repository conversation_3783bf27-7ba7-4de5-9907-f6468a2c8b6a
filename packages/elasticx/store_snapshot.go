package elasticx

import "github.com/olivere/elastic"

type ElasticSnapshotDatabaseStore interface {
	CrateSnapshotRepository(repository string, settings map[string]interface{}) error
	GetAllSnapshot(repository string) ([]*elastic.Snapshot, error)
	DeleteSnapshot(repository string, snapshot string) error
	GetCatIndices() ([]elastic.CatIndicesResponseRow, error)
	CreateSnapshot(repository string, snapshot string) (*elastic.SnapshotCreateResponse, error)
	VerifyRepository(repository string) bool
}
