package esi_task_domain_assets

import (
	"sync"

	"git.gobies.org/foeye/foeye3/packages/elasticx"
)

type Store struct {
	Instance  *elasticx.ElasticDatabase
	IndexName string
	TypeName  string
	fileLock  sync.Mutex
}

func NewStore(elasticDatabase *elasticx.ElasticDatabase) *Store {
	return &Store{
		Instance:  elasticDatabase,
		IndexName: elasticx.IndexNameOfTaskDomainAsset(),
		TypeName:  elasticx.TypeNameOfTaskDomainAssets,
	}
}
