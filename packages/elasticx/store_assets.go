package elasticx

import (
	"context"

	"github.com/gin-gonic/gin"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/internal/mux/model/m_assets"

	"git.gobies.org/foeye/foeye3/api/risk/asset/exclusive"
	miningExclusive "git.gobies.org/foeye/foeye3/api/risk/mining/exclusive"
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_dashboard"
	"git.gobies.org/foeye/foeye3/model/m_ip_matrix"
	"git.gobies.org/foeye/foeye3/model/m_net_topos"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/model/m_product_inspection"
	"git.gobies.org/foeye/foeye3/model/m_report"
	"git.gobies.org/foeye/foeye3/model/m_second_categories"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/model/m_user_rules"
	"git.gobies.org/foeye/foeye3/packages/exchange"
	"git.gobies.org/foeye/foeye3/responses/r_asset"
	"git.gobies.org/foeye/foeye3/responses/r_net_topos"
)

// ElasticSearch index related constant definition for {assets}.
const (
	IndexNameOfAssets = "fofaee_assets"
	TypeNameOfAssets  = "ips"
)

type IndexAssetsStore interface {
	GetListForIPRange(ctx context.Context, ops ...Option) (*elastic.SearchResult, error)
	GetListAllWriteToFile(ctx context.Context, filename string, ops ...Option) (string, error)
	GetListAllWithState(ctx context.Context, ops ...Option) ([]map[string]string, error)
	Count(ctx context.Context) (int, error)

	GetList(ctx context.Context, ips []string) (interface{}, error)
	GetListAll(ipRangeManager string, xcType string) (interface{}, error)
	GetListAllToday() (interface{}, error)
	BullUpdateStateByIP(ip string, state int) error
	UpdateByQuery(query elastic.Query, body map[string]interface{}, single bool) error
	CountWithTag(realname, name string) (int, error)

	CountAssetsNumber(ctx context.Context, instance []*m_report.ReportTypeIpRanges) (int64, error)
	DeleteIndexTag(query elastic.Query, script *elastic.Script) error
	UpdateIndexByQueryScript(query elastic.Query, script *elastic.Script, indices ...string) error
	DeleteByIpRangeOrKeyword(ipRange []string, keyword string) error

	// GetAssetServerList API2.0.
	GetAssetServerList(ctx context.Context, tags []*m_tag.Tag, pageNum, pageSize int, keyword string, ipRanges ...string) ([]*m_assets.AssetsList, int64, error)

	GetSourceIPs() ([]string, error)
	GetEsClient() *elastic.Client
	GetAssetNum() (int64, error)
	GetAllAssets(ctx context.Context) ([]*m_user_rules.AssetsUserRule, error)

	GetListByDefault(ctx context.Context, QL *r_asset.QueryListAndKeyword, ipRangeManager string) ([]*m_asset.AssetListInfo, int64, error)
	GetListByDefaultIp(ctx context.Context, QL *r_asset.QueryListAndKeyword, ipRangeManager string) ([]*m_asset.AssetIpListInfo, int64, error)
	GetDomainList(ctx context.Context, QL *r_asset.QueryListAndKeyword, ipRangeManager string) ([][]*m_asset.ResultDomainData, int64, error)
	GetBusinessList(ctx context.Context, QL *r_asset.QueryListAndKeyword, ipRangeManager string) (map[string]*m_asset.ResultBusinessParentData, error)
	GetAssetAdvancedScreen(ctx context.Context, tags []*m_tag.Tag, QL *r_asset.QueryListAndKeyword, ipRangeManager string) (interface{}, error)
	GetAssetStatisticsInfo(ctx context.Context, QL *r_asset.QueryListAndKeyword, tagMaps map[string]string, components []*m_second_categories.SecondCategories, ipRangeManager string) (interface{}, error)
	GetAssetCountDataByTask(ctx context.Context, LatestTaskId int, LastTaskId int) (*m_asset.TaskAssetCountData, error)
	UpdateBulkAssetVuls(ctx context.Context, ips map[string]int) error
	GetGenerateReportTags(ctx context.Context, tags []*m_tag.Tag, item map[string]uint) ([]*m_task.Taglist, error)
	GetListWithCategory(ctx context.Context, categories []*m_second_categories.SecondCategories, XcType string, ipRangeManager string) (interface{}, error)

	AggTagBindAssetsCount(customLabel []string) (*elastic.SearchResult, error)
	GetAssetDetail(ctx context.Context, item *m_asset.AssetDetails) (*m_asset.ResponseAssetDetail, error)
	GetAssetTopo(ctx context.Context, QL *r_net_topos.QueryList) ([]*m_net_topos.SubIpRanges, error)
	GetUserRule(ips []string) (map[string]*m_user_rules.AssetsUserRule, error)
	UpdateUserRule(data map[string]*m_user_rules.AssetsUserRule) error
	GetUserRuleByRuleId(ruleId uint) (map[string]*m_user_rules.AssetsUserRule, error)
	CalculateTheNumberOfRiskyAssets(ctx context.Context, query string) (int, error)
	GetAllIpMatrix() (map[string]*m_ip_matrix.IpMatrixInfo, error)
	UpdateAllIpMatrix(data map[string]*m_ip_matrix.IpMatrixInfo) error
	ExistsIpMatrixMapping() (bool, error)
	GetAssetsByExport(ctx context.Context, QL *r_asset.QueryListAndKeyword, ipRangeManager string) ([]*m_asset.AssetIpListInfo, int64, error)
	UpdateAssetsMapping(ctx context.Context) error
	PutAssetLevelMapping() error
	GetComponentInformation() ([]m_product_inspection.ComponentInformation, error)
	GetOtherType(DB *database.GormDatabase) (m_product_inspection.OtherType, error)
	CountTotal() (int, error)
	BulkUpdateBusiness(ctx context.Context, ips []string, name string) error
	GetAssetClassificationStatistics(ipRangeManager string) ([]m_overviews.AssetClassificationStatistics, error)
	GetComponentRanking(ipRangeManager string) ([]m_overviews.RankingList, error)
	GetCompanyRanking(ipRangeManager string) ([]m_overviews.RankingList, error)
	CountAsset(ipRangeManager string) ([]m_overviews.Index, error)
	CountAssetForXc(ipRangeManager string) ([]m_overviews.Index, error)
	CountRule(ipRangeManager string) ([]m_overviews.Index, error)
	CountRuleForXc(ipRangeManager string) ([]m_overviews.Index, error)
	CountPortForXc(ipRangeManager string) ([]m_overviews.Index, error)
	CountXcPortForXc(ipRangeManager string, ports string) (int, error)
	CountXcRuleForXc(ipRangeManager string) (int, error)
	GetBasicSoftwareRank(ipRangeManager string, xcType string) ([]m_overviews.BasicSoftwareRank, error)
	GetRuleDataRank(ipRangeManager string, xcType string) ([]m_overviews.SoftwareAndHardwareRank, error)
	CountBusinessApp(ipRangeManager string) ([]m_overviews.Index, error)
	GetUsernameByIp(ips []string) (map[string]string, error)
	GetBusinessCount(ctx context.Context, ipRangeManager string, query []*elastic.TermQuery) (int64, error)
	GetBusinessInfo(ctx context.Context, ipRangeManager, business string, tag map[string]string) (*m_asset.BaseBusinessInfo, error)
	GetBusinessUpdateTime(ctx context.Context, ipRangeManager, business string) (string, error)
	GetBusinessCategory(ctx context.Context, ipRangeManager, business string) ([]*m_asset.AssetBusinessStatistics, error)
	GetBusinessOsRule(ctx context.Context, ipRangeManager, ip, port string) (string, string, error)
	GetAssetAllIp(limit, size int) ([]string, error)
	GetAssetIpBySize(size int) ([]string, error)
	GetBusinessAllIpByNames(ctx context.Context, names []string) ([]string, error)

	GetAssetListMenu(ctx context.Context, ipRangeManager string) ([]m_overviews.MenuIndex, error)
	CountAssetTotal(ipRangeManager string, xcType string) int
	AssetOverviewCountBusinessApp(ipRangeManager string) ([]m_overviews.Index, error)
	AssetOverviewCountDomain(ipRangeManager string) ([]m_overviews.Index, error)
	AssetOverviewCountAsset(ipRangeManager string) ([]m_overviews.Index, []m_overviews.Index, []m_asset.Component, error)
	AssetOverviewCountAssetLevel(ipRangeManager string) ([]m_overviews.Index, error)
	AssetOverviewCountPortProtocol(ipRangeManager string) ([]m_overviews.Index, []m_overviews.Index, error)
	AssetOverviewTags(ipRangeManager string, tagMaps map[string]string) ([]m_asset.LabelDistribution, error)
	UpdateAssetVuls(threat *m_threat.ThreatVul) error
	AssetsRiskInterface
	AssetsRiskDangerInterface
	DashboardInterface
	GetAssetBasicInfoCount(ctx context.Context, boolQuery *elastic.BoolQuery, ipRangeManager string) (map[string]int, error)
	GetAssetBusiness(ctx context.Context, boolQuery *elastic.BoolQuery, ipRangeManager string) ([]*m_asset.Bucket, error)
	GetListForProtocol(ctx context.Context, protocols []interface{}) interface{}

	GetNewDomainList(ctx context.Context, QL *r_asset.QueryDomainList, ipRangeManager string) ([]m_asset.ResultNewDomainData, int64, error)
	GetDomainByExport(ctx context.Context, QL *r_asset.QueryDomainList) ([]*m_asset.ResultNewDomainData, int64, error)

	DelDomain(ctx *gin.Context, QL *r_asset.QueryDomainList) error

	GetIpsByDomains(ctx context.Context, domains []string) ([]string, error)
}

func IndexNameOfAsset() string {
	return IndexNameOfAssets
}

type AssetsRiskInterface interface {
	GetAssetIpTags(ips []string) ([]*exclusive.AssetsIpTags, error)
	GetAssetTagAdvancedSearch(ips []string, customTag map[string]string) (map[string][]string, []*exclusive.CustomFields, error)
	GetAssetIps(searchSource *elastic.SearchSource) ([]string, error)
	GetAssetTags(ips []string) (map[string]*exclusive.RiskAssetTags, error)
}

type AssetOfMining interface {
	MiningAssetWithPagination(ctx context.Context, secondCateTag string, params exchange.RiskMiningOfParams) (*miningExclusive.RiskMiningPagination, error)
	Sidebar(ctx context.Context, secondCateTag string, params exchange.RiskMiningOfParams, all bool) ([]*exchange.SidebarOfSingle, error)
	SummaryOfTag(ctx context.Context, secondCateTag string) (exchange.RiskMiningSummariesTags, error)
	SummaryOfType(ctx context.Context, secondCateTag string) (exchange.RiskMiningAggregationValueItems, error)
	MiningCount(ctx context.Context, secondCateTag string, params exchange.RiskMiningOfParams) (int, error)
	MiningAsset(ctx context.Context, secondCateTag string, params exchange.RiskMiningOfParams) (*exchange.RiskMiningOfData, error)
}

type AssetsRiskDangerInterface interface {
	GetDangerList(ctx context.Context, ipRangeManager string, params exchange.RiskDangerParams) ([]exchange.ResultRiskDanger, int, error)
	GetDangerFilter(ctx context.Context, tags []*m_tag.Tag, params exchange.RiskDangerParams, ipRangeManager string) (*exchange.ResultDangerPortFilterData, error)
	GetDangerFilterUp(ctx context.Context, tags []*m_tag.Tag, params exchange.RiskDangerParams, ipRangeManager string) (*exchange.ResultDangerPortFilterUpData, error)
	GetDangerExport(ctx context.Context, ipRangeManager string, params exchange.RiskDangerParams) ([]exchange.ResultRiskDanger, error)
	GetDangerStatistics(ctx context.Context, tags []*m_tag.Tag, params exchange.RiskDangerParams, ipRangeManager string) ([]exchange.Data, error)
}

type DashboardInterface interface {
	GetDashboardCategory(ctx context.Context, ipRangeManager, category string) ([]*m_threat.SystemBucket, int64, error)
	GetDashboardCount(ctx context.Context, ipRangeManager string, boolQuery *elastic.BoolQuery, isSubdomain bool) (int64, error)
	GetDashboardRule(ctx context.Context, ipRangeManager string) ([]*m_dashboard.Rule, map[string]float64, error)
}
