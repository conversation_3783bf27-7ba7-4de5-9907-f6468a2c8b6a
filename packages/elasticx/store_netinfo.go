package elasticx

import (
	"context"

	"git.gobies.org/foeye/foeye3/model/m_netinfo"

	"github.com/gin-gonic/gin"
)

const (
	IndexNameOfNetinfos = "fofaee_netinfos"
	TypeNameOfNetinfos  = "netinfos"
)

type ESNetInfoDatabaseInter interface {
	GetUnIdentifyAssets(ctx *gin.Context) ([]string, error)
	GetUnIdentifyAssetsMap(ctx context.Context) (map[string][]string, error)
	GetUnIdentifyAssets2(ctx context.Context, size int) (map[string]*m_netinfo.NetInfo, error)
	BatchUpdateUnIdentifyAssets(ctx context.Context, m map[string]*m_netinfo.NetInfo) error
	UpdateNetInfoIsPush(ctx context.Context, q []string) error
	TotalCount() int64
}

func IndexNameOfNetinfo() string {
	return IndexNameOfNetinfos
}
