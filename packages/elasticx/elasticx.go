package elasticx

import (
	"log"
	"os"
	"sync"
	"time"

	"git.gobies.org/foeye/foeye3/config"

	"github.com/olivere/elastic"
)

// Command constants definition.
const (
	IsPrettyOfTrue  = true
	IsPrettyOfFalse = false

	DefaultPaginationNumber = 1
	DefaultPaginationSize   = 10
)

// Document related constants definition.
const (
	DefaultDocumentSource = "_source"
	DefaultResultNotFound = "not_found"
)

// Document query with scroll, related constant definition.
const (
	DefaultScrollQueryBulkSize = 100
	DefaultScrollKeepAlive     = "1m"
)

// ElasticDatabase is a wrapper for the gorm framework.
type ElasticDatabase struct {
	Client *elastic.Client
}

var singleton *ElasticDatabase
var once sync.Once

func NewElasticDatabase(configure *config.Configure) (*ElasticDatabase, error) {
	var err error
	var client *elastic.Client

	once.Do(func() {

		params := []elastic.ClientOptionFunc{
			elastic.SetURL(configure.Elastic.Host),
			elastic.SetSniff(false),
			elastic.SetGzip(configure.Elastic.IsUseGzipCompress),
			elastic.SetHealthcheckInterval(10 * time.Second),
			elastic.SetMaxRetries(5),
			elastic.SetErrorLog(log.New(os.Stderr, "ELASTIC ", log.LstdFlags)),
			elastic.SetInfoLog(log.New(os.Stdout, "", log.LstdFlags)),
		}

		if configure.Elastic.IsShowQuery {
			params = append(params, elastic.SetTraceLog(log.New(os.Stdout, "", log.LstdFlags)))
		}

		// Initialize elastic client instance.
		client, err = elastic.NewClient(params...)
		if err != nil {
			return
		}

		singleton = &ElasticDatabase{Client: client}
	})

	return singleton, err
}
