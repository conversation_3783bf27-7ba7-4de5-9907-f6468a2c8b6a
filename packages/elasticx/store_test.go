package elasticx

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIndexName(t *testing.T) {
	indexName := IndexNameOfAsset()
	assert.Equal(t, IndexNameOfAssets, indexName)

	indexName = IndexNameOfViolation()
	assert.Equal(t, IndexNameOfViolations, indexName)

	indexName = IndexNameOfIndexTag()
	assert.Equal(t, IndexNameOfIndexTags, indexName)

	indexName = IndexNameOfNetinfo()
	assert.Equal(t, IndexNameOfNetinfos, indexName)

	indexName = IndexNameOfService()
	assert.Equal(t, IndexNameOfServices, indexName)

	indexName = IndexNameOfSiteurl()
	assert.Equal(t, IndexNameOfSiteurls, indexName)

	indexName = IndexNameOfSubDomain()
	assert.Equal(t, IndexNameOfSubDomains, indexName)

	indexName = IndexNameOfTaskAsset()
	assert.Equal(t, IndexNameOfTaskAssets, indexName)

	indexName = IndexNameOfThreat()
	assert.Equal(t, IndexNameOfThreats, indexName)
}

func TestClient(t *testing.T) {
	client := Client()
	assert.Nil(t, client)

	var f Factory
	SetClient(f)

	client = Client()
	assert.Equal(t, client, f)
}
