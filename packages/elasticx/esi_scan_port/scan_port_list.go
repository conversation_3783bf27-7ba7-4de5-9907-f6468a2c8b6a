package esi_scan_port

import (
	"context"
	"io"
	"sync"

	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/olivere/elastic"
)

var mutex sync.Mutex

// GetListWithNoneStd 获取非标端口列表数据.
func (e *Store) GetListWithNoneStd(ctx context.Context) (*elastic.SearchResult, error) {

	query := elastic.NewMatchAllQuery()

	var r = new(elastic.SearchResult)

	mutex.Lock()
	defer mutex.Unlock()

	fsc := elastic.NewFetchSourceContext(true).Include("ports", "protocols")
	service := e.Instance.Client.Scroll().
		Index(e.IndexName).
		Type(e.TypeName).
		FetchSourceContext(fsc).
		Query(query)

	result, err := service.
		Size(elasticx.NoneStdScrollQueryBulkSize).
		Do(ctx)

	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil && len(result.Hits.Hits) > 0 {
			for _, item := range result.Hits.Hits {
				if r.Hits == nil {
					r.Hits = &elastic.SearchHits{Hits: make([]*elastic.SearchHit, 0)}
					r.Hits.Hits = append(r.Hits.Hits, item)
				} else {
					r.Hits.Hits = append(r.Hits.Hits, item)
				}
			}

			r.Hits.TotalHits += result.Hits.TotalHits
			result, err = e.Instance.Client.
				Scroll().
				Scroll(elasticx.DefaultScrollKeepAlive).
				ScrollId(result.ScrollId).
				Index(e.IndexName).
				Type(e.TypeName).
				Query(query).
				Size(elasticx.DefaultScrollQueryBulkSize).
				Do(ctx)

			if err != nil {
				break
			}
		} else {
			break
		}
	}
	//debuger.ShowContentForJSON(r, true, true, true)
	if err != nil && err != io.EOF {
		return nil, err
	}
	return r, nil
}
