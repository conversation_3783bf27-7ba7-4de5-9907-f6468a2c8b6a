package esi_service

import (
	"encoding/json"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/model/m_product_inspection"
	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/stretchr/testify/assert"
)

const searchResult = `{
	"took": 1,
	"hits": {
		"total": 1,
		"max_score": 1,
		"hits": [
				{
"_index": "fofaee_subdomain",
"_type": "subdomain",
"_id": "vpn.pacholke.net",
"_version": 1,
"_score": 1,
"_source": {
"appserver": [
"nginx"
],
"asn": {
"as_number": 22773,
"as_organization": "ASN-CXA-ALL-CCI-22773-RDC"
},
"body": "",
"charset": "iso-8859-1",
"dom": {
"ehash": "cb0440d8b13b10391d8314eabf764594",
"fhash": "4607600793733671761",
"foid": "xjy573ZdgyjV4ShNansCENf0DMVvJK1e",
"nhash": "7316009209076966777",
"p": {
"0": "1",
"1": "1",
"2": "0",
"3": "1",
"4": "0",
"5": "1",
"6": "0",
"7": "0",
"8": "0",
"9": "0",
"10": "0",
"11": "0",
"12": "1",
"13": "1",
"14": "0",
"15": "1",
"16": "1",
"17": "1",
"18": "1",
"19": "0",
"20": "0",
"21": "1",
"22": "0",
"23": "1",
"24": "0",
"25": "0",
"26": "1",
"27": "1",
"28": "0",
"29": "0",
"30": "0",
"31": "1",
"32": "1",
"33": "1",
"34": "1",
"35": "0",
"36": "0",
"37": "1",
"38": "1",
"39": "0",
"40": "0",
"41": "1",
"42": "1",
"43": "0",
"44": "0",
"45": "0",
"46": "1",
"47": "0",
"48": "0",
"49": "0",
"50": "1",
"51": "0",
"52": "0",
"53": "1",
"54": "0",
"55": "1",
"56": "0",
"57": "1",
"58": "0",
"59": "1",
"60": "1",
"61": "0",
"62": "1",
"63": "1"
},
"shash_bit": "1101010000001101111001010011000111100110011000100010010101011011",
"sim_hash": "-3166622960490240677",
"tag_count": 10,
"tag_len": 63
},
"domain": "pacholke.net",
"fid": "cb0440d8b13b10391d8314eabf764594",
"fidv2": "xjy573ZdgyjV4ShNansCENf0DMVvJK1e",
"fraud_name": "",
"geoip": {
"city_name": "Tulsa",
"continent_code": "NA",
"country_code2": "US",
"country_code3": "US",
"country_name": "United States",
"dma_code": 671,
"latitude": 36.0606,
"location": {
"lat": 36.0606,
"lon": -95.9456
},
"longitude": -95.9456,
"postal_code": "74136",
"real_region_name": "Oklahoma",
"region_name": "OK",
"timezone": "America/Chicago"
},
"header": "HTTP/1.1 200 OK Connection: close Content-Length: 493 Accept-Ranges: bytes Content-Type: text/html Date: Tue, 16 Apr 2024 08:21:21 GMT Last-Modified: Wed, 27 Mar 2024 01:24:02 GMT Server: nginx ",
"honeypot_name": "",
"host": "vpn.pacholke.net",
"ip": "************",
"ipcnet": "***********",
"is_fraud": false,
"is_honeypot": false,
"is_ipv6": false,
"isdomain": true,
"lastchecktime": "2024-04-16 16:21:22",
"lastupdatetime": "2024-04-16 16:21:22",
"port": 80,
"product": [
"NGINX"
],
"protocol": "http",
"rule_tags": [
{
"category": "Service",
"cn_category": "服务",
"cn_company": "Nginx",
"cn_parent_category": "支撑系统",
"cn_product": "NGINX",
"company": "Nginx",
"level": "3",
"parent_category": "Support System",
"product": "NGINX",
"rule_id": "209",
"softhard": "2"
}
],
"server": "nginx",
"status_code": 200,
"sub_body": "",
"subbody": null,
"subdomain": "vpn",
"title": "",
"v": 5,
"version": [
"nginx/"
]
}
}
		]
	},
	"_shards": {
		"total": 5,
		"successful": 5,
		"failed": 0
	}
}
`

func (suite *ElasticIndexAssetsSuite) Test_GetUseProtocol() {
	data, err := suite.ins.GetUseProtocol()
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
	//mosso.DebugShowContentWithJSON(data)
}

func (suite *ElasticIndexAssetsSuite) Test_GetUsePort() {
	data, err := suite.ins.GetUsePort()
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
	//mosso.DebugShowContentWithJSON(data)
}

func (suite *ElasticIndexAssetsSuite) Test_GetUnrecognizedProtocol() {
	data, err := suite.ins.GetUnrecognizedProtocol()
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
	//mosso.DebugShowContentWithJSON(data)
}

func (suite *ElasticIndexAssetsSuite) Test_GetUnknownAsset() {
	data, err := suite.ins.GetUnknownAsset()
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
	//mosso.DebugShowContentWithJSON(data)
}

func (suite *ElasticIndexAssetsSuite) Test_GetUnknownAssetBySize() {
	var sr *elastic.SearchResult
	err := json.Unmarshal([]byte(searchResult), &sr)
	assert.NoError(suite.T(), err)
	fsc := elastic.NewFetchSourceContext(true).Include("ip", "title", "port", "protocol", "banner", "header", "cert", "body", "fid")
	defer gomonkey.ApplyMethodReturn(suite.elasticdatabase.Client.Search(elasticx.IndexNameOfService(), elasticx.IndexNameOfSubDomain()).Size(50).Query(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("product"))).Pretty(true).Sort("lastupdatetime", false).FetchSourceContext(fsc), "Do", sr, nil).Reset()
	data, err := suite.ins.GetUnknownAssetBySize(50)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *ElasticIndexAssetsSuite) Test_GetAssetFeature() {
	data, err := suite.ins.GetAssetFeature()
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
	//mosso.DebugShowContentWithJSON(data)
}

func (suite *ElasticIndexAssetsSuite) Test_CreateBulkAssetFeature() {
	data := []m_product_inspection.Service{
		{
			Protocol: "ftp",
			Banner:   "220---------- Welcome",
		},
	}
	err := suite.ins.CreateBulkAssetFeature(data)
	assert.NoError(suite.T(), err)
}
