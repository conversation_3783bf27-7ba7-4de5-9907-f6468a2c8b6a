package esi_service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"

	"github.com/olivere/elastic"
	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_user_rules"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/fofa-backend/fofacore"
)

func (e *Store) GetListAllWriteToFile(ctx context.Context, filename string, ops ...elasticx.Option) (string, error) {
	var err error

	defer func() {
		if err != nil && err != io.EOF {
			os.Remove(filename)
		}
	}()

	file, err := elasticx.MustOpenFile(filename)
	if err != nil {
		return "", err
	}
	defer file.Close()

	search := e.Instance.Client.
		Scroll().
		Scroll(elasticx.DefaultScrollKeepAlive).
		Index(e.IndexName).
		Type(e.TypeName).
		Query(elastic.NewMatchAllQuery()).
		Size(elasticx.DefaultScrollQueryBulkSize)

	result, err := search.Do(ctx)

	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
			for _, hit := range result.Hits.Hits {
				if hit.Source != nil {
					cur, err := hit.Source.MarshalJSON()
					if err != nil {
						continue
					}

					cur = append(cur[0:len(cur)-1], []byte(fmt.Sprintf(`,"_id":"%s"}`, hit.Id))...)
					cur = append(cur, '\n')
					if _, err := file.Write(cur); err != nil {
						log.Printf("failed write context to file: %+v\n", err)
					}
				}
			}

			// Proceed to the next read.
			result, err = search.ScrollId(result.ScrollId).Do(ctx)
			if err != nil {
				break
			}
		} else {
			break
		}
	}

	if err != nil && err != io.EOF {
		return "", err
	}

	return filename, nil
}

// GetServiceByFofa 根据fofa语法解析出来的es条件获取service索引的规则信息，id和数据的映射
func (e *Store) GetServiceByFofa(query string, state bool) (map[string]*m_user_rules.ServiceSubdomain, error) {
	data := make(map[string]*m_user_rules.ServiceSubdomain)
	//fofa := es_asset_search.NewFofaQueryParser()
	//fofaQuery, err := fofa.ParseToES6(query)
	//if err != nil {
	//	return nil, err
	//}
	qi := fofacore.QueryInput{
		Query: query,
		Full:  true,
		Fraud: !state,
	}
	out, err := fofacore.ParseFEQuery(&qi)
	if err != nil {
		return nil, err
	}

	qr, ok := out.Query["query"]
	if !ok {
		return nil, errors.New("query is nil")
	}

	body, err := json.MarshalIndent(qr, "", "  ")
	if err != nil {
		return nil, err
	}
	//fofaQuery := fmt.Sprintf(`{"query":%s,"size":%d}`, string(body), 10000000)
	log.Println("service_fofaquery:", string(body))
	res, err := e.Instance.Client.
		Search(e.IndexName).
		Type(e.TypeName).
		Query(elastic.NewRawStringQuery(string(body))).
		Size(10000000).
		Do(context.Background())
	if err != nil {
		return nil, err
	}
	for _, val := range res.Hits.Hits {
		tmp := new(m_user_rules.ServiceSubdomain)
		err = json.Unmarshal(*val.Source, &tmp)
		if err != nil {
			return nil, err
		}
		data[val.Id] = tmp
	}

	return data, nil
}

// GetServiceByRuleId 根据规则ID获取service索引的规则信息，id和数据的映射
func (e *Store) GetServiceByRuleId(ruleId uint) (map[string]*m_user_rules.ServiceSubdomain, error) {
	query := elastic.NewTermQuery("rule_tags.rule_id", ruleId)
	data := make(map[string]*m_user_rules.ServiceSubdomain)
	res, err := e.Instance.Client.
		Search(e.IndexName).
		Type(e.TypeName).
		Query(query).
		Size(10000000).
		Do(context.Background())
	if err != nil {
		return nil, err
	}
	for _, val := range res.Hits.Hits {
		tmp := new(m_user_rules.ServiceSubdomain)
		err = json.Unmarshal(*val.Source, &tmp)
		if err != nil {
			return nil, err
		}
		data[val.Id] = tmp
	}
	return data, nil
}

// GetAsnCertLatitudeAndLongitudeByIp 获取asn,ssl证书,经纬度
func (e *Store) GetAsnCertLatitudeAndLongitudeByIp(ip string) (map[int]m_asset.AssetAsnCertLatitudeAndLongitude, error) {
	data := make(map[int]m_asset.AssetAsnCertLatitudeAndLongitude)
	if ip == "" {
		return data, nil
	}
	query := elastic.NewTermQuery("ip", ip)
	res, err := e.Instance.Client.
		Search(e.IndexName).
		Type(e.TypeName).
		Query(query).
		Size(65535).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	for _, val := range res.Hits.Hits {
		tmp := m_asset.AssetAsnCertLatitudeAndLongitude{}
		err = json.Unmarshal(*val.Source, &tmp)
		if err != nil {
			return data, err
		}
		data[tmp.Port] = tmp
	}
	return data, nil
}
