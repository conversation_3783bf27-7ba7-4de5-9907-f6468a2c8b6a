package esi_service

import (
	"context"
	"fmt"

	"github.com/stretchr/testify/assert"
)

func (suite *ElasticIndexAssetsSuite) Test_GetListAllWriteToFile() {
	actual, err := suite.ins.GetListAllWriteToFile(
		context.TODO(),
		"test_data/service/service_2020_0000025.txt",
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *ElasticIndexAssetsSuite) Test_GetServiceByFofa() {
	res, err := suite.ins.GetServiceByFofa(`banner="ssh"`, true)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res)
	for k, v := range res {
		fmt.Println(k, v.Product)
	}
	//mosso.DebugShowContentWithJSON(res)
}

func (suite *ElasticIndexAssetsSuite) Test_GetServiceByRuleId() {
	data, err := suite.ins.GetServiceByRuleId(10000004)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
	//mosso.DebugShowContentWithJSON(data)
}
