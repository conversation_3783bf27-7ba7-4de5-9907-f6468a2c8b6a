package esi_service

import (
	"context"
	"encoding/json"
	"html/template"
	"io"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/model/m_product_inspection"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/ipx"
)

// GetUseProtocol 获取使用协议
func (e *Store) GetUseProtocol() ([]m_product_inspection.UseProtocol, error) {
	data := make([]m_product_inspection.UseProtocol, 0)
	protocolAgg := elastic.NewTermsAggregation().Field("protocol").Size(50)
	sr, err := e.Instance.Client.
		Search(e.IndexName).
		Type(e.TypeName).
		Query(elastic.NewBoolQuery().MustNot(elastic.NewTermQuery("protocol", "unknown"))).
		Aggregation("protocolAgg", protocolAgg).
		Pretty(true).
		Do(context.Background())
	if err != nil && err != io.EOF {
		return nil, err
	}
	res, exists := sr.Aggregations.Terms("protocolAgg")
	if exists {
		for _, val := range res.Buckets {
			tmp := m_product_inspection.UseProtocol{}
			tmp.ProtocolName = val.Key.(string)
			tmp.ProtocolNumber = int(val.DocCount)
			data = append(data, tmp)
		}
	}
	return data, nil
}

// GetUnrecognizedProtocol 获取未识别协议
func (e *Store) GetUnrecognizedProtocol() ([]m_product_inspection.UnrecognizedProtocol, error) {
	data := make([]m_product_inspection.UnrecognizedProtocol, 0)
	fsc := elastic.NewFetchSourceContext(true).Include("ip", "banner", "port")
	sr, err := e.Instance.Client.
		Search(e.IndexName).
		Type(e.TypeName).
		Query(elastic.NewTermQuery("protocol", "unknown")).
		Pretty(true).
		FetchSourceContext(fsc).
		Size(50).
		Do(context.Background())
	if err != nil && err != io.EOF {
		return nil, err
	}
	for _, val := range sr.Hits.Hits {
		tmp := m_product_inspection.UnrecognizedProtocol{}
		err = json.Unmarshal(*val.Source, &tmp)
		if err != nil {
			return nil, err
		}
		data = append(data, tmp)
	}

	return data, nil
}

// GetUsePort 获取使用端口
func (e *Store) GetUsePort() ([]m_product_inspection.UsePort, error) {
	data := make([]m_product_inspection.UsePort, 0)
	portAgg := elastic.NewTermsAggregation().Field("port").Size(50)
	sr, err := e.Instance.Client.
		Search(e.IndexName).
		Type(e.TypeName).
		Aggregation("portAgg", portAgg).
		Pretty(true).
		Do(context.Background())
	if err != nil && err != io.EOF {
		return nil, err
	}
	res, exists := sr.Aggregations.Terms("portAgg")
	if exists {
		for _, val := range res.Buckets {
			tmp := m_product_inspection.UsePort{}
			tmp.PortName = val.Key.(string)
			tmp.PortNumber = int(val.DocCount)
			data = append(data, tmp)
		}
	}
	return data, nil
}

// GetUnknownAssetBySize 获取未知资产指定数量
func (e *Store) GetUnknownAssetBySize(size int) ([]m_product_inspection.UnknownAsset, error) {
	data := make([]m_product_inspection.UnknownAsset, 0)
	fsc := elastic.NewFetchSourceContext(true).Include("ip", "title", "port", "protocol", "banner", "header", "cert", "body", "fid")
	sr := e.Instance.Client.
		Search(e.IndexName, elasticx.IndexNameOfSubDomain()).
		Size(size).
		Query(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("product"))).
		Pretty(true).
		Sort("lastupdatetime", false).
		FetchSourceContext(fsc)
	res, err := sr.Do(context.Background())

	if err != nil {
		return nil, err
	}

	for _, searchHit := range res.Hits.Hits {
		tmp := m_product_inspection.UnknownAsset{}
		err = json.Unmarshal(*searchHit.Source, &tmp)
		if err != nil {
			return nil, err
		}
		tmp.Body = template.HTMLEscapeString(tmp.Body)
		tmp.Banner = template.HTMLEscapeString(tmp.Banner)
		tmp.Title = template.HTMLEscapeString(tmp.Title)
		tmp.Cert = template.HTMLEscapeString(tmp.Cert)
		data = append(data, tmp)
	}
	return data, nil
}

// GetUnknownAsset 获取未知资产
func (e *Store) GetUnknownAsset() ([]m_product_inspection.UnknownAsset, error) {
	data := make([]m_product_inspection.UnknownAsset, 0)
	fsc := elastic.NewFetchSourceContext(true).Include("ip", "title", "port", "protocol", "banner", "header", "cert", "body", "fid")
	sr := e.Instance.Client.
		Scroll(e.IndexName, elasticx.IndexNameOfSubDomain()).
		Size(100000).
		Query(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("product"))).
		Pretty(true).
		//Sort("lastupdatetime", false).
		FetchSourceContext(fsc)
	for {
		res, err := sr.Do(context.Background())
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}
		for _, searchHit := range res.Hits.Hits {
			tmp := m_product_inspection.UnknownAsset{}
			err = json.Unmarshal(*searchHit.Source, &tmp)
			if err != nil {
				return nil, err
			}
			tmp.Body = template.HTMLEscapeString(tmp.Body)
			tmp.Banner = template.HTMLEscapeString(tmp.Banner)
			tmp.Title = template.HTMLEscapeString(tmp.Title)
			tmp.Cert = template.HTMLEscapeString(tmp.Cert)
			data = append(data, tmp)
		}
	}
	return data, nil
}

// GetAssetFeature 获取资产特征
func (e *Store) GetAssetFeature() ([]m_product_inspection.Service, error) {
	data := make([]m_product_inspection.Service, 0)
	protocols := []interface{}{"snmp", "printer-job-language", "telnet", "rtsp", "pptp", "smtp", "pop3", "imap", "ssh", "ftp"}
	sourceFetch := elastic.NewFetchSourceContext(true).Include("protocol", "banner")
	sr := e.Instance.Client.
		Scroll(e.IndexName).
		Scroll("5m").
		Query(elastic.NewBoolQuery().Must(elastic.NewTermsQuery("protocol", protocols...))).
		Pretty(true).
		Sort("lastupdatetime", false).
		FetchSourceContext(sourceFetch)
	for {
		res, err := sr.Do(context.Background())
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}
		for _, searchHit := range res.Hits.Hits {
			tmp := m_product_inspection.Service{}
			err = json.Unmarshal(*searchHit.Source, &tmp)
			if err != nil {
				return nil, err
			}
			data = append(data, tmp)
		}
	}
	return data, nil
}

// CreateBulkAssetFeature 批量插入资产特征
func (e *Store) CreateBulkAssetFeature(data []m_product_inspection.Service) error {
	bulk := e.Instance.Client.Bulk()
	count := 0
	for _, val := range data {
		count++
		tmp := m_product_inspection.CreateService{
			BanLen:  len(val.Banner),
			Ip:      ipx.GenIpAddr(),
			Port:    ipx.GenPort(),
			Service: val,
		}
		req := elastic.NewBulkIndexRequest().Index(e.IndexName).Type(e.TypeName).Doc(tmp)
		bulk.Add(req)
		if count >= 1000 {
			_, err := bulk.Do(context.Background())
			if err != nil {
				return err
			}
			bulk.Reset()
			count = 0
		}
	}
	if bulk.NumberOfActions() == 0 {
		return nil
	}
	_, err := bulk.Do(context.Background())
	return err
}
