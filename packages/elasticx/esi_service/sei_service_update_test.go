package esi_service

import (
	"encoding/json"

	"git.gobies.org/foeye/foeye3/model/m_user_rules"

	"github.com/stretchr/testify/assert"
)

func (suite *ElasticIndexAssetsSuite) Test_UpdateUserRule() {
	data := make(map[string]*m_user_rules.ServiceSubdomain)
	res := `{
  "************:22": {
    "ip": "************",
    "port": 22,
    "product": [
      "OpenSSH",
      "test_rule",
      "中文_2",
      "a2"
    ],
    "rule_tags": [
      {
        "category": "Other Support System",
        "cn_category": "其他支撑系统",
        "cn_company": "其他",
        "cn_parent_category": "支撑系统",
        "cn_product": "OpenSSH",
        "company": "other",
        "level": "3",
        "parent_category": "Support System",
        "product": "OpenSSH",
        "rule_id": "7512",
        "softhard": "2"
      },
      {
        "category": "Other Office Equipment",
        "cn_category": "其他办公外设",
        "cn_company": "test_company",
        "cn_parent_category": "办公外设",
        "cn_product": "test_rule",
        "company": "test_company",
        "level": "2",
        "parent_category": "Office Equipment",
        "product": "test_rule",
        "rule_id": "10000003",
        "softhard": "2"
      },
      {
        "category": "Electronic Mail System",
        "cn_category": "电子邮件系统",
        "cn_company": "中文_1",
        "cn_parent_category": "企业应用",
        "cn_product": "中文_2",
        "company": "中文_1",
        "level": "3",
        "parent_category": "Enterprise Application",
        "product": "中文_2",
        "rule_id": "10000004",
        "softhard": "3"
      },
      {
        "category": "Cloud Computing",
        "cn_category": "云计算",
        "cn_company": "a1",
        "cn_parent_category": "支撑系统",
        "cn_product": "a2",
        "company": "a1",
        "level": "1",
        "parent_category": "Support System",
        "product": "a2",
        "rule_id": "22",
        "softhard": "1"
      }
    ]
  }
}`

	err := json.Unmarshal([]byte(res), &data)
	assert.NoError(suite.T(), err)

	err = suite.ins.UpdateUserRule(data)
	assert.NoError(suite.T(), err)
}
