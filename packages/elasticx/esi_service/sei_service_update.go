package esi_service

import (
	"context"

	"git.gobies.org/foeye/foeye3/model/m_user_rules"

	"github.com/olivere/elastic"
)

// UpdateUserRule 更新service索引的规则信息，id和数据的映射
func (e *Store) UpdateUserRule(data map[string]*m_user_rules.ServiceSubdomain) error {
	if len(data) == 0 {
		return nil
	}
	bulk := e.Instance.Client.Bulk().Refresh("true")
	for k, v := range data {
		req := elastic.NewBulkUpdateRequest().Index(e.IndexName).Type(e.TypeName).Id(k).Doc(v)
		bulk.Add(req)
	}
	_, err := bulk.Do(context.Background())
	return err
}
