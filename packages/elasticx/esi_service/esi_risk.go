package esi_service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/api/risk/asset/exclusive"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
)

func (e *Store) GetServiceSubdomainIp(serviceSubdomainSearchSource *elastic.SearchSource) ([]string, error) {
	data := make([]string, 0)
	ss := e.Instance.Client.
		Scroll(e.IndexName, elasticx.IndexNameOfSubDomain()).
		SearchSource(serviceSubdomainSearchSource).
		Size(5000)
	for {
		sr, err := ss.Do(context.Background())
		if err == io.EOF {
			break
		}
		if err != nil {
			return data, err
		}

		for _, v := range sr.Hits.Hits {
			tmp := new(exclusive.AssetIp)
			err := json.Unmarshal(*v.Source, &tmp)
			if err != nil {
				return data, err
			}
			data = append(data, tmp.Ip)
		}
	}
	return data, nil
}

func (e *Store) GetServiceSubdomainInfo(ipPortVulFile map[string]map[string][]map[int]string) (map[string][]exclusive.RiskAServiceSubdomainInfo, error) {
	data := make(map[string][]exclusive.RiskAServiceSubdomainInfo, 0)
	fetchSourceContext := elastic.NewFetchSourceContext(true).Include("ip", "port", "protocol", "product", "lastupdatetime")
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	selectQuery := make([]elastic.Query, 0)
	if len(ipPortVulFile) > 0 {
		for _, ipPorts := range ipPortVulFile {
			for ip, ports := range ipPorts {
				for _, port := range ports {
					for portT, _ := range port {
						selectQuery = append(selectQuery,
							elastic.NewBoolQuery().Must(
								elastic.NewTermQuery("ip", ip),
								elastic.NewTermQuery("port", portT),
							),
						)
					}
				}
			}
		}
	}
	if len(selectQuery) > 0 {
		query.Should(selectQuery...)
	}
	ss := e.Instance.Client.
		Scroll(e.IndexName, elasticx.IndexNameOfSubDomain()).
		FetchSourceContext(fetchSourceContext).
		Size(5000)
	for {
		sr, err := ss.Do(context.Background())
		if err == io.EOF {
			break
		}
		if err != nil {
			return data, err
		}

		for _, v := range sr.Hits.Hits {
			tmp := new(exclusive.RiskAServiceSubdomainInfo)
			err := json.Unmarshal(*v.Source, &tmp)
			if err != nil {
				panic(err)
				return data, err
			}
			if _, ok := data[fmt.Sprintf("%s~%d", tmp.Ip, tmp.Port)]; ok {
				data[fmt.Sprintf("%s~%d", tmp.Ip, tmp.Port)] = append(
					data[fmt.Sprintf("%s~%d", tmp.Ip, tmp.Port)],
					*tmp,
				)
			} else {
				data[fmt.Sprintf("%s~%d", tmp.Ip, tmp.Port)] = []exclusive.RiskAServiceSubdomainInfo{*tmp}
			}
		}
	}
	return data, nil
}
