package elasticx

import (
	"context"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/internal/mux/model/m_assets"

	"git.gobies.org/foeye/foeye3/internal/mux/packages/exchange/e_task"
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/model/m_poc"
	"git.gobies.org/foeye/foeye3/model/m_report"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/responses/r_threat"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"
)

// ElasticSearch index related constant definition for {subdomain}.
const (
	IndexNameOfThreats = "fofaee_threats"
	TypeNameOfThreats  = "threats"
)

type IndexThreatsStore interface {
	GetListAllWriteToFile(ctx context.Context, filename string, ops ...Option) (string, error)
	GetThreatByDefault(ctx context.Context, QL *r_threat.QueryListAndKeyword) ([]*m_threat.ResultVulnerabilityInfo, interface{}, int64, error)
	GetThreatByIp(ctx context.Context, QL *r_threat.QueryListAndKeyword) ([]*m_threat.IpParentData, interface{}, int64, error)
	GetThreatByThreat(ctx context.Context, QL *r_threat.QueryListAndKeyword) ([]*m_threat.PocParentData, interface{}, int64, error)
	GetThreatAdvancedScreen(ctx context.Context, tags []*m_tag.Tag, QL *r_threat.QueryListAndKeyword) (interface{}, error)
	GetThreatByExport(ctx context.Context, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool, mysqlStore database.Factory, ) (interface{}, error)
	DeleteThreatByIds(ctx context.Context, QL *r_threat.QueryListAndKeyword) error
	GetThreatByManagerEmailNew(ctx context.Context, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool) (interface{}, []string, error)
	GetThreatByManagerEmailCount(ctx context.Context, QL *r_threat.QueryListAndKeyword) (int, error)
	CountThreatNumber(ctx context.Context, instance []*m_report.ReportTypeIpRanges) (int64, error)
	CountThreatTotal(repaired bool, ipRangeManager string, xcType string) int
	UpdateThreatBatch(ctx context.Context, ids []string) error
	GetThreatPocListByIp(ctx context.Context, taskId int, ip string) ([]*m_asset.NoStdPorts, error)
	GetAllThreatAssetList(ctx context.Context) (map[string]int, error)
	PutAssetLevelMapping() error
	GetThreatByIds(ctx context.Context, QL *r_threat.QueryListAndKeyword) (map[string]interface{}, error)
	UpdateThreatById(data map[string]interface{}, id string) error
	GetThreatById(id string) (map[string]interface{}, error)
	GetThreatCount(ctx *gin.Context, repaired bool, ipRangeManager string, xcType string) int64
	GetPortCount(ctx *gin.Context, repaired bool, ipRangeManager string, xcType string) (int, error)
	GetViolations(ctx *gin.Context, repaired bool, ipRangeManager string, xcType string) int64
	GetThreatLevelCount(ipRangeManager string) ([]m_overviews.Vulnerability, error)
	GetThreatResponsibleCount(ipRangeManager string) (*m_overviews.ResponsibleRankList, error)
	GetThreatNumberCount(ipRangeManager string) ([]m_overviews.NumberRank, error)
	GetWeakPassword(ipRangeManager string) ([]m_overviews.WeakPassword, error)
	GetThreatRanking(ipRangeManager string) ([]m_overviews.RankingList, error)
	GetThreat(ipRangeManager string, isWeakPassword bool) ([]m_overviews.Index, error)
	GetThreatXc(ipRangeManager string) ([]m_overviews.Index, error)
	GetThreatExpXc(ipRangeManager string) ([]m_overviews.Index, error)
	GetThreatList(ctx context.Context, ipRangeManager, business string, size int) (map[string]string, error)
	GetThreatIsExist(ctx context.Context, ipRangeManager, ip, port string) (bool, error)
	GetThreatCountDataByTask(ctx context.Context, LatestTaskId int, LastTaskId int) (*m_threat.TaskThreatCountData, error)
	GetThreatsOverviewCount(ctx context.Context, ipRangeManager string, boolQuery *elastic.BoolQuery) (int64, error)
	GetThreatsOverviewVulTypeCount(ctx context.Context, ipRangeManager string, pocMap map[string]*m_poc.PocPlus, boolQuery *elastic.BoolQuery) ([]m_threat.Exp, error)
	GetThreatsOverviewByPoc(ctx context.Context, ipRangeManager string, ascending bool, boolQuery *elastic.BoolQuery) ([]m_threat.ThreatNew, error)
	GetThreatsOverviewByTag(ctx context.Context, ipRangeManager string, tags []*m_tag.Tag) (*m_threat.ResultThreatOverview, error)
	GetThreatVulFileByIps(ips []string, taskId int) (map[string][]string, error)
	GetDashboardCategory(ctx context.Context, ipRangeManager, category string, pocMpa map[string]string) ([]*m_threat.SystemBucket, int64, error)
	GetThreatListMenu(ctx context.Context, ipRangeManager string) ([]m_overviews.MenuIndex, error)
	GetThreatNumberList(ipRangeManager string, QL *r_threat.QueryListForXcThreat) ([]m_overviews.NumberRank, int64, error)
	// API 2.0
	GetThreatServerByDefault(ctx context.Context, QL *r_threat.QueryListAndKeyword) (interface{}, int64, error)
	GetThreatServerByTaskFinished(ctx context.Context, QL *e_task.QueryTaskFinishedListContainExtra) (interface{}, int64, error)
	GetOriginalThreat(ctx context.Context, QL *r_threat.QueryListAndKeyword) (*elastic.SearchResult, int64, error)
	GetOriginalThreatForDCC(ctx context.Context, foeyeTaskID int) ([]*elastic.SearchHit, error)
	GetThreatByIps(ips []interface{}) (map[string][]*m_assets.AssetVulnerability, error)
	BullUpdateStateByIP(ip string, state int) error
	GetListAll(ipRangeManager string) (interface{}, error)

	UpdateThreatStatus(ctx context.Context, QL *m_threat.RequestUpdateThreatState) error
}

func IndexNameOfThreat() string {
	return IndexNameOfThreats
}
