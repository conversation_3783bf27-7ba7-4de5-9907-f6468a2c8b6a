package esi_threats

import (
	"context"
	"errors"
	"sort"

	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"

	"github.com/olivere/elastic"
)

// GetDashboardCategory get m_dashboard category.
func (store *Store) GetDashboardCategory(ctx context.Context, ipRangeManager, category string, pocMap map[string]string) ([]*m_threat.SystemBucket, int64, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("task_ids", 0)).
		Must(elastic.NewTermsQuery("state", 1, 2))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(boolQuery).
		Size(0).
		Pretty(true)

	switch category {
	case "level":
		serv.Aggregation("common", elastic.NewTermsAggregation().Field("level").Size(5))

	case "exp":
		boolQuery.Must(elastic.NewTermQuery("has_exp", 1))
		serv.Aggregation("common", elastic.NewTermsAggregation().Field("vulfile").Size(10))

	case "weak":
		boolQuery.Must(elastic.NewTermQuery("vulType", "弱口令"))
		serv.Aggregation("common", elastic.NewTermsAggregation().Field("vulfile").Size(5))

	default:
		return nil, 0, errors.New("this category does not exist")
	}

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, 0, err
	}

	if result.Hits.TotalHits < 1 {
		return nil, 0, nil
	}

	data := make([]*m_threat.SystemBucket, 0)
	terms, find := result.Aggregations.Terms("common")
	if find {
		if category == "level" {
			for _, bucket := range terms.Aggregations {
				agg, err := convertAggregationBucketToBucket(bucket)
				if err != nil {
					continue
				}

				if len(agg) < 4 {
					tempMap := map[int]struct{}{
						0: {},
						1: {},
						2: {},
						3: {},
					}

					for _, v := range agg {
						if _, ok := tempMap[v.Key]; !ok {
							agg = append(agg, &m_threat.Bucket{
								Key:      v.Key,
								DocCount: 0,
							})
						}
					}
				}

				sort.SliceStable(agg, func(i, j int) bool {
					return agg[i].Key > agg[j].Key
				})

				for _, v := range agg {
					switch v.Key {
					case 0:
						data = append(data, &m_threat.SystemBucket{
							Key:      "低危",
							DocCount: v.DocCount,
						})

					case 1:
						data = append(data, &m_threat.SystemBucket{
							Key:      "中危",
							DocCount: v.DocCount,
						})

					case 2:
						data = append(data, &m_threat.SystemBucket{
							Key:      "高危",
							DocCount: v.DocCount,
						})

					case 3:
						data = append(data, &m_threat.SystemBucket{
							Key:      "严重",
							DocCount: v.DocCount,
						})
					}
				}
			}
		} else if category == "exp" || category == "weak" {
			for _, bucket := range terms.Aggregations {
				agg, err := ConvertAggregationBucketToSystemBucket(bucket)
				if err != nil {
					continue
				}

				for _, value := range agg {
					if _, ok := pocMap[value.Key]; ok {
						data = append(data, &m_threat.SystemBucket{
							Key:      pocMap[value.Key],
							DocCount: value.DocCount,
						})
					}
				}
			}
		}
	}

	return data, result.Hits.TotalHits, nil
}
