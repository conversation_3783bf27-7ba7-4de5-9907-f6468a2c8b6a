package esi_threats

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/util/timer"
	"git.gobies.org/foeye/foeye3/responses/r_asset_history"

	"github.com/olivere/elastic"
)

// GetThreat 计算漏洞数量
func (store *Store) GetThreat(ipRangeManager string, isWeakPassword bool) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)
	tmpData["add"] = 0
	tmpData["total"] = 0
	tmpData["high"] = 0
	tmpData["medium"] = 0
	tmpData["low"] = 0
	tmpData["serious"] = 0
	data := make([]m_overviews.Index, 0)
	levelTitle := []string{"低危", "中危", "高危", "严重"}
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	if isWeakPassword {
		query.Must(elastic.NewTermQuery("vulType", "弱口令"))
	} else {
		query.MustNot(elastic.NewTermQuery("vulType", "弱口令"))
	}
	query.Must(elastic.NewTermQuery("task_ids", 0))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	levelsAggregation := elastic.NewTermsAggregation().Field("level").Size(5)
	newThreat := elastic.NewFilterAggregation().Filter(elastic.NewRangeQuery("createtime").
		Gte(timer.CurrentDateStr() + " 00:00:00").
		Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))
	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Size(0).
		Query(query).
		Aggregation("levels", levelsAggregation).
		Aggregation("new", newThreat).
		Pretty(true).
		Do(context.Background())

	if err != nil {
		return data, err
	}
	total := searchResult.TotalHits()
	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "levels":
			var levels *r_asset_history.Levels
			err = json.Unmarshal(*rawMessage, &levels)
			if err != nil {
				return data, err
			}
			for _, level := range levels.Keys {
				switch levelTitle[level.Key] {
				case "低危":
					tmpData["low"] = level.Count
				case "中危":
					tmpData["medium"] = level.Count
				case "高危":
					tmpData["high"] = level.Count
				case "严重":
					tmpData["serious"] = level.Count
				}
			}
		case "new":
			tmp := new(m_overviews.Doc)
			err = json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.DocCount
		}
	}
	tmpData["total"] = int(total)
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// GetThreatXc 计算信创首页漏洞数量
func (store *Store) GetThreatXc(ipRangeManager string) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)
	addTmpData := make(map[string]int)
	tmpData["total"] = 0
	tmpData["fixed"] = 0
	tmpData["unfixed"] = 0
	addTmpData["fixed_add"] = 0
	addTmpData["unfixed_add"] = 0
	tmpData["add"] = 0
	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("task_ids", 0))
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2, 4}...))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	newThreat := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr() + " 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))

	fixedQuery := query.Must(elastic.NewTermQuery("state", 4))
	fixedSearchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Size(0).
		Query(fixedQuery).
		Pretty(true).
		Aggregation("new_fixed", newThreat).
		Do(context.Background())
	if err != nil {
		return data, err
	}

	for key, rawMessage := range fixedSearchResult.Aggregations {
		switch key {
		case "new_fixed":
			// mosso.DebugShowContentWithJSON(*rawMessage)
			tmp := new(m_overviews.Doc)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			addTmpData["fixed_add"] = tmp.DocCount
		}
	}
	unfixedQuery := elastic.NewBoolQuery()
	unfixedQuery.Must(elastic.NewTermQuery("task_ids", 0))
	unfixedQuery.Must(elastic.NewTermsQuery("state", []interface{}{1, 2, 4}...))

	/*---------------------------分域---------------------------------*/
	ipQueryList = es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		unfixedQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	unfixedQuery.Must(elastic.NewTermsQuery("state", 1, 2))
	unfixedSearchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Size(0).
		Query(unfixedQuery).
		Pretty(true).
		Aggregation("new_unfixed", newThreat).
		Do(context.Background())

	if err != nil {
		return data, err
	}

	for key, rawMessage := range unfixedSearchResult.Aggregations {
		switch key {
		case "new_unfixed":
			// mosso.DebugShowContentWithJSON(*rawMessage)
			tmp := new(m_overviews.Doc)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			addTmpData["unfixed_add"] = tmp.DocCount
		}
	}

	tmpData["fixed"] = int(fixedSearchResult.TotalHits())
	tmpData["unfixed"] = int(unfixedSearchResult.TotalHits())
	tmpData["total"] = int(fixedSearchResult.TotalHits()) + int(unfixedSearchResult.TotalHits())
	tmpData["add"] = addTmpData["unfixed_add"] + addTmpData["fixed_add"]

	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// GetThreatExpXc  计算信创首页EXP漏洞数量
func (store *Store) GetThreatExpXc(ipRangeManager string) ([]m_overviews.Index, error) {
	data := make([]m_overviews.Index, 0)

	tmpData := make(map[string]int)
	tmpData["total"] = 0
	tmpData["high"] = 0
	tmpData["medium"] = 0
	tmpData["low"] = 0
	tmpData["serious"] = 0
	tmpData["add"] = 0
	levelTitle := []string{"低危", "中危", "高危", "严重"}

	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("task_ids", 0))
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	query.Must(elastic.NewTermQuery("has_exp", 1))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	newThreat := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr() + " 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))
	levelsAggregation := elastic.NewTermsAggregation().Field("level").Size(5)

	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Size(0).
		Query(query).
		Aggregation("levels", levelsAggregation).
		Aggregation("new", newThreat).
		Pretty(true).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	tmpData["total"] = int(searchResult.TotalHits())

	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "levels":
			var levels *r_asset_history.Levels
			err = json.Unmarshal(*rawMessage, &levels)
			if err != nil {
				return data, err
			}
			for _, level := range levels.Keys {
				switch levelTitle[level.Key] {
				case "低危":
					tmpData["low"] = level.Count
				case "中危":
					tmpData["medium"] = level.Count
				case "高危":
					tmpData["high"] = level.Count
				case "严重":
					tmpData["serious"] = level.Count
				}
			}
		case "new":
			// mosso.DebugShowContentWithJSON(*rawMessage)
			tmp := new(m_overviews.Doc)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.DocCount
		}
	}
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// GetThreatList get threat list.
func (store *Store) GetThreatList(ctx context.Context, ipRangeManager, business string, size int) (map[string]string, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	boolQuery.Must(elastic.NewTermQuery("task_ids", 0))
	boolQuery.Must(elastic.NewTermQuery("business_app", business))
	boolQuery.MustNot(elastic.NewTermQuery("port", 0))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Size(size).
		SearchSource(elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"ip", "port"}, nil)).
		Query(boolQuery).
		Sort("level", true).
		Pretty(true)

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, err
	}

	if result.Hits.TotalHits < 1 {
		return nil, err
	}

	ipMap := make(map[string]string, 0)
	for _, hits := range result.Hits.Hits {
		if len(ipMap) >= size {
			break
		}

		var tmp map[string]interface{}
		if err = json.Unmarshal(*hits.Source, &tmp); err != nil {
			continue
		}

		if _, ok := tmp["ip"].(string); ok {
			if _, ok = tmp["port"].(float64); ok {
				ipMap[tmp["ip"].(string)] = strconv.FormatFloat(tmp["port"].(float64), 'f', -1, 64)
			}
		}
	}

	return ipMap, nil
}

// GetThreatIsExist get threat is exist.
func (store *Store) GetThreatIsExist(ctx context.Context, ipRangeManager, ip, port string) (bool, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermsQuery("ip", ip))
	if port != "" {
		boolQuery.Must(elastic.NewTermsQuery("port", port))
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Size(1).
		Query(boolQuery).
		SearchSource(elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"ip", "port"}, nil)).
		Pretty(true)

	result, err := serv.Do(ctx)
	if err != nil {
		return false, err
	}

	if result.Hits.TotalHits < 1 {
		return false, nil
	}

	return true, nil
}
