package esi_threats

import (
	"context"

	"github.com/stretchr/testify/assert"
)

func (suite *ElasticIndexThreatSuite) Test_PutAssetLevelMapping() {
	err := suite.ins.PutAssetLevelMapping()
	assert.NoError(suite.T(), err)
}

func (suite *ElasticIndexThreatSuite) Test_UpdateThreatBatch() {
	ids := []string{"048973453beb66eecd5b0027117c399b", "7b4380f03252e55d6f736eabfd13873d", "ff15cf716e284a65de1a6a191cda438d"}
	err := suite.ins.UpdateThreatBatch(context.Background(), ids)
	assert.NoError(suite.T(), err)
}
