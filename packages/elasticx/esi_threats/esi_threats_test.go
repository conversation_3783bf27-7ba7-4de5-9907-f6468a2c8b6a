package esi_threats

import (
	"io/ioutil"
	"net/http/httptest"
	"testing"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"
)

func TestElasticIndexThreatSuite(t *testing.T) {
	suite.Run(t, new(ElasticIndexThreatSuite))
}

type ElasticIndexThreatSuite struct {
	suite.Suite
	ins             *Store
	configure       *config.Configure
	rec             *httptest.ResponseRecorder
	ctx             *gin.Context
	elasticdatabase *elasticx.ElasticDatabase
	afterDropTables bool
}

var (
	indexes = map[string]string{
		elasticx.IndexNameOfThreat(): readDataFromJsonFile("threats_settings.json"),
	}
)

func (suite *ElasticIndexThreatSuite) BeforeSuite() {
	gomonkey.ApplyFunc(elasticx.IndexNameOfThreat, func() string {
		return "fofaee_threats_esi_threat_test"
	})
	assert.Equal(suite.T(), "fofaee_threats_esi_threat_test", elasticx.IndexNameOfThreat())

	gomonkey.ApplyGlobalVar(&indexes, map[string]string{
		elasticx.IndexNameOfThreat(): readDataFromJsonFile("threats_settings.json"),
	})
}

func (suite *ElasticIndexThreatSuite) SetupSuite() {
	suite.BeforeSuite()
	var err error
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../../../"),
	)
	assert.NotNil(suite.T(), suite.configure)

	suite.rec = httptest.NewRecorder()
	suite.ctx, _ = gin.CreateTestContext(suite.rec)

	suite.elasticdatabase, err = elasticx.NewElasticDatabase(suite.configure)
	assert.NoError(suite.T(), err)

	suite.ins = NewStore(suite.elasticdatabase)

	suite.afterDropTables = true
	suite.autoMigrateSystemPresetData()
}

func (suite *ElasticIndexThreatSuite) TearDownSuite() {
	if suite.afterDropTables {
		// 删除es数据
		deleteIndexes := make([]string, 0, len(indexes))
		for index := range indexes {
			deleteIndexes = append(deleteIndexes, index)
		}
		_, err := elastic.NewIndicesDeleteService(suite.elasticdatabase.Client).Index(deleteIndexes).Do(suite.ctx)
		assert.NoError(suite.T(), err)
	}
}

func (suite *ElasticIndexThreatSuite) autoMigrateSystemPresetData() {
	// create index
	for index, settingsAndMappings := range indexes {
		_, err := suite.elasticdatabase.Client.CreateIndex(index).Body(settingsAndMappings).Do(suite.ctx)
		assert.NoError(suite.T(), err)
	}

	// insert data to threats
	_, err := suite.elasticdatabase.Client.Index().Index(elasticx.IndexNameOfThreat()).Type("threats").BodyString(readDataFromJsonFile("threats_doc.json")).Refresh("true").Do(suite.ctx)
	assert.NoError(suite.T(), err)
}

func readDataFromJsonFile(filename string) string {
	bytes, err := ioutil.ReadFile("./../../../static/assets/asset/" + filename)
	if err != nil {
		panic(err)
	}
	return string(bytes)
}
