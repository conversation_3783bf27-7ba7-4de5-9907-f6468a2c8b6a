package esi_threats

import (
	"context"
	"math"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/model/m_report"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_report"

	"github.com/olivere/elastic"
)

// CountThreatNumber count threat number.
func (store *Store) CountThreatNumber(ctx context.Context, instance []*m_report.ReportTypeIpRanges) (int64, error) {
	var totalHits int64
	if len(instance) < 1 {
		serv := store.Instance.Client.Search().
			Index(store.IndexName).
			Type(store.TypeName).
			Pretty(true).
			Query(elastic.NewMatchAllQuery())

		result, err := serv.Do(ctx)
		if err != nil {
			log.Println(err)
			return 0, err
		}
		totalHits += result.Hits.TotalHits
		return totalHits, nil
	}

	var pageSize = 500
	pageCount := int(math.Ceil(float64(len(instance)) / float64(pageSize)))

	for i := 1; i <= pageCount; i++ {
		t := i * pageSize
		if t > len(instance) {
			t = len(instance)
		}

		boolQuery := esi_report.NewReportBoolQuery(instance[(i-1)*pageSize : t])
		boolQuery.Must(elastic.NewTermQuery("task_ids", 0))

		serv := store.Instance.Client.Search().
			Index(store.IndexName).
			Type(store.TypeName).
			Pretty(true).
			Query(boolQuery)

		result, err := serv.Do(ctx)
		if err != nil {
			log.Println(err)
			return 0, err
		}
		totalHits += result.Hits.TotalHits
	}
	return totalHits, nil
}
