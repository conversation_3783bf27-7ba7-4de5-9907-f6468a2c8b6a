package esi_threats

import (
	"context"
	"io"
	"testing"

	"git.gobies.org/foeye-dependencies/configure"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/factory"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/responses/r_threat"
	elastic2 "git.gobies.org/foeye/foeye3/store/elastic"
	"git.gobies.org/foeye/foeye3/store/storage"
)

func TestStore_GetThreatByExport(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	ctx := context.Background()
	QL := &r_threat.QueryListAndKeyword{}
	isLicenseMosaic := false

	defer gomonkey.ApplyMethodReturn(store.Instance.Client.Scroll(), "Do", nil, io.EOF).Reset()

	configure := config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../../../"),
	)

	gormDatabase, err := database.NewGormDatabase(configure)
	assert.NoError(t, err)

	mysqlStore := factory.NewMysqlDatabaseFactoryOr(gormDatabase)
	export, err := store.GetThreatByExport(ctx, QL, isLicenseMosaic, mysqlStore)
	assert.Equal(t, any(nil), export)
	assert.Equal(t, nil, err)

}
