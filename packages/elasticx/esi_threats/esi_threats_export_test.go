package esi_threats

import (
	"context"
	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/responses/r_threat"
	elastic2 "git.gobies.org/foeye/foeye3/store/elastic"
	"git.gobies.org/foeye/foeye3/store/storage"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"io"
	"testing"
)

func TestStore_GetThreatByExport(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	ctx := context.Background()
	QL := &r_threat.QueryListAndKeyword{}
	isLicenseMosaic := false

	defer gomonkey.ApplyMethodReturn(store.Instance.Client.Scroll(), "Do", nil, io.EOF).Reset()

	export, err := store.GetThreatByExport(ctx, QL, isLicenseMosaic)
	assert.Equal(t, any(nil), export)
	assert.Equal(t, nil, err)

}
