package esi_threats

import (
	"context"
	"fmt"
	"io"
	"os"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/olivere/elastic"
)

func (store *Store) GetListAllWriteToFile(ctx context.Context, filename string, ops ...elasticx.Option) (string, error) {
	var err error

	defer func() {
		if err != nil && err != io.EOF {
			os.Remove(filename)
		}
	}()

	file, err := elasticx.MustOpenFile(filename)
	if err != nil {
		return "", err
	}
	defer file.Close()

	search := store.Instance.Client.
		Scroll().
		Scroll(elasticx.DefaultScrollKeepAlive).
		Query(elastic.NewBoolQuery().Must(elastic.NewTermsQuery("task_ids", 0))).
		Index(store.IndexName).
		Type(store.TypeName).
		Size(elasticx.DefaultScrollQueryBulkSize)

	result, err := search.Do(ctx)

	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
			for _, hit := range result.Hits.Hits {
				if hit.Source != nil {
					cur, err := hit.Source.MarshalJSON()
					if err != nil {
						continue
					}

					cur = append(cur[0:len(cur)-1], []byte(fmt.Sprintf(`,"id":"%s"}`, hit.Id))...)
					cur = append(cur, '\n')
					if _, err := file.Write(cur); err != nil {
						log.Printf("failed write context to file: %+v\n", err)
					}
				}
			}

			// Proceed to the next read.
			result, err = search.ScrollId(result.ScrollId).Do(ctx)
			if err != nil {
				break
			}
		} else {
			break
		}
	}

	if err != nil && err != io.EOF {
		return "", err
	}

	return filename, nil
}
