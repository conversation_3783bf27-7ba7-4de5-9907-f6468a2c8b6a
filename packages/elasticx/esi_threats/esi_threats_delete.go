package esi_threats

import (
	"context"
	"encoding/json"

	"git.gobies.org/foeye/foeye3/internal/mux/model/m_threat"
	"git.gobies.org/foeye/foeye3/model/m_netinfo"
	"git.gobies.org/foeye/foeye3/responses/r_threat"

	"github.com/olivere/elastic"
)

// DeleteThreatByIds delete threat by ids.
func (store *Store) DeleteThreatByIds(ctx context.Context, QL *r_threat.QueryListAndKeyword) error {
	boolQuery := newBoolQuery(QL)
	result, err := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"task_ids"}, nil)).
		Query(boolQuery).
		Size(int(m_netinfo.MaxCountLimit * 2)).
		Pretty(true).
		Do(ctx)

	if err != nil {
		return err
	}

	taskIdsInfo := sourceResultDataByTaskIds(result, int(QL.TaskID))
	if len(taskIdsInfo) < 1 {
		deleteService := store.Instance.Client.DeleteByQuery().
			Index(store.IndexName).
			Type(store.TypeName).
			Query(boolQuery).
			Refresh("true")

		if _, err = deleteService.Do(ctx); err != nil {
			return err
		}
	} else {
		batchSize := 1000
		for i := 0; i < len(taskIdsInfo); i += batchSize {
			end := i + batchSize
			if end > len(taskIdsInfo) {
				end = len(taskIdsInfo)
			}
			bulkRequest := store.Instance.Client.Bulk().Refresh("true")
			for _, info := range taskIdsInfo[i:end] {
				doc := map[string]interface{}{"task_ids": info.TaskIds}
				req := elastic.NewBulkUpdateRequest().Index(store.IndexName).Type(store.TypeName).Id(info.Id).Doc(doc)
				bulkRequest.Add(req)
			}
			if _, err = bulkRequest.Do(ctx); err != nil {
				return err
			}
		}
	}
	return nil
}

// sourceResultDataByTaskIds get result task_ids.
func sourceResultDataByTaskIds(sr *elastic.SearchResult, taskId int) []m_threat.TaskIds {
	taskInfo := make([]m_threat.TaskIds, 0)
	for _, source := range sr.Hits.Hits {
		if source != nil {
			marshalJSON, err := source.Source.MarshalJSON()
			if err != nil {
				continue
			}

			info := new(m_threat.TaskIds)
			err = json.Unmarshal(marshalJSON, &info)
			if err != nil {
				continue
			}
			taskIds := make([]int, 0)
			for _, v := range info.TaskIds {
				if v != taskId {
					taskIds = append(taskIds, v)
				}
			}
			taskInfo = append(taskInfo, m_threat.TaskIds{Id: source.Id, TaskIds: taskIds})
		}
	}
	return taskInfo
}
