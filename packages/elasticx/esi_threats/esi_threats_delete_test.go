package esi_threats

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye/foeye3/model/m_netinfo"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/responses/r_threat"
)

func (suite *ElasticIndexThreatSuite) Test_DeleteThreatByIds_Update() {
	QL := &r_threat.QueryListAndKeyword{
		ThreatBatch: m_threat.ThreatBatch{
			Switch: "unfixed",
			Type:   "threat",
			Ids:    []string{},
		},
	}
	boolQuery := newBoolQuery(QL)
	source := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"task_ids"}, nil)
	req := json.RawMessage(`{"task_ids":[0,51,52]}`)
	result := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: 60,
			MaxScore:  nil,
			Hits: []*elastic.SearchHit{
				{
					Id:     "eec9f1e90bb1bab5c2645fb2dc19305b",
					Source: &req,
				},
				{
					Id:     "eec9f1e90bb1bab5c2645fb2dc193052",
					Source: &req,
				},
			},
		},
	}
	defer gomonkey.ApplyMethodReturn(suite.elasticdatabase.Client.Search().Index().SearchSource(source).Query(boolQuery).Size(int(m_netinfo.MaxCountLimit*2)).Pretty(true), "Do", result, nil).Reset()
	defer gomonkey.ApplyMethodReturn(suite.elasticdatabase.Client.Bulk().Refresh("true"), "Do", nil, nil).Reset()

	err := suite.ins.DeleteThreatByIds(context.Background(), QL)
	assert.NoError(suite.T(), err)
}

func (suite *ElasticIndexThreatSuite) Test_DeleteThreatByIds_Delete() {
	QL := &r_threat.QueryListAndKeyword{
		ThreatBatch: m_threat.ThreatBatch{
			Switch: "unfixed",
			Type:   "threat",
			Ids:    []string{},
		},
	}
	boolQuery := newBoolQuery(QL)
	source := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"task_ids"}, nil)
	result := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			TotalHits: 0,
			MaxScore:  nil,
			Hits:      []*elastic.SearchHit{},
		},
	}
	defer gomonkey.ApplyMethodReturn(suite.elasticdatabase.Client.Search().Index().SearchSource(source).Query(boolQuery).Size(int(m_netinfo.MaxCountLimit*2)).Pretty(true), "Do", result, nil).Reset()
	defer gomonkey.ApplyMethodReturn(suite.elasticdatabase.Client.DeleteByQuery().Index().Query(boolQuery).Refresh("true"), "Do", nil, nil).Reset()

	err := suite.ins.DeleteThreatByIds(context.Background(), QL)
	assert.NoError(suite.T(), err)
}

func (suite *ElasticIndexThreatSuite) Test_SourceResultDataByTaskIds() {
	t := suite.T()

	rawdata := `{
		"_index": "fofaee_violation",
		"_type": "violation",
		"_id": "violation1",
		"_source": {
			"task_ids": [0,1,2]
		}
	}`

	var source map[string]interface{}
	err := json.Unmarshal([]byte(rawdata), &source)
	if err != nil {
		panic(err)
	}

	sourceRawMessage, err := json.Marshal(source["_source"])
	if err != nil {
		panic(err)
	}

	rawMessage := json.RawMessage(sourceRawMessage)

	// 模拟返回的数据结构
	getResult := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			Hits: []*elastic.SearchHit{
				{
					Source: &rawMessage,
				},
			},
		},
	}

	t.Run("taskId is 0", func(t *testing.T) {

		result := sourceResultDataByTaskIds(getResult, 0)
		assert.Equal(t, []int{1, 2}, result[0].TaskIds)
	})
	t.Run("taskId is 1", func(t *testing.T) {

		result := sourceResultDataByTaskIds(getResult, 1)
		assert.Equal(t, []int{0, 2}, result[0].TaskIds)
	})
}
