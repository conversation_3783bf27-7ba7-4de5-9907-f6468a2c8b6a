package esi_threats

import (
	"context"
	"encoding/json"
	"io"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/stringsx"
	"git.gobies.org/foeye/foeye3/packages/util/inactivated"
	"git.gobies.org/foeye/foeye3/responses/r_threat"

	"github.com/microcosm-cc/bluemonday"
	"github.com/olivere/elastic"
)

// GetThreatByExport export threat.
func (store *Store) GetThreatByExport(ctx context.Context, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool, mysqlStore database.Factory) (interface{}, error) {
	boolQuery := newBoolQuery(QL)

	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range defaultFields {
		fsc.Include(v)
	}
	store.fileLock.Lock()
	defer store.fileLock.Unlock()

	service := store.Instance.Client.Scroll().
		Index(store.IndexName).
		Type(store.TypeName).
		FetchSourceContext(fsc).
		Pretty(true).
		Query(boolQuery).
		Size(1000).
		Sort("lastupdatetime", false)

	result, err := service.Do(ctx)
	if err != nil {
		if err == io.EOF {
			return nil, nil
		}
		return nil, err
	}

	r := new(elastic.SearchResult)

	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil && len(result.Hits.Hits) > 0 {
			for _, item := range result.Hits.Hits {
				if r.Hits == nil {
					r.Hits = &elastic.SearchHits{Hits: make([]*elastic.SearchHit, 0)}
					r.Hits.Hits = append(r.Hits.Hits, item)
				} else {
					r.Hits.Hits = append(r.Hits.Hits, item)
				}
			}

			r.Hits.TotalHits += result.Hits.TotalHits
			result, err = store.Instance.Client.Scroll().
				Scroll(elasticx.DefaultScrollKeepAlive).
				ScrollId(result.ScrollId).
				Index(store.IndexName).
				Type(store.TypeName).
				FetchSourceContext(fsc).
				Pretty(true).
				Query(boolQuery).
				Sort("lastupdatetime", false).
				Do(ctx)

			if err != nil {
				break
			}
		} else {
			break
		}
	}

	if err != nil && err != io.EOF {
		return nil, err
	}

	if r.Hits == nil {
		return nil, nil
	}

	return sourceExportData(r, QL, isLicenseMosaic, mysqlStore), nil
}

// sourceExportData source export data.
func sourceExportData(sr *elastic.SearchResult, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool, mysqlStore database.Factory) []map[string]interface{} {
	var datum = make([]map[string]interface{}, 0)
	var n int
	p := bluemonday.StrictPolicy()
	for _, source := range sr.Hits.Hits {
		if source != nil {
			marshalJSON, err := source.Source.MarshalJSON()
			if err != nil {
				continue
			}
			temp := map[string]interface{}{}
			err = json.Unmarshal(marshalJSON, &temp)
			if err != nil {
				continue
			}

			stripTags(temp, p)

			temp["keyword_tags"] = ""
			poc, err := mysqlStore.Poc().GetPocByName(temp["common_title"].(string))

			if poc != nil && err == nil {
				extracted, _ := stringsx.ExtractAndRemoveKeywords(poc.Tags, []string{"信创", "热点", "两高一弱"})
				temp["keyword_tags"] = extracted
			}

			if isLicenseMosaic {
				if _, ok := temp["ip"]; ok {
					temp["ip"] = inactivated.InactivatedAssetsAddMosaic(temp["ip"].(string), QL.Number, n)
				}
			}
			n++
			datum = append(datum, temp)
		}
	}
	return datum
}

// stripTags
func stripTags(temp map[string]interface{}, p *bluemonday.Policy) {
	if tt, ok := temp["common_description"]; ok && tt != nil {
		temp["common_description"] = p.Sanitize(tt.(string))
	}

	if tt, ok := temp["common_impact"]; ok && tt != nil {
		temp["common_impact"] = p.Sanitize(tt.(string))
	}

	if tt, ok := temp["recommandation"]; ok && tt != nil {
		temp["recommandation"] = p.Sanitize(tt.(string))
	}
}
