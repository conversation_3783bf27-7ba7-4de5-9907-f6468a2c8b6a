package esi_threats

import (
	"sync"

	"git.gobies.org/foeye/foeye3/packages/elasticx"
)

type Store struct {
	Instance  *elasticx.ElasticDatabase
	IndexName string
	TypeName  string
	fileLock  sync.Mutex
}

func NewStore(elasticDatabase *elasticx.ElasticDatabase) *Store {
	return &Store{
		Instance:  elasticDatabase,
		IndexName: elasticx.IndexNameOfThreat(),
		TypeName:  elasticx.TypeNameOfThreats,
	}
}

var defaultFields = []string{
	"ip",                 // IP地址
	"common_title",       // 漏洞名称
	"cveId",              // CVE编号
	"vulType",            // 漏洞类型
	"level",              // 漏洞等级
	"url",                // 漏洞地址
	"createtime",         // 发现时间
	"lastupdatetime",     // 上次扫描
	"notice_time",        // 通报时间
	"name",               // 备注信息
	"mac",                // MAC地址
	"province",           // 地理位置
	"city",               // 城市
	"manager_mobile",     // 电话
	"manager_email",      // 邮箱
	"task_ids",           // 任务ID
	"username",           // 负责人
	"company",            // 管理单元
	"computer_room",      // 机房信息
	"business_app",       // 业务系统
	"has_exp",            // 漏洞是否可以验证 1可以验证，0不可以验证
	"last_response",      // 漏洞响应结果
	"common_description", // 漏洞描述
	"common_impact",      // 漏洞危害
	"recommandation",     // 解决方案
	"custom_fields",      // 自定义标签
	"state",              // 状态
	"hostinfo",           // 漏洞验证地址
	"asset_level",        // 资产等级
	"is_xc",              // 信创标识（TRUE/FALSE）
	"port",               // 端口
	"online",             // 在线状态
	"status",             // 漏洞状态
	"comment",            // 备注
}

var ipFields = []string{
	"doc_count",          // 漏洞数量
	"name",               // 备注名称
	"province",           // 地理位置
	"city",               // 城市
	"company",            // 管理单元
	"business_app",       // 业务系统
	"mac",                // MAC地址
	"username",           // 负责人
	"manager_mobile",     // 电话
	"manager_email",      // 邮箱
	"common_title",       // 漏洞名称
	"cveId",              // CVE编号
	"level",              // 漏洞等级
	"url",                // 漏洞地址
	"createtime",         // 发现时间
	"notice_time",        // 通报时间
	"common_description", // 漏洞描述
	"common_impact",      // 漏洞危害
	"recommandation",     // 解决方案
	"has_exp",            // 漏洞是否可以验证 1可以验证，0不可以验证
	"state",              // 状态
	"hostinfo",           // 漏洞验证地址
	"asset_level",        // 资产等级
	"is_xc",              // 信创标识（TRUE/FALSE）
	"online",             // 资产在线状态
}

var pocFields = []string{
	"common_title",       // 漏洞名称
	"level",              // 漏洞等级
	"cveId",              // CVE编号
	"ip_count",           // IP数量
	"ip",                 // ip地址
	"url",                // 漏洞地址
	"createtime",         // 发现时间
	"notice_time",        // 通报时间
	"name",               // 备注名称
	"province",           // 地理位置
	"city",               // 城市
	"company",            // 管理单元
	"business_app",       // 业务系统
	"mac",                // MAC地址
	"username",           // 负责人
	"manager_mobile",     // 电话
	"manager_email",      // 邮箱
	"common_description", // 漏洞描述
	"common_impact",      // 漏洞危害
	"recommandation",     // 解决方案
	"has_exp",            // 漏洞是否可以验证 1可以验证，0不可以验证
	"state",              // 状态
	"hostinfo",           // 漏洞验证地址
	"asset_level",        // 资产等级
	"is_xc",              // 信创标识（TRUE/FALSE）
}
