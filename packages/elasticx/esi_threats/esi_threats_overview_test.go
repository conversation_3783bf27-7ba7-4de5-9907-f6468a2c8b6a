package esi_threats

import (
	"context"
	"encoding/json"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye/foeye3/model/m_poc"
	"git.gobies.org/foeye/foeye3/model/m_tag"
)

var (
	Rawdata = `{
		"_index": "fofaee_threats",
		"_type": "threats",
		"_id": "dedbb424a3a08d2029bca43051d77e94",
		"_score": 1,
		"_source": {
			"hosts": null,
			"ip": "***********",
			"port": 443,
			"name": "",
			"common_title": "Swagger api 未授权访问漏洞",
			"mac": "",
			"net_bios": null,
			"add_way": "self_defined",
			"business_app": "宝宝宝宝",
			"asset_level": "",
			"province": "局域网",
			"city": "",
			"company": "",
			"username": "",
			"belong_user_id": 0,
			"manager_mobile": "",
			"manager_email": "",
			"computer_room": "",
			"descriptions": null,
			"country": null,
			"operator": null,
			"operating_company": null,
			"rule_tags": [
				"NGINX",
				"OpenSSH",
				"HARBOR"
			],
			"cat_tags": [
				"服务",
				"其他支撑系统",
				"虚拟化"
			],
			"company_tags": [
				"Nginx",
				"其他"
			],
			"task_ids": [
				0,
				58
			],
			"state": 1,
			"level": 1,
			"hostinfo": "https://***********",
			"vulfile": "CVD-2023-3326.json",
			"url": "https://***********/swagger.json",
			"obj_type": 1,
			"object": "***********",
			"intranet_ip": 1,
			"addition": null,
			"merge_md5": "",
			"scan_engine": 4,
			"port_list": [
				{
					"protocol": "https",
					"port": 443,
					"banner": "HTTP/1.1 200 OK\r\nServer: nginx\r\nDate: Sat, 17 Aug 2024 02:42:00 GMT\r\nContent-Type: text/html\r\nContent-Length: 785\r\nConnection: keep-alive\r\nLast-Modified: Mon, 12 Aug 2024 10:48:47 GMT\r\nETag: \"66b9e88f-311\"\r\nCache-Control: no-store, no-cache, must-revalidate\r\nAccept-Ranges: bytes\r\nStrict-Transport-Security: max-age=31536000; includeSubdomains; preload\r\nX-Frame-Options: DENY\r\nContent-Security-Policy: frame-ancestors 'none'",
					"certs": {
						"cert_date": "2021-03-09 14:00:00",
						"is_expired": false,
						"is_match": false,
						"is_valid": false,
						"iss_sub_eq": true,
						"issuer_cn": "************",
						"issuer_cns": [
							"************"
						],
						"issuer_org": [
							"HSXA"
						],
						"not_after": "2032-12-31 06:34:03",
						"not_before": "2023-01-03 06:34:03",
						"sig_alth": "SHA256-RSA",
						"sn": "12223109813808884632",
						"subject_cn": "************",
						"subject_names": [
							"************"
						],
						"subject_org": [
							"HSXA"
						],
						"v": "v1",
						"valid_type": "SelfSigned"
					}
				},
				{
					"protocol": "ssh",
					"port": 22,
					"banner": "SSH-2.0-OpenSSH_7.4\r\n\nserver_host_key:\nfingerprint:cnCFSV7ETpNnschbEBNGlAw8W3zUl2IRjXJ4sjZtaPs\nfingerprint_sha256:727085495ec44e9367b1c85b101346940c3c5b7cd49762118d7278b2366d68fb",
					"certs": null
				},
				{
					"protocol": "http",
					"port": 80,
					"banner": "HTTP/1.1 308 Permanent Redirect\r\nServer: nginx/1.27.0\r\nDate: Sat, 17 Aug 2024 02:44:10 GMT\r\nContent-Type: text/html\r\nContent-Length: 171\r\nConnection: keep-alive\r\nLocation: https://***********:443/",
					"certs": null
				}
			],
			"rule_infos": [
				{
					"belong_level": 0,
					"rule_id": 209,
					"second_cat_tag": "服务",
					"soft_hard_code": 2,
					"first_cat_tag": "支撑系统",
					"level_code": 3,
					"company": "Nginx",
					"ports": [
						80,
						443
					],
					"title": "NGINX"
				},
				{
					"belong_level": 0,
					"rule_id": 7512,
					"second_cat_tag": "其他支撑系统",
					"soft_hard_code": 2,
					"first_cat_tag": "支撑系统",
					"level_code": 3,
					"company": "其他",
					"ports": [
						22
					],
					"title": "OpenSSH"
				},
				{
					"belong_level": 0,
					"rule_id": 8111,
					"second_cat_tag": "虚拟化",
					"soft_hard_code": 2,
					"first_cat_tag": "支撑系统",
					"level_code": 5,
					"company": "其他",
					"ports": [
						80,
						443
					],
					"title": "HARBOR"
				},
				{
					"belong_level": 0,
					"rule_id": 7556,
					"second_cat_tag": "操作系统",
					"soft_hard_code": 2,
					"first_cat_tag": "系统软件",
					"level_code": 2,
					"company": "其他",
					"ports": [
						80,
						443
					],
					"title": "Linux"
				}
			],
			"common_description": "<p>Swagger 是一个规范和完整的框架，用于生成、描述、调用和可视化 RESTful 风格的 Web 服务。<br></p><p>Swagger 会根据开发人员在代码中的设置来自动生成 API 说明文档，若存在相关的配置缺陷，攻击者可以未授权翻查 Swagger 接口文档，得到系统功能 API 接口的详细参数，再构造参数发包，通过回显获取系统大量的敏感信息。<br></p>",
			"common_impact": "<p>攻击者可以未授权翻查 Swagger 接口文档，得到系统功能 API 接口的详细参数，再构造参数发包，通过回显获取系统大量的敏感信息。<br></p>",
			"recommandation": "<p>1. 屏蔽所有 Swagger 的相关资源。如果使用 SpringBoot 框架,只需在 application.properties 或者 application.yml 配置文件中配置 swagger.production=true。</p><p>2. 排查接口是否存在敏感信息泄露（例如：账号密码、SecretKey、OSS 配置等），若有则进行相应整改。</p>",
			"custom_fields": null,
			"gid": null,
			"uploaded": 0,
			"notice_time": null,
			"cveId": "",
			"vulType": "未授权访问",
			"has_exp": 1,
			"is_ipv6": false,
			"is_xc": 0,
			"last_response": "",
			"has_response": 0,
			"createtime": "2024-08-17 20:13:56",
			"lastchecktime": "2024-08-17 20:13:56",
			"lastupdatetime": "2024-08-17 20:13:56"
		}
	}`
)

func (suite *ElasticIndexThreatSuite) Test_GetThreatsOverviewCount() {
	count, err := suite.ins.GetThreatsOverviewCount(context.Background(), "127.0.0.1", elastic.NewBoolQuery())
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(0), count)
}

func (suite *ElasticIndexThreatSuite) Test_GetThreatsOverviewVulTypeCount() {
	pocs := map[string]*m_poc.PocPlus{
		"Elasticsearch_Unauthorized.json": {
			Poc: m_poc.Poc{
				Name:        "Elasticsearch未授权访问",
				Product:     "xxxx",
				Fofaquery:   "xxx",
				FofaRecords: 0,
				Level:       1,
			},
			GroupName: "",
		},
		"custom_poc_3.go": {
			Poc: m_poc.Poc{
				Name:        "自定义名称",
				Product:     "xxxx",
				Fofaquery:   "xxx",
				FofaRecords: 0,
				Level:       1,
			},
			GroupName: "",
		},
		"redis_unauthorized_access.json": {
			Poc: m_poc.Poc{
				Name:        "Redis 未授权访问漏洞",
				Product:     "xxxx",
				Fofaquery:   "xxx",
				FofaRecords: 0,
				Level:       1,
			},
			GroupName: "",
		},
	}
	jsonStr := `{"buckets": [{"key": "Elasticsearch_Unauthorized.json","doc_count": 39},{"key": "custom_poc_3.go","doc_count": 36},{"key": "redis_unauthorized_access.json","doc_count": 12}]}`
	bytes := []byte(jsonStr)
	msg := json.RawMessage(bytes)
	aggs := elastic.Aggregations{"vulfiles": &msg}

	searchResult := elastic.SearchResult{}
	searchResult.Aggregations = aggs
	defer gomonkey.ApplyMethodReturn(suite.elasticdatabase.Client.Search().Index(), "Do", &searchResult, nil).Reset()

	list, err := suite.ins.GetThreatsOverviewVulTypeCount(context.Background(), "127.0.0.1", pocs, elastic.NewBoolQuery())
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 3, len(list))
}

func (suite *ElasticIndexThreatSuite) Test_GetThreatsOverviewByPoc() {
	var hit *elastic.SearchHit
	err := json.Unmarshal([]byte(Rawdata), &hit)
	searchResult := elastic.SearchResult{}
	searchResult.Hits = &elastic.SearchHits{TotalHits: int64(10), Hits: []*elastic.SearchHit{hit}}
	defer gomonkey.ApplyMethodReturn(suite.elasticdatabase.Client.Search().Index(), "Do", &searchResult, nil).Reset()
	poc, err := suite.ins.GetThreatsOverviewByPoc(context.Background(), "127.0.0.1", true, elastic.NewBoolQuery())
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), poc)
}

func (suite *ElasticIndexThreatSuite) Test_GetThreatsOverviewByTag() {
	tags := []*m_tag.Tag{
		{
			Name:    "xxx",
			Tag:     "xxx",
			TagType: "xxx",
		},
	}
	tag, err := suite.ins.GetThreatsOverviewByTag(context.Background(), "127.0.0.1", tags)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), tag)
}

func (suite *ElasticIndexThreatSuite) TestUpdateThreatStatus() {
	updateResponse := &elastic.UpdateResponse{
		Result: "updated",
	}

	patch := gomonkey.ApplyMethodReturn(&elastic.UpdateService{}, "Do", updateResponse, nil)
	defer patch.Reset()

	QL := &m_threat.RequestUpdateThreatState{
		Id:      "52e7a7f92b7dc9936e773b91d9898535",
		Status:  4,
		Comment: "暂不明确",
	}

	err := suite.ins.UpdateThreatStatus(context.Background(), QL)

	assert.NoError(suite.T(), err)
}
