package esi_threats

import (
	"context"
	"encoding/json"
	"io"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/internal/mux/model/m_assets"
	"git.gobies.org/foeye/foeye3/internal/mux/model/m_threat"
	"git.gobies.org/foeye/foeye3/internal/mux/packages/exchange/e_task"
	"git.gobies.org/foeye/foeye3/responses/r_threat"
)

// GetThreatServerByDefault get threat unfixed by default.
func (store *Store) GetThreatServerByDefault(ctx context.Context, QL *r_threat.QueryListAndKeyword) (interface{}, int64, error) {

	boolQuery := newBoolQuery(QL)

	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range apiDefaultFields {
		fsc.Include(v)
	}

	if QL.Field == "" {
		QL.Field = "lastupdatetime"
	}

	service := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		FetchSourceContext(fsc).
		Pretty(true).
		Query(boolQuery).
		From((QL.Number-1)*QL.Size).
		Size(QL.Size).
		Sort(QL.Field, QL.Sort)

	result, err := service.Do(ctx)
	if err != nil {
		return nil, 0, err
	}

	// debuger.ShowContentForJSON(result, true, true, true)
	return threatServerResultDataByDefault(result), result.Hits.TotalHits, nil
}

// threatServerResultDataByDefault es data convert To interface{}.
func threatServerResultDataByDefault(sr *elastic.SearchResult) interface{} {
	data := make([]*m_threat.ThreatList, 0)
	for _, source := range sr.Hits.Hits {
		if source != nil {
			marshalJSON, err := source.Source.MarshalJSON()
			if err != nil {
				continue
			}
			info := new(m_threat.ThreatList)
			info.ID = source.Id
			err = json.Unmarshal(marshalJSON, &info)
			if err != nil {
				continue
			}
			data = append(data, info)
		}
	}
	return data
}

// taskThreatServerResultDataByDefault es data convert To interface{}.
func taskThreatServerResultDataByDefault(sr *elastic.SearchResult) interface{} {
	data := make([]*m_threat.List, 0)
	for _, source := range sr.Hits.Hits {
		if source != nil {
			marshalJSON, err := source.Source.MarshalJSON()
			if err != nil {
				continue
			}
			info := new(m_threat.List)
			info.ID = source.Id
			err = json.Unmarshal(marshalJSON, &info)
			if err != nil {
				continue
			}
			data = append(data, info)
		}
	}
	return data
}

// GetThreatServerByTaskFinished  get threat data by task_id.
func (store *Store) GetThreatServerByTaskFinished(ctx context.Context, QL *e_task.QueryTaskFinishedListContainExtra) (interface{}, int64, error) {

	boolQuery := elastic.NewBoolQuery().Must()
	boolQuery.Must(elastic.NewTermQuery("task_ids", QL.Id))

	service := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Pretty(true).
		Query(boolQuery).
		From((QL.Page-1)*QL.PerPage).
		Size(QL.PerPage).
		Sort("lastupdatetime", false)

	result, err := service.Do(ctx)
	if err != nil {
		return nil, 0, err
	}

	// mosso.DebugShowContentWithJSON(result)
	return taskThreatServerResultDataByDefault(result), result.Hits.TotalHits, nil
}

// GetOriginalThreat 获取es原始数据.
func (store *Store) GetOriginalThreat(ctx context.Context, QL *r_threat.QueryListAndKeyword) (*elastic.SearchResult, int64, error) {
	boolQuery := newBoolQuery(QL)
	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range defaultFields {
		fsc.Include(v)
	}
	if QL.Field == "" {
		QL.Field = "lastupdatetime"
	}
	service := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		FetchSourceContext(fsc).
		Pretty(true).
		Query(boolQuery).
		From((QL.Number-1)*QL.Size).
		Size(QL.Size).
		Sort(QL.Field, QL.Sort).
		Aggregation("level_count", elastic.NewTermsAggregation().Field("level"))

	result, err := service.Do(ctx)
	if err != nil {
		return nil, 0, err
	}

	return result, result.Hits.TotalHits, nil
}

// GetOriginalThreatForDCC 获取es原始数据.
func (store *Store) GetOriginalThreatForDCC(ctx context.Context, foeyeTaskID int) ([]*elastic.SearchHit, error) {
	service := store.Instance.Client.Scroll().Index(store.IndexName).Type(store.TypeName).Scroll("1m")
	service.Query(elastic.NewTermQuery("task_ids", foeyeTaskID))

	hits := []*elastic.SearchHit{}

	result, err := service.Do(ctx)
	if err != nil {
		return nil, err
	}

	hits = append(hits, result.Hits.Hits...)

	for {
		result, err = store.Instance.Client.Scroll().ScrollId(result.ScrollId).Do(ctx)
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, err
		}
		if len(result.Hits.Hits) <= 0 {
			break
		}
		hits = append(hits, result.Hits.Hits...)
	}

	return hits, nil
}

// GetThreatByIps 根据ip地址获取漏洞信息.
func (store *Store) GetThreatByIps(ips []interface{}) (map[string][]*m_assets.AssetVulnerability, error) {
	data := make(map[string][]*m_assets.AssetVulnerability)
	service := store.Instance.Client.
		Scroll().
		Index(store.IndexName).
		Type(store.TypeName).
		Scroll("1m").
		Query(elastic.NewTermsQuery("ip", ips...))

	for {
		sr, err := service.Do(context.Background())
		if err == io.EOF {
			break
		}
		if err != nil {
			return data, err
		}
		for _, hit := range sr.Hits.Hits {
			tmp := new(m_assets.Vulnerability)
			err := json.Unmarshal(*hit.Source, &tmp)
			if err != nil {
				return data, err
			}
			if _, ok := data[tmp.Ip]; ok {
				data[tmp.Ip] = append(data[tmp.Ip], &m_assets.AssetVulnerability{
					Ip:                tmp.Ip,
					Name:              "",
					VulnerabilityName: tmp.CommonTitle,
					Origin:            "漏洞专扫",
					Level:             m_threat.Level(tmp.Level).String(),
					RiskUrl:           tmp.Url,
					Time:              tmp.CreateTime,
				})
			} else {
				data[tmp.Ip] = []*m_assets.AssetVulnerability{
					{
						Ip:                tmp.Ip,
						Name:              "",
						VulnerabilityName: tmp.CommonTitle,
						Origin:            "漏洞专扫",
						Level:             m_threat.Level(tmp.Level).String(),
						RiskUrl:           tmp.Url,
						Time:              tmp.CreateTime,
					},
				}
			}
		}
	}
	return data, nil
}
