package esi_threats

import (
	"bytes"
	"fmt"
	"strings"
	"time"

	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/responses/r_threat"

	"github.com/olivere/elastic"
)

const (
	maxLimit  = 1000000
	top5Limit = 5

	CountryOfLocalAreaNetwork = "局域网"
)

// newBoolQuery create new bool query.
func newBoolQuery(QL *r_threat.QueryListAndKeyword) *elastic.BoolQuery {
	boolQuery := elastic.NewBoolQuery().Must()
	boolQuery.Must(elastic.NewTermQuery("task_ids", 0))
	if QL.TaskID > 0 {
		boolQuery.Must(elastic.NewTermQuery("task_ids", QL.TaskID))
	}

	if QL.Switch == m_threat.PocTypeUnFixed {
		boolQuery.Must(elastic.NewTermsQuery("state", 1, 2))
	} else if QL.Switch == m_threat.PocTypeFixed {
		boolQuery.Must(elastic.NewTermQuery("state", 4))
	}
	// 页面查询
	if QL.Keyword != "" {
		boolQuery.Must(newKeywordQuery(QL.Keyword))
	}

	if QL.Verity != "" {
		boolQuery.Must(elastic.NewTermQuery("has_exp", QL.Verity))
		if QL.Verity == "2" {
			boolQuery.Must(elastic.NewTermsQuery("has_exp", 0, 1))
		}
	}

	// 指定了在线状态筛选
	if QL.Online == 2 || QL.Online == 1 {
		boolQuery.Must(elastic.NewTermQuery("online", QL.Online))
	}

	// 高级筛选-漏洞类型(信创/非信创)
	setXcType(QL, boolQuery)

	// 高级筛选 - 漏洞状态
	if QL.Status == 10 {
		boolQuery.MustNot(elastic.NewExistsQuery("status"))
	} else if QL.Status > 0 {
		boolQuery.Must(elastic.NewTermQuery("status", QL.Status))
	}

	if QL.IpRange != "" {
		//res := es_asset.ConvertIpContent(QL.IpRange)
		//boolQuery.Filter(res...)
		//es.Must().Should()会抵销Shoule的功能，所以用Filter()
		res := es_asset.ConvertIpContentString(QL.IpRange)
		r := stringSliceToInterface(res)
		boolQuery.Filter(elastic.NewTermsQuery("ip", r...))
	}

	// 高级筛选
	SetSingleMust("level", QL.Level, boolQuery)
	SetSingleMust("vulType", QL.VulType, boolQuery)
	SetSingleMust("company", QL.Company, boolQuery)
	SetSingleMust("business_app", QL.BusinessApp, boolQuery)
	SetSingleMust("computer_room", QL.ComputerRoom, boolQuery)
	SetSingleMust("asset_level", QL.AssetLevel, boolQuery)
	SetSingleMust("username", QL.Username, boolQuery)

	if QL.FindTime != "" {
		if strings.Contains(QL.FindTime, ",") {
			strArr := strings.Split(QL.FindTime, ",")
			if len(strArr) == 2 {
				boolQuery.Must(elastic.NewRangeQuery("createtime").Gte(strArr[0] + " 00:00:00").Lte(strArr[1] + " 00:00:00"))
			}
		}
	}

	setCustomFields(QL, boolQuery)

	if QL.Ids != nil {
		r := stringSliceToInterface(QL.Ids)

		if len(r) > 0 {
			boolQuery.Must(elastic.NewTermsQuery("_id", r...))
		}
	}

	if QL.CommonTitleList != nil {
		r := stringSliceToInterface(QL.CommonTitleList)

		if len(r) > 0 {
			boolQuery.Must(elastic.NewTermsQuery("common_title.raw", r...))
		}
	}

	if QL.NotInIds != nil {
		r := stringSliceToInterface(QL.NotInIds)

		if len(r) > 0 {
			boolQuery.MustNot(elastic.NewTermsQuery("_id", r...))
		}
	}

	// 排除漏洞类型
	if QL.Except != nil {
		r := stringSliceToInterface(QL.Except)

		boolQuery.MustNot(elastic.NewTermsQuery("vulType", r...))
	}

	// 未修复漏洞天数
	if QL.Repaired != 0 {
		boolQuery.Must(elastic.NewRangeQuery("createtime").
			Lte(time.Now().AddDate(0, 0, -QL.Repaired).Format(constant.UserDefinedFormat4)))
	}

	return boolQuery
}

func setXcType(QL *r_threat.QueryListAndKeyword, boolQuery *elastic.BoolQuery) {
	if QL.XcType != nil {
		if *QL.XcType == 1 {
			boolQuery.Must(elastic.NewTermQuery("is_xc", 1))
		} else if *QL.XcType == 0 {
			boolQuery.MustNot(elastic.NewTermQuery("is_xc", 1))
		}
	}
}

func setCustomFields(QL *r_threat.QueryListAndKeyword, boolQuery *elastic.BoolQuery) {
	if QL.CustomFieldsKey != nil && QL.CustomFieldsValue != nil {
		keyLen := len(QL.CustomFieldsKey)
		valLen := len(QL.CustomFieldsValue)
		if keyLen == valLen {
			for idx, fieldKey := range QL.CustomFieldsKey {
				tempKey := fmt.Sprintf("custom_fields.%s", fieldKey)
				tempVal := QL.CustomFieldsValue[idx]
				valStr := strings.Split(tempVal, ",")
				fieldQuery := elastic.NewBoolQuery()
				for _, s := range valStr {
					fieldQuery.Should(elastic.NewTermQuery(tempKey, s))
				}
				boolQuery.Must(fieldQuery)
			}
		}
	}
}

func SetSingleMust(fieldName string, values []string, boolQuery *elastic.BoolQuery) {
	if values != nil {
		r := stringSliceToInterface(values)

		boolQuery.Must(elastic.NewTermsQuery(fieldName, r...))
	}
}

// stringSliceToInterface convert string slice to array interface.
func stringSliceToInterface(data []string) []interface{} {
	r := make([]interface{}, 0, len(data))
	for _, item := range data {
		if item != "" {
			r = append(r, item)
		}
	}

	return r
}

// newKeywordQuery create new keyword search.
func newKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", Escape(keyword))).
		Field("ip.ip_raw").
		Field("hosts").
		Field("common_title").
		Field("cveId").
		Field("url").
		Field("company").
		Field("name").
		Field("province").
		Field("business_app").
		Field("mac").
		Field("username").
		Field("manager_mobile").
		Field("manager_email").
		Field("notice_time").
		Field("status")
	return field
}

// Escape special character convert to escape character.
func Escape(s string) string {
	ra := []string{"+", "-", "=", "&", "|", ">", "<", "!", "(", ")", "{", "}", "[", "]", "^", `~`, `*`, `?`, `:`, `\\`, `/`, `\`, ` `}
	exists := func(v string) bool {
		for _, s := range ra {
			if v == s {
				return true
			}
		}
		return false
	}
	buf := bytes.NewBuffer(nil)
	var prevBack bool
	for _, v := range s {
		if prevBack || !exists(string(v)) {
			buf.WriteString(string(v))
			prevBack = false
		} else {
			buf.WriteString(`\`)
			buf.WriteString(string(v))
			if string(v) == `\` {
				prevBack = true
			}
		}
	}
	return buf.String()
}
