package esi_threats

import (
	"context"

	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye/foeye3/responses/r_threat"
)

func (suite *ElasticIndexThreatSuite) Test_GetListAllWriteToFile() {
	actual, err := suite.ins.GetListAllWriteToFile(
		context.TODO(),
		"test_data/threats/threats_2020_0000027.txt",
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *ElasticIndexThreatSuite) Test_GetThreatByExport() {
	instance := new(r_threat.QueryListAndKeyword)
	actual, err := suite.ins.GetThreatByExport(
		context.TODO(),
		instance,
		false,
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *ElasticIndexThreatSuite) Test_GetManagerEmail() {
	instance := new(r_threat.QueryListAndKeyword)
	actual, _, err := suite.ins.GetThreatByManagerEmailNew(
		context.TODO(),
		instance,
		false,
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *ElasticIndexThreatSuite) Test_GetThreatByManagerEmail() {
	instance := new(r_threat.QueryListAndKeyword)
	actual, _, err := suite.ins.GetThreatByManagerEmailNew(
		context.TODO(),
		instance,
		false,
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *ElasticIndexThreatSuite) Test_GetThreatLevelCount() {
	data, err := suite.ins.GetThreatLevelCount("")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *ElasticIndexThreatSuite) Test_GetThreatVulFileByIp() {
	data, err := suite.ins.GetThreatVulFileByIps([]string{"***********"}, 0)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *ElasticIndexThreatSuite) Test_GetThreatByManagerEmailCount() {
	instance := new(r_threat.QueryListAndKeyword)
	count, err := suite.ins.GetThreatByManagerEmailCount(
		context.TODO(),
		instance,
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), count)
}
