package esi_threats

import (
	"context"
	"encoding/json"

	"github.com/olivere/elastic"
)

// GetAllThreatAssetList get all threats assets list.
func (store *Store) GetAllThreatAssetList(ctx context.Context) (map[string]int, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("task_ids", 0))
	boolQuery.Must(elastic.NewTermsQuery("state", 1, 2))
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"ip"}, nil)

	service := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(searchSource).
		Query(boolQuery).
		Size(100000).
		Pretty(true)

	result, err := service.Do(ctx)
	if err != nil {
		return nil, err
	}

	// mosso.DebugShowContentWithJSON(result)

	items := make(map[string]int, 0)
	for _, hit := range result.Hits.Hits {
		temp := map[string]string{}
		if err = json.Unmarshal(*hit.Source, &temp); err != nil {
			continue
		}
		if temp["ip"] != "" {
			if _, ok := items[temp["ip"]]; ok {
				items[temp["ip"]] += 1
			} else {
				items[temp["ip"]] = 1
			}
		}
	}

	return items, nil
}
