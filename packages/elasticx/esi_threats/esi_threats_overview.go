package esi_threats

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"git.gobies.org/foeye/foeye3/model/m_poc"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"

	"github.com/olivere/elastic"
)

// GetThreatsOverviewCount get threat overview count.
func (store *Store) GetThreatsOverviewCount(ctx context.Context, ipRangeManager string, boolQuery *elastic.BoolQuery) (int64, error) {
	boolQuery.Must(elastic.NewTermQuery("task_ids", 0))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(boolQuery).
		Size(0).
		Pretty(true)

	result, err := serv.Do(ctx)
	if err != nil {
		return 0, err
	}

	return result.Hits.TotalHits, nil
}

// GetThreatsOverviewVulTypeCount get threats Overview VulType.
func (store *Store) GetThreatsOverviewVulTypeCount(ctx context.Context, ipRangeManager string, pocMap map[string]*m_poc.PocPlus, boolQuery *elastic.BoolQuery) ([]m_threat.Exp, error) {
	boolQuery.Must(elastic.NewTermQuery("task_ids", 0))
	boolQuery.Must(elastic.NewTermsQuery("state", 1, 2))
	boolQuery.MustNot(elastic.NewTermQuery("vulfile", ""))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(boolQuery).
		Size(0).
		Aggregation("vulfiles", elastic.NewTermsAggregation().Field("vulfile").Size(10).ShardSize(100)).
		Pretty(true)

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, err
	}

	data := make([]m_threat.Exp, 0)
	terms, find := result.Aggregations.Terms("vulfiles")
	if find {
		for _, bucket := range terms.Aggregations {
			agg, err := ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}

			for _, v := range agg {
				if v.Key != "" && v.DocCount > 0 {
					if _, ok := pocMap[v.Key]; ok {
						data = append(data, m_threat.Exp{
							Name:        pocMap[v.Key].Name,
							DocCount:    v.DocCount,
							Level:       pocMap[v.Key].Level,
							AssetType:   pocMap[v.Key].Product,
							FofaRecords: pocMap[v.Key].FofaRecords,
							FofaQuery:   pocMap[v.Key].Fofaquery,
						})
					}
				}
			}
		}
	}

	return data, nil
}

// GetThreatsOverviewByPoc get threat overview by poc.
func (store *Store) GetThreatsOverviewByPoc(ctx context.Context, ipRangeManager string, ascending bool, boolQuery *elastic.BoolQuery) ([]m_threat.ThreatNew, error) {
	boolQuery.Must(elastic.NewTermQuery("task_ids", 0))
	boolQuery.Must(elastic.NewTermsQuery("state", 1, 2))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(boolQuery).
		Size(15).
		Sort("lastupdatetime", ascending).
		Pretty(true)

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, err
	}

	data := make([]m_threat.ThreatNew, 0)
	for _, hits := range result.Hits.Hits {
		tmp := new(m_threat.ThreatNew)
		if err = json.Unmarshal(*hits.Source, &tmp); err != nil {
			continue
		}

		var tem map[string]interface{}
		if err = json.Unmarshal(*hits.Source, &tem); err != nil {
			continue
		}

		tmp.Name = ""
		if _, ok := tem["common_title"].(string); ok {
			tmp.Name = tem["common_title"].(string)
		}

		if _, ok := tem["lastupdatetime"].(string); ok {
			tmp.LastUpdateTime = tem["lastupdatetime"].(string)
		}

		if len(tmp.CreateTime) > 10 {
			a, _ := time.Parse(constant.FormatChinaDate, time.Now().Format(constant.FormatChinaDate))
			b, _ := time.Parse(constant.FormatChinaDate, tmp.CreateTime[:10])

			data = append(data, m_threat.ThreatNew{
				Name:           tmp.Name,
				Level:          tmp.Level,
				CreateTime:     tmp.CreateTime,
				Username:       tmp.Username,
				NoticeTime:     tmp.NoticeTime,
				LastUpdateTime: tmp.LastUpdateTime,
				DayCount:       int(a.Sub(b).Hours() / 24),
			})
		} else {
			data = append(data, m_threat.ThreatNew{
				Name:           tmp.Name,
				Level:          tmp.Level,
				CreateTime:     tmp.CreateTime,
				Username:       tmp.Username,
				NoticeTime:     tmp.NoticeTime,
				LastUpdateTime: tmp.LastUpdateTime,
			})
		}
	}
	return data, nil
}

// GetThreatsOverviewByTag get threats overview by tag.
func (store *Store) GetThreatsOverviewByTag(ctx context.Context, ipRangeManager string, tags []*m_tag.Tag) (*m_threat.ResultThreatOverview, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("task_ids", 0))
	boolQuery.Must(elastic.NewTermsQuery("state", 1, 2))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(boolQuery).
		Size(0).
		Aggregation(m_tag.SystemPresetTagCategoryOfComputerRoom, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfComputerRoom).Size(top5Limit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfResponsiblePerson, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfResponsiblePerson).Size(top5Limit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfCompany, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfCompany).Size(top5Limit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfBusinessApp, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfBusinessApp).Size(top5Limit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfAssetLevel, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfAssetLevel).Size(top5Limit)).
		Pretty(true)

	for _, v := range tags {
		temp := elastic.NewTermsAggregation().Field("custom_fields." + v.Realname).Size(1000)
		serv.Aggregation("custom_fields."+v.Realname, temp)
	}

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, err
	}

	// mosso.DebugShowContentWithJSON(result)
	return aggregationTagName(result, tags), nil
}

// aggregationTagName aggregation tag name.
func aggregationTagName(sr *elastic.SearchResult, tags []*m_tag.Tag) *m_threat.ResultThreatOverview {
	sets := []string{
		m_tag.SystemPresetTagCategoryOfResponsiblePerson,
		m_tag.SystemPresetTagCategoryOfCompany,
		m_tag.SystemPresetTagCategoryOfComputerRoom,
		m_tag.SystemPresetTagCategoryOfBusinessApp,
		m_tag.SystemPresetTagCategoryOfAssetLevel,
	}

	for _, v := range tags {
		sets = append(sets, "custom_fields."+v.Realname)
	}

	response := new(m_threat.ResultThreatOverview)
	for _, set := range sets {
		terms, find := sr.Aggregations.Terms(set)
		if find {
			for _, bucket := range terms.Aggregations {
				if bucket != nil {
					switch set {
					case m_tag.SystemPresetTagCategoryOfResponsiblePerson:
						processAggregationBucket(bucket, &response.ThreatTags.Username)
					case m_tag.SystemPresetTagCategoryOfCompany:
						processAggregationBucket(bucket, &response.ThreatTags.Company)
					case m_tag.SystemPresetTagCategoryOfComputerRoom:
						processAggregationBucket(bucket, &response.ThreatTags.ComputerRoom)
					case m_tag.SystemPresetTagCategoryOfBusinessApp:
						processAggregationBucket(bucket, &response.ThreatTags.BusinessApp)
					case m_tag.SystemPresetTagCategoryOfAssetLevel:
						processAggregationBucket(bucket, &response.ThreatTags.AssetLevel)
					default:
						for _, v := range tags {
							strArr := strings.Split(set, ".")
							if len(strArr) == 2 && strArr[1] == v.Realname {
								agg, err := ConvertAggregationBucketToSystemBucket(bucket)
								if err != nil {
									continue
								}

								temp := make([]m_threat.TagName, 0)
								for _, v := range agg {
									if v.Key != "" && v.DocCount > 0 {
										temp = append(temp, m_threat.TagName{
											Key:      v.Key,
											DocCount: v.DocCount,
										})
									}
								}

								if len(temp) > 0 {
									tmp := make(map[string][]m_threat.TagName, 0)
									tmp[v.Name] = temp
									response.ThreatTags.CustomFields = append(response.ThreatTags.CustomFields, tmp)
								}
							}
						}
					}
				}
			}
		}
	}
	return response
}

func processAggregationBucket(bucket *json.RawMessage, tagList *[]m_threat.TagName) {
	agg, err := ConvertAggregationBucketToSystemBucket(bucket)
	if err != nil {
		return
	}

	for _, v := range agg {
		if v.Key != "" && v.DocCount > 0 {
			*tagList = append(*tagList, m_threat.TagName{
				Key:      v.Key,
				DocCount: v.DocCount,
			})
		}
	}
}

func (store *Store) UpdateThreatStatus(ctx context.Context, QL *m_threat.RequestUpdateThreatState) error {
	// 构建更新文档的请求
	updateReq := map[string]interface{}{
		"status":  QL.Status,
		"comment": QL.Comment,
	}

	// 执行更新操作
	_, err := store.Instance.Client.Update().
		Index(store.IndexName).
		Type(store.TypeName).
		Id(QL.Id).
		Refresh("true").
		Doc(updateReq).
		Do(ctx)

	if err != nil {
		return fmt.Errorf("Error executing update request: %s", err)
	}
	return nil
}
