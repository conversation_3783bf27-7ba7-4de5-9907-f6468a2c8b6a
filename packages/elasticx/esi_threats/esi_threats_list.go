package esi_threats

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"sort"
	"strconv"
	"strings"

	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/responses/r_asset_history"
	"git.gobies.org/foeye/foeye3/responses/r_threat"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"
	log "github.com/sirupsen/logrus"
)

// GetThreatByDefault get task vulnerability unfixed by default.
func (store *Store) GetThreatByDefault(ctx context.Context, QL *r_threat.QueryListAndKeyword) ([]*m_threat.ResultVulnerabilityInfo, interface{}, int64, error) {
	boolQuery := newBoolQuery(QL)

	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range defaultFields {
		fsc.Include(v)
	}

	if QL.Field == "" {
		QL.Field = "lastupdatetime"
	}

	service := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		FetchSourceContext(fsc).
		Pretty(true).
		Query(boolQuery).
		From((QL.Number-1)*QL.Size).
		Size(QL.Size).
		Sort(QL.Field, QL.Sort).
		Aggregation("level_count", elastic.NewTermsAggregation().Field("level"))

	result, err := service.Do(ctx)
	if err != nil {
		return nil, nil, 0, err
	}

	// debuger.ShowContentForJSON(result, true, true, true)
	return sourceResultDataByDefault(result), aggregationLevelCountByDefault(result), result.Hits.TotalHits, nil
}

// GetThreatByIp get task vulnerability by ip.
func (store *Store) GetThreatByIp(ctx context.Context, QL *r_threat.QueryListAndKeyword) ([]*m_threat.IpParentData, interface{}, int64, error) {
	boolQuery := newBoolQuery(QL)
	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range ipFields {
		fsc.Include(v)
	}

	temp := elastic.NewTopHitsAggregation().
		FetchSourceContext(fsc).
		Size(1).
		Sort("createtime", false)

	ipAggregation := elastic.NewTermsAggregation().Field("ip.ip_raw").Size(1000)
	ipAggregation.SubAggregation("child_ip_view", temp)
	service := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Pretty(true).
		Query(boolQuery).
		Size(0).
		Aggregation("ip_view", ipAggregation).
		Aggregation("level_count", elastic.NewTermsAggregation().Field("level"))

	result, err := service.Do(ctx)
	if err != nil {
		return nil, nil, 0, err
	}
	// debuger.ShowContentForJSON(result, true, true, true)
	total, data := aggregationByIp(result, QL)
	return data, aggregationLevelCountByIpOrPoc(result), total, nil
}

// GetThreatByThreat get task vulnerability by Vulnerability.
func (store *Store) GetThreatByThreat(ctx context.Context, QL *r_threat.QueryListAndKeyword) ([]*m_threat.PocParentData, interface{}, int64, error) {
	boolQuery := newBoolQuery(QL)
	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range pocFields {
		fsc.Include(v)
	}
	//temp := elastic.NewTopHitsAggregation().
	//	FetchSourceContext(fsc).
	//	Size(100).
	//	Sort("createtime", false)

	pocAggregation := elastic.NewTermsAggregation().Field("common_title.raw").Size(1000)
	//pocAggregation.SubAggregation("child_poc_view", temp) // 已经去掉

	service := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Pretty(true).
		Query(boolQuery).
		Size(0).
		Aggregation("poc_view", pocAggregation).
		Aggregation("level_count", elastic.NewTermsAggregation().Field("level"))

	result, err := service.Do(ctx)
	if err != nil {
		return nil, nil, 0, err
	}
	// debuger.ShowContentForJSON(result, true, true, true)
	total, data := aggregationByVulnerability(result, QL)
	return data, aggregationLevelCountByIpOrPoc(result), total, nil
}

// sourceResultDataByDefault source result by default.
func sourceResultDataByDefault(sr *elastic.SearchResult) []*m_threat.ResultVulnerabilityInfo {
	data := make([]*m_threat.ResultVulnerabilityInfo, 0)
	for _, source := range sr.Hits.Hits {
		if source != nil {
			marshalJSON, err := source.Source.MarshalJSON()
			if err != nil {
				continue
			}
			info := new(m_threat.ResultVulnerabilityInfo)
			info.ID = source.Id
			if err = json.Unmarshal(marshalJSON, &info); err != nil {
				continue
			}
			info.UID = info.IP
			if info.Province == CountryOfLocalAreaNetwork {
				info.Province = ""
			}
			data = append(data, info)
		}
	}
	return data
}

// aggregationLevelCountByDefault aggregation level number by default.
func aggregationLevelCountByDefault(sr *elastic.SearchResult) interface{} {
	instance := new(m_threat.VulCount)
	if item, ok := convertAggregationBucketToInterface(sr, instance).(*m_threat.VulCount); ok {
		return item
	}
	return nil
}

// aggregationLevelCountByIpOrPoc aggregation level count by ip 和 poc 维度.
func aggregationLevelCountByIpOrPoc(sr *elastic.SearchResult) interface{} {
	instance := new(m_threat.ResultVulnerabilityTotalData)
	if item, ok := convertAggregationBucketToInterface(sr, instance).(*m_threat.ResultVulnerabilityTotalData); ok {
		return item
	}
	return nil
}

// aggregationByIp aggregation by ip.
func aggregationByIp(sr *elastic.SearchResult, QL *r_threat.QueryListAndKeyword) (int64, []*m_threat.IpParentData) {
	data := make([]*m_threat.IpParentData, 0)
	var total int
	if sr != nil && sr.Aggregations != nil {
		agg, find := sr.Aggregations.Terms("ip_view")
		if find {
			total = len(agg.Buckets)
			for _, bucket := range agg.Buckets {
				if bucket != nil {
					instance := new(m_threat.IpParentData)
					instance.IP = bucket.Key.(string)
					instance.UID = bucket.Key.(string)
					instance.DocCount = int(bucket.DocCount)
					//20240522封板去掉子项 子项改为前端调用列表借口获取
					hits, find := bucket.Aggregations.TopHits("child_ip_view")
					if find {
						for _, source := range hits.Hits.Hits {
							marshalJSON, err := source.Source.MarshalJSON()
							if err != nil {
								continue
							}
							// 保留ip的相关信息
							if err = json.Unmarshal(marshalJSON, &instance); err != nil {
								continue
							}
							break
							//temp := new(m_threat.IpChildData)
							//temp.ID = source.Id
							//if err = json.Unmarshal(marshalJSON, &temp); err != nil {
							//	continue
							//}
							//instance.IpChildData = append(instance.IpChildData, temp)
						}
					}
					if instance.Province == CountryOfLocalAreaNetwork {
						instance.Province = ""
					}
					data = append(data, instance)
				}
			}
		}
	}
	if total < QL.Number*QL.Size {
		return int64(total), data[((QL.Number - 1) * QL.Size):total]
	}
	return int64(total), data[((QL.Number - 1) * QL.Size) : QL.Number*QL.Size]
}

// aggregationByVulnerability aggregation by threat.
func aggregationByVulnerability(sr *elastic.SearchResult, QL *r_threat.QueryListAndKeyword) (int64, []*m_threat.PocParentData) {
	data := make([]*m_threat.PocParentData, 0)
	var total int
	if sr != nil && sr.Aggregations != nil {
		agg, find := sr.Aggregations.Terms("poc_view")
		if find {
			total = len(agg.Buckets)
			for _, bucket := range agg.Buckets {
				if bucket != nil {
					instance := new(m_threat.PocParentData)
					instance.CommonTitle = bucket.Key.(string)
					instance.DocCount = int(bucket.DocCount)
					//20240522封板去掉子项 子项改为前端调用列表借口获取
					//hits, find := bucket.Aggregations.TopHits("child_poc_view")
					//if find {
					//	for _, source := range hits.Hits.Hits {
					//		marshalJSON, err := source.Source.MarshalJSON()
					//		if err != nil {
					//			continue
					//		}
					//		if err = json.Unmarshal(marshalJSON, &instance); err != nil {
					//			continue
					//		}
					//		temp := new(m_threat.PocChildData)
					//		temp.ID = source.Id
					//		if err = json.Unmarshal(marshalJSON, &temp); err != nil {
					//			continue
					//		}
					//		temp.UID = temp.IP
					//		instance.PocChildData = append(instance.PocChildData, temp)
					//	}
					//}
					data = append(data, instance)
				}
			}
		}
	}
	if total < QL.Number*QL.Size {
		return int64(total), data[((QL.Number - 1) * QL.Size):total]
	}
	return int64(total), data[((QL.Number - 1) * QL.Size) : QL.Number*QL.Size]
}

// convertAggregationBucketToInterface convert aggregation bucket to interface.
func convertAggregationBucketToInterface(sr *elastic.SearchResult, instance interface{}) interface{} {
	if item, ok := instance.(*m_threat.VulCount); ok {
		for _, bucket := range sr.Aggregations {
			if bucket != nil {
				marshalJSON, err := bucket.MarshalJSON()
				if err != nil {
					continue
				}
				agg := new(m_threat.Buckets)
				err = json.Unmarshal(marshalJSON, &agg)
				if err != nil {
					continue
				}
				for _, v := range agg.Buckets {
					switch v.Key {
					case 0: // 低危漏洞数量
						item.LowVulCount = v.DocCount
					case 1: // 中危漏洞数量
						item.ModerateVulCount = v.DocCount
					case 2: // 高危漏洞数量
						item.HighVulCount = v.DocCount
					case 3: // 严重漏洞数量
						item.VeryHighVulCount = v.DocCount
					}
				}
			}
		}
		return item
	} else if item, ok := instance.(*m_threat.ResultVulnerabilityTotalData); ok {
		for _, bucket := range sr.Aggregations {
			if bucket != nil {
				marshalJSON, err := bucket.MarshalJSON()
				if err != nil {
					continue
				}
				agg := new(m_threat.Buckets)
				err = json.Unmarshal(marshalJSON, &agg)
				if err != nil {
					continue
				}
				for _, v := range agg.Buckets {
					switch v.Key {
					case 0: // 低危漏洞数量
						item.LowVulCount = v.DocCount
					case 1: // 中危漏洞数量
						item.ModerateVulCount = v.DocCount
					case 2: // 高危漏洞数量
						item.HighVulCount = v.DocCount
					case 3: // 严重漏洞数量
						item.VeryHighVulCount = v.DocCount
					}
				}
			}
		}
		item.AllVeryHighVulCount = sr.Hits.TotalHits
		return item
	}
	return nil
}

// GetThreatAdvancedScreen get advancedScreen screen.
func (store *Store) GetThreatAdvancedScreen(ctx context.Context, tags []*m_tag.Tag, QL *r_threat.QueryListAndKeyword) (interface{}, error) {
	boolQuery := newBoolQuery(QL)
	// boolQuery.MustNot(elastic.NewTermsQuery("vulType", ""))

	service := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(boolQuery).
		Size(0).
		Aggregation("level_count", elastic.NewTermsAggregation().Field("level").Size(maxLimit)).
		Aggregation("has_exp", elastic.NewTermsAggregation().Field("has_exp").Size(maxLimit)).
		Aggregation("vulType", elastic.NewTermsAggregation().Field("vulType").Size(maxLimit)).
		Aggregation("online", elastic.NewTermsAggregation().Field("online").Size(maxLimit)).
		Aggregation("status", elastic.NewTermsAggregation().Field("status").Size(maxLimit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfComputerRoom, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfComputerRoom).Size(maxLimit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfResponsiblePerson, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfResponsiblePerson).Size(maxLimit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfCompany, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfCompany).Size(maxLimit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfBusinessApp, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfBusinessApp).Size(maxLimit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfAssetLevel, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfAssetLevel).Size(maxLimit)).
		Pretty(true)

	for _, v := range tags {
		temp := elastic.NewTermsAggregation().Field("custom_fields." + v.Realname).Size(1000)
		service.Aggregation("custom_fields."+v.Realname, temp)
	}

	result, err := service.Do(ctx)
	if err != nil {
		return nil, err
	}
	return aggregationAdvancedScreen(result, tags), nil
}

func aggregationAdvancedScreen(sr *elastic.SearchResult, tags []*m_tag.Tag) interface{} {
	sets := []string{
		"vulType",
		"has_exp",
		"level_count",
		"online",
		"status",
		m_tag.SystemPresetTagCategoryOfResponsiblePerson,
		m_tag.SystemPresetTagCategoryOfCompany,
		m_tag.SystemPresetTagCategoryOfComputerRoom,
		m_tag.SystemPresetTagCategoryOfBusinessApp,
		m_tag.SystemPresetTagCategoryOfAssetLevel,
	}

	for _, v := range tags {
		sets = append(sets, "custom_fields."+v.Realname)
	}

	response := new(m_threat.AdvancedScreenResponse)
	for _, set := range sets {
		terms, find := sr.Aggregations.Terms(set)
		if !find {
			continue
		}
		for _, bucket := range terms.Aggregations {
			if bucket == nil {
				continue
			}

			switch set {
			case "vulType":
				agg, err := ConvertAggregationBucketToSystemBucket(bucket)
				if err != nil {
					continue
				}
				response.VulType = extractValues(agg)

			case "has_exp":
				agg, err := convertAggregationBucketToBucket(bucket)
				if err != nil {
					continue
				}

				response.Verity = GetEXP(agg)

			case m_tag.SystemPresetTagCategoryOfResponsiblePerson:
				agg, err := ConvertAggregationBucketToSystemBucket(bucket)
				if err != nil {
					continue
				}
				response.Username = extractValues(agg)

			case m_tag.SystemPresetTagCategoryOfCompany:
				agg, err := ConvertAggregationBucketToSystemBucket(bucket)
				if err != nil {
					continue
				}
				response.Company = extractValues(agg)

			case m_tag.SystemPresetTagCategoryOfComputerRoom:
				agg, err := ConvertAggregationBucketToSystemBucket(bucket)
				if err != nil {
					continue
				}
				response.ComputerRoom = extractValues(agg)

			case m_tag.SystemPresetTagCategoryOfBusinessApp:
				agg, err := ConvertAggregationBucketToSystemBucket(bucket)
				if err != nil {
					continue
				}
				response.BusinessApp = extractValues(agg)

			case m_tag.SystemPresetTagCategoryOfAssetLevel:
				agg, err := ConvertAggregationBucketToSystemBucket(bucket)
				if err != nil {
					continue
				}
				response.AssetLevel = extractValues(agg)

			case "level_count":
				agg, err := convertAggregationBucketToBucket(bucket)
				if err != nil {
					continue
				}
				response.Level = CalLevelCount(agg)

			case "online":
				agg, err := convertAggregationBucketToBucket(bucket)
				if err != nil {
					continue
				}
				response.Online = OnlineInfos(agg)

			case "status":
				agg, err := convertAggregationBucketToBucket(bucket)
				if err != nil {
					continue
				}
				response.Status = StatusInfos(agg)

			default:
				handleCustomFields(tags, set, bucket, response)
			}
		}
	}
	if len(response.Verity) == 2 {
		response.Verity = append(response.Verity, &m_threat.SystemPreset{
			Key:   2,
			Value: "全部",
		})
	}

	sort.SliceStable(response.Level, func(i, j int) bool {
		return response.Level[i].Key > response.Level[j].Key
	})
	return response
}

func handleCustomFields(tags []*m_tag.Tag, set string, bucket *json.RawMessage, response *m_threat.AdvancedScreenResponse) {
	for _, v := range tags {
		strArr := strings.Split(set, ".")
		if len(strArr) == 2 && strArr[1] == v.Realname {
			agg, err := ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			CalCustomFields(agg, response, v)
		}
	}
}

// GetEXP　是否支持EXP
func GetEXP(agg []*m_threat.Bucket) []*m_threat.SystemPreset {
	exps := []*m_threat.SystemPreset{}
	for _, v := range agg {
		if v.Key == 0 && v.DocCount > 0 {
			exps = append(exps, &m_threat.SystemPreset{
				Key:   0,
				Value: "不支持",
			})
		} else if v.Key == 1 && v.DocCount > 0 {
			exps = append(exps, &m_threat.SystemPreset{
				Key:   1,
				Value: "支持",
			})
		}
	}
	return exps
}

func CalCustomFields(agg []*m_threat.SystemBucket, response *m_threat.AdvancedScreenResponse, v *m_tag.Tag) {
	temp := make([]string, 0)
	for _, v := range agg {
		if v.Key != "" && v.DocCount > 0 {
			temp = append(temp, v.Key)
		}
	}
	if len(temp) > 0 {
		response.CustomFields = append(response.CustomFields, &m_threat.CustomFields{
			Key:   v.Realname,
			Value: v.Name,
			List:  temp,
		})
	}
}

func OnlineInfos(agg []*m_threat.Bucket) []*m_threat.SystemPreset {
	onlines := []*m_threat.SystemPreset{}
	for _, v := range agg {
		switch v.Key {
		case 0: // 未知情况
			if v.DocCount > 0 {
				onlines = append(onlines, &m_threat.SystemPreset{
					Key:   0,
					Value: "未知",
				})
			}
		case 1: // 在线
			if v.DocCount > 0 {
				onlines = append(onlines, &m_threat.SystemPreset{
					Key:   1,
					Value: "在线",
				})
			}
		case 2: // 离线
			if v.DocCount > 0 {
				onlines = append(onlines, &m_threat.SystemPreset{
					Key:   2,
					Value: "离线",
				})
			}
		}
	}
	return onlines
}

func StatusInfos(agg []*m_threat.Bucket) []*m_threat.SystemPreset {
	status := []*m_threat.SystemPreset{}
	kv := map[int]string{
		10: "待处理", // 查询时10代表待处理，展示或返回数据status为0是代表待处理
		1:  "待核查",
		2:  "误报",
		3:  "接受风险",
		4:  "其他",
	}
	status = append(status, &m_threat.SystemPreset{
		Key:   10,
		Value: kv[10],
	})
	for _, v := range agg {
		if msg, ok := kv[v.Key]; ok && v.DocCount > 0 {
			status = append(status, &m_threat.SystemPreset{
				Key:   v.Key,
				Value: msg,
			})
		}
	}
	return status
}

func CalLevelCount(agg []*m_threat.Bucket) []*m_threat.SystemPreset {
	levels := []*m_threat.SystemPreset{}
	for _, v := range agg {
		switch v.Key {
		case 0: // 低危漏洞数量
			if v.DocCount > 0 {
				levels = append(levels, &m_threat.SystemPreset{
					Key:   0,
					Value: "低危",
				})
			}
		case 1: // 中危漏洞数量
			if v.DocCount > 0 {
				levels = append(levels, &m_threat.SystemPreset{
					Key:   1,
					Value: "中危",
				})
			}
		case 2: // 高危漏洞数量
			if v.DocCount > 0 {
				levels = append(levels, &m_threat.SystemPreset{
					Key:   2,
					Value: "高危",
				})
			}
		case 3: // 严重漏洞数量
			if v.DocCount > 0 {
				levels = append(levels, &m_threat.SystemPreset{
					Key:   3,
					Value: "严重",
				})
			}
		}
	}
	return levels
}

func extractValues(agg []*m_threat.SystemBucket) []string {
	var result []string
	for _, v := range agg {
		if v.Key != "" && v.DocCount > 0 {
			result = append(result, v.Key)
		}
	}
	return result
}

// ConvertAggregationBucketToSystemBucket ...
func ConvertAggregationBucketToSystemBucket(bucket *json.RawMessage) ([]*m_threat.SystemBucket, error) {
	marshalJSON, err := bucket.MarshalJSON()
	if err != nil {
		return nil, err
	}
	agg := make([]*m_threat.SystemBucket, 0)
	if err = json.Unmarshal(marshalJSON, &agg); err != nil {
		return nil, err
	}

	temp := make([]*m_threat.SystemBucket, 0)
	for _, v := range agg {
		if v.Key != "" && v.DocCount > 0 {
			temp = append(temp, v)
		}
	}
	return temp, nil
}

// convertAggregationBucketToBucket
func convertAggregationBucketToBucket(bucket *json.RawMessage) ([]*m_threat.Bucket, error) {
	marshalJSON, err := bucket.MarshalJSON()
	if err != nil {
		return nil, err
	}
	agg := make([]*m_threat.Bucket, 0)
	if err = json.Unmarshal(marshalJSON, &agg); err != nil {
		return nil, err
	}
	return agg, nil
}

// GetThreatByIds delete threat by ids.
func (store *Store) GetThreatByIds(ctx context.Context, QL *r_threat.QueryListAndKeyword) (map[string]interface{}, error) {
	data := make(map[string]interface{})
	boolQuery := newBoolQuery(QL)
	if len(QL.Ids) > 0 {
		r := stringSliceToInterface(QL.Ids)

		boolQuery.Must(elastic.NewTermsQuery("_id", r...))
	}

	search := store.Instance.Client.
		Scroll().
		Scroll(elasticx.DefaultScrollKeepAlive).
		Index(store.IndexName).
		Type(store.TypeName).
		Query(boolQuery).
		Size(elasticx.DefaultScrollQueryBulkSize)

	result, err := search.Do(ctx)

	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
			for _, hit := range result.Hits.Hits {
				if hit.Source != nil {
					cur, err := hit.Source.MarshalJSON()
					if err != nil {
						continue
					}
					tmp := make(map[string]interface{})
					if err := json.Unmarshal(cur, &tmp); err == nil {
						data[hit.Id] = tmp
					}
				}
			}

			// Proceed to the next read.
			result, err = search.ScrollId(result.ScrollId).Do(ctx)
			if err != nil {
				break
			}
		} else {
			break
		}
	}

	return data, nil
}

// GetThreatById 通过漏洞id获取漏洞信息
func (store *Store) GetThreatById(id string) (map[string]interface{}, error) {
	data := make(map[string]interface{})
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("_id", id))

	search := store.Instance.Client.
		Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(boolQuery).Size(1)
	result, err := search.Do(context.Background())
	if err != nil {
		return nil, err
	}
	for _, hit := range result.Hits.Hits {
		if hit.Source != nil {
			if err := json.Unmarshal(*hit.Source, &data); err != nil {
				return nil, err
			}
		}
	}

	return data, nil
}

// UpdateThreatById 更新漏洞状态
func (store *Store) UpdateThreatById(data map[string]interface{}, id string) error {
	_, err := store.Instance.Client.Update().Index(store.IndexName).Type(store.TypeName).Id(id).Doc(data).Do(context.Background())
	return err
}

// GetThreatCount 获取已修复漏洞和未修复漏洞数量
func (store *Store) GetThreatCount(ctx *gin.Context, repaired bool, ipRangeManager string, xcType string) int64 {
	query := elastic.NewBoolQuery()
	if repaired {
		query.Must(elastic.NewTermsQuery("state", 4))
	} else {
		query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	}
	if xcType == "1" {
		query.Must(elastic.NewTermQuery("is_xc", 1))
	} else if xcType == "0" {
		query.MustNot(elastic.NewTermQuery("is_xc", 1))
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	query.Must(elastic.NewTermQuery("task_ids", 0))
	searchResult, err := store.Instance.Client.Search().Index(elasticx.IndexNameOfThreat()).Query(query).Size(0).Do(ctx)
	if err != nil {
		return 0
	}
	return searchResult.TotalHits()
}

// GetPortCount 获取端口数量
func (store *Store) GetPortCount(ctx *gin.Context, repaired bool, ipRangeManager string, xcType string) (int, error) {
	query := elastic.NewBoolQuery()
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}

	if xcType == "1" {
		query.Must(elastic.NewTermQuery("is_xc", 1))
	} else if xcType == "0" {
		query.MustNot(elastic.NewTermQuery("is_xc", 1))
	}

	/*---------------------------分域---------------------------------*/
	portsAggregation := elastic.NewTermsAggregation().Field("ports").Size(65535)
	searchService := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		Query(query).
		Size(0).
		Aggregation("ports", portsAggregation)
	searchResult, err := searchService.
		Pretty(true).
		Do(ctx)
	if err != nil {
		return 0, err
	}
	ports := m_asset.UnmarshalJsonRawMessage(*searchResult.Aggregations["ports"])
	return es_asset.Statistics(ports), nil
}

// GetViolations 获取已修复违规和未修复违规
func (store *Store) GetViolations(ctx *gin.Context, repaired bool, ipRangeManager string, xcType string) int64 {
	query := elastic.NewBoolQuery()
	if repaired {
		query.Must(elastic.NewTermQuery("status", "has_been_repaired"))
	} else {
		query.Must(elastic.NewTermQuery("status", "not_repair"))
	}
	if xcType == "1" {
		query.Must(elastic.NewTermQuery("is_xc", 1))
	} else if xcType == "0" {
		query.MustNot(elastic.NewTermQuery("is_xc", 1))
	}
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	searchResult, err := store.Instance.Client.Search().Index(elasticx.IndexNameOfViolation()).Query(query).Size(0).Do(ctx)
	if err != nil {
		return 0
	}
	return searchResult.TotalHits()
}

// GetThreatPocListByIp get threat poc list by ip.
func (store *Store) GetThreatPocListByIp(ctx context.Context, taskId int, ip string) ([]*m_asset.NoStdPorts, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("ip", ip))
	boolQuery.Must(elastic.NewTermQuery("task_ids", taskId))
	boolQuery.Must(elastic.NewTermsQuery("state", 1, 2))

	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"port_list"}, nil)
	service := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(searchSource).
		Query(boolQuery).
		Pretty(true)

	result, err := service.Do(ctx)
	if err != nil {
		return nil, err
	}

	if result.Hits.TotalHits < 1 {
		return nil, nil
	}

	data := make([]*m_asset.NoStdPorts, 0)
	portMap := make(map[int]struct{})
	for _, hit := range result.Hits.Hits {
		temp := new(m_asset.EsResultRuleInfos)
		if err = json.Unmarshal(*hit.Source, &temp); err != nil {
			continue
		}

		for _, v := range temp.PortList {
			if v.Protocol != "" {
				if _, ok := portMap[v.Port]; !ok {
					data = append(data, &m_asset.NoStdPorts{
						Key:   v.Port,
						Value: v.Protocol,
					})
					portMap[v.Port] = struct{}{}
				}
			}
		}
	}

	sort.SliceStable(data, func(i, j int) bool {
		return data[i].Key < data[j].Key
	})

	return data, nil
}

// GetThreatLevelCount get threat level count
func (store *Store) GetThreatLevelCount(ipRangeManager string) ([]m_overviews.Vulnerability, error) {
	levelTitle := []string{"低危", "中危", "高危", "严重"}
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	query.Must(elastic.NewTermQuery("task_ids", 0))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	levelsAggregation := elastic.NewTermsAggregation().Field("level").Size(5).OrderByKeyDesc()
	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Size(0).
		Query(query).
		Aggregation("levels", levelsAggregation).
		Pretty(true).
		Do(context.Background())
	data := make([]m_overviews.Vulnerability, 0)
	if err != nil {
		return data, err
	}
	total := searchResult.TotalHits()
	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "levels":
			var levels *r_asset_history.Levels
			if err = json.Unmarshal(*rawMessage, &levels); err != nil {
				continue
			}

			percentTotal := 0
			for i, level := range levels.Keys {
				percent := level.Count * 100 / int(total)
				if i == (len(levels.Keys) - 1) {
					percent = 100 - percentTotal
				}
				tmp := m_overviews.Vulnerability{
					Level:   levelTitle[level.Key],
					Count:   level.Count,
					Percent: percent,
				}
				percentTotal += percent

				data = append(data, tmp)
			}
		}
	}
	return data, nil
}

// GetThreatResponsibleCount 负责人排行
func (store *Store) GetThreatResponsibleCount(ipRangeManager string) (*m_overviews.ResponsibleRankList, error) {
	data := new(m_overviews.ResponsibleRankList)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	query.Must(elastic.NewTermQuery("task_ids", 0))
	query.MustNot(elastic.NewTermQuery("username", ""))
	query.Must(elastic.NewExistsQuery("username"))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	userAggregation := elastic.NewTermsAggregation().Field("username").Size(5).OrderByCountDesc()
	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Size(100000).
		Query(query).
		Aggregation("users", userAggregation).
		Pretty(true).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "users":
			agg := new(m_threat.SystemBuckets)
			if err = json.Unmarshal(*rawMessage, &agg); err != nil {
				continue
			}
			for _, v := range agg.SystemBucket {
				data.ThreatResponsibleRank = append(data.ThreatResponsibleRank, m_overviews.ResponsibleRank{
					UserName: v.Key,
					Count:    v.DocCount,
				})
			}

		}
	}
	data.Total = int(searchResult.Hits.TotalHits)
	return data, nil
}

// GetThreatNumberCount 漏洞排行
func (store *Store) GetThreatNumberCount(ipRangeManager string) ([]m_overviews.NumberRank, error) {
	data := make([]m_overviews.NumberRank, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	query.Must(elastic.NewTermQuery("task_ids", 0))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range []string{"level"} {
		fsc.Include(v)
	}
	temp := elastic.NewTopHitsAggregation().
		FetchSourceContext(fsc).
		Sort("createtime", false).Size(1)
	titleAggregation := elastic.NewTermsAggregation().Field("common_title.raw")
	titleAggregation.SubAggregation("poc_view", temp).Size(5).OrderByCountDesc()
	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Size(0).
		Query(query).
		Aggregation("threat_titles", titleAggregation).
		Pretty(true).
		Do(context.Background())
	if err != nil {
		return data, err
	}

	if searchResult != nil && searchResult.Aggregations != nil {
		agg, find := searchResult.Aggregations.Terms("threat_titles")
		if find {

			for _, bucket := range agg.Buckets {
				if bucket != nil {
					instance := new(m_overviews.NumberRank)
					instance.Name = bucket.Key.(string)
					instance.Count = int(bucket.DocCount)
					hits, find := bucket.Aggregations.TopHits("poc_view")
					if find {
						for _, source := range hits.Hits.Hits {
							marshalJSON, err := source.Source.MarshalJSON()
							if err != nil {
								continue
							}
							fmt.Println(string(marshalJSON))
							temp := new(m_overviews.PocViewForOverview)
							if err = json.Unmarshal(marshalJSON, &temp); err != nil {
								continue
							}
							switch temp.Level {
							case 0:
								instance.Level = "0"
							case 1:
								instance.Level = "1"
							case 2:
								instance.Level = "2"
							case 3:
								instance.Level = "3"
							}
						}
					}
					data = append(data, *instance)
				}
			}
		}
	}

	return data, nil
}

// GetWeakPassword get weak password
func (store *Store) GetWeakPassword(ipRangeManager string) ([]m_overviews.WeakPassword, error) {
	data := make([]m_overviews.WeakPassword, 0)
	levelTitle := []string{"低危", "中危", "高危", "严重"}
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	query.Must(elastic.NewTermQuery("task_ids", 0))
	query.Must(elastic.NewTermQuery("vulType", "弱口令"))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	weakPasswordAgg := elastic.NewTermsAggregation().Field("name").Size(10)
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"ip", "common_title", "level", "vulType", "createtime"}, nil)
	temp := elastic.NewTopHitsAggregation().
		SearchSource(searchSource).
		Size(10).
		Sort("lastupdatetime", false)
	weakPasswordAgg.SubAggregation("weak_password_top", temp)
	res, err := store.Instance.Client.Search(store.IndexName).
		Type(store.TypeName).
		Query(query).
		Aggregation("weak_password", weakPasswordAgg).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	total := 0
	items, isExists := res.Aggregations.Terms("weak_password")
	if isExists {
		for _, item := range items.Buckets {
			total += int(item.DocCount)
			tmpPie := m_overviews.WeakPassword{
				Name:    item.Key.(string),
				Count:   int(item.DocCount),
				Percent: "",
			}
			if hits, isExists := item.Aggregations.TopHits("weak_password_top"); isExists {
				for _, hit := range hits.Hits.Hits {
					tmp := m_overviews.TopBucket{}
					if err = json.Unmarshal(*hit.Source, &tmp); err != nil {
						continue
					}

					tmpPie.List = append(tmpPie.List, m_overviews.WeakPasswordList{
						Ip:       tmp.Ip,
						Name:     tmp.CommonName,
						Level:    levelTitle[tmp.Level],
						VulType:  tmp.VulType,
						CreateAt: tmp.CreateTime,
					})
				}
			}
			data = append(data, tmpPie)
		}
	}
	for i, item := range data {
		data[i].Percent = fmt.Sprintf("%.2f", float64(item.Count)*100/float64(total))
	}
	return data, nil
}

// GetThreatRanking 获取漏洞排行
func (store *Store) GetThreatRanking(ipRangeManager string) ([]m_overviews.RankingList, error) {
	data := make([]m_overviews.RankingList, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	query.Must(elastic.NewTermQuery("task_ids", 0))
	query.MustNot(elastic.NewTermQuery("vulfile", ""))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	commonTitleAggregation := elastic.NewTermsAggregation().Field("common_title.raw").Size(10).ShardSize(100)
	searchResult, err := store.Instance.Client.Search(store.IndexName).
		Type(store.TypeName).
		Query(query).
		Size(0).
		Aggregation("threats", commonTitleAggregation).
		Do(context.Background())
	if err != nil {
		return data, err
	}

	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "threats":
			tmp := m_overviews.Aggregations{}
			if err = json.Unmarshal(*rawMessage, &tmp); err != nil {
				return data, err
			}
			for _, bucket := range tmp.Buckets {
				data = append(data, m_overviews.RankingList{
					Name:  bucket.Key,
					Count: bucket.DocCount,
				})
			}
		}
	}
	return data, nil
}

// CountThreatTotal 根据管理范围计算已修复漏洞和未修复漏洞数量
func (store *Store) CountThreatTotal(repaired bool, ipRangeManager string, xcType string) int {
	query := elastic.NewBoolQuery()
	if repaired {
		query.Must(elastic.NewTermsQuery("state", 4))
	} else {
		query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	}
	if xcType != "" {
		if xcType == "1" {
			query.Must(elastic.NewTermQuery("is_xc", 1))
		} else if xcType == "0" {
			query.MustNot(elastic.NewTermQuery("is_xc", 1))
		}
	}
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	query.Must(elastic.NewTermQuery("task_ids", 0))
	num, err := store.Instance.Client.Count(elasticx.IndexNameOfThreat()).Query(query).Do(context.Background())
	if err != nil {
		return 0
	}
	return int(num)
}

// 获取定时任务漏洞统计数据
func (store *Store) GetThreatCountDataByTask(ctx context.Context, latestTaskId int, lastTaskId int) (*m_threat.TaskThreatCountData, error) {
	// 获取上次任务统计
	expCount := elastic.NewFilterAggregation().Filter(
		elastic.NewTermQuery("has_exp", 1)).SubAggregation("exp_count_val", elastic.NewValueCountAggregation().Field("has_exp"))
	weakCount := elastic.NewFilterAggregation().Filter(
		elastic.NewTermQuery("vulType", "弱口令")).SubAggregation("weak_count_val", elastic.NewValueCountAggregation().Field("vulType"))
	commandCount := elastic.NewFilterAggregation().Filter(
		elastic.NewTermQuery("vulType", "默认口令")).SubAggregation("command_count_val", elastic.NewValueCountAggregation().Field("vulType"))

	latestAggs := elastic.NewFilterAggregation().Filter(elastic.NewTermQuery("task_ids", latestTaskId))
	latestAggs.SubAggregation("threat_count", elastic.NewValueCountAggregation().Field("ip"))
	latestAggs.SubAggregation("exp_count", expCount)
	latestAggs.SubAggregation("weak_count", weakCount)
	latestAggs.SubAggregation("command_count", commandCount)

	// 获取上上次任务统计
	lastAggs := elastic.NewFilterAggregation().Filter(elastic.NewTermQuery("task_ids", lastTaskId))
	lastAggs.SubAggregation("threat_count", elastic.NewValueCountAggregation().Field("ip"))
	lastAggs.SubAggregation("exp_count", expCount)
	lastAggs.SubAggregation("weak_count", weakCount)
	lastAggs.SubAggregation("command_count", commandCount)

	// 聚合数据
	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Size(0).
		Pretty(true)

	if latestTaskId > 0 {
		serv.Aggregation("latest_val", latestAggs)
	}

	if lastTaskId > 0 {
		serv.Aggregation("last_val", lastAggs)
	}

	result, err := serv.Do(ctx)

	if err != nil {
		return nil, err
	}

	// 解析数据
	taskThreatData := new(m_threat.TaskThreatCountData)
	for key, data := range result.Aggregations {
		taskThreatDataInfo := m_threat.TaskThreatCountDataInfo{}
		switch key {
		case "latest_val":
			if err := json.Unmarshal(*data, &taskThreatDataInfo); err != nil {
				return nil, err
			}
			taskThreatData.LatestData = taskThreatDataInfo
		case "last_val":
			if err := json.Unmarshal(*data, &taskThreatDataInfo); err != nil {
				return nil, err
			}
			taskThreatData.LastData = taskThreatDataInfo
		default:

		}
	}

	return taskThreatData, nil
}

// GetThreatVulFileByIps 根据IP获取漏洞vulfile
func (store *Store) GetThreatVulFileByIps(ips []string, taskId int) (map[string][]string, error) {
	ipList := make([]interface{}, 0)
	for _, v := range ips {
		ipList = append(ipList, v)
	}
	fetchContent := elastic.NewFetchSourceContext(true).Include("vulfile", "ip")
	data := make(map[string][]string, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("ip", ipList...))
	query.Must(elastic.NewTermQuery("task_ids", taskId))
	query.Must(elastic.NewTermsQuery("state", 1, 2))
	ss := store.Instance.Client.Scroll(store.IndexName).
		Type(store.TypeName).
		FetchSourceContext(fetchContent).
		Query(query).
		Size(100).
		Pretty(true)

	for {
		result, err := ss.Do(context.TODO())
		if err == io.EOF {
			break
		}
		if err != nil {
			return data, err
		}
		if result == nil {
			return data, errors.New("expected results != nil; got nil")
		}
		if result.Hits == nil {
			return data, errors.New("expected results.Hits != nil; got nil")
		}
		for _, res := range result.Hits.Hits {
			tmp := make(map[string]string)

			err = json.Unmarshal(*res.Source, &tmp)
			if err != nil {
				return data, err
			}
			if ip, exists := tmp["ip"]; exists {
				if v, exists := tmp["vulfile"]; exists {
					data[ip] = append(data[ip], v)
				}
			}

		}
	}
	return data, nil
}

func (store *Store) GetThreatNumberList(ipRangeManager string, QL *r_threat.QueryListForXcThreat) ([]m_overviews.NumberRank, int64, error) {
	data := make([]m_overviews.NumberRank, 0)
	var total int64 = 0
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	query.Must(elastic.NewTermQuery("task_ids", 0))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range []string{"level"} {
		fsc.Include(v)
	}
	if QL.XcType != "" {
		if QL.XcType == "1" {
			query.Must(elastic.NewTermQuery("is_xc", 1))
		} else if QL.XcType == "0" {
			query.MustNot(elastic.NewTermQuery("is_xc", 1))
		}
	}
	if QL.Verity == "1" {
		verity, _ := strconv.Atoi(QL.Verity)
		query.Must(elastic.NewTermQuery("has_exp", verity))
	}
	if QL.VulType == "弱口令" {
		query.Must(elastic.NewTermQuery("vulType", QL.VulType))
	}
	if QL.Level != "" {
		level, _ := strconv.Atoi(QL.Level)
		query.Must(elastic.NewTermQuery("level", level))
	}
	temp := elastic.NewTopHitsAggregation().
		FetchSourceContext(fsc).
		Sort("createtime", false).Size(1)
	titleAggregation := elastic.NewTermsAggregation().Field("common_title.raw").Size(6000).OrderByCountDesc()
	titleAggregation.SubAggregation("page", elastic.NewBucketSortAggregation().From((QL.Number-1)*QL.Size).Size(QL.Size))
	titleAggregation.SubAggregation("poc_view", temp)
	titleCountAggregation := elastic.NewCardinalityAggregation().Field("common_title.raw")

	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Size(0).
		Query(query).
		Pretty(true).
		Aggregation("threat_titles", titleAggregation).
		Aggregation("threat_count", titleCountAggregation).
		Do(context.Background())
	if err != nil {
		return data, 0, err
	}
	if searchResult != nil && searchResult.Aggregations != nil {
		agg, find := searchResult.Aggregations.Terms("threat_titles")
		if find {
			for _, bucket := range agg.Buckets {
				if bucket != nil {
					instance := new(m_overviews.NumberRank)
					instance.Name = bucket.Key.(string)
					instance.Count = int(bucket.DocCount)
					hits, find := bucket.Aggregations.TopHits("poc_view")
					if find {
						for _, source := range hits.Hits.Hits {
							marshalJSON, err := source.Source.MarshalJSON()
							if err != nil {
								continue
							}
							fmt.Println(string(marshalJSON))
							temp := new(m_overviews.PocViewForOverview)
							if err = json.Unmarshal(marshalJSON, &temp); err != nil {
								continue
							}
							instance.Level = strconv.Itoa(temp.Level)
						}
					}
					data = append(data, *instance)
				}
			}
		}
		countAgg, countFind := searchResult.Aggregations.Cardinality("threat_count")
		if countFind {
			total = int64(*countAgg.Value)
		}

	}
	return data, total, nil
}

// GetThreatListMenu 信创风险资产管理-搜索菜单
func (store *Store) GetThreatListMenu(ctx context.Context, ipRangeManager string) ([]m_overviews.MenuIndex, error) {
	data := make([]m_overviews.MenuIndex, 0)
	data = append(data, m_overviews.MenuIndex{Name: "EXP", Count: 0, Key: "verity", Value: "1"})
	data = append(data, m_overviews.MenuIndex{Name: "弱口令", Count: 0, Key: "vul_type", Value: "弱口令"})
	levelTitle := []string{"严重", "高危", "中危", "低危"}
	levelMap := map[string]int{"严重": 3, "高危": 2, "中危": 1, "低危": 0}
	for _, temData := range levelTitle {
		tmp := m_overviews.MenuIndex{Name: temData, Count: 0, Key: "level", Value: strconv.Itoa(levelMap[temData])}
		data = append(data, tmp)
	}
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("state", []interface{}{1, 2}...))
	query.Must(elastic.NewTermQuery("task_ids", 0))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	verityAgg := elastic.NewFilterAggregation().Filter(elastic.NewTermQuery("has_exp", 1))
	vulTypeAgg := elastic.NewFilterAggregation().Filter(elastic.NewTermQuery("vulType", "弱口令"))
	levelAgg := elastic.NewTermsAggregation().Field("level").Size(4)
	xcAgg := elastic.NewTermsAggregation().Field("is_xc")
	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Size(0).
		Query(query).
		Pretty(true).
		Aggregation("verity_agg", verityAgg).
		Aggregation("vul_type_agg", vulTypeAgg).
		Aggregation("level_agg", levelAgg).
		Aggregation("xc_agg", xcAgg).
		Do(ctx)
	if err != nil {
		return data, err
	}
	if searchResult != nil && searchResult.Aggregations != nil {
		for key, rawMessage := range searchResult.Aggregations {
			switch key {
			case "verity_agg":
				tmp := new(m_overviews.Doc)
				err := json.Unmarshal(*rawMessage, &tmp)
				if err != nil {
					log.Println(err)
					return data, err
				}
				for index, tmpData := range data {
					if tmpData.Key == "verity" {
						data[index].Count = tmp.DocCount
					}
				}
			case "vul_type_agg":
				tmp := new(m_overviews.Doc)
				err := json.Unmarshal(*rawMessage, &tmp)
				if err != nil {
					log.Println(err)
					return data, err
				}
				for index, tmpData := range data {
					if tmpData.Key == "vul_type" {
						data[index].Count = tmp.DocCount
					}
				}
			case "level_agg":
				tmp := new(m_threat.Buckets)
				err := json.Unmarshal(*rawMessage, &tmp)
				if err != nil {
					log.Println(err)
					return data, err
				}
				for _, v := range tmp.Buckets {
					keyString := strconv.Itoa(v.Key)
					for index, tmpData := range data {
						if tmpData.Key == "level" && tmpData.Value == keyString {
							data[index].Count = v.DocCount
						}
					}
				}
			case "xc_agg":
				tmp := new(m_overviews.AggregationsPlus)
				err := json.Unmarshal(*rawMessage, &tmp)
				if err != nil {
					log.Println(err)
					return data, err
				}
				if len(tmp.Buckets) > 0 {
					for _, item := range tmp.Buckets {
						keyString := fmt.Sprintf("%.f", item.Key)
						if keyString == "1" {
							data = append(data, m_overviews.MenuIndex{
								Name:  "xc",
								Count: item.DocCount,
								Key:   "xc_type",
								Value: keyString,
							})
						} else {
							data = append(data, m_overviews.MenuIndex{
								Name:  "nxc",
								Count: item.DocCount,
								Key:   "xc_type",
								Value: keyString,
							})
						}
					}
				} else {
					//没有聚合数据返回零
					data = append(data, m_overviews.MenuIndex{
						Name:  "nxc",
						Count: 0,
						Key:   "xc_type",
						Value: "0",
					})
					data = append(data, m_overviews.MenuIndex{
						Name:  "xc",
						Count: 0,
						Key:   "xc_type",
						Value: "1",
					})
				}

			}

		}
	}
	return data, nil
}
