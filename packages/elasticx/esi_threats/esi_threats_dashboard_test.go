package esi_threats

import "github.com/stretchr/testify/assert"

func (suite *ElasticIndexThreatSuite) Test_GetDashboardCategoryLevel() {
	data, _, err := suite.ins.GetDashboardCategory(suite.ctx, "", "level", nil)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *ElasticIndexThreatSuite) Test_GetDashboardCategoryExp() {
	data, _, err := suite.ins.GetDashboardCategory(suite.ctx, "", "exp", nil)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *ElasticIndexThreatSuite) Test_GetDashboardCategoryWeak() {
	data, _, err := suite.ins.GetDashboardCategory(suite.ctx, "", "weak", nil)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *ElasticIndexThreatSuite) Test_GetDashboardCategoryTest() {
	data, _, err := suite.ins.GetDashboardCategory(suite.ctx, "", "test", nil)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), data)
}
