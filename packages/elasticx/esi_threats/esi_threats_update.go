package esi_threats

import (
	"context"
	"fmt"
	"time"

	"github.com/olivere/elastic"
	"github.com/thedevsaddam/gojsonq/v2"

	"git.gobies.org/foeye/foeye3/packages/constant"
)

// UpdateThreatBatch update threat batch.
func (store *Store) UpdateThreatBatch(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return nil
	}
	currentTime := time.Now().Format(constant.FormatChina24DateTime)
	batchSize := 1000
	for i := 0; i < len(ids); i += batchSize {
		end := i + batchSize
		if end > len(ids) {
			end = len(ids)
		}
		req := store.Instance.Client.Bulk().Index(store.IndexName).Type(store.TypeName)
		for _, id := range ids[i:end] {
			doc := elastic.NewBulkUpdateRequest().Id(id).Doc(map[string]string{"notice_time": currentTime})
			req.Add(doc)
		}

		if _, err := req.Do(ctx); err != nil {
			return err
		}
	}
	return nil
}

// PutAssetLevelMapping fofaee_threats的mapping添加资产等级
func (store *Store) PutAssetLevelMapping() error {
	aa := `{
			"properties": {
				"asset_level": {
        			"type": "keyword",
        			"index": true
      			}
		  	}
}`

	res, err := store.Instance.Client.GetFieldMapping().Field("asset_level").Index(store.IndexName).Do(context.Background())
	if err != nil {
		return err
	}
	gp := gojsonq.New().FromInterface(res)
	rr := gp.Find(fmt.Sprintf("%s.mappings.%s.asset_level.full_name", store.IndexName, store.TypeName))
	if rr == nil {
		_, err = store.Instance.Client.PutMapping().Index(store.IndexName).Type(store.TypeName).BodyString(aa).Do(context.Background())
		return err
	}
	return nil
}
