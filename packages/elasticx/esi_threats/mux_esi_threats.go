package esi_threats

// API 2.0 threat search filed.
var apiDefaultFields = []string{
	"id",                 // 漏洞ID
	"common_title",       // 漏洞名称
	"country",            // 国家
	"city",               // 城市
	"vulType",            // 漏洞类型
	"business_app",       // 业务系统
	"hostinfo",           // 漏洞地址和端口
	"common_impact",      // 漏洞危害
	"mac",                // MAC地址
	"province",           // 地理位置
	"recommandation",     // 解决方案
	"notice_time",        // 通报时间
	"common_description", // 漏洞描述
	"company",            // 管理单元
	"state",              // 漏洞状态
	"vulfile",            // POC文件名称
	"createtime",         // 发现时间
	"lastupdatetime",     // 最后更新时间,最后发现时间
	"level",              // 漏洞等级
	"custom_fields",      // 自定义标签
	"ip",                 // IP地址
	"cveId",              // CVE编号
	"manager_mobile",     // 电话
	"url",                // 漏洞地址
	"computer_room",      // 机房信息
	"port",               // 端口
	"manager_email",      // 邮箱
	"username",           // 负责人
	"name",               // 备注信息
	"last_response",      // 漏洞响应结果
}
