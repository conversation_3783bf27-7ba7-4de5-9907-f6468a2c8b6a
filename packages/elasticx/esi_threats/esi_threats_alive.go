package esi_threats

import (
	"context"
	"encoding/json"
	"git.gobies.org/foeye/foeye3/model/m_alive_check_configs"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"github.com/olivere/elastic"
	"io"
)

// GetListAll 获取所有漏洞信息
func (store *Store) GetListAll(ipRangeManager string) (interface{}, error) {
	//返回结果
	var result []m_alive_check_configs.AllOnlineIP
	// 选择需要的字段
	fsc := elastic.NewFetchSourceContext(true).Include([]string{"ip", "online"}...)
	// 查询条件
	query := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	//查询数据
	ss := store.Instance.Client.Scroll(store.IndexName).Type(store.TypeName).FetchSourceContext(fsc).Query(query).Size(10000)
	for true {
		res, err := ss.Do(context.Background())
		if err == io.EOF {
			break
		}
		if err != nil {
			return result, err
		}
		for _, searchHit := range res.Hits.Hits {
			tmp := m_alive_check_configs.AllOnlineIP{}
			res, err := searchHit.Source.MarshalJSON()
			if err != nil {
				continue
			}
			err = json.Unmarshal(res, &tmp)
			if err != nil {
				continue
			}
			result = append(result, tmp)
		}
	}
	return result, nil
}

// BullUpdateStateByIP 根据IP更新资产状态
func (store *Store) BullUpdateStateByIP(ip string, state int) error {
	if state == 0 { // 离线
		state = 2
	}
	_, err := store.Instance.Client.UpdateByQuery().
		Index(store.IndexName).                // 设置索引名称
		Query(elastic.NewTermQuery("ip", ip)). // 查找 ip 字段匹配的文档
		Script(elastic.NewScriptInline(`
			if (ctx._source.containsKey("online")) {
				// 如果 online 字段存在，直接更新
				ctx._source.online = params.state;
			} else {
				// 如果 online 字段不存在，创建并赋值
				ctx._source.online = params.state;
			}`).
						Param("state", state)). // 设置 state 值
		Do(context.Background()) // 执行更新操作

	return err
}
