package esi_threats

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/responses/r_common"
	"git.gobies.org/foeye/foeye3/responses/r_threat"
	elastic2 "git.gobies.org/foeye/foeye3/store/elastic"
	"git.gobies.org/foeye/foeye3/store/storage"
)

func (suite *ElasticIndexThreatSuite) Test_GetWeakPassword() {
	weakPassword, err := suite.ins.GetWeakPassword("***********")
	assert.NoError(suite.T(), err)
	ss, _ := json.Marshal(weakPassword)
	assert.Equal(suite.T(), string(ss), `[{"name":"Login :: Damn Vulnerable Web Application (DVWA) v1.10 *Development*","count":1,"percent":"100.00","list":[{"ip":"***********","name":"SSH弱口令","level":"严重","vul_type":"弱口令","create_at":"2021-07-29 18:26:04"}]}]`)
}

func TestStore_GetThreatByDefault(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)
	ctx := context.Background()
	QL := &r_threat.QueryListAndKeyword{
		SelectType: "state",
		Keyword:    "task_ids",
	}
	defer gomonkey.ApplyMethodReturn(store.Instance.Client.Search(), "Do", nil, fmt.Errorf("mock err")).Reset()
	infoList, inter, num, err := store.GetThreatByDefault(ctx, QL)
	assert.Equal(t, ([]*m_threat.ResultVulnerabilityInfo)(nil), infoList)
	assert.Equal(t, (any)(nil), inter)
	assert.Equal(t, int64(0), num)
	assert.Equal(t, fmt.Errorf("mock err"), err)
}

func TestStore_GetThreatByIp(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)
	ctx := context.Background()
	QL := &r_threat.QueryListAndKeyword{
		SelectType: "state",
		Keyword:    "task_ids",
	}
	defer gomonkey.ApplyMethodReturn(store.Instance.Client.Search(), "Do", nil, fmt.Errorf("mock err")).Reset()
	infoList, inter, num, err := store.GetThreatByIp(ctx, QL)
	assert.Equal(t, ([]*m_threat.IpParentData)(nil), infoList)
	assert.Equal(t, (any)(nil), inter)
	assert.Equal(t, int64(0), num)
	assert.Equal(t, fmt.Errorf("mock err"), err)
}

func TestStore_GetThreatByThreat(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)
	ctx := context.Background()
	QL := &r_threat.QueryListAndKeyword{
		SelectType: "state",
		Keyword:    "task_ids",
	}
	defer gomonkey.ApplyMethodReturn(store.Instance.Client.Search(), "Do", nil, fmt.Errorf("mock err")).Reset()
	infoList, inter, num, err := store.GetThreatByThreat(ctx, QL)
	assert.Equal(t, ([]*m_threat.PocParentData)(nil), infoList)
	assert.Equal(t, (any)(nil), inter)
	assert.Equal(t, int64(0), num)
	assert.Equal(t, fmt.Errorf("mock err"), err)
}

func Test_sourceResultDataByDefault(t *testing.T) {
	info := m_threat.ResultVulnerabilityInfo{
		CommonTitle: "test_title",
		Level:       2,
		UID:         "**********",
		IP:          "**********",
		BusinessApp: "业务系统",
		Province:    "局域网",
	}
	data, err := json.Marshal(info)
	assert.NoError(t, err)

	sr := &elastic.SearchResult{
		Hits: &elastic.SearchHits{
			Hits: []*elastic.SearchHit{
				{
					Source: (*json.RawMessage)(&data),
				},
			},
		},
	}
	infos := sourceResultDataByDefault(sr)
	info.Province = ""
	assert.Equal(t, []*m_threat.ResultVulnerabilityInfo{
		&info,
	}, infos)
	assert.Equal(t, "", infos[0].Province)
}

func Test_aggregationLevelCountByDefault(t *testing.T) {
	bs := m_threat.Buckets{
		Buckets: []m_threat.Bucket{
			{
				Key:      0,
				DocCount: 20,
			},
			{
				Key:      1,
				DocCount: 20,
			},
			{
				Key:      2,
				DocCount: 20,
			},
			{
				Key:      3,
				DocCount: 20,
			},
		},
	}

	data, err := json.Marshal(bs)
	assert.NoError(t, err)
	sr := &elastic.SearchResult{
		Aggregations: elastic.Aggregations{
			"b1": (*json.RawMessage)(&data),
		},
		Hits: &elastic.SearchHits{},
	}
	item := aggregationLevelCountByDefault(sr)
	assert.Equal(t, (any)(&m_threat.VulCount{
		LowVulCount:      20,
		ModerateVulCount: 20,
		HighVulCount:     20,
		VeryHighVulCount: 20,
	}), item)
}

func Test_aggregationLevelCountByIpOrPoc(t *testing.T) {
	bs := m_threat.Buckets{
		Buckets: []m_threat.Bucket{
			{
				Key:      0,
				DocCount: 20,
			},
			{
				Key:      1,
				DocCount: 20,
			},
			{
				Key:      2,
				DocCount: 20,
			},
			{
				Key:      3,
				DocCount: 20,
			},
		},
	}

	data, err := json.Marshal(bs)
	assert.NoError(t, err)
	sr := &elastic.SearchResult{
		Aggregations: elastic.Aggregations{
			"b1": (*json.RawMessage)(&data),
		},
		Hits: &elastic.SearchHits{},
	}
	item := aggregationLevelCountByIpOrPoc(sr)
	assert.Equal(t, (any)(&m_threat.ResultVulnerabilityTotalData{
		LowVulCount:      20,
		ModerateVulCount: 20,
		HighVulCount:     20,
		VeryHighVulCount: 20,
	}), item)
}

func Test_aggregationByIp(t *testing.T) {
	sr := &elastic.SearchResult{
		Aggregations: elastic.Aggregations{},
	}
	QL := &r_threat.QueryListAndKeyword{
		QueryList: r_common.QueryList{
			Number: 1,
			Size:   1,
		},
	}
	defer gomonkey.ApplyMethodReturn(
		sr.Aggregations,
		"Terms",
		&elastic.AggregationBucketKeyItems{
			Buckets: []*elastic.AggregationBucketKeyItem{
				{
					Aggregations: map[string]*json.RawMessage{},
					Key:          "test_key",
					DocCount:     20,
				},
			},
		},
		true).Reset()
	ins := m_threat.IpParentData{
		UID:        "",
		AssetLevel: "",
		DocCount:   0,
		Name:       "",
	}
	data, err := json.Marshal(ins)
	assert.NoError(t, err)
	defer gomonkey.ApplyMethodReturn(
		elastic.Aggregations{},
		"TopHits",
		&elastic.AggregationTopHitsMetric{
			Hits: &elastic.SearchHits{
				Hits: []*elastic.SearchHit{
					{
						Source: (*json.RawMessage)(&data),
					},
				},
			},
		},
		true).Reset()
	count, dList := aggregationByIp(sr, QL)
	assert.Equal(t, int64(1), count)
	assert.Equal(t, []*m_threat.IpParentData{{}}, dList)
}

func Test_aggregationByVulnerability(t *testing.T) {
	sr := &elastic.SearchResult{
		Aggregations: elastic.Aggregations{},
	}
	QL := &r_threat.QueryListAndKeyword{
		QueryList: r_common.QueryList{
			Number: 1,
			Size:   1,
		},
	}
	defer gomonkey.ApplyMethodReturn(
		sr.Aggregations,
		"Terms",
		&elastic.AggregationBucketKeyItems{
			Buckets: []*elastic.AggregationBucketKeyItem{
				{
					Aggregations: map[string]*json.RawMessage{},
					Key:          "test_key",
					DocCount:     20,
				},
			},
		},
		true).Reset()

	count, dList := aggregationByVulnerability(sr, QL)
	assert.Equal(t, int64(1), count)
	assert.Equal(t, []*m_threat.PocParentData{{
		CommonTitle: "test_key",
		DocCount:    20,
	}}, dList)
}

func Test_convertAggregationBucketToInterface(t *testing.T) {
	t.Run("VulCount", func(t *testing.T) {
		bs := m_threat.Buckets{
			Buckets: []m_threat.Bucket{
				{
					Key:      0,
					DocCount: 20,
				},
				{
					Key:      1,
					DocCount: 20,
				},
				{
					Key:      2,
					DocCount: 20,
				},
				{
					Key:      3,
					DocCount: 20,
				},
			},
		}

		m := new(m_threat.VulCount)
		data, err := json.Marshal(bs)
		assert.NoError(t, err)
		sr := &elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"b1": (*json.RawMessage)(&data),
			},
		}
		item := convertAggregationBucketToInterface(sr, m)
		assert.Equal(t, (any)(&m_threat.VulCount{
			LowVulCount:      20,
			ModerateVulCount: 20,
			HighVulCount:     20,
			VeryHighVulCount: 20,
		}), item)
	})
	t.Run("ResultVulnerabilityTotalData", func(t *testing.T) {
		bs := m_threat.Buckets{
			Buckets: []m_threat.Bucket{
				{
					Key:      0,
					DocCount: 20,
				},
				{
					Key:      1,
					DocCount: 20,
				},
				{
					Key:      2,
					DocCount: 20,
				},
				{
					Key:      3,
					DocCount: 20,
				},
			},
		}

		m := new(m_threat.ResultVulnerabilityTotalData)
		data, err := json.Marshal(bs)
		assert.NoError(t, err)
		sr := &elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"b1": (*json.RawMessage)(&data),
			},
			Hits: &elastic.SearchHits{},
		}
		item := convertAggregationBucketToInterface(sr, m)
		assert.Equal(t, (any)(&m_threat.ResultVulnerabilityTotalData{
			LowVulCount:      20,
			ModerateVulCount: 20,
			HighVulCount:     20,
			VeryHighVulCount: 20,
		}), item)
	})
}

func TestStore_GetThreatAdvancedScreen(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)
	ctx := context.Background()
	tags := []*m_tag.Tag{{
		Realname: "test_name",
	}}
	QL := &r_threat.QueryListAndKeyword{}
	defer gomonkey.ApplyMethodReturn(store.Instance.Client.Search(), "Do", nil, fmt.Errorf("mock err")).Reset()
	screen, err := store.GetThreatAdvancedScreen(ctx, tags, QL)
	assert.Equal(t, any(nil), screen)
	assert.Equal(t, fmt.Errorf("mock err"), err)

}

func Test_aggregationAdvancedScreen(t *testing.T) {
	sr := &elastic.SearchResult{
		Aggregations: map[string]*json.RawMessage{},
	}
	tags := []*m_tag.Tag{{
		Realname: "test_name",
	}}
	data := []byte(`{}`)
	defer gomonkey.ApplyMethodReturn(
		sr.Aggregations,
		"Terms",
		&elastic.AggregationBucketKeyItems{
			Aggregations: elastic.Aggregations{
				"k1": (*json.RawMessage)(&data),
			},
		},
		true).Reset()
	screen := aggregationAdvancedScreen(sr, tags)
	assert.Equal(t, (any)(&m_threat.AdvancedScreenResponse{}), screen)
}

func TestStore_GetThreatByIds(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)
	ctx := context.Background()
	QL := &r_threat.QueryListAndKeyword{
		SelectType: "state",
		Keyword:    "task_ids",
		ThreatBatch: m_threat.ThreatBatch{
			Ids: []string{"101"},
		},
	}
	tmp := map[string]any{
		"k1": "v1",
	}
	data, err := json.Marshal(tmp)
	assert.NoError(t, err)
	defer gomonkey.ApplyMethodReturn(
		store.Instance.Client.Scroll(),
		"Do",
		&elastic.SearchResult{
			Hits: &elastic.SearchHits{
				Hits: []*elastic.SearchHit{
					{
						Source: (*json.RawMessage)(&data),
					},
				},
			},
		},
		fmt.Errorf("mock err")).Reset()

	m, err := store.GetThreatByIds(ctx, QL)
	assert.Equal(t, (any)(map[string]any{
		"": map[string]any{
			"k1": "v1",
		},
	}), m)
	assert.NoError(t, err)

}

func TestStore_GetThreatById(t *testing.T) {
	t.Run("search err", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)

		tmp := map[string]any{
			"k1": "v1",
		}
		data, err := json.Marshal(tmp)
		assert.NoError(t, err)
		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Hits: &elastic.SearchHits{
					Hits: []*elastic.SearchHit{
						{
							Source: (*json.RawMessage)(&data),
						},
					},
				},
			},
			fmt.Errorf("mock err")).Reset()

		m, err := store.GetThreatById("101")
		assert.Equal(t, (map[string]any)(nil), m)
		assert.Equal(t, fmt.Errorf("mock err"), err)
	})
	t.Run("search success", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)

		tmp := map[string]any{
			"k1": "v1",
		}
		data, err := json.Marshal(tmp)
		assert.NoError(t, err)
		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Hits: &elastic.SearchHits{
					Hits: []*elastic.SearchHit{
						{
							Source: (*json.RawMessage)(&data),
						},
					},
				},
			},
			nil).Reset()

		m, err := store.GetThreatById("101")
		assert.Equal(t, map[string]any{
			"k1": "v1",
		}, m)
		assert.NoError(t, err)
	})
}

func TestStore_UpdateThreatById(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)
	defer gomonkey.ApplyMethodReturn(store.Instance.Client.Update(), "Do", nil, fmt.Errorf("mock err")).Reset()
	err := store.UpdateThreatById(map[string]any{}, "101")
	assert.Equal(t, fmt.Errorf("mock err"), err)
}

func TestStore_GetThreatCount(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip")}).Reset()

	t.Run("success case1", func(t *testing.T) {
		ctx, _ := gin.CreateTestContext(nil)
		repaired := true
		ipRangeManager := "test_iprange"
		xcType := "1"
		count := store.GetThreatCount(ctx, repaired, ipRangeManager, xcType)
		assert.Equal(t, int64(0), count)
	})
	t.Run("success case1", func(t *testing.T) {
		ctx, _ := gin.CreateTestContext(nil)
		repaired := false
		ipRangeManager := "test_iprange"
		xcType := "2"
		count := store.GetThreatCount(ctx, repaired, ipRangeManager, xcType)
		assert.Equal(t, int64(0), count)
	})
}

func TestStore_GetPortCount(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip")}).Reset()
	defer gomonkey.ApplyMethodReturn(store.Instance.Client.Search(), "Do", nil, fmt.Errorf("mock err")).Reset()
	t.Run("success case1", func(t *testing.T) {
		ctx, _ := gin.CreateTestContext(nil)
		repaired := true
		ipRangeManager := "test_iprange"
		xcType := "1"
		count, err := store.GetPortCount(ctx, repaired, ipRangeManager, xcType)
		assert.Equal(t, 0, count)
		assert.Equal(t, fmt.Errorf("mock err"), err)
	})
	t.Run("success case1", func(t *testing.T) {
		ctx, _ := gin.CreateTestContext(nil)
		repaired := false
		ipRangeManager := "test_iprange"
		xcType := "0"
		count, err := store.GetPortCount(ctx, repaired, ipRangeManager, xcType)
		assert.Equal(t, 0, count)
		assert.Equal(t, fmt.Errorf("mock err"), err)
	})
}

func TestStore_GetViolations(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip")}).Reset()
	defer gomonkey.ApplyMethodReturn(store.Instance.Client.Search(), "Do", nil, fmt.Errorf("mock err")).Reset()
	t.Run("success case1", func(t *testing.T) {
		ctx, _ := gin.CreateTestContext(nil)
		repaired := true
		ipRangeManager := "test_iprange"
		xcType := "1"
		count := store.GetViolations(ctx, repaired, ipRangeManager, xcType)
		assert.Equal(t, int64(0), count)
	})
	t.Run("success case1", func(t *testing.T) {
		ctx, _ := gin.CreateTestContext(nil)
		repaired := false
		ipRangeManager := "test_iprange"
		xcType := "0"
		count := store.GetViolations(ctx, repaired, ipRangeManager, xcType)
		assert.Equal(t, int64(0), count)
	})
}

func TestStore_GetThreatPocListByIp(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)
	ctx := context.Background()
	taskId := 101
	ip := "**********"
	ins := m_asset.EsResultRuleInfos{
		PortList: []m_asset.PortDetail{
			{
				Protocol: "tcp",
				Port:     80,
			},
			{
				Protocol: "tcp",
				Port:     443,
			},
		},
	}
	data, err := json.Marshal(ins)
	assert.NoError(t, err)

	defer gomonkey.ApplyMethodReturn(
		store.Instance.Client.Search(),
		"Do",
		&elastic.SearchResult{
			Hits: &elastic.SearchHits{
				TotalHits: 1,
				Hits: []*elastic.SearchHit{
					{
						Source: (*json.RawMessage)(&data),
					},
				},
			},
		},
		nil).Reset()

	pList, err := store.GetThreatPocListByIp(ctx, taskId, ip)
	assert.Equal(t, []*m_asset.NoStdPorts{
		{
			Value: "tcp",
			Key:   80,
		},
		{
			Value: "tcp",
			Key:   443,
		},
	}, pList)
	assert.Equal(t, nil, err)
}

func TestStore_GetThreatResponsibleCount(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)
	ipRangeManager := "testIpRangeManager"
	ins := m_threat.SystemBuckets{
		SystemBucket: []m_threat.SystemBucket{
			{
				Key:      "user",
				DocCount: 1,
			},
		},
	}
	data, err := json.Marshal(ins)
	assert.NoError(t, err)

	defer gomonkey.ApplyMethodReturn(
		store.Instance.Client.Search(),
		"Do",
		&elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"users": (*json.RawMessage)(&data),
			},
			Hits: &elastic.SearchHits{},
		},
		nil).Reset()
	defer gomonkey.ApplyFuncReturn(
		es_asset.ConvertIpContent,
		[]elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

	pList, err := store.GetThreatResponsibleCount(ipRangeManager)
	assert.Equal(t, &m_overviews.ResponsibleRankList{
		ThreatResponsibleRank: []m_overviews.ResponsibleRank{
			{
				UserName: "user",
				Count:    1,
			},
		},
	}, pList)
	assert.Equal(t, nil, err)
}

func TestStore_GetThreatNumberCount(t *testing.T) {
	t.Run("level 0", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)
		ipRangeManager := "testIpRangeManager"
		ins := m_threat.SystemBuckets{
			SystemBucket: []m_threat.SystemBucket{
				{
					Key:      "user",
					DocCount: 1,
				},
			},
		}
		data, err := json.Marshal(ins)
		assert.NoError(t, err)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{
					"users": (*json.RawMessage)(&data),
				},
				Hits: &elastic.SearchHits{},
			},
			nil).Reset()
		defer gomonkey.ApplyMethodReturn(
			elastic.SearchResult{}.Aggregations,
			"Terms",
			&elastic.AggregationBucketKeyItems{
				Buckets: []*elastic.AggregationBucketKeyItem{
					{
						Aggregations: map[string]*json.RawMessage{},
						Key:          "test_key",
						DocCount:     20,
					},
				},
			},
			true).Reset()
		rank := m_overviews.PocViewForOverview{
			Level: 0,
		}
		data2, err := json.Marshal(rank)
		assert.NoError(t, err)
		defer gomonkey.ApplyMethodReturn(
			elastic.Aggregations{},
			"TopHits",
			&elastic.AggregationTopHitsMetric{
				Hits: &elastic.SearchHits{
					Hits: []*elastic.SearchHit{
						{
							Source: (*json.RawMessage)(&data2),
						},
					},
				},
			},
			true).Reset()
		defer gomonkey.ApplyFuncReturn(
			es_asset.ConvertIpContent,
			[]elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

		pList, err := store.GetThreatNumberCount(ipRangeManager)
		assert.Equal(t, []m_overviews.NumberRank{{
			Count: 20, Level: "0", Name: "test_key",
		}}, pList)
		assert.Equal(t, nil, err)

	})
	t.Run("level 1", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)
		ipRangeManager := "testIpRangeManager"
		ins := m_threat.SystemBuckets{
			SystemBucket: []m_threat.SystemBucket{
				{
					Key:      "user",
					DocCount: 1,
				},
			},
		}
		data, err := json.Marshal(ins)
		assert.NoError(t, err)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{
					"users": (*json.RawMessage)(&data),
				},
				Hits: &elastic.SearchHits{},
			},
			nil).Reset()
		defer gomonkey.ApplyMethodReturn(
			elastic.SearchResult{}.Aggregations,
			"Terms",
			&elastic.AggregationBucketKeyItems{
				Buckets: []*elastic.AggregationBucketKeyItem{
					{
						Aggregations: map[string]*json.RawMessage{},
						Key:          "test_key",
						DocCount:     20,
					},
				},
			},
			true).Reset()
		rank := m_overviews.PocViewForOverview{
			Level: 1,
		}
		data2, err := json.Marshal(rank)
		assert.NoError(t, err)
		defer gomonkey.ApplyMethodReturn(
			elastic.Aggregations{},
			"TopHits",
			&elastic.AggregationTopHitsMetric{
				Hits: &elastic.SearchHits{
					Hits: []*elastic.SearchHit{
						{
							Source: (*json.RawMessage)(&data2),
						},
					},
				},
			},
			true).Reset()
		defer gomonkey.ApplyFuncReturn(
			es_asset.ConvertIpContent,
			[]elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

		pList, err := store.GetThreatNumberCount(ipRangeManager)
		assert.Equal(t, []m_overviews.NumberRank{{
			Count: 20, Level: "1", Name: "test_key",
		}}, pList)
		assert.Equal(t, nil, err)

	})
	t.Run("level 2", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)
		ipRangeManager := "testIpRangeManager"
		ins := m_threat.SystemBuckets{
			SystemBucket: []m_threat.SystemBucket{
				{
					Key:      "user",
					DocCount: 1,
				},
			},
		}
		data, err := json.Marshal(ins)
		assert.NoError(t, err)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{
					"users": (*json.RawMessage)(&data),
				},
				Hits: &elastic.SearchHits{},
			},
			nil).Reset()
		defer gomonkey.ApplyMethodReturn(
			elastic.SearchResult{}.Aggregations,
			"Terms",
			&elastic.AggregationBucketKeyItems{
				Buckets: []*elastic.AggregationBucketKeyItem{
					{
						Aggregations: map[string]*json.RawMessage{},
						Key:          "test_key",
						DocCount:     20,
					},
				},
			},
			true).Reset()
		rank := m_overviews.PocViewForOverview{
			Level: 2,
		}
		data2, err := json.Marshal(rank)
		assert.NoError(t, err)
		defer gomonkey.ApplyMethodReturn(
			elastic.Aggregations{},
			"TopHits",
			&elastic.AggregationTopHitsMetric{
				Hits: &elastic.SearchHits{
					Hits: []*elastic.SearchHit{
						{
							Source: (*json.RawMessage)(&data2),
						},
					},
				},
			},
			true).Reset()
		defer gomonkey.ApplyFuncReturn(
			es_asset.ConvertIpContent,
			[]elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

		pList, err := store.GetThreatNumberCount(ipRangeManager)
		assert.Equal(t, []m_overviews.NumberRank{{
			Count: 20, Level: "2", Name: "test_key",
		}}, pList)
		assert.Equal(t, nil, err)

	})
	t.Run("level 3", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)
		ipRangeManager := "testIpRangeManager"
		ins := m_threat.SystemBuckets{
			SystemBucket: []m_threat.SystemBucket{
				{
					Key:      "user",
					DocCount: 1,
				},
			},
		}
		data, err := json.Marshal(ins)
		assert.NoError(t, err)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{
					"users": (*json.RawMessage)(&data),
				},
				Hits: &elastic.SearchHits{},
			},
			nil).Reset()
		defer gomonkey.ApplyMethodReturn(
			elastic.SearchResult{}.Aggregations,
			"Terms",
			&elastic.AggregationBucketKeyItems{
				Buckets: []*elastic.AggregationBucketKeyItem{
					{
						Aggregations: map[string]*json.RawMessage{},
						Key:          "test_key",
						DocCount:     20,
					},
				},
			},
			true).Reset()
		rank := m_overviews.PocViewForOverview{
			Level: 3,
		}
		data2, err := json.Marshal(rank)
		assert.NoError(t, err)
		defer gomonkey.ApplyMethodReturn(
			elastic.Aggregations{},
			"TopHits",
			&elastic.AggregationTopHitsMetric{
				Hits: &elastic.SearchHits{
					Hits: []*elastic.SearchHit{
						{
							Source: (*json.RawMessage)(&data2),
						},
					},
				},
			},
			true).Reset()
		defer gomonkey.ApplyFuncReturn(
			es_asset.ConvertIpContent,
			[]elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

		pList, err := store.GetThreatNumberCount(ipRangeManager)
		assert.Equal(t, []m_overviews.NumberRank{{
			Count: 20, Level: "3", Name: "test_key",
		}}, pList)
		assert.Equal(t, nil, err)

	})
}

func TestStore_GetThreatRanking(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)
	ipRangeManager := "testIpRangeManager"
	ins := m_overviews.Aggregations{
		Buckets: []m_overviews.Buckets{
			{
				Key:      "test_threats",
				DocCount: 20,
			},
		},
	}
	data, err := json.Marshal(ins)
	assert.NoError(t, err)

	defer gomonkey.ApplyMethodReturn(
		store.Instance.Client.Search(),
		"Do",
		&elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"threats": (*json.RawMessage)(&data),
			},
			Hits: &elastic.SearchHits{},
		},
		nil).Reset()

	defer gomonkey.ApplyFuncReturn(
		es_asset.ConvertIpContent,
		[]elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

	pList, err := store.GetThreatRanking(ipRangeManager)
	assert.Equal(t, []m_overviews.RankingList{
		{
			Name:  "test_threats",
			Count: 20,
		},
	}, pList)
	assert.Equal(t, nil, err)
}

func TestStore_CountThreatTotal(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()
	t.Run("query err", func(t *testing.T) {
		defer gomonkey.ApplyMethodReturn(store.Instance.Client.Count(), "Do", int64(0), fmt.Errorf("mock err")).Reset()
		repaired := false
		ipRangeManager := "test_iprange"
		xcType := "0"
		count := store.CountThreatTotal(repaired, ipRangeManager, xcType)
		assert.Equal(t, 0, count)
	})
	t.Run("success case", func(t *testing.T) {
		defer gomonkey.ApplyMethodReturn(store.Instance.Client.Count(), "Do", int64(100), nil).Reset()
		repaired := true
		ipRangeManager := "test_iprange"
		xcType := "1"
		count := store.CountThreatTotal(repaired, ipRangeManager, xcType)
		assert.Equal(t, 100, count)
	})
}

func TestStore_GetThreatCountDataByTask(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

	data := []byte("{}")
	defer gomonkey.ApplyMethodReturn(
		store.Instance.Client.Search(),
		"Do",
		&elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"latest_val": (*json.RawMessage)(&data),
				"last_val":   (*json.RawMessage)(&data),
			},
			Hits: &elastic.SearchHits{},
		},
		nil).Reset()

	ctx := context.Background()
	latestTaskId := 1
	lastTaskId := 1
	cd, err := store.GetThreatCountDataByTask(ctx, latestTaskId, lastTaskId)
	assert.Equal(t, &m_threat.TaskThreatCountData{}, cd)
	assert.Equal(t, nil, err)

}

func TestStore_GetThreatNumberList(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

	ins := map[string]string{
		"ip":      "ip",
		"vulfile": "vulfile",
	}
	data, err := json.Marshal(ins)
	assert.NoError(t, err)
	idx := 1
	defer gomonkey.ApplyMethod(
		&elastic.ScrollService{},
		"Do",
		func(*elastic.ScrollService, context.Context) (*elastic.SearchResult, error) {
			if idx == 1 {
				idx++
				return &elastic.SearchResult{
					Hits: &elastic.SearchHits{
						Hits: []*elastic.SearchHit{
							{
								Source: (*json.RawMessage)(&data),
							},
						},
					},
				}, nil
			} else {
				return nil, io.EOF
			}
			return nil, nil
		}).Reset()

	ips := []string{"**********"}
	taskId := 1
	m, err := store.GetThreatVulFileByIps(ips, taskId)
	assert.Equal(t, map[string][]string{"ip": {"vulfile"}}, m)
	assert.Equal(t, nil, err)
}

func TestStore_GetThreatListMenu(t *testing.T) {
	t.Run("success case", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)
		ipRangeManager := "test_ipRangeManager"
		ctx := context.Background()

		verity_agg := m_overviews.Doc{DocCount: 20}
		verity_agg_data, err := json.Marshal(verity_agg)
		assert.NoError(t, err)

		level_agg := m_threat.Buckets{
			Buckets: []m_threat.Bucket{
				{
					Key:      1,
					DocCount: 20,
				},
			},
		}
		level_agg_data, err := json.Marshal(level_agg)
		assert.NoError(t, err)

		xc_agg := m_overviews.AggregationsPlus{
			Buckets: []m_overviews.BucketsPlus{
				{
					Key:      1,
					DocCount: 20,
				},
				{
					Key:      2,
					DocCount: 20,
				},
			},
		}
		xc_agg_data, err := json.Marshal(xc_agg)
		assert.NoError(t, err)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{
					"verity_agg":   (*json.RawMessage)(&verity_agg_data),
					"vul_type_agg": (*json.RawMessage)(&verity_agg_data),
					"level_agg":    (*json.RawMessage)(&level_agg_data),
					"xc_agg":       (*json.RawMessage)(&xc_agg_data),
				},
				Hits: &elastic.SearchHits{},
			},
			nil).Reset()

		defer gomonkey.ApplyFuncReturn(
			es_asset.ConvertIpContent,
			[]elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

		menu, err := store.GetThreatListMenu(ctx, ipRangeManager)
		assert.Equal(t, []m_overviews.MenuIndex{
			{Name: "EXP", Count: 20, Key: "verity", Value: "1"},
			{Name: "弱口令", Count: 20, Key: "vul_type", Value: "弱口令"},
			{Name: "严重", Count: 0, Key: "level", Value: "3"},
			{Name: "高危", Count: 0, Key: "level", Value: "2"},
			{Name: "中危", Count: 20, Key: "level", Value: "1"},
			{Name: "低危", Count: 0, Key: "level", Value: "0"},
			{Name: "xc", Count: 20, Key: "xc_type", Value: "1"},
			{Name: "nxc", Count: 20, Key: "xc_type", Value: "2"},
		}, menu)
		assert.Equal(t, nil, err)
	})
	t.Run("xc_agg data not found", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)
		ipRangeManager := "test_ipRangeManager"
		ctx := context.Background()

		verity_agg := m_overviews.Doc{DocCount: 20}
		verity_agg_data, err := json.Marshal(verity_agg)
		assert.NoError(t, err)

		level_agg := m_threat.Buckets{
			Buckets: []m_threat.Bucket{
				{
					Key:      1,
					DocCount: 20,
				},
			},
		}
		level_agg_data, err := json.Marshal(level_agg)
		assert.NoError(t, err)

		xc_agg := m_overviews.AggregationsPlus{
			Buckets: []m_overviews.BucketsPlus{},
		}
		xc_agg_data, err := json.Marshal(xc_agg)
		assert.NoError(t, err)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{
					"verity_agg":   (*json.RawMessage)(&verity_agg_data),
					"vul_type_agg": (*json.RawMessage)(&verity_agg_data),
					"level_agg":    (*json.RawMessage)(&level_agg_data),
					"xc_agg":       (*json.RawMessage)(&xc_agg_data),
				},
				Hits: &elastic.SearchHits{},
			},
			nil).Reset()

		defer gomonkey.ApplyFuncReturn(
			es_asset.ConvertIpContent,
			[]elastic.Query{elastic.NewTermsQuery("ip", "**********")}).Reset()

		menu, err := store.GetThreatListMenu(ctx, ipRangeManager)
		assert.Equal(t, []m_overviews.MenuIndex{
			{Name: "EXP", Count: 20, Key: "verity", Value: "1"},
			{Name: "弱口令", Count: 20, Key: "vul_type", Value: "弱口令"},
			{Name: "严重", Count: 0, Key: "level", Value: "3"},
			{Name: "高危", Count: 0, Key: "level", Value: "2"},
			{Name: "中危", Count: 20, Key: "level", Value: "1"},
			{Name: "低危", Count: 0, Key: "level", Value: "0"},
			{Name: "nxc", Count: 0, Key: "xc_type", Value: "0"},
			{Name: "xc", Count: 0, Key: "xc_type", Value: "1"},
		}, menu)
		assert.Equal(t, nil, err)
	})
}

func TestStore_GetEXP(t *testing.T) {
	t.Run("success case", func(t *testing.T) {
		agg := []*m_threat.Bucket{{Key: 0, DocCount: 1}, {Key: 1, DocCount: 1}}
		result := GetEXP(agg)
		assert.Len(t, result, 2)

		excepts := []*m_threat.SystemPreset{{Key: 0, Value: "不支持"}, {Key: 1, Value: "支持"}}

		for i, obj := range excepts {
			assert.Equal(t, obj.Key, result[i].Key)
			assert.Equal(t, obj.Value, result[i].Value)
		}
	})
}

func TestStore_CalCustomFields(t *testing.T) {
	t.Run("all case", func(t *testing.T) {
		agg := []*m_threat.SystemBucket{{Key: "0", DocCount: 1}, {Key: "1", DocCount: 1}}

		response := m_threat.AdvancedScreenResponse{}
		v := m_tag.Tag{Name: "张三", Realname: "zhangsan"}

		CalCustomFields(agg, &response, &v)

		assert.Equal(t, response.CustomFields[0].Key, v.Realname)
		assert.Equal(t, response.CustomFields[0].Value, v.Name)
		assert.Equal(t, response.CustomFields[0].List, []string{"0", "1"})
	})
}

func TestStore_CalLevelCount(t *testing.T) {
	t.Run("all case", func(t *testing.T) {
		agg := []*m_threat.Bucket{
			{Key: 0, DocCount: 1},
			{Key: 1, DocCount: 1},
			{Key: 2, DocCount: 1},
			{Key: 3, DocCount: 1},
		}

		result := CalLevelCount(agg)

		assert.Len(t, result, 4)
		for _, obj := range result {
			assert.Contains(t, []int{0, 1, 2, 3}, obj.Key)
			assert.Contains(t, []string{"低危", "中危", "高危", "严重"}, obj.Value)
		}
	})
}

func TestStore_extractValues(t *testing.T) {
	t.Run("all case", func(t *testing.T) {
		agg := []*m_threat.SystemBucket{
			{Key: "0", DocCount: 1},
			{Key: "1", DocCount: 1},
		}

		result := extractValues(agg)

		assert.Len(t, result, 2)
		assert.Equal(t, []string{"0", "1"}, result)
	})
}

func TestStore_StatusInfos(t *testing.T) {
	agg := []*m_threat.Bucket{
		{Key: 1, DocCount: 1},
		{Key: 2, DocCount: 1},
		{Key: 3, DocCount: 1},
		{Key: 4, DocCount: 1},
	}

	result := StatusInfos(agg)

	kv := map[int]string{
		10: "待处理",
		1:  "待核查",
		2:  "误报",
		3:  "接受风险",
		4:  "其他",
	}
	for _, r := range result {
		assert.Equal(t, kv[r.Key], r.Value)
	}
}
