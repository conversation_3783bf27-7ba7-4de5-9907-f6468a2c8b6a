package esi_threats

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	"git.gobies.org/foeye-dependencies/mosso"

	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/responses/r_threat"
)

func (store *Store) GetThreatByManagerEmailNew(ctx context.Context, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool) (interface{}, []string, error) {
	boolQuery := newBoolQuery(QL)
	boolQuery.MustNot(elastic.NewTermQuery("manager_email", ""))
	data := make(map[string][]map[string]interface{})
	noticeIds := make([]string, 0)
	fields := elastic.NewSearchSource().FetchSourceIncludeExclude(defaultFields, nil)
	search := store.Instance.Client.
		Scroll().
		Scroll(elasticx.DefaultScrollKeepAlive).
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(fields).
		Pretty(true).
		Query(boolQuery).
		Size(elasticx.DefaultScrollQueryBulkSize)
	mosso.DebugShowContentWithJSON(boolQuery)
	result, err := search.Do(ctx)

	if err != nil || err == io.EOF {
		return data, noticeIds, err
	}

	for {
		if result.Hits == nil || result.Hits.Hits == nil || len(result.Hits.Hits) == 0 {
			break
		}
		for _, hit := range result.Hits.Hits {
			if hit.Source == nil {
				continue
			}

			temp := map[string]interface{}{}
			if err = json.Unmarshal(*hit.Source, &temp); err != nil {
				continue
			}
			email := fmt.Sprintf("%v", temp["manager_email"])
			noticeIds = append(noticeIds, hit.Id)
			data[email] = append(data[email], temp)
		}

		// 如果没有更多的数据需要滚动查询，则退出循环
		if result.ScrollId == "" {
			break
		}

		result, err = search.ScrollId(result.ScrollId).Do(ctx)
		if err != nil {
			break
		}
	}
	_, err = store.Instance.Client.ClearScroll(result.ScrollId).Do(ctx)
	if err != nil {
		return nil, nil, err
	}

	var datum = make([][]map[string]interface{}, 0)
	for _, temp := range data {
		datum = append(datum, temp)
	}
	return datum, noticeIds, nil
}

func (store *Store) GetThreatByManagerEmailCount(ctx context.Context, QL *r_threat.QueryListAndKeyword) (int, error) {
	var count int
	boolQuery := newBoolQuery(QL)
	boolQuery.MustNot(elastic.NewTermQuery("manager_email", ""))
	searchResult, err := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(boolQuery).
		Size(0).
		Aggregation("manager_email", elastic.NewTermsAggregation().Field("manager_email")).
		Pretty(true).Do(ctx)

	if err != nil {
		return count, err
	}
	items, isExists := searchResult.Aggregations.Terms("manager_email")
	if isExists {
		count = len(items.Buckets)
	}
	return count, nil
}
