package esi_assets

import (
	"context"
	"encoding/json"
	"strings"

	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/olivere/elastic"
)

func (store *Store) GetAssetDetail(ctx context.Context, item *m_asset.AssetDetails) (*m_asset.ResponseAssetDetail, error) {
	boolQuery := elastic.NewBoolQuery()
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude(defaultIpFields, nil)

	if item.Id != "" {
		boolQuery.Must(elastic.NewTermQuery("ip", item.Id))
		store.IndexName = elasticx.IndexNameOfAsset()
	} else if item.TaskId != "" {
		if strings.Contains(item.TaskId, "_") {
			str := strings.Split(item.TaskId, "_")
			if len(str) == 2 && str[0] != "" && str[1] != "" {
				boolQuery.Must(elastic.NewTermQuery("task_id", str[0]))
				boolQuery.Must(elastic.NewTermQuery("ip", str[1]))
			}
		}
		store.IndexName = elasticx.IndexNameOfTaskAsset()
	}

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(searchSource).
		Query(boolQuery).
		Pretty(true)

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, err
	}

	return aggDetails(result)
}

func aggDetails(sr *elastic.SearchResult) (*m_asset.ResponseAssetDetail, error) {
	instance := new(m_asset.ResponseAssetDetail)
	for _, hit := range sr.Hits.Hits {
		temp := new(m_asset.EsResultRuleInfos)
		if err := json.Unmarshal(*hit.Source, &temp); err != nil {
			continue
		}

		for _, v := range temp.RuleInfos {
			if v.SoftHardCode == 1 {
				instance.Asset.HardTypeName = v.SecondCatTag
				break
			}
		}

		for _, v := range temp.TitleList {
			if v.Title != "" {
				instance.Asset.WebsiteTitle = v.Title
				break
			}
		}

		item := new(m_asset.AssetListInfo)
		if err := json.Unmarshal(*hit.Source, &item); err != nil {
			continue
		}

		for _, v := range temp.PortList {
			if v.Protocol == "rdp" {
				item.RdpImage = v.RdpImage
				break
			}
		}

		websiteMap := make(map[int]string, 0)
		for _, title := range temp.TitleList {
			if title.WebsiteImage != "" {
				websiteMap[title.Port] = title.WebsiteImage
			}
		}

		convertInstanceToRuleInfo(temp, item, nil)
		instance.RuleInfos = item.RuleInfos

		info := new(m_asset.AssetIpListInfo)
		if err := json.Unmarshal(*hit.Source, &info); err != nil {
			continue
		}
		convertInstanceToPortInfo(temp, info)

		instance.PortList = info.PortList
		for _, port := range instance.PortList {
			if image, ok := websiteMap[port.Port]; ok {
				port.WebsiteImage = image
			}
		}

		if item.Province == CountryOfLocalAreaNetwork {
			item.Province = ""
		}
		instance.Asset.IP = info.IP
		instance.Asset.AssetLevel = info.AssetLevel
		instance.Asset.Mac = item.Mac
		instance.Asset.ComputerRoom = item.ComputerRoom
		instance.Asset.BusinessApp = item.BusinessApp
		instance.Asset.Company = item.Company
		instance.Asset.City = item.City
		instance.Asset.Province = item.Province
		instance.Asset.Os = info.Os
		instance.Asset.Hosts = temp.Hosts
		instance.Asset.Name = item.Name
		instance.Asset.Username = item.Username
		instance.Asset.RdpImage = item.RdpImage
		instance.Asset.Hostname = item.Hostname
		instance.Asset.CustomFields = info.CustomFields
	}
	return instance, nil
}
