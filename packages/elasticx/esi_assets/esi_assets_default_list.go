package esi_assets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye-dependencies/address"
	"git.gobies.org/foeye-dependencies/logger"
	"git.gobies.org/fofa-backend/fofacore"

	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_threats"
	"git.gobies.org/foeye/foeye3/responses/r_asset"
)

// GetListByDefault get list by default.
func (store *Store) GetListByDefault(ctx context.Context, QL *r_asset.QueryListAndKeyword, ipRangeManager string) ([]*m_asset.AssetListInfo, int64, error) {
	boolQuery := newBoolQuery(QL)
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude(defaultFields, nil)

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	if QL.TaskID > 0 {
		store.IndexName = elasticx.IndexNameOfTaskAsset()
	} else {
		store.IndexName = elasticx.IndexNameOfAsset()
	}

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(searchSource).
		Query(boolQuery).
		From((QL.Number-1)*QL.Size).
		Size(QL.Size).
		Pretty(true).
		SortBy(elastic.NewScriptSort(elastic.NewScript("doc['rule_tags'].values.size()"), "number").Desc()).
		Sort("lastupdatetime", false)

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, 0, err
	}

	return AggAssetDefault(result, QL), result.Hits.TotalHits, nil
}

// AggAssetDefault agg asset default.
func AggAssetDefault(sr *elastic.SearchResult, QL *r_asset.QueryListAndKeyword) []*m_asset.AssetListInfo {
	data := make([]*m_asset.AssetListInfo, 0)
	imageMap := make(map[string]struct{}, 0)
	for _, image := range imageBackground {
		imageMap[image] = struct{}{}
	}
	for _, hit := range sr.Hits.Hits {
		temp := new(m_asset.EsResultRuleInfos)
		if err := json.Unmarshal(*hit.Source, &temp); err != nil {
			continue
		}

		instance := new(m_asset.AssetListInfo)
		if err := json.Unmarshal(*hit.Source, &instance); err != nil {
			continue
		}

		for _, v := range temp.PortList {
			if v.Protocol == "rdp" {
				instance.RdpImage = v.RdpImage
				break
			}
		}

		for _, v := range temp.TitleList {
			if v.WebsiteImage != "" {
				instance.WebsiteImage = v.WebsiteImage
				break
			}
		}

		for _, v := range temp.TitleList {
			if v.Title != "" {
				instance.WebsiteTitle = v.Title
				break
			}
		}

		instance.ID = hit.Id
		instance.UID = instance.IP
		convertInstanceToRuleInfo(temp, instance, imageMap)

		if QL.TaskID > 0 {
			instance.ThreatStatus = nil
		} else {
			instance.ThreatStatus = calculateAssetRisk(temp, QL.NoStdPorts)
		}
		data = append(data, instance)
	}
	return data
}

// convertInstanceToRuleInfo convert instance to rule info.
func convertInstanceToRuleInfo(temp *m_asset.EsResultRuleInfos, instance *m_asset.AssetListInfo, imageMap map[string]struct{}) {
	instance.RuleInfos = nil
	strMap := make(map[int][]*m_asset.RuleDesc, 0)
	for _, v := range temp.RuleInfos {
		if v.Title != "" {
			var str string
			if v.SoftHardCode == 2 {
				str = fmt.Sprintf("%s / %s / %s\n\n%s", "软件", v.FirstCatTag, v.SecondCatTag, v.Company)
			} else if v.SoftHardCode == 1 {
				str = fmt.Sprintf("%s / %s / %s\n\n%s", "硬件", v.FirstCatTag, v.SecondCatTag, v.Company)
			} else {
				str = fmt.Sprintf("%s / %s / %s\n\n%s", "其他", v.FirstCatTag, v.SecondCatTag, v.Company)
			}

			if v.Version != "" && v.Version != "0" {
				strMap[v.LevelCode] = append(strMap[v.LevelCode], &m_asset.RuleDesc{
					Icon:        v.Title,
					Name:        v.Title + "/" + v.Version,
					Description: str,
					IsXc:        v.IsXc,
					Version:     v.Version,
				})
			} else {
				strMap[v.LevelCode] = append(strMap[v.LevelCode], &m_asset.RuleDesc{
					Icon:        v.Title,
					Name:        v.Title,
					Description: str,
					IsXc:        v.IsXc,
					Version:     v.Version,
				})
			}

			if instance.IconBackground != "" {
				continue
			}

			if v.FirstCatTag == "安全产品" {
				instance.IconBackground = "安全产品"
			} else if _, ok := imageMap[v.SecondCatTag]; ok {
				instance.IconBackground = v.SecondCatTag
			}
		}
	}

	for index, value := range strMap {
		switch index {
		case 5:
			instance.RuleInfos = append(instance.RuleInfos, &m_asset.RuleInfos{
				Key:      business,
				RuleDesc: value,
				Sort:     5,
			})
		case 4:
			instance.RuleInfos = append(instance.RuleInfos, &m_asset.RuleInfos{
				Key:      support,
				RuleDesc: value,
				Sort:     4,
			})
		case 3:
			instance.RuleInfos = append(instance.RuleInfos, &m_asset.RuleInfos{
				Key:      service,
				RuleDesc: value,
				Sort:     3,
			})
		case 2:
			instance.RuleInfos = append(instance.RuleInfos, &m_asset.RuleInfos{
				Key:      system,
				RuleDesc: value,
				Sort:     2,
			})
		case 1:
			instance.RuleInfos = append(instance.RuleInfos, &m_asset.RuleInfos{
				Key:      hard,
				RuleDesc: value,
				Sort:     1,
			})
		}
	}

	if len(instance.RuleInfos) < 5 {
		tempRule := make(map[string]struct{}, 0)
		for _, v := range instance.RuleInfos {
			tempRule[v.Key] = struct{}{}
		}

		for i, v := range [5]string{hard, system, service, support, business} {
			if _, ok := tempRule[v]; !ok {
				instance.RuleInfos = append(instance.RuleInfos, &m_asset.RuleInfos{
					Key:      v,
					RuleDesc: nil,
					Sort:     i + 1,
				})
			}
		}
	}

	sort.SliceStable(instance.RuleInfos, func(i, j int) bool {
		return instance.RuleInfos[i].Sort > instance.RuleInfos[j].Sort
	})
}

// GetListByDefaultIp get list by default ip.
func (store *Store) GetListByDefaultIp(ctx context.Context, QL *r_asset.QueryListAndKeyword, ipRangeManager string) ([]*m_asset.AssetIpListInfo, int64, error) {
	boolQuery := newBoolQuery(QL)
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude(defaultIpFields, nil)

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	if QL.TaskID > 0 {
		store.IndexName = elasticx.IndexNameOfTaskAsset()
	} else {
		store.IndexName = elasticx.IndexNameOfAsset()
	}

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(searchSource).
		Query(boolQuery).
		From((QL.Number-1)*QL.Size).
		Size(QL.Size).
		Pretty(true).
		SortBy(elastic.NewScriptSort(elastic.NewScript("doc['rule_tags'].values.size()"), "number").Desc()).
		Sort("lastupdatetime", false)

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, 0, err
	}
	return aggregationByIp(result, QL), result.Hits.TotalHits, nil
}

// aggregationByIp aggregation by ip.
func aggregationByIp(sr *elastic.SearchResult, QL *r_asset.QueryListAndKeyword) []*m_asset.AssetIpListInfo {
	data := make([]*m_asset.AssetIpListInfo, 0)
	for _, hit := range sr.Hits.Hits {
		temp := new(m_asset.EsResultRuleInfos)
		if err := json.Unmarshal(*hit.Source, &temp); err != nil {
			continue
		}

		instance := new(m_asset.AssetIpListInfo)
		if err := json.Unmarshal(*hit.Source, &instance); err != nil {
			continue
		}
		instance.ID = hit.Id
		instance.Domain = temp.Hosts
		instance.UID = instance.IP
		for _, v := range temp.PortList {
			if v.Protocol == "rdp" {
				instance.RdpImage = v.RdpImage
				break
			}
		}

		for _, v := range temp.TitleList {
			if v.WebsiteImage != "" {
				instance.WebsiteImage = v.WebsiteImage
				break
			}
		}

		for _, v := range temp.TitleList {
			if v.Title != "" {
				instance.WebsiteTitle = v.Title
				break
			}
		}

		for _, v := range instance.PortList {
			v.Banner = ""
			v.Certs = ""
		}

		convertInstanceToPortInfo(temp, instance)

		if QL.TaskID > 0 {
			instance.ThreatStatus = nil
		} else {
			instance.ThreatStatus = calculateAssetRisk(temp, QL.NoStdPorts)
		}
		data = append(data, instance)
	}

	data = sliceLinkUrl(data)

	return data
}

// convertInstanceToPortInfo convert instance to port info
func convertInstanceToPortInfo(temp *m_asset.EsResultRuleInfos, instance *m_asset.AssetIpListInfo) {
	strMap := make(map[int][]*m_asset.RuleDesc, 0)
	var os string
	for _, v := range temp.RuleInfos {
		if v.Title != "" {
			for _, port := range v.Ports {
				database := ""
				if v.SecondCatTag == "数据库系统" {
					database = v.Title
				}
				if v.RuleVersion != nil && v.RuleVersion[fmt.Sprintf("%d", port)] != "" && v.RuleVersion[fmt.Sprintf("%d", port)] != "0" {
					strMap[port] = append(strMap[port], &m_asset.RuleDesc{
						Icon:        v.Title,
						Name:        v.Title + "/" + v.RuleVersion[fmt.Sprintf("%d", port)],
						Description: fmt.Sprintf("%s|%s", v.SecondCatTag, v.FirstCatTag),
						IsXc:        v.IsXc,
						IsVersion:   true,
						LevelCode:   v.LevelCode,
						Database:    database,
					})
				} else {
					strMap[port] = append(strMap[port], &m_asset.RuleDesc{
						Icon:        v.Title,
						Name:        v.Title,
						Description: fmt.Sprintf("%s|%s", v.SecondCatTag, v.FirstCatTag),
						IsXc:        v.IsXc,
						LevelCode:   v.LevelCode,
						Database:    database,
					})
				}
			}
			if v.SecondCatTag == "操作系统" {
				os = v.Title
			}
		}
	}

	titleMap := make(map[int]string, 0)
	hostMap := make(map[int]string, 0)
	for _, v := range temp.TitleList {
		titleMap[v.Port] = v.Title
		hostMap[v.Port] = v.Host
	}

	for _, v := range instance.PortList {
		if _, ok := strMap[v.Port]; ok {
			v.RuleInfo = strMap[v.Port]
		}

		if _, ok := titleMap[v.Port]; ok {
			v.Title = titleMap[v.Port]
		}

		if _, ok := hostMap[v.Port]; ok {
			v.Host = hostMap[v.Port]
		}
	}

	if temp.OsVersion != "" {
		str := strings.Split(temp.OsVersion, "/")
		if len(str) == 2 && str[0] != "" && str[1] != "" && str[1] != "0" {
			instance.Os = temp.OsVersion
		}
	}

	if instance.Os == "" {
		instance.Os = os
	}

	sort.SliceStable(instance.PortList, func(i, j int) bool {
		return len(instance.PortList[i].RuleInfo) > len(instance.PortList[j].RuleInfo)
	})
}

// calculateAssetRisk calculate asset risk.
func calculateAssetRisk(temp *m_asset.EsResultRuleInfos, ports []string) *m_asset.ThreatStatus {
	instance := new(m_asset.ThreatStatus)
	ruleMap := SetRuleMap(temp, instance)

	protocol := make(map[string]struct{}, 0)
	for _, v := range publicRiskyProtocols {
		protocol[v] = struct{}{}
	}

	// 高危端口及服务
	SetHighRiskPort(temp, protocol, instance, ruleMap)

	// 重要漏洞
	SetVulnerability(temp, instance)

	portMaps := make(map[string]struct{}, 0)
	for _, v := range ports {
		portMaps[v] = struct{}{}
	}

	// 非标端口
	SetNonStandardPort(temp, portMaps, instance)

	if temp.RuleTags != nil {
		for _, tag := range temp.RuleTags {
			// 集成工具
			for _, v := range integrationTools {
				if tag == v && v != "" {
					instance.IntegrationTools = append(instance.IntegrationTools, v)
				}
			}
			// 扫描器
			for _, v := range scanners {
				if tag == v && v != "" {
					instance.Scanners = append(instance.Scanners, v)
				}
			}
			// 远程运维
			for _, v := range remoteOps {
				if tag == v && v != "" {
					instance.RemoteOps = append(instance.RemoteOps, v)
				}
			}
		}
	}

	if len(instance.RiskyPortAndServer) < 1 {
		instance.RiskyPortAndServer = nil
	} else {
		sort.SliceStable(instance.RiskyPortAndServer, func(i, j int) bool {
			return instance.RiskyPortAndServer[i].Key < instance.RiskyPortAndServer[j].Key
		})
	}

	return instance
}

func SetNonStandardPort(temp *m_asset.EsResultRuleInfos, portMaps map[string]struct{}, instance *m_asset.ThreatStatus) {
	if temp.PortList != nil {
		for _, v := range temp.PortList {
			if v.Protocol != "" {
				if _, ok := portMaps[strconv.Itoa(v.Port)]; ok {
					instance.NoStdPorts = append(instance.NoStdPorts, &m_asset.NoStdPorts{
						Key:   v.Port,
						Value: v.Protocol,
					})
				}
			}
		}
	}
}

func SetVulnerability(temp *m_asset.EsResultRuleInfos, instance *m_asset.ThreatStatus) {
	if temp.Vuls != nil {
		for _, v := range temp.Vuls {
			if v.Name != "" {
				instance.ThreatIp = append(instance.ThreatIp, &m_asset.ThreatIp{
					Name:     v.Name,
					Level:    v.Level,
					Filename: v.Filename,
				})
			}
		}
	}
}

func SetHighRiskPort(temp *m_asset.EsResultRuleInfos, protocol map[string]struct{}, instance *m_asset.ThreatStatus, ruleMap map[int]string) {
	for _, v := range temp.PortList {
		if _, ok := protocol[v.Protocol]; ok {
			instance.RiskyPortAndServer = append(instance.RiskyPortAndServer, &m_asset.NoStdPorts{
				Key:   v.Port,
				Value: v.Protocol,
			})
		} else if v.Protocol == "http" || v.Protocol == "https" {
			for _, rule := range publicHttpRuleNames {
				if _, ok = ruleMap[v.Port]; ok && ruleMap[v.Port] == rule {
					instance.RiskyPortAndServer = append(instance.RiskyPortAndServer, &m_asset.NoStdPorts{
						Key:   v.Port,
						Value: v.Protocol,
					})
				}
			}
		}
	}
}

func SetRuleMap(temp *m_asset.EsResultRuleInfos, instance *m_asset.ThreatStatus) map[int]string {
	ruleMap := make(map[int]string, 0)
	for _, v := range temp.RuleInfos {
		if v.Title != "" {
			for _, port := range v.Ports {
				ruleMap[port] = v.Title
			}

			// 代理软件
			for _, proxy := range proxySoftWares {
				if v.Title == proxy && proxy != "" {
					instance.ProxySoftWare = append(instance.ProxySoftWare, proxy)
				}
			}
		}
	}
	return ruleMap
}

// GetAssetAdvancedScreen get asset advanced screen.
func (store *Store) GetAssetAdvancedScreen(ctx context.Context, tags []*m_tag.Tag, QL *r_asset.QueryListAndKeyword, ipRangeManager string) (interface{}, error) {
	boolQuery := newBoolQuery(QL)

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Pretty(true).
		Query(boolQuery).
		Size(0).
		Aggregation("add_way", elastic.NewTermsAggregation().Field("add_way").Size(MaxCountLimit)).
		Aggregation("rule_infos.title", elastic.NewTermsAggregation().Field("rule_infos.title").Size(MaxCountLimit)).
		Aggregation("is_ipv6", elastic.NewTermsAggregation().Field("is_ipv6").Size(MaxCountLimit)).
		Aggregation("state", elastic.NewTermsAggregation().Field("state").Size(MaxCountLimit)).
		Aggregation("ports", elastic.NewTermsAggregation().Field("ports").Size(65535)).
		Aggregation("protocols", elastic.NewTermsAggregation().Field("protocols").Size(MaxCountLimit)).
		Aggregation("rule_infos.company", elastic.NewTermsAggregation().Field("rule_infos.company").Size(MaxCountLimit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfComputerRoom, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfComputerRoom).Size(MaxCountLimit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfResponsiblePerson, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfResponsiblePerson).Size(MaxCountLimit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfCompany, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfCompany).Size(MaxCountLimit)).
		Aggregation(m_tag.SystemPresetTagCategoryOfBusinessApp, elastic.NewTermsAggregation().Field(m_tag.SystemPresetTagCategoryOfBusinessApp).Size(MaxCountLimit))

	for _, v := range tags {
		temp := elastic.NewTermsAggregation().Field("custom_fields." + v.Realname).Size(100000)
		serv.Aggregation("custom_fields."+v.Realname, temp)
	}

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, err
	}
	// mosso.DebugShowContentWithJSON(result)
	return aggregationAdvancedScreen(result, tags), nil
}

// aggregationAdvancedScreen aggregation advanced screen.
func aggregationAdvancedScreen(sr *elastic.SearchResult, tags []*m_tag.Tag) *m_asset.AdvancedScreenResponse {
	sets := []string{
		"add_way",
		"rule_infos.title",
		"is_ipv6",
		"state",
		"ports",
		"protocols",
		"rule_infos.company",
		m_tag.SystemPresetTagCategoryOfResponsiblePerson,
		m_tag.SystemPresetTagCategoryOfCompany,
		m_tag.SystemPresetTagCategoryOfComputerRoom,
		m_tag.SystemPresetTagCategoryOfBusinessApp,
	}

	for _, v := range tags {
		sets = append(sets, "custom_fields."+v.Realname)
	}

	response := new(m_asset.AdvancedScreenResponse)
	for _, set := range sets {
		terms, find := sr.Aggregations.Terms(set)
		if find {
			handleAggs(terms, set, response, tags)
		}
	}
	if len(response.AddWay) == 2 {
		response.AddWay = append(response.AddWay, &m_asset.SystemPreset{
			Key:   "self_defined,flow",
			Value: "全部",
		})
	}

	if len(response.State) == 2 {
		response.State = append(response.State, &m_asset.SystemPreset{
			Key:   "0,1",
			Value: "全部",
		})
	}

	if len(response.IpType) == 2 {
		response.IpType = append(response.IpType, &m_asset.SystemPreset{
			Key:   "true,false",
			Value: "全部",
		})
	}
	return response
}

func handleAggs(terms *elastic.AggregationBucketKeyItems, set string, response *m_asset.AdvancedScreenResponse, tags []*m_tag.Tag) {
	for _, bucket := range terms.Aggregations {
		switch set {
		case "add_way":
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.AddWay = GetAddWay(agg)

		case "state":
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.State = GeState(agg)

		case "is_ipv6":
			agg := make([]*m_asset.SystemBucketBool, 0)
			marshalJSON, err := bucket.MarshalJSON()
			if err != nil {
				continue
			}
			if err = json.Unmarshal(marshalJSON, &agg); err != nil {
				continue
			}

			response.IpType = SetIpType(agg)

		case "ports":
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.Ports = extractValues(agg)

		case "protocols":
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.Protocols = extractValues(agg)

		case "rule_infos.company":
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.CompanyTitle = extractValues(agg)

		case "rule_infos.title":
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.Rules = extractValues(agg)

		case m_tag.SystemPresetTagCategoryOfResponsiblePerson:
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.Username = extractValues(agg)

		case m_tag.SystemPresetTagCategoryOfCompany:
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.Company = extractValues(agg)

		case m_tag.SystemPresetTagCategoryOfComputerRoom:
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.ComputerRoom = extractValues(agg)

		case m_tag.SystemPresetTagCategoryOfBusinessApp:
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			response.BusinessApp = extractValues(agg)

		default:
			for _, v := range tags {
				if strings.Contains(set, v.Realname) {
					agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
					if err != nil {
						continue
					}
					temp := extractValues(agg)
					if len(temp) > 0 {
						response.CustomFields = append(response.CustomFields, &m_asset.CustomFields{
							Key:   v.Realname,
							Value: v.Name,
							List:  temp,
						})
					}
				}
			}
		}
	}
}

func SetIpType(agg []*m_asset.SystemBucketBool) []*m_asset.SystemPreset {
	ipTypes := []*m_asset.SystemPreset{}
	for _, v := range agg {
		if v.KeyAsString == "false" && v.DocCount > 0 {
			ipTypes = append(ipTypes, &m_asset.SystemPreset{
				Key:   "false",
				Value: "IPv4",
			})
		} else if v.KeyAsString == "true" && v.DocCount > 0 {
			ipTypes = append(ipTypes, &m_asset.SystemPreset{
				Key:   "true",
				Value: "IPv6",
			})
		}
	}
	return ipTypes
}

func GeState(agg []*m_threat.SystemBucket) []*m_asset.SystemPreset {
	states := []*m_asset.SystemPreset{}
	for _, v := range agg {
		if v.Key == "0" && v.DocCount > 0 {
			states = append(states, &m_asset.SystemPreset{
				Key:   "0",
				Value: "离线",
			})
		} else if v.Key == "1" && v.DocCount > 0 {
			states = append(states, &m_asset.SystemPreset{
				Key:   "1",
				Value: "在线",
			})
		}
	}
	return states
}

func GetAddWay(agg []*m_threat.SystemBucket) []*m_asset.SystemPreset {
	addWay := []*m_asset.SystemPreset{}
	for _, v := range agg {
		if v.Key == "self_defined" && v.DocCount > 0 {
			addWay = append(addWay, &m_asset.SystemPreset{
				Key:   "self_defined",
				Value: "主动探测",
			})
		} else if v.Key == "flow" && v.DocCount > 0 {
			addWay = append(addWay, &m_asset.SystemPreset{
				Key:   "flow",
				Value: "流量发现",
			})
		}
	}
	return addWay
}

func extractValues(agg []*m_threat.SystemBucket) []string {
	var result []string
	for _, v := range agg {
		if v.Key != "" && v.DocCount > 0 {
			result = append(result, v.Key)
		}
	}
	return result
}

// GetDomainList get domain list.
func (store *Store) GetDomainList(ctx context.Context, QL *r_asset.QueryListAndKeyword, ipRangeManager string) ([][]*m_asset.ResultDomainData, int64, error) {
	boolQuery := newBoolQuery(QL)

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude(defaultDomainFields, nil)
	temp := elastic.NewTopHitsAggregation().
		SearchSource(searchSource).
		Size(50).
		Sort("lastupdatetime", false)

	ipAggregation := elastic.NewTermsAggregation().Field("hosts")
	ipAggregation.SubAggregation("child_domain_view", temp).Size(maxCountLimit)

	serv := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Pretty(true).
		Query(boolQuery).
		Size(0).
		Aggregation("domain_view", ipAggregation)

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, 0, err
	}

	return aggDomainList(result, QL)
}

func (store *Store) GetNewDomainList(ctx context.Context, QL *r_asset.QueryDomainList, ipRangeManager string) ([]m_asset.ResultNewDomainData, int64, error) {
	// 用于存储查询结果的结构体列表
	var domains []m_asset.ResultNewDomainData
	boolQuery := elastic.NewBoolQuery()
	if len(QL.Ids) == 0 || (len(QL.Ids) == 1 && QL.Ids[0] == "") {
		boolQuery = NewDomainBoolQuery(QL)
	} else {
		r := stringSliceToInterface(QL.Ids)
		boolQuery.Must(elastic.NewTermsQuery("_id", r...))
	}

	// 计算分页参数
	from := (QL.Number - 1) * QL.Size // 起始位置
	if from < 0 {
		from = 0 // 防止出现负数
	}

	//执行搜索查询
	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfDomainAsset()). // 指定索引
		Query(boolQuery). // 查询条件
		Sort("lastupdatetime", false). // 按 lastupdatetime 倒序排序
		From(from). // 分页起始位置
		Size(QL.Size). // 每页大小
		Do(ctx) // 执行查询
	if err != nil {
		return nil, 0, err
	}

	// 获取总文档数
	totalCount := searchResult.TotalHits()

	// 遍历搜索结果并解析文档
	for _, hit := range searchResult.Hits.Hits {
		var domain m_asset.ResultNewDomainData
		source, _ := json.Marshal(&hit.Source)
		if err := json.Unmarshal(source, &domain); err != nil {
			return nil, 0, err
		}
		for i := 0; i < len(domain.ResolutionInfo); i++ {
			if address.IsDomain(domain.ResolutionInfo[i].IP, false) {
				domain.ResolutionInfo[i].IsIp = 1
			} else {
				domain.ResolutionInfo[i].IsIp = 0
			}
		}
		domains = append(domains, domain)
	}

	return domains, totalCount, nil
}

func aggDomainList(result *elastic.SearchResult, QL *r_asset.QueryListAndKeyword) ([][]*m_asset.ResultDomainData, int64, error) {
	var total int64
	data := make([][]*m_asset.ResultDomainData, 0)

	if result != nil && result.Aggregations != nil {
		agg, find := result.Aggregations.Terms("domain_view")
		if find {
			total = int64(len(agg.Buckets))
			buckets := agg.Buckets
			if total > int64(QL.Size) {
				beginIndex := 0
				endIndex := QL.Size
				if QL.Number > 0 {
					beginIndex = (QL.Number - 1) * QL.Size
					endIndex = (QL.Number) * QL.Size
					if endIndex > int(total) {
						endIndex = int(total)
					}
				}
				buckets = buckets[beginIndex:endIndex]
			}

			for _, bucket := range buckets {
				if bucket.Key == "" || bucket.DocCount < 1 {
					continue
				}
				instance := make([]*m_asset.ResultDomainData, 0)
				hits, find := bucket.Aggregations.TopHits("child_domain_view")
				if find {
					for _, hit := range hits.Hits.Hits {
						threats := new(m_asset.EsResultRuleInfos)
						if err := json.Unmarshal(*hit.Source, &threats); err != nil {
							continue
						}

						var os string
						for _, v := range threats.RuleInfos {
							if v.Title != "" && v.SecondCatTag == "操作系统" {
								os = v.Title
							}
						}

						t := new(m_asset.BusinessData)
						if err := json.Unmarshal(*hit.Source, &t); err != nil {
							continue
						}

						if t.OsVersion != "" {
							str := strings.Split(t.OsVersion, "/")
							if len(str) == 2 && str[0] != "" && str[1] != "" && str[1] != "0" {
								os = t.OsVersion
							}
						}

						if t.Os != "" {
							os = t.Os
						}

						instance = append(instance, &m_asset.ResultDomainData{
							UID:              t.IP,
							Domain:           bucket.Key.(string),
							Ip:               t.IP,
							IpCount:          len(hits.Hits.Hits),
							PortQuantity:     t.PortSize,
							ProtocolQuantity: len(t.Protocols),
							RulesQuantity:    len(t.RuleTags),
							Os:               os,
							AssetLevel:       t.AssetLevel,
							FindTime:         t.Createtime,
							PreTime:          t.Lastupdatetime,
							State:            t.State,
							ThreatStatus:     calculateAssetRisk(threats, QL.NoStdPorts),
						})
					}
				}
				data = append(data, instance)
			}
		}
	}
	return data, total, nil
}

// CalculateTheNumberOfRiskyAssets custom poc fofa records.
func (store *Store) CalculateTheNumberOfRiskyAssets(ctx context.Context, query string) (int, error) {
	qi := fofacore.QueryInput{
		Query: query,
		Full:  true,
		Fraud: true,
	}
	out, err := fofacore.ParseFEQuery(&qi)
	if err != nil {
		return 0, err
	}

	qr, ok := out.Query["query"]
	if !ok {
		return 0, errors.New("query is nil")
	}

	body, err := json.MarshalIndent(qr, "", "  ")
	if err != nil {
		return 0, err
	}
	serv := store.Instance.Client.
		Search(elasticx.IndexNameOfSubDomain(), elasticx.IndexNameOfService()).
		Type("subdomain", "service").
		Query(elastic.NewRawStringQuery(string(body))).
		Size(0)

	result, err := serv.Do(ctx)
	if err != nil {
		return 0, err
	}

	return int(result.Hits.TotalHits), nil
}

func (store *Store) DelDomain(ctx *gin.Context, QL *r_asset.QueryDomainList) error {
	// ============================================   构造 筛选条件  =========================================
	// 删除全部 或 高级筛选后删除全部
	boolQuery := elastic.NewBoolQuery()
	if len(QL.Ids) == 0 || (len(QL.Ids) == 1 && QL.Ids[0] == "") {
		boolQuery = NewDomainBoolQuery(QL)
	} else {
		r := stringSliceToInterface(QL.Ids)
		boolQuery.Must(elastic.NewTermsQuery("_id", r...))
	}
	// ============================================   构造 筛选条件  =========================================
	// 提取查询结果中的 domain 字段并存入切片
	var domains []string

	fetchSource := elastic.NewFetchSourceContext(true).Include("domain")

	// 执行搜索查询
	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfDomainAsset()). // 指定索引
		Query(boolQuery). // 查询条件
		Sort("lastupdatetime", false).Size(60000). // 按 lastupdatetime 倒序排序
		FetchSourceContext(fetchSource). // 使用 fetchSourceContext 来控制返回字段
		Do(ctx)

	if err != nil {
		fmt.Println("Error executing search:", err)
		return err
	}
	for _, hit := range searchResult.Hits.Hits {
		var source map[string]interface{}
		if err := json.Unmarshal(*hit.Source, &source); err != nil {
			fmt.Println("Error unmarshalling source:", err)
			continue
		}

		// 从解码后的 source 中获取 domain 字段
		if domain, ok := source["domain"].(string); ok {
			domains = append(domains, domain)
		}
	}
	r := stringSliceToInterface(domains)
	if len(domains) > 0 {
		// 先删除域名总库
		query := elastic.NewBoolQuery()
		query.Must(elastic.NewTermsQuery("domain", r...))
		_, err := store.Instance.Client.DeleteByQuery(elasticx.IndexNameOfDomainAsset()).Query(query).Refresh("true").Do(context.Background())
		if err != nil {
			logger.Warnw("[DeleteByIpRangeOrKeyword] ip ranges ", "err", err.Error())
			return err
		}

		// 构建脚本剔除 ctx._source.hosts 数组中的所有包含在 domainsToRemove 中的元素
		script := elastic.NewScriptInline(`
    if (ctx._source.containsKey('hosts')) {
        for (domain in params.domainsToRemove) {
            ctx._source.hosts.removeIf(host -> host.contains(domain));
        }
        ctx._source.host = String.join(",", ctx._source.hosts);
    }
`).Param("domainsToRemove", r)

		boolQuery := elastic.NewBoolQuery()
		boolQuery.Must(elastic.NewTermsQuery("hosts", r...))
		// 执行 UpdateByQuery
		_, err = store.Instance.Client.UpdateByQuery(elasticx.IndexNameOfAsset()).
			Query(boolQuery).
			Script(script).
			Refresh("true").
			Do(context.TODO())

		if err != nil {
			logger.Warnw("[DeleteByIpRangeOrKeyword] ip ranges ", "err", err.Error())
			return err
		}
	}
	return nil
}

// GetIpsByDomains 通过域名获取ip
func (store *Store) GetIpsByDomains(ctx context.Context, domains []string) ([]string, error) {
	boolQuery := elastic.NewBoolQuery().Must()
	r := stringSliceToInterface(domains)
	if len(r) > 0 {
		boolQuery.Must(elastic.NewTermsQuery("hosts", r...))
	}

	// 执行搜索查询
	searchResult, err := store.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()). // 指定索引
		Query(boolQuery). // 查询所有文档
		Do(ctx) // 执行请求
	if err != nil {
		return nil, err
	}

	ips := make([]string, 0)
	// 遍历搜索结果并解析文档
	for _, hit := range searchResult.Hits.Hits {
		temp := new(m_asset.BaseResult)
		if err := json.Unmarshal(*hit.Source, &temp); err != nil {
			continue
		}

		ips = append(ips, temp.Ip)
	}

	return ips, nil

}
