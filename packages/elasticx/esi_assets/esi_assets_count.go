package esi_assets

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/olivere/elastic"
	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/util/timer"
)

// Count returns all document total number
func (store *Store) Count(ctx context.Context) (int, error) {
	r, err := store.Instance.Client.
		Search(store.IndexName).
		Type(store.TypeName).
		Query(elastic.NewMatchAllQuery()).
		Do(ctx)

	if err != nil {
		return 0, err
	}

	if r == nil {
		return 0, fmt.Errorf("query asset index to failure")
	}

	return int(r.TotalHits()), nil
}

// CountTotal returns all document total number
func (store *Store) CountTotal() (int, error) {
	r, err := store.Instance.Client.
		Count(store.IndexName).
		Type(store.TypeName).
		Query(elastic.NewMatchAllQuery()).
		Do(context.Background())

	if err != nil {
		return 0, err
	}

	return int(r), nil
}

// CountAsset 计算IP数量
func (store *Store) CountAsset(ipRangeManager string) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)
	tmpData["add"] = 0
	tmpData["total"] = 0
	tmpData["ipv4"] = 0
	tmpData["ipv6"] = 0
	tmpData["online"] = 0
	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	newIp := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr() + " 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))
	ip := elastic.NewTermsAggregation().Field("is_ipv6")
	state := elastic.NewTermsAggregation().Field("state")

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	searchResult, err := store.Instance.Client.
		Search(store.IndexName).
		Type(store.TypeName).
		Query(query).Size(0).
		Aggregation("new", newIp).
		Aggregation("state", state).
		Aggregation("ip", ip).
		Do(context.Background())
	if err != nil {
		return data, err
	}

	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "new":
			tmp := new(m_overviews.Doc)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.DocCount
		case "state":
			tmp := new(m_overviews.AggregationsPlus)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			for _, item := range tmp.Buckets {
				if item.Key == "1" {
					tmpData["online"] = item.DocCount
				}
			}
		case "ip":
			tmp := new(m_overviews.AggregationsPlus)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			for _, item := range tmp.Buckets {
				if item.KeyAsString == "false" {
					tmpData["ipv4"] = item.DocCount
				} else {
					tmpData["ipv6"] = item.DocCount
				}
			}
		}
	}
	tmpData["total"] = int(searchResult.Hits.TotalHits)
	// 判断总数 减去 ipv6 不等于 ipv4 的时候，就把值赋值给ipv4
	if tmpData["total"]-tmpData["ipv6"] != tmpData["ipv4"] {
		tmpData["ipv4"] = tmpData["total"] - tmpData["ipv6"]
	}
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// CountAssetForXc 计算IP数量 信创ip 非信创ip
func (store *Store) CountAssetForXc(ipRangeManager string) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)

	tmpData["total"] = 0
	tmpData["xc"] = 0
	tmpData["not_xc"] = 0
	tmpData["add"] = 0
	tmpData["xc_add"] = 0
	tmpData["nxc_add"] = 0

	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	xcType := elastic.NewTermsAggregation().Field("is_xc")
	newIp := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr() + " 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))

	newXcIp := elastic.NewFilterAggregation().
		Filter(elastic.NewBoolQuery().Must(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr()+" 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02")+" 00:00:00"), elastic.NewTermQuery("is_xc", 1)))

	newNxcIp := elastic.NewFilterAggregation().
		Filter(elastic.NewBoolQuery().Must(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr()+" 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02")+" 00:00:00"), elastic.NewTermQuery("is_xc", 0)))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	searchResult, err := store.Instance.Client.
		Search(store.IndexName).
		Type(store.TypeName).
		Query(query).Size(0).
		Aggregation("new", newIp).
		Aggregation("xc_type", xcType).
		Aggregation("new_xc", newXcIp).
		Aggregation("new_nxc", newNxcIp).Do(context.Background())
	if err != nil {
		return data, err
	}
	fmt.Println(searchResult)
	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "xc_type":
			tmp := new(m_overviews.AggregationsOverview)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}

			for _, item := range tmp.Buckets {
				if item.Key == m_task.IsXcType {
					tmpData["xc"] = item.DocCount
				}
			}

		case "new":
			tmp := new(m_overviews.Doc)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.DocCount

		case "new_xc":
			tmp := new(m_overviews.Doc)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["xc_add"] = tmp.DocCount
		case "new_nxc":
			tmp := new(m_overviews.Doc)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["nxc_add"] = tmp.DocCount
		}
	}
	tmpData["total"] = int(searchResult.Hits.TotalHits)
	tmpData["not_xc"] = tmpData["total"] - tmpData["xc"]
	tmpData["nxc_add"] = tmpData["add"] - tmpData["xc_add"]
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// CountRuleForXc 计算组件总数  信创组件 非信创组件
func (store *Store) CountRuleForXc(ipRangeManager string) ([]m_overviews.Index, error) {

	tmpData := make(map[string]int)

	tmpData["total"] = 0
	tmpData["xc"] = 0
	tmpData["not_xc"] = 0
	tmpData["add"] = 0

	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())

	newIp := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr() + " 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))
	componentAgg := elastic.NewValueCountAggregation().Field("rule_infos.title")
	newComponentAgg := newIp.SubAggregation("new_component", componentAgg)

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	searchResult, err := store.Instance.Client.
		Search(store.IndexName).
		Type(store.TypeName).
		Query(query).Size(0).
		Aggregation("new", newIp).
		Aggregation("component", componentAgg).
		Aggregation("new_component", newComponentAgg).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "component":
			tmp := new(m_asset.CardinalityAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["total"] = tmp.Value
		case "new_component":
			tmp := new(m_asset.ComponentCardinalityAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.Component.Value
		}
	}
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// CountPortForXc 计算信创总览端口总数 端口新增
func (store *Store) CountPortForXc(ipRangeManager string) ([]m_overviews.Index, error) {

	tmpData := make(map[string]int)

	tmpData["total"] = 0
	tmpData["add"] = 0
	tmpData["common"] = 0

	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())

	newIp := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr() + " 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))

	portAgg := elastic.NewValueCountAggregation().Field("ports")
	newPortAgg := newIp.SubAggregation("new_port", portAgg)

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	searchResult, err := store.Instance.Client.
		Search(store.IndexName).
		Type(store.TypeName).
		Query(query).Size(0).
		Aggregation("new", newIp).
		Aggregation("port", portAgg).
		Aggregation("new_port_agg", newPortAgg).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "port":
			tmp := new(m_asset.CardinalityAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["total"] = tmp.Value
		case "new_port_agg":
			tmp := new(m_asset.NewPortCardinalityAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.NewPort.Value
		}
	}
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

func (store *Store) CountXcRuleForXc(ipRangeManager string) (int, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewExistsQuery("rule_infos"))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	xcRuleCount := 0
	store.fileLock.Lock()
	defer store.fileLock.Unlock()
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"rule_infos"}, nil)
	serv := store.Instance.Client.Scroll().
		Scroll(elasticx.DefaultScrollKeepAlive).
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(searchSource).
		Pretty(true).
		Query(boolQuery).
		Size(10000)
	result, err := serv.Do(context.Background())
	if err != nil {
		if err == io.EOF {
			return 0, nil
		}
		return 0, err
	}
	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
			for _, hit := range result.Hits.Hits {
				asset := new(m_overviews.OverviewAssetData)
				if err = json.Unmarshal(*hit.Source, &asset); err != nil {
					continue
				}
				for _, ruleInfo := range asset.RuleInfos {
					if ruleInfo.IsXc == m_task.IsXcType {
						xcRuleCount = xcRuleCount + 1
					}
				}
			}
			result, err = serv.ScrollId(result.ScrollId).Do(context.Background())
			if err != nil {
				break
			}
		} else {
			break
		}
	}

	return xcRuleCount, nil
}

func (store *Store) CountXcPortForXc(ipRangeManager string, ports string) (int, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewExistsQuery("rule_infos"))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	comPortCount := 0
	store.fileLock.Lock()
	defer store.fileLock.Unlock()
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"ports"}, nil)
	serv := store.Instance.Client.Scroll().
		Scroll(elasticx.DefaultScrollKeepAlive).
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(searchSource).
		Pretty(true).
		Query(boolQuery).
		Size(10000)
	result, err := serv.Do(context.Background())
	if err != nil {
		if err == io.EOF {
			return 0, nil
		}
		return 0, err
	}
	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
			for _, hit := range result.Hits.Hits {
				asset := new(m_overviews.OverviewAssetData)
				if err = json.Unmarshal(*hit.Source, &asset); err != nil {
					continue
				}

				if len(ports) != 0 {
					for _, port := range asset.Ports {
						if find := strings.Contains(ports, port); find {
							comPortCount = comPortCount + 1
						}

					}
				}
			}
			result, err = serv.ScrollId(result.ScrollId).Do(context.Background())
			if err != nil {
				break
			}
		} else {
			break
		}
	}

	return comPortCount, nil
}

// GetBasicSoftwareRank 信创资产总览-基础软件分布。
func (store *Store) GetBasicSoftwareRank(ipRangeManager string, xcType string) ([]m_overviews.BasicSoftwareRank, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewExistsQuery("rule_infos"))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	store.fileLock.Lock()
	defer store.fileLock.Unlock()
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"rule_infos"}, nil)
	serv := store.Instance.Client.Scroll().
		Scroll(elasticx.DefaultScrollKeepAlive).
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(searchSource).
		Pretty(true).
		Query(boolQuery).
		Size(10000)

	result, err := serv.Do(context.Background())
	if err != nil {
		if err == io.EOF {
			return nil, nil
		}
		return nil, err
	}
	fmt.Println("----------------------------", len(result.Hits.Hits))
	ruleMap := map[string][]*m_overviews.Rule{
		"中间件":   make([]*m_overviews.Rule, 0),
		"操作系统":  make([]*m_overviews.Rule, 0),
		"数据库系统": make([]*m_overviews.Rule, 0),
	}
	SecondCat := []string{"中间件", "操作系统", "数据库系统"}
	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
			for _, hit := range result.Hits.Hits {
				rules := new(m_overviews.XcRuleInfoData)
				if err = json.Unmarshal(*hit.Source, &rules); err != nil {
					continue
				}

				CalRuleCount(rules, SecondCat, xcType, ruleMap)
			}
			result, err = serv.ScrollId(result.ScrollId).Do(context.Background())
			if err != nil {
				break
			}
		} else {
			break
		}
	}

	data := make([]m_overviews.BasicSoftwareRank, 0)
	for _, sc := range SecondCat {
		sort.Sort(m_overviews.RuleSort(ruleMap[sc]))
		n := len(ruleMap[sc])
		if n > 5 {
			n = 5
		}
		total := 0
		for _, r := range ruleMap[sc] {
			total = total + r.Count
		}
		data = append(data, m_overviews.BasicSoftwareRank{
			Category: sc,
			Total:    total,
			Rules:    ruleMap[sc][0:n],
		})
	}
	return data, nil
}

func CalRuleCount(rules *m_overviews.XcRuleInfoData, SecondCat []string, xcType string, ruleMap map[string][]*m_overviews.Rule) {
	for _, ruleInfo := range rules.XcRuleInfos {
		for _, sc := range SecondCat {
			if ruleInfo.SecondCatTag != sc {
				continue
			}

			// 判断获取数据的类型 信创 非信创 全部
			ruleBool := false
			if xcType == "" {
				ruleBool = true
			} else {
				xcInt, _ := strconv.Atoi(xcType)
				if ruleInfo.IsXc == xcInt {
					ruleBool = true
				}
			}

			if ruleBool {
				hasRule := false
				for _, r := range ruleMap[sc] {
					if r.Title == ruleInfo.Title {
						r.Count = r.Count + 1
						hasRule = true
					}
				}

				if !hasRule {
					ruleMap[sc] = append(ruleMap[sc], &m_overviews.Rule{
						Title: ruleInfo.Title,
						Count: 1,
					})
				}
			}

		} //range SecondCat
	}
}

// GetRuleDataRank 信创资产总览-数据排行。
func (store *Store) GetRuleDataRank(ipRangeManager string, xcType string) ([]m_overviews.SoftwareAndHardwareRank, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewExistsQuery("rule_infos"))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	store.fileLock.Lock()
	defer store.fileLock.Unlock()
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"rule_infos"}, nil)
	serv := store.Instance.Client.Scroll().
		Scroll(elasticx.DefaultScrollKeepAlive).
		Index(store.IndexName).
		Type(store.TypeName).
		SearchSource(searchSource).
		Pretty(true).
		Query(boolQuery).
		Size(10000)

	result, err := serv.Do(context.Background())
	if err != nil {
		if err == io.EOF {
			return nil, nil
		}
		return nil, err
	}
	srRules := make([]*m_overviews.Rank, 0)
	scRules := make([]*m_overviews.Rank, 0)
	hrRules := make([]*m_overviews.Rank, 0)
	hcRules := make([]*m_overviews.Rank, 0)

	dataMap := map[string][]*m_overviews.Rank{
		"software_rule":    srRules,
		"software_company": scRules,
		"hardware_rule":    hrRules,
		"hardware_company": hcRules,
	}
	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
			for _, hit := range result.Hits.Hits {
				rules := new(m_overviews.XcRuleInfoData)
				if err = json.Unmarshal(*hit.Source, &rules); err != nil {
					continue
				}
				for _, ruleInfo := range rules.XcRuleInfos {
					// 判断获取数据的类型 信创 非信创 全部
					hardRuleBool := IsHard(xcType, ruleInfo)

					if hardRuleBool {
						HandleHardRule(dataMap, ruleInfo)
					}

					softRuleBool := IsSoft(xcType, ruleInfo)

					if softRuleBool {
						HandleSorftRule(dataMap, ruleInfo)
					}
				}
			}
			result, err = serv.ScrollId(result.ScrollId).Do(context.Background())
			if err != nil {
				break
			}
		} else {
			break
		}

	}

	data := make([]m_overviews.SoftwareAndHardwareRank, 0)

	for key, ruleRank := range dataMap {
		sort.Sort(m_overviews.RankSort(ruleRank))
		n := len(ruleRank)
		if n > 10 {
			n = 10
		}
		total := 0
		for _, r := range ruleRank {
			total = total + r.DocCount
		}
		data = append(data, m_overviews.SoftwareAndHardwareRank{
			RankType: key,
			Total:    total,
			Rules:    ruleRank[0:n],
		})
	}

	return data, nil
}

func HandleSorftRule(dataMap map[string][]*m_overviews.Rank, ruleInfo *m_overviews.XcRuleInfos) {
	softwareHasRule := false
	for _, r := range dataMap["software_rule"] {
		if r.Key == ruleInfo.Title {
			r.DocCount = r.DocCount + 1
			softwareHasRule = true
		}
	}
	if !softwareHasRule {
		dataMap["software_rule"] = append(dataMap["software_rule"], &m_overviews.Rank{
			Key:      ruleInfo.Title,
			DocCount: 1,
		})
	}

	softwareCompanyHasRule := false
	for _, r := range dataMap["software_company"] {
		if r.Key == ruleInfo.Company {
			r.DocCount = r.DocCount + 1
			softwareCompanyHasRule = true
		}
	}
	if !softwareCompanyHasRule {
		dataMap["software_company"] = append(dataMap["software_company"], &m_overviews.Rank{
			Key:      ruleInfo.Company,
			DocCount: 1,
		})
	}
}

func IsSoft(xcType string, ruleInfo *m_overviews.XcRuleInfos) bool {
	softRuleBool := false
	// 判断获取数据的类型 信创 非信创 全部
	if xcType == "" {
		if ruleInfo.SoftHardCode == 2 {
			softRuleBool = true
		}
	} else {
		intXcType, _ := strconv.Atoi(xcType)
		if ruleInfo.SoftHardCode == 2 && ruleInfo.IsXc == intXcType {
			softRuleBool = true
		}
	}
	return softRuleBool
}

func HandleHardRule(dataMap map[string][]*m_overviews.Rank, ruleInfo *m_overviews.XcRuleInfos) {
	hasRule := false
	for _, r := range dataMap["hardware_rule"] {
		if r.Key == ruleInfo.Title {
			r.DocCount = r.DocCount + 1
			hasRule = true
		}
	}
	if !hasRule {
		dataMap["hardware_rule"] = append(dataMap["hardware_rule"], &m_overviews.Rank{
			Key:      ruleInfo.Title,
			DocCount: 1,
		})
	}

	companyHasRule := false
	for _, r := range dataMap["hardware_company"] {
		if r.Key == ruleInfo.Company {
			r.DocCount = r.DocCount + 1
			companyHasRule = true
		}
	}
	if !companyHasRule {
		dataMap["hardware_company"] = append(dataMap["hardware_company"], &m_overviews.Rank{
			Key:      ruleInfo.Company,
			DocCount: 1,
		})
	}
}

func IsHard(xcType string, ruleInfo *m_overviews.XcRuleInfos) bool {
	hardRuleBool := false
	if xcType == "" {
		if ruleInfo.SoftHardCode == 1 {
			hardRuleBool = true
		}
	} else {
		intXcType, _ := strconv.Atoi(xcType)
		if ruleInfo.SoftHardCode == 1 && ruleInfo.IsXc == intXcType {
			hardRuleBool = true
		}
	}
	return hardRuleBool
}

// CountRule 计算指纹命总数
func (store *Store) CountRule(ipRangeManager string) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)
	tmpData["total"] = 0
	tmpData["add"] = 0
	tmpData["system"] = 0
	tmpData["database"] = 0
	tmpData["middleware"] = 0
	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	nestedAgg := elastic.NewNestedAggregation()
	nestedAgg.Path("first_tag_num")
	nestedAgg.SubAggregation("rule_count", elastic.NewSumAggregation().Field("first_tag_num.num"))

	secondNumAggs := elastic.NewSumAggregation().Field("second_tag_num.num")
	secondAggs := elastic.NewTermsAggregation().Field("second_tag_num.second_cat_tag").SubAggregation("sums", secondNumAggs).Size(maxCountLimit)
	secondTagNum := elastic.NewNestedAggregation().Path("second_tag_num").SubAggregation("tag_nums", secondAggs)

	newRule := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr()+" 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02")+" 00:00:00")).
		SubAggregation("sum_second_tag_num", elastic.NewNestedAggregation().Path("second_tag_num").
			SubAggregation("sum", secondNumAggs))

	serv, err := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(query).
		Size(0).
		Pretty(true).
		Aggregation("second_tags", secondTagNum).
		Aggregation("new_rule", newRule).
		Aggregation("nested_rule", nestedAgg).
		Do(context.Background())
	if err != nil {
		return data, err
	}

	secondTags := m_asset.UnmarshalJsonRawMessageForNestedAggregation(*serv.Aggregations["second_tags"])
	for _, secondTag := range secondTags {
		switch secondTag.Key {
		case "数据库系统":
			tmpData["database"] = int(secondTag.Sums["value"])
		case "操作系统":
			tmpData["system"] = int(secondTag.Sums["value"])
		case "中间件":
			tmpData["middleware"] = int(secondTag.Sums["value"])
		}
	}

	newRules, isExists := serv.Aggregations.Terms("new_rule")
	if isExists {
		sumSecondTagNum, isExists := newRules.Sum("sum_second_tag_num")
		if isExists {
			if sum, isExists := sumSecondTagNum.Sum("sum"); isExists {
				tmpData["add"] = int(*sum.Value)
			}
		}
	}

	nestedRule, isExists := serv.Aggregations.Nested("nested_rule")
	if isExists {
		ruleCount, fond := nestedRule.Sum("rule_count")
		if fond {
			value := ruleCount.Value
			tmpData["total"] = int(*value)
		}
	}

	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// CountBusinessApp 计算业务系统总数
func (store *Store) CountBusinessApp(ipRangeManager string) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)
	tmpData["system"] = 0
	tmpData["database"] = 0
	tmpData["middleware"] = 0
	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	query.Must(elastic.NewExistsQuery("business_app")).
		MustNot(elastic.NewTermQuery("business_app", ""))

	secondNumAggs := elastic.NewSumAggregation().Field("second_tag_num.num")
	secondAggs := elastic.NewTermsAggregation().Field("second_tag_num.second_cat_tag").SubAggregation("sums", secondNumAggs).Size(maxCountLimit)
	secondTagNum := elastic.NewNestedAggregation().Path("second_tag_num").SubAggregation("tag_nums", secondAggs)

	serv, err := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(query).
		Size(0).
		Pretty(true).
		Aggregation("second_tags", secondTagNum).
		Do(context.Background())
	if err != nil {
		return data, err
	}

	secondTags := m_asset.UnmarshalJsonRawMessageForNestedAggregation(*serv.Aggregations["second_tags"])
	for _, secondTag := range secondTags {
		switch secondTag.Key {
		case "数据库系统":
			tmpData["database"] = int(secondTag.Sums["value"])
		case "操作系统":
			tmpData["system"] = int(secondTag.Sums["value"])
		case "中间件":
			tmpData["middleware"] = int(secondTag.Sums["value"])
		}
	}

	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// CountAssetTotal 根据管理范围获取资产数量
func (store *Store) CountAssetTotal(ipRangeManager string, xcType string) int {
	query := elastic.NewBoolQuery()

	if xcType == "1" {
		query.Must(elastic.NewTermQuery("is_xc", 1))
	} else if xcType == "0" {
		query.MustNot(elastic.NewTermQuery("is_xc", 1))
	} else {
		query.Must(elastic.NewMatchAllQuery())
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	r, err := store.Instance.Client.
		Count(store.IndexName).
		Type(store.TypeName).
		Query(query).
		Do(context.Background())

	if err != nil {
		return 0
	}

	return int(r)
}

// AssetOverviewCountBusinessApp 资产概览计算业务系统前10
func (store *Store) AssetOverviewCountBusinessApp(ipRangeManager string) ([]m_overviews.Index, error) {
	dataList := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	query.Must(elastic.NewExistsQuery("business_app")).
		MustNot(elastic.NewTermQuery("business_app", ""))

	businessAggs := elastic.NewCardinalityAggregation().Field("business_app")
	businessListAgg := elastic.NewTermsAggregation().Field("business_app").Size(10)
	newBusinessAgg := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(time.Now().AddDate(0, 0, -1).Format("2006-01-02")+" 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02")+" 00:00:00")).
		SubAggregation("business", businessAggs)

	serv, err := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(query).
		Size(0).
		Pretty(true).
		Aggregation("new_business_app", newBusinessAgg).
		Aggregation("business", businessAggs).
		Aggregation("business_list", businessListAgg).
		Do(context.Background())
	if err != nil {
		return dataList, err
	}

	for key, rawMessage := range serv.Aggregations {
		switch key {
		case "business_list":
			tmp := new(m_overviews.Aggregations)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return dataList, err
			}
			for _, val := range tmp.Buckets {
				dataList = append(dataList, m_overviews.Index{
					Name:  val.Key,
					Count: val.DocCount,
				})
			}
		}
	}

	return dataList, nil
}

// AssetOverviewCountDomain 资产概览计算域名统计
func (store *Store) AssetOverviewCountDomain(ipRangeManager string) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)
	tmpData["total"] = 0
	tmpData["add"] = 0

	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	query.Must(elastic.NewExistsQuery("hosts"))
	query.MustNot(elastic.NewTermQuery("hosts", ""))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	domain := elastic.NewCardinalityAggregation().Field("hosts")
	domains := elastic.NewTermsAggregation().Field("hosts").Size(maxCountLimit)

	newDomain := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(time.Now().Format("2006-01-02")+" 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02")+" 00:00:00")).
		SubAggregation("hosts", domain)

	serv, err := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(query).
		Size(0).
		Pretty(true).
		Aggregation("new_domain", newDomain).
		Aggregation("domain", domains).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	for key, rawMessage := range serv.Aggregations {
		switch key {
		case "new_domain":
			tmp := new(m_asset.DomainCardinalityAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.Domain.Value
		case "domain":
			agg, find := serv.Aggregations.Terms("domain")
			if find {
				tmpData["total"] = len(agg.Buckets)
			}
		}
	}
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// AssetOverviewCountAssetLevel 资产概览资产等级前10
func (store *Store) AssetOverviewCountAssetLevel(ipRangeManager string) ([]m_overviews.Index, error) {

	data := make([]m_overviews.Index, 0)

	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	query.Must(elastic.NewExistsQuery("asset_level")).
		MustNot(elastic.NewTermQuery("asset_level", ""))

	assetLevelAgg := elastic.NewTermsAggregation().Field("asset_level").Size(10)

	serv, err := store.Instance.Client.Search().
		Index(store.IndexName).
		Type(store.TypeName).
		Query(query).
		Size(0).
		Pretty(true).
		Aggregation("asset_level", assetLevelAgg).
		Do(context.Background())
	if err != nil {
		return data, err
	}

	for key, rawMessage := range serv.Aggregations {
		switch key {
		case "asset_level":
			tmp := new(m_overviews.Aggregations)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			for _, val := range tmp.Buckets {
				data = append(data, m_overviews.Index{
					Name:  val.Key,
					Count: val.DocCount,
				})
			}
		}
	}
	return data, nil
}

// AssetOverviewCountAsset 资产概览计算IP数量和组件数量
func (store *Store) AssetOverviewCountAsset(ipRangeManager string) ([]m_overviews.Index, []m_overviews.Index, []m_asset.Component, error) {
	tmpData := make(map[string]int)
	tmpData["add"] = 0
	tmpData["total"] = 0
	comTmpData := make(map[string]int)
	comTmpData["add"] = 0
	comTmpData["total"] = 0
	data := make([]m_overviews.Index, 0)
	comData := make([]m_overviews.Index, 0)
	comInfoData := make([]m_asset.Component, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	newIp := elastic.NewFilterAggregation().
		Filter(elastic.NewRangeQuery("createtime").
			Gte(timer.CurrentDateStr() + " 00:00:00").
			Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))

	componentTopFetchSource := elastic.NewFetchSourceContext(true).Include("rule_infos.company", "rule_infos.title", "rule_infos.level_code", "rule_infos.second_cat_tag")
	componentTop := elastic.NewTopHitsAggregation().Size(1).FetchSourceContext(componentTopFetchSource)
	componentListAgg := elastic.NewTermsAggregation().Field("rule_infos.title.raw").Size(10).SubAggregation("rule_info", componentTop)

	componentAgg := elastic.NewValueCountAggregation().Field("rule_infos.title")
	newComponentAgg := newIp.SubAggregation("component", componentAgg)

	secondNumAggs := elastic.NewSumAggregation().Field("second_tag_num.num")
	secondTagNum := elastic.NewNestedAggregation().Path("second_tag_num").SubAggregation("tag_nums", secondNumAggs)

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	searchResult, err := store.Instance.Client.
		Search(store.IndexName).
		Type(store.TypeName).
		Query(query).Size(0).
		Aggregation("new", newIp).
		Aggregation("component", secondTagNum).
		Aggregation("new_component", newComponentAgg).
		Aggregation("component_list", componentListAgg).
		Do(context.Background())
	if err != nil {
		return data, comData, comInfoData, err
	}

	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "new":
			tmp := new(m_overviews.Doc)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, comData, comInfoData, err
			}
			tmpData["add"] = tmp.DocCount
		case "component":
			tmp := new(m_asset.ComponentAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, comData, comInfoData, err
			}
			comTmpData["total"] = int(tmp.TagNums.Value)
		case "new_component":
			tmp := new(m_asset.ComponentCardinalityAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, comData, comInfoData, err
			}
			comTmpData["add"] = tmp.Component.Value
		}
	}
	tmpData["total"] = int(searchResult.Hits.TotalHits)
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	for key, val := range comTmpData {
		comData = append(comData, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}

	buckets, isOk := searchResult.Aggregations.Terms("component_list")
	if isOk {
		for _, bucket := range buckets.Buckets {
			comInfo := m_asset.Component{}
			hits, isOk := bucket.Aggregations.TopHits("rule_info")
			if isOk {
			LOOPHOLE:
				for _, val := range hits.Hits.Hits {
					tmp := new(m_asset.ComponentTop)
					err := json.Unmarshal(*val.Source, &tmp)
					if err != nil {
						log.Println(err)
						return data, comData, comInfoData, err
					}
					for _, ruleInfo := range tmp.RuleInfos {
						if ruleInfo.Title == bucket.Key {
							comInfo.Name = ruleInfo.Title
							comInfo.Company = ruleInfo.Company
							comInfo.Count = int(bucket.DocCount)
							comInfo.Category = ruleInfo.SecondCatTag
							comInfo.Level = m_asset.LevelCodeToLevelName(ruleInfo.LevelCode)
							comInfoData = append(comInfoData, comInfo)
							break LOOPHOLE
						}
					}
				}
			}
		}
	}

	return data, comData, comInfoData, nil
}

// AssetOverviewTags 资产概览标签统计
func (store *Store) AssetOverviewTags(ipRangeManager string, tagMaps map[string]string) ([]m_asset.LabelDistribution, error) {
	tags := make([]string, 0)
	for key := range tagMaps {
		tags = append(tags, key)
	}
	data := make([]m_asset.LabelDistribution, 0)

	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())

	companiesAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("company").Size(10000)
	managersAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("username").Size(10000)
	computerRoomAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("computer_room").Size(10000)
	businessAppAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("business_app").Size(10000)
	assetLevelAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("asset_level").Size(10000)

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	searchService := store.Instance.Client.
		Search(store.IndexName).
		Type(store.TypeName).
		Query(query).Size(0).
		Aggregation("companies", companiesAggregation).
		Aggregation("managers", managersAggregation).
		Aggregation("business_app", businessAppAggregation).
		Aggregation("computer_room", computerRoomAggregation).
		Aggregation("asset_level", assetLevelAggregation)

	if len(tags) > 0 {
		for _, key := range tags {
			fieldKey := fmt.Sprintf("custom_fields.%s", key)
			tagAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field(fieldKey).Size(65535)
			searchService.Aggregation(fieldKey, tagAggregation)
		}
	}
	searchResult, err := searchService.
		Pretty(true).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "companies":
			data, err = SetOverViewsAggs("管理单元", "company", rawMessage, data)
			if err != nil {
				return data, err
			}
		case "managers":
			data, err = SetOverViewsAggs("负责人", "username", rawMessage, data)
			if err != nil {
				return data, err
			}
		case "business_app":
			data, err = SetOverViewsAggs("业务系统", "business_app", rawMessage, data)
			if err != nil {
				return data, err
			}
		case "computer_room":
			data, err = SetOverViewsAggs("机房信息", "computer_room", rawMessage, data)
			if err != nil {
				return data, err
			}

		case "asset_level":
			data, err = SetOverViewsAggs("资产等级", "asset_level", rawMessage, data)
			if err != nil {
				return data, err
			}
		default:
			// 是否自定义标签 左侧统计
			if !strings.Contains(key, "custom_fields") {
				continue
			}
			existedKey := key[14:]
			v, ok := tagMaps[existedKey]
			if !ok {
				continue
			}

			data, err = SetOverViewsAggs(v, key, rawMessage, data)
			if err != nil {
				return data, err
			}
		}
	}
	return data, nil
}

func SetOverViewsAggs(name, key string, rawMessage *json.RawMessage, data []m_asset.LabelDistribution) ([]m_asset.LabelDistribution, error) {
	tmpData := m_asset.LabelDistribution{}
	tmp := new(m_overviews.Aggregations)
	err := json.Unmarshal(*rawMessage, &tmp)
	if err != nil {
		return data, err
	}

	tmpIndexes := make([]m_asset.Index, 0)
	for _, val := range tmp.Buckets {
		if val.Key != "" {
			tmpIndexes = append(tmpIndexes, m_asset.Index{
				Name:  val.Key,
				Value: val.DocCount,
			})
		}
	}
	tmpData.Name = name
	tmpData.Data = tmpIndexes
	tmpData.Key = key
	if len(tmpData.Data) > 0 {
		data = append(data, tmpData)
	}
	return data, nil
}

// AssetOverviewCountPortProtocol 资产概览常用端口和常用协议统计
func (store *Store) AssetOverviewCountPortProtocol(ipRangeManager string) ([]m_overviews.Index, []m_overviews.Index, error) {
	ports := make([]m_overviews.Index, 0)
	protocols := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	//将搜索范围与资产统计保持一致，结果取前10个
	portAgg := elastic.NewTermsAggregation().Field("port_list.port").Size(maxCountLimit)
	protocolAgg := elastic.NewTermsAggregation().Field("protocols").Size(maxCountLimit)
	searchResult, err := store.Instance.Client.
		Search(store.IndexName).
		Type(store.TypeName).
		Query(query).Size(0).
		Aggregation("port", portAgg).
		Aggregation("protocol", protocolAgg).
		Do(context.Background())
	if err != nil {
		return ports, protocols, err
	}

	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "port":
			tmp := new(m_asset.IntAggregations)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				return ports, protocols, err
			}
			//统计前10个
			size := int(math.Min(10, float64(len(tmp.Buckets))))
			for i := 0; i < size; i++ {
				ports = append(ports, m_overviews.Index{
					Name:  fmt.Sprintf("%d", tmp.Buckets[i].Key),
					Count: tmp.Buckets[i].DocCount,
				})
			}
		case "protocol":
			tmp := new(m_overviews.Aggregations)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				return ports, protocols, err
			}
			size := int(math.Min(10, float64(len(tmp.Buckets))))
			for i := 0; i < size; i++ {
				if tmp.Buckets[i].Key != "" {
					protocols = append(protocols, m_overviews.Index{
						Name:  tmp.Buckets[i].Key,
						Count: tmp.Buckets[i].DocCount,
					})
				}
			}
		}
	}
	return ports, protocols, nil
}
