package esi_assets

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	elastic2 "git.gobies.org/foeye/foeye3/store/elastic"
	"git.gobies.org/foeye/foeye3/store/storage"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye-dependencies/mosso"

	"git.gobies.org/foeye/foeye3/model/m_asset"
)

func (suite *ElasticIndexAssetsSuite) TestCount() {
	res, err := suite.ins.Count(context.Background())
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res)
}

func (suite *ElasticIndexAssetsSuite) TestCountTotal() {
	res, err := suite.ins.CountTotal()
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res)
}

func (suite *ElasticIndexAssetsSuite) TestAssetOverviewCountBusinessApp() {
	businessSystem, err := suite.ins.AssetOverviewCountBusinessApp("***********-100")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), businessSystem)
}

func (suite *ElasticIndexAssetsSuite) TestAssetOverviewCountAsset() {
	ipList, comList, comInfoList, err := suite.ins.AssetOverviewCountAsset("***********-100")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), ipList)
	assert.NotNil(suite.T(), comList)
	assert.NotNil(suite.T(), comInfoList)
}

func (suite *ElasticIndexAssetsSuite) TestAssetOverviewCountAssetLevel() {
	assetLevelList, err := suite.ins.AssetOverviewCountAssetLevel("***********-100")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), assetLevelList)
}

func (suite *ElasticIndexAssetsSuite) TestAssetOverviewCountPortProtocol() {
	portList, protocolList, err := suite.ins.AssetOverviewCountPortProtocol("***********-100")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), portList)
	assert.NotNil(suite.T(), protocolList)
}

func (suite *ElasticIndexAssetsSuite) TestAssetOverviewTags() {
	tagMaps := make(map[string]string)
	tagMaps["biaoqianfenlei"] = "A1标签"
	tagMaps["biaoqianfenleiP1d4W0"] = "B1标签"
	tagMaps["biaoqianfenleiSVpeUi"] = "C1标签"
	data, err := suite.ins.AssetOverviewTags("***********-100", tagMaps)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *ElasticIndexAssetsSuite) TestAssetOverviewCountDomain() {
	searchResult := elastic.SearchResult{}
	aggs := []byte(`{
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [
				{
					"key": "www.fofa.info",
					"doc_count": 1				
				}
			]
		}`)
	searchResult.Aggregations = elastic.Aggregations{
		"domain": (*json.RawMessage)(&aggs),
	}

	defer gomonkey.ApplyMethodReturn(suite.ins.Instance.Client.Search(), "Do", &searchResult, nil).Reset()
	defer gomonkey.ApplyMethodReturn(suite.ins.Instance.Client.Search(), "Do", &searchResult, nil).Reset()

	data, err := suite.ins.AssetOverviewCountDomain("***********-100")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
	//assert.Equal(suite.T(), 1, data[0].Count)
	//assert.Equal(suite.T(), "total", data[0].Name)
}

func (suite *ElasticIndexAssetsSuite) TestCountBusinessApp() {
	data, err := suite.ins.CountBusinessApp("**********/24")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *ElasticIndexAssetsSuite) TestCountRule() {
	hits := []*elastic.SearchHit{}
	jsonStr := ` {
"doc_count": 337,
"rule_count": {
"value": 696
}
}`
	bytes := []byte(jsonStr)
	msg := json.RawMessage(bytes)
	aggs := elastic.Aggregations{"nested_rule": &msg}

	jsonStr2 := `{}`
	bytes2 := []byte(jsonStr2)
	msg2 := json.RawMessage(bytes2)
	aggs["second_tags"] = &msg2

	assetsResults := elastic.SearchResult{
		Header:          nil,
		TookInMillis:    2,
		TerminatedEarly: false,
		NumReducePhases: 0,
		Clusters:        nil,
		ScrollId:        "",
		Hits: &elastic.SearchHits{
			TotalHits: 1,
			MaxScore:  nil,
			Hits:      hits,
		},
		Suggest:      nil,
		Aggregations: aggs,
		TimedOut:     false,
		Error:        nil,
		Profile:      nil,
		Shards:       nil,
		Status:       0,
	}
	defer gomonkey.ApplyMethodReturn(suite.ins.Instance.Client.Search(), "Do", &assetsResults, nil).Reset()
	defer gomonkey.ApplyFuncReturn(m_asset.UnmarshalJsonRawMessageForNestedAggregation, []*m_asset.NestedBucket{}).Reset()

	data, err := suite.ins.CountRule("**********/24")
	assert.NoError(suite.T(), err)
	mosso.DebugShowContentWithJSON(data)
	for _, d := range data {
		if d.Name == "total" {
			assert.Equal(suite.T(), int(696), d.Count)
		}
	}

}

func (suite *ElasticIndexAssetsSuite) TestAssetOverviewCountAssetIpRangeManager() {
	ipList, comList, comInfoList, err := suite.ins.AssetOverviewCountAsset("")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), ipList)
	assert.NotNil(suite.T(), comList)
	assert.NotNil(suite.T(), comInfoList)
}

func TestStoreCountAsset(t *testing.T) {
	t.Run("query err", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{},
			},
			fmt.Errorf("mock err")).Reset()

		defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip")}).Reset()

		ipRangeManager := "test_ipRangeManager"
		list, err := store.CountAsset(ipRangeManager)
		assert.Equal(t, []m_overviews.Index{}, list)
		assert.Equal(t, fmt.Errorf("mock err"), err)
	})
	t.Run("query success", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)

		newIns := m_overviews.Doc{}
		newData, err := json.Marshal(newIns)
		assert.NoError(t, err)

		state := m_overviews.AggregationsPlus{
			Buckets: []m_overviews.BucketsPlus{
				{
					Key: "1",
				},
			},
		}
		stateData, err := json.Marshal(state)
		assert.NoError(t, err)

		ip := m_overviews.AggregationsPlus{
			Buckets: []m_overviews.BucketsPlus{
				{
					KeyAsString: "false",
				},
				{
					KeyAsString: "true",
				},
			},
		}
		ipData, err := json.Marshal(ip)
		assert.NoError(t, err)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{
					"new":   (*json.RawMessage)(&newData),
					"state": (*json.RawMessage)(&stateData),
					"ip":    (*json.RawMessage)(&ipData),
				},
				Hits: &elastic.SearchHits{
					TotalHits: 20,
				},
			},
			nil).Reset()

		defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip")}).Reset()

		ipRangeManager := "test_ipRangeManager"
		_, err = store.CountAsset(ipRangeManager)
		assert.Equal(t, nil, err)
	})
}

func TestStoreCountAssetForXc(t *testing.T) {
	t.Run("query err", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{},
			},
			fmt.Errorf("mock err")).Reset()

		defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip")}).Reset()

		ipRangeManager := "test_ipRangeManager"
		list, err := store.CountAssetForXc(ipRangeManager)
		assert.Equal(t, []m_overviews.Index{}, list)
		assert.Equal(t, fmt.Errorf("mock err"), err)
	})
	t.Run("query success", func(t *testing.T) {
		conf := storage.MockConfigureUniversal()
		defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)

		newIns := m_overviews.Doc{}
		newData, err := json.Marshal(newIns)
		assert.NoError(t, err)

		xc_type := m_overviews.AggregationsOverview{
			Buckets: []m_overviews.BucketsOverview{
				{
					Key: 1,
				},
			},
		}
		xc_typeData, err := json.Marshal(xc_type)
		assert.NoError(t, err)

		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{
					"xc_type": (*json.RawMessage)(&xc_typeData),
					"new_xc":  (*json.RawMessage)(&newData),
					"new_nxc": (*json.RawMessage)(&newData),
					"new":     (*json.RawMessage)(&newData),
				},
				Hits: &elastic.SearchHits{
					TotalHits: 20,
				},
			},
			nil).Reset()

		defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip")}).Reset()

		ipRangeManager := "test_ipRangeManager"
		_, err = store.CountAssetForXc(ipRangeManager)
		assert.Equal(t, nil, err)
	})
}

func TestStoreCountRuleForXc(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	component := m_asset.CardinalityAggregation{
		Value: 1,
	}
	componentData, err := json.Marshal(component)
	assert.NoError(t, err)

	new_component := m_asset.ComponentCardinalityAggregation{
		Component: m_asset.CardinalityAggregation{
			Value: 1,
		},
	}
	new_componentData, err := json.Marshal(new_component)
	assert.NoError(t, err)

	defer gomonkey.ApplyMethodReturn(
		store.Instance.Client.Search(),
		"Do",
		&elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"component":     (*json.RawMessage)(&componentData),
				"new_component": (*json.RawMessage)(&new_componentData),
			},
			Hits: &elastic.SearchHits{
				TotalHits: 20,
			},
		},
		nil).Reset()

	defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip")}).Reset()

	ipRangeManager := "test_ipRangeManager"
	_, err = store.CountRuleForXc(ipRangeManager)
	assert.Equal(t, nil, err)
}

func TestStoreCountPortForXc(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockServer := elastic2.NewMockServer()
	defer mockServer.Close()
	esClient, _ := mockServer.NewElasticClient()
	esDB := &elasticx.ElasticDatabase{Client: esClient}

	store := NewStore(esDB)

	port := m_asset.CardinalityAggregation{
		Value: 1,
	}
	portData, err := json.Marshal(port)
	assert.NoError(t, err)

	new_port_agg := m_asset.NewPortCardinalityAggregation{
		NewPort: m_asset.CardinalityAggregation{
			Value: 1,
		},
	}
	new_port_aggData, err := json.Marshal(new_port_agg)
	assert.NoError(t, err)

	defer gomonkey.ApplyMethodReturn(
		store.Instance.Client.Search(),
		"Do",
		&elastic.SearchResult{
			Aggregations: elastic.Aggregations{
				"port":         (*json.RawMessage)(&portData),
				"new_port_agg": (*json.RawMessage)(&new_port_aggData),
			},
			Hits: &elastic.SearchHits{
				TotalHits: 20,
			},
		},
		nil).Reset()

	defer gomonkey.ApplyFuncReturn(es_asset.ConvertIpContent, []elastic.Query{elastic.NewTermsQuery("ip")}).Reset()

	ipRangeManager := "test_ipRangeManager"
	_, err = store.CountPortForXc(ipRangeManager)
	assert.Equal(t, nil, err)
}

func TestStoreAssetOverviewCountPortProtocol(t *testing.T) {
	t.Run("query err", func(t *testing.T) {
		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)
		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{},
			fmt.Errorf("mock err")).Reset()

		ipRangeManager := ""
		ports, protocol, err := store.AssetOverviewCountPortProtocol(ipRangeManager)
		assert.Equal(t, []m_overviews.Index{}, ports)
		assert.Equal(t, []m_overviews.Index{}, protocol)
		assert.Equal(t, fmt.Errorf("mock err"), err)
	})
	t.Run("success case", func(t *testing.T) {
		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		store := NewStore(esDB)
		port := m_asset.IntAggregations{
			Buckets: []m_asset.IntBuckets{
				{
					Key:      22,
					DocCount: 125,
				},
			},
		}
		port_data, err := json.Marshal(port)
		assert.NoError(t, err)

		protocol := m_overviews.Aggregations{
			Buckets: []m_overviews.Buckets{
				{
					Key:      "",
					DocCount: 33,
				},
				{
					Key:      "http",
					DocCount: 20,
				},
			},
		}
		protocol_data, err := json.Marshal(protocol)
		assert.NoError(t, err)
		defer gomonkey.ApplyMethodReturn(
			store.Instance.Client.Search(),
			"Do",
			&elastic.SearchResult{
				Aggregations: elastic.Aggregations{
					"port":     (*json.RawMessage)(&port_data),
					"protocol": (*json.RawMessage)(&protocol_data),
				},
			},
			nil).Reset()

		ipRangeManager := ""
		ports, protocols, err := store.AssetOverviewCountPortProtocol(ipRangeManager)
		assert.Equal(t, []m_overviews.Index{
			{
				Name:  "22",
				Count: 125,
			},
		}, ports)
		assert.Equal(t, []m_overviews.Index{
			{
				Name:  "http",
				Count: 20,
			},
		}, protocols)
		assert.Equal(t, nil, err)
	})
}

func TestSetOverViewsAggs(t *testing.T) {
	t.Run("json.Unmarshal err", func(t *testing.T) {
		data := make([]m_asset.LabelDistribution, 0)
		defer gomonkey.ApplyFuncReturn(json.Unmarshal, fmt.Errorf("mock err")).Reset()
		rawMessage := json.RawMessage(`{"aa":123}`)
		result, err := SetOverViewsAggs("管理单元", "company", &rawMessage, data)
		assert.Error(t, err)
		assert.Equal(t, data, result)
	})
	t.Run("success case", func(t *testing.T) {
		data := make([]m_asset.LabelDistribution, 0)
		rawMessage := json.RawMessage(`{
"buckets": [
{
"key": "",
"doc_count": 1109
}
,
{
"key": "票务系统",
"doc_count": 147
}
]
}`)
		result, err := SetOverViewsAggs("管理单元", "company", &rawMessage, data)
		assert.NoError(t, err)
		fmt.Println("---------", result)
		assert.Equal(t, "管理单元", result[0].Name)
		assert.Equal(t, "company", result[0].Key)
		assert.Len(t, result[0].Data, 1)
		assert.Equal(t, "票务系统", result[0].Data[0].Name)
		assert.Equal(t, 147, result[0].Data[0].Value)
	})
}

func TestCalRuleCount(t *testing.T) {

	t.Run("success case", func(t *testing.T) {
		RuleInfos := []*m_overviews.XcRuleInfos{
			{
				SoftHardCode: 1,
				SecondCatTag: "中间件",
				FirstCatTag:  "",
				Company:      "",
				Title:        "title1",
				IsXc:         1,
			},
			{
				SoftHardCode: 1,
				SecondCatTag: "操作系统",
				FirstCatTag:  "",
				Company:      "",
				Title:        "title2",
				IsXc:         0,
			},
			{
				SoftHardCode: 1,
				SecondCatTag: "数据库系统",
				FirstCatTag:  "",
				Company:      "",
				Title:        "title3",
				IsXc:         0,
			},
			{
				SoftHardCode: 1,
				SecondCatTag: "数据库系统",
				FirstCatTag:  "",
				Company:      "",
				Title:        "title3",
				IsXc:         0,
			},
		}
		rules := m_overviews.XcRuleInfoData{XcRuleInfos: RuleInfos}

		ruleMap := map[string][]*m_overviews.Rule{
			"中间件":   make([]*m_overviews.Rule, 0),
			"操作系统":  make([]*m_overviews.Rule, 0),
			"数据库系统": make([]*m_overviews.Rule, 0),
		}
		SecondCat := []string{"中间件", "操作系统", "数据库系统"}

		CalRuleCount(&rules, SecondCat, "0", ruleMap)

		assert.Len(t, ruleMap["中间件"], 0)
		assert.Equal(t, 1, ruleMap["操作系统"][0].Count)
		assert.Equal(t, "title2", ruleMap["操作系统"][0].Title)
		assert.Equal(t, 2, ruleMap["数据库系统"][0].Count)
		assert.Equal(t, "title3", ruleMap["数据库系统"][0].Title)

	})
}
