package elasticx

import (
	"context"

	"git.gobies.org/foeye/foeye3/model/m_asset"

	"git.gobies.org/foeye/foeye3/api/risk/asset/exclusive"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/model/m_product_inspection"
	"git.gobies.org/foeye/foeye3/model/m_user_rules"
)

// ElasticSearch index related constant definition for {subdomain}.
const (
	IndexNameOfServices = "fofaee_service"
	TypeNameOfService   = "service"
)

type IndexServiceStore interface {
	GetListAllWriteToFile(ctx context.Context, filename string, ops ...Option) (string, error)
	GetServiceByFofa(query string, state bool) (map[string]*m_user_rules.ServiceSubdomain, error)
	UpdateUserRule(data map[string]*m_user_rules.ServiceSubdomain) error
	GetServiceByRuleId(ruleId uint) (map[string]*m_user_rules.ServiceSubdomain, error)
	GetUseProtocol() ([]m_product_inspection.UseProtocol, error)
	GetUsePort() ([]m_product_inspection.UsePort, error)
	GetUnrecognizedProtocol() ([]m_product_inspection.UnrecognizedProtocol, error)
	GetUnknownAsset() ([]m_product_inspection.UnknownAsset, error)
	GetUnknownAssetBySize(size int) ([]m_product_inspection.UnknownAsset, error)
	GetAssetFeature() ([]m_product_inspection.Service, error)
	CreateBulkAssetFeature(data []m_product_inspection.Service) error
	RiskServiceStore
	GetAsnCertLatitudeAndLongitudeByIp(ip string) (map[int]m_asset.AssetAsnCertLatitudeAndLongitude, error)
}
type RiskServiceStore interface {
	GetServiceSubdomainIp(serviceSubdomainSearchSource *elastic.SearchSource) ([]string, error)
	GetServiceSubdomainInfo(ipPortVulFile map[string]map[string][]map[int]string) (map[string][]exclusive.RiskAServiceSubdomainInfo, error)
}

func IndexNameOfService() string {
	return IndexNameOfServices
}
