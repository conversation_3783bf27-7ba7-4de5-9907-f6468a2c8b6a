package es_asset

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.gobies.org/foeye-dependencies/logger"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/internal/mux/packages/exchange/e_assets"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"git.gobies.org/foeye/foeye3/responses/r_common"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/model/m_second_categories"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/util/inactivated"
	"git.gobies.org/foeye/foeye3/responses/r_asset"
	"git.gobies.org/foeye/foeye3/responses/r_asset_history"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"
)

var (
	Indexes = map[string]string{
		"asset":          elasticx.IndexNameOfAsset(),
		"service":        elasticx.IndexNameOfService(),
		"subdomain":      elasticx.IndexNameOfSubDomain(),
		"fofaee_siteurl": elasticx.IndexNameOfSiteurl(),
	}

	DomainIndex = map[string]string{
		"fofaee_domain_assets": elasticx.IndexNameOfDomainAsset(),
		"fofaee_assets":        elasticx.IndexNameOfAsset(),
	}

	defaultSource = []string{
		"os",
		"os_version",
		"ip",
		"state",
		"vuls",
		"name",
		"mac",
		"port_list",
		"city",
		"add_way",
		"company",
		"business_app",
		"username",
		"computer_room",
		"custom_fields",
		"rule_infos",
		"createtime",
		"lastupdatetime",
	}

	aulSource = []string{
		"ip",                 // IP地址
		"common_title",       // 漏洞名称
		"cveId",              // CVE编号
		"vulType",            // 漏洞类型
		"level",              // 漏洞等级
		"createtime",         // 发现时间
		"lastupdatetime",     // 上次扫描
		"name",               // 备注信息
		"last_response",      // 漏洞响应结果
		"common_description", // 漏洞描述
		"common_impact",      // 漏洞危害
		"recommandation",     // 解决方案
		"state",              // 状态
		"rule_infos",         // 规则信息
		"createtime",         // 创建时间
		"vulfile",            // 漏洞文件
	}

	violationSource = []string{
		"ip",             // ip地址
		"content",        // 违规端口
		"createtime",     // 发现时间
		"noticed_at",     // 通报时间
		"inspected_at",   // 核查时间
		"status",         // 修复情况
		"violation_type", // 检测类型
	}
)

// ESAssetDatabaseInter interface
type ESAssetDatabaseInter interface {
	GetSourceIps() ([]string, error)
	GetAssetDetails(ctx *gin.Context, id string) (*m_asset.Asset, error)
	GetAssetVulesDetails(ctx *gin.Context, ip string, filename string) (*m_asset.AssetVulsDetail, error)
	SubdomainBodyDetails(ctx *gin.Context, ip string, port string) (*map[string]interface{}, error)
	GetAssetList(ctx *gin.Context, query *r_asset.QueryListAndKeyword, portMaps map[int]bool, ipRangeManager string, isLicenseMosaic bool) (int64, []*m_asset.Asset, error)
	GetAssetFilterCondition(ctx *gin.Context, components []*m_second_categories.SecondCategories, tagMaps map[string]string, ipRangeManager string) (interface{}, error)
	GetAssetStatistics(ctx *gin.Context, query *r_asset.QueryListAndKeyword, tagMaps map[string]string, components []*m_second_categories.SecondCategories, from bool, ipRangeManager string) (interface{}, error)
	DeleteAssets(ctx *gin.Context, query *r_asset.QueryListAndKeyword, ipRangeManager string) error
	DeleteAssetsMux(ctx *gin.Context, query *e_assets.RuquestQueryListAndKeywordDeleteAssetsMux) error
	UpdateAssetTags(ctx *gin.Context, query *r_asset.QueryListAndKeyword, ipRangeManager string) error
	UpdateCustomNames(query *r_asset.QueryListAndKeyword, ipRangeManager string, customTags map[string]string) error
	GetListWithCategory(ctx *gin.Context, categorys []*m_second_categories.SecondCategories) (interface{}, error)
	GetAssetDetailsInfo(ctx *gin.Context, item *m_asset.AssetDetails) (*m_asset.Asset, error)
	GetThreatsStatistics(ctx *gin.Context, repaired bool, tagMaps map[string]string, ipRangeManager string) (*r_asset_history.ThreatInfo, error)
	GetViolationsStatistics(ctx *gin.Context, repaired bool, tagMaps map[string]string, ipRangeManager string) (*r_asset_history.ComplianceInfo, error)
	GetAssetVulesDetailsByIp(ctx *gin.Context, item *m_asset.AssetDetails) ([]*m_asset.AssetAulDetail, error)
	GetAssetViolationListByIp(ctx *gin.Context, item *m_asset.AssetDetails) ([]*m_asset.AssetViolation, error)
	IsDomain(ctx *gin.Context) (bool, error)
	GetDomainInfo(ctx *gin.Context, item *m_asset.AssetDetails) (m_asset.Domain, error)

	GetRuleInfoByIp(ctx *gin.Context, ips []string) (map[string]int, error)
}

// ESAssetDatabase operation instance of module.
type ESAssetDatabase struct {
	Instance     *elasticx.ElasticDatabase
	mysqlFactory database.Factory
	configure    *config.Configure
}

func NewESAssetDatabase(configure *config.Configure, es *elasticx.ElasticDatabase, mysqlFactory database.Factory) *ESAssetDatabase {
	c := &ESAssetDatabase{
		Instance:     es,
		configure:    configure,
		mysqlFactory: mysqlFactory,
	}

	return c
}

// GetSourceIps 资产ip
func (asset *ESAssetDatabase) GetSourceIps() ([]string, error) {
	searchResult, err := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		SearchSource(elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"ip"}, nil)).
		Size(10000000).
		Pretty(true). // pretty print request and response JSON
		Do(context.Background()) // execute
	if err != nil {
		return nil, err
	}
	if searchResult.TotalHits() == 0 {
		return nil, nil
	}
	var res []string
	for _, hit := range searchResult.Hits.Hits {
		var r map[string]string
		err := json.Unmarshal(*hit.Source, &r)
		if err != nil {
			continue
		}
		res = append(res, r["ip"])
	}
	return res, nil
}

// GetAssetDetails 资产详情
func (asset *ESAssetDatabase) GetAssetDetails(ctx *gin.Context, id string) (*m_asset.Asset, error) {
	query := elastic.NewTermQuery("ip", id)
	searchSource := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"state", "name", "mac", "username", "business_app", "company", "computer_room", "custom_fields"}, nil)

	searchResult, err := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		SearchSource(searchSource).
		Pretty(true).
		Query(query).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	total := searchResult.TotalHits()
	if total == 0 {
		return nil, nil
	}
	item := new(m_asset.Asset)
	err = json.Unmarshal(*searchResult.Hits.Hits[0].Source, &item)
	if err != nil {
		return nil, err
	}
	item.ConvertCommon()

	return item, nil
}

// GetAssetDetailsInfo 任务资产详情
func (asset *ESAssetDatabase) GetAssetDetailsInfo(ctx *gin.Context, item *m_asset.AssetDetails) (*m_asset.Asset, error) {
	boolQuery := elastic.NewBoolQuery().Must()
	var indexName string
	if item.Id != "" {
		boolQuery.Must(elastic.NewTermQuery("ip", item.Id))
		indexName = elasticx.IndexNameOfAsset()
	} else if item.TaskId != "" {
		if strings.Contains(item.TaskId, "_") {
			str := strings.Split(item.TaskId, "_")
			if len(str) == 2 && str[1] != "" {
				boolQuery.Must(elastic.NewTermQuery("task_id", str[0]))
				boolQuery.Must(elastic.NewTermQuery("ip", str[1]))
			}
		}
		indexName = elasticx.IndexNameOfTaskAsset()
	}
	searchResult, err := asset.Instance.Client.Search().
		Index(indexName).
		Type("ips").
		Pretty(true).
		Query(boolQuery).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	if searchResult.TotalHits() == 0 {
		return nil, nil
	}

	instance := new(m_asset.Asset)
	err = json.Unmarshal(*searchResult.Hits.Hits[0].Source, &instance)
	if err != nil {
		return nil, err
	}

	if instance.OsVersion != "" {
		str := strings.Split(instance.OsVersion, "/")
		if len(str) == 2 && str[0] != "" && str[1] != "" && str[1] != "0" {
			instance.OS = instance.OsVersion
		}
	}

	instance.ConvertCommon()

	return instance, nil
}

// GetAssetVulesDetails 资产列表危险属性漏洞详情接
func (asset *ESAssetDatabase) GetAssetVulesDetails(ctx *gin.Context, ip string, filename string) (*m_asset.AssetVulsDetail, error) {
	query := elastic.NewBoolQuery()
	// query.Must(elastic.NewTermsQuery("ip", ip))
	query.Must(elastic.NewTermsQuery("vulfile", filename))
	// 获取需要拿出来的字段
	defaults := []string{
		"common_title",
		"level",
		"cveId",
		"common_description",
		"common_impact",
		"recommandation",
	}
	searchResult, err := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		SearchSource(elastic.NewSearchSource().FetchSourceIncludeExclude(defaults, nil)).
		Pretty(true).
		Query(query).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	//mosso.DebugShowContentWithJSON(searchResult)
	total := searchResult.TotalHits()
	if total == 0 {
		return nil, nil
	}
	item := new(m_asset.AssetVulsDetail)

	err = json.Unmarshal(*searchResult.Hits.Hits[0].Source, &item)
	if err != nil {
		return nil, err
	}

	return item, nil
}

// SubdomainBodyDetails 获取网页源代码
func (asset *ESAssetDatabase) SubdomainBodyDetails(ctx *gin.Context, ip string, port string) (*map[string]interface{}, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("ip", ip))
	query.Must(elastic.NewTermsQuery("port", port))
	// 获取需要拿出来的字段
	defaults := []string{
		"body",
	}
	searchResult, err := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfSubDomain()).
		SearchSource(elastic.NewSearchSource().FetchSourceIncludeExclude(defaults, nil)).
		Pretty(true).
		Query(query).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	// debuger.ShowContentForJSON(searchResult, true, true, true)
	total := searchResult.TotalHits()
	if total == 0 {
		return nil, nil
	}
	item := new(map[string]interface{})

	err = json.Unmarshal(*searchResult.Hits.Hits[0].Source, &item)
	if err != nil {
		return nil, err
	}
	return item, nil
}

// GetAssetStatistics 统计数据
func (asset *ESAssetDatabase) GetAssetStatistics(
	ctx *gin.Context,
	q *r_asset.QueryListAndKeyword,
	tagMaps map[string]string,
	components []*m_second_categories.SecondCategories,
	from bool,
	ipRangeManager string,
) (interface{}, error) {
	query := getQueryFromRequest(asset.configure, q, asset)

	/*---------------------------分域---------------------------------*/
	ipQueryList := convertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	tagKeys := make([]string, 0)
	for key := range tagMaps {
		tagKeys = append(tagKeys, key)
	}
	searchResult, err := getDataResult(ctx, asset.configure, asset, query, tagKeys)
	if err != nil {
		return nil, err
	}

	res := AssetStatistics(searchResult, components, from, tagMaps)
	// 将map转换成切片
	resData := make([]map[string]interface{}, 0)
	for i := 0; i < 15; i++ {
		if len(res[i]) != 0 {
			resData = append(resData, res[i])
		}

	}
	// return res, err
	return resData, err
}

// AssetStatistics 函数根据传入的搜索结果、组件列表、from参数和标签映射表，返回统计信息的映射表
//
// searchResult: elastic的搜索结果对象
// components: 组件列表
// from: 是否从特定的来源获取数据
// tagMaps: 自定义标签的映射表
//
// 返回值:
// 一个map[int]map[string]interface{}类型的映射表，其中key为整数索引，value为包含多个统计信息的映射表
func AssetStatistics(searchResult *elastic.SearchResult, components []*m_second_categories.SecondCategories, from bool, tagMaps map[string]string) map[int]map[string]interface{} {
	res := make(map[int]map[string]interface{}, 0)
	ips := map[string]interface{}{
		"key": "ip", "doc_count": int(searchResult.TotalHits()), "title": "存活IP",
	}
	// res = append(res, ips)
	res[0] = ips
	tags := make([]interface{}, 0)
	tagsTotal := 0
	for key, rawMessage := range searchResult.Aggregations {
		items := make(map[string]interface{})
		switch key {
		// 左侧统计 Start
		case "ports":
			SetItem("端口", "ports", rawMessage, items, 2, res)
		case "protocols":
			SetItem("服务", "protocols", rawMessage, items, 3, res)
		case "rules":
			SetItem("产品", "rules", rawMessage, items, 5, res)
		case "rule_companies":
			SetItem("厂商", "rule_companies", rawMessage, items, 4, res)
		case "first_tags":
			firstTags := m_asset.UnmarshalJsonRawMessageForNestedAggregation(*rawMessage)
			secondRawMessage := searchResult.Aggregations["second_tags"]
			secondTags := m_asset.UnmarshalJsonRawMessageForNestedAggregation(*secondRawMessage)

			firstCategoriesRawMessage := searchResult.Aggregations["first_categories"]
			firstCategories := m_asset.UnmarshalJsonRawMessageForCategoryCompanyProductAggregation(*firstCategoriesRawMessage)
			secondCategoriesRawMessage := searchResult.Aggregations["second_categories"]
			secondCategories := m_asset.UnmarshalJsonRawMessageForCategoryCompanyProductAggregation(*secondCategoriesRawMessage)

			_, ruleCategories, categoryWithCompanyProduct, total := ConvertComponents(firstTags, secondTags, firstCategories, secondCategories, components)

			items["title"] = "组件"
			items["key"] = "rule_categories"
			items["doc_count"] = int(total)
			items["children"] = ruleCategories
			res[1] = items
			if from {
				itemsWithCompanyAndProduct := map[string]interface{}{
					"title":     "子类",
					"key":       "category_company_product",
					"doc_count": int(total),
					"children":  categoryWithCompanyProduct,
				}
				res[6] = itemsWithCompanyAndProduct
			}
		case "companies":
			tagsTotal, tags = SetCompanies(rawMessage, items, tagsTotal, tags)

		case "managers":
			tagsTotal, tags = SetManagers(rawMessage, items, tagsTotal, tags)

		case "business_app":
			tagsTotal, tags = SetBusiness(rawMessage, items, tagsTotal, tags)

		case "computer_room":
			tagsTotal, tags = SetComputerRoom(rawMessage, items, tagsTotal, tags)

		case "asset_level":
			tagsTotal, tags = SetAssetLevel(rawMessage, items, tagsTotal, tags)

		// 左侧统计 End
		// 高危统计 Start
		case "remote_ops":
			count := m_asset.UnmarshalJsonRawMessageForHigherLevel(*rawMessage)
			items["title"] = "远程运维"
			items["key"] = "remote_ops"
			items["doc_count"] = count

			res[7] = items
		case "vuls_total":
			vuls := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			count := Statistics(vuls)
			items["title"] = "有重要漏洞的IP"
			items["key"] = "vuls_total"
			items["doc_count"] = count

			res[8] = items
		case "proxy_ware":
			count := m_asset.UnmarshalJsonRawMessageForHigherLevel(*rawMessage)
			items["title"] = "安装了代理软件的IP"
			items["key"] = "proxy_ware"
			items["doc_count"] = count

			res[9] = items
		case "scanners":
			count := m_asset.UnmarshalJsonRawMessageForHigherLevel(*rawMessage)
			items["title"] = "扫描器"
			items["key"] = "scanners"
			items["doc_count"] = count

			res[10] = items
		case "integration_tools":
			count := m_asset.UnmarshalJsonRawMessageForHigherLevel(*rawMessage)
			items["title"] = "集成工具"
			items["key"] = "integration_tools"
			items["doc_count"] = count

			res[11] = items
		case "no_standard_ports_ips":
			count := m_asset.UnmarshalJsonRawMessageForHigherLevel(*rawMessage)
			items["title"] = "开放非标端口的IP"
			items["key"] = "port"
			items["doc_count"] = count

			res[12] = items
		case "protocol_ips":
			count := m_asset.UnmarshalJsonRawMessageForHigherLevel(*rawMessage)
			items["title"] = "开放高危端口及服务的IP"
			items["key"] = "protocol_ips"
			items["doc_count"] = count

			res[13] = items

		// 高危统计 End
		default:
			// 是否自定义标签 左侧统计
			if !strings.Contains(key, "custom_fields") {
				continue
			}
			existedKey := key[14:]
			v, ok := tagMaps[existedKey]
			if !ok {
				continue
			}
			value := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			if len(value) == 0 {
				continue
			}

			temp := make([]map[string]interface{}, 0)
			for _, tag := range value {
				item := make(map[string]interface{})
				item["title"] = tag.Key
				item["key"] = tag.Key
				item["doc_count"] = tag.Count
				temp = append(temp, item)
			}

			count := Statistics(value)
			items["title"] = v
			items["key"] = key
			items["doc_count"] = count
			items["children"] = temp
			tagsTotal += count
			tags = append(tags, items)

		}
	}
	res[14] = map[string]interface{}{
		"title":     "标签",
		"key":       "tags",
		"doc_count": tagsTotal,
		"children":  tags,
	}
	return res
}

func SetAssetLevel(rawMessage *json.RawMessage, items map[string]interface{}, tagsTotal int, tags []interface{}) (int, []interface{}) {
	assetLevel := m_asset.UnmarshalJsonRawMessage(*rawMessage)
	temp := make([]map[string]interface{}, 0)
	for _, tag := range assetLevel {
		item := make(map[string]interface{})
		item["title"] = tag.Key
		item["key"] = tag.Key
		item["doc_count"] = tag.Count
		temp = append(temp, item)
	}

	count := Statistics(assetLevel)
	items["title"] = "资产等级"
	items["key"] = "asset_level"
	items["doc_count"] = count
	items["children"] = temp
	if len(assetLevel) != 0 {
		tagsTotal += count
		tags = append(tags, items)
	}
	return tagsTotal, tags
}

func SetComputerRoom(rawMessage *json.RawMessage, items map[string]interface{}, tagsTotal int, tags []interface{}) (int, []interface{}) {
	computerRoom := m_asset.UnmarshalJsonRawMessage(*rawMessage)
	temp := make([]map[string]interface{}, 0)
	for _, tag := range computerRoom {
		item := make(map[string]interface{})
		item["title"] = tag.Key
		item["key"] = tag.Key
		item["doc_count"] = tag.Count
		temp = append(temp, item)
	}

	count := Statistics(computerRoom)
	items["title"] = "机房信息"
	items["key"] = "computer_room"
	items["doc_count"] = count
	items["children"] = temp
	if len(computerRoom) != 0 {
		tagsTotal += count
		tags = append(tags, items)
	}
	return tagsTotal, tags
}

func SetCompanies(rawMessage *json.RawMessage, items map[string]interface{}, tagsTotal int, tags []interface{}) (int, []interface{}) {
	companies := m_asset.UnmarshalJsonRawMessage(*rawMessage)
	temp := make([]map[string]interface{}, 0)
	for _, tag := range companies {
		item := make(map[string]interface{})
		item["title"] = tag.Key
		item["key"] = tag.Key
		item["doc_count"] = tag.Count
		temp = append(temp, item)
	}

	count := Statistics(companies)
	items["title"] = "管理单元"
	items["key"] = "companies"
	items["doc_count"] = count
	items["children"] = temp
	if len(companies) != 0 {
		tagsTotal += count
		tags = append(tags, items)
	}
	return tagsTotal, tags
}

func SetManagers(rawMessage *json.RawMessage, items map[string]interface{}, tagsTotal int, tags []interface{}) (int, []interface{}) {
	managers := m_asset.UnmarshalJsonRawMessage(*rawMessage)
	temp := make([]map[string]interface{}, 0)
	for _, tag := range managers {
		item := make(map[string]interface{})
		item["title"] = tag.Key
		item["key"] = tag.Key
		item["doc_count"] = tag.Count
		temp = append(temp, item)
	}

	count := Statistics(managers)
	items["title"] = "负责人信息"
	items["key"] = "managers"
	items["doc_count"] = count
	items["children"] = temp
	if len(managers) != 0 {
		tagsTotal += count
		tags = append(tags, items)
	}
	return tagsTotal, tags
}

func SetBusiness(rawMessage *json.RawMessage, items map[string]interface{}, tagsTotal int, tags []interface{}) (int, []interface{}) {
	businessApp := m_asset.UnmarshalJsonRawMessage(*rawMessage)
	temp := make([]map[string]interface{}, 0)
	for _, tag := range businessApp {
		item := make(map[string]interface{})
		item["title"] = tag.Key
		item["key"] = tag.Key
		item["doc_count"] = tag.Count
		temp = append(temp, item)
	}

	count := Statistics(businessApp)
	items["title"] = "业务系统"
	items["key"] = "business_app"
	items["doc_count"] = count
	items["children"] = temp
	if len(businessApp) != 0 {
		tagsTotal += count
		tags = append(tags, items)
	}
	return tagsTotal, tags
}

func SetItem(title, key string, rawMessage *json.RawMessage, items map[string]interface{}, index int, res map[int]map[string]interface{}) {
	children := m_asset.UnmarshalJsonRawMessage(*rawMessage)
	items["title"] = "端口"
	items["key"] = "ports"
	items["doc_count"] = Statistics(children)
	items["children"] = children
	res[index] = items
}

// GetAssetFilterCondition 聚合返回高级筛选条件
func (asset *ESAssetDatabase) GetAssetFilterCondition(ctx *gin.Context, components []*m_second_categories.SecondCategories, tagMaps map[string]string, ipRangeManager string) (interface{}, error) {
	tagKeys := make([]string, 0)
	for key := range tagMaps {
		tagKeys = append(tagKeys, key)
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := convertIpContent(ipRangeManager)
	query := new(elastic.BoolQuery)
	if len(ipQueryList) > 0 {
		query = elastic.NewBoolQuery().Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	searchResult, err := getDataResult(ctx, asset.configure, asset, query, tagKeys)
	if err != nil {
		return nil, err
	}

	if len(searchResult.Aggregations) == 0 {
		return nil, nil
	}
	result := make(map[string]interface{})
	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "ports":
			result["ports"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		case "protocols":
			result["protocols"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		case "rule_companies":
			result["rule_companies"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		case "companies":
			result["companies"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		case "managers":
			result["managers"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		case "business_app":
			result["business_app"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		case "computer_room":
			result["computer_room"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		case "asset_level":
			result["asset_level"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		case "rules":
			result["rules"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		case "first_tags":
			firstTags := m_asset.UnmarshalJsonRawMessageForNestedAggregation(*rawMessage)
			secondRawMessage := searchResult.Aggregations["second_tags"]
			secondTags := m_asset.UnmarshalJsonRawMessageForNestedAggregation(*secondRawMessage)

			firstCategoriesRawMessage := searchResult.Aggregations["first_categories"]
			firstCategories := m_asset.UnmarshalJsonRawMessageForCategoryCompanyProductAggregation(*firstCategoriesRawMessage)
			secondCategoriesRawMessage := searchResult.Aggregations["second_categories"]
			secondCategories := m_asset.UnmarshalJsonRawMessageForCategoryCompanyProductAggregation(*secondCategoriesRawMessage)

			ruleCategories, _, _, _ := ConvertComponents(firstTags, secondTags, firstCategories, secondCategories, components)
			result["rule_categories"] = ruleCategories
		default:
			// 是否自定义标签 左侧统计
			if !strings.Contains(key, "custom_fields") {
				continue
			}
			existedKey := key[14:]
			v, ok := tagMaps[existedKey]
			if !ok {
				continue
			}
			tags := make(map[string]interface{})
			value := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			if len(value) == 0 {
				continue
			}
			tags["name"] = v
			tags["tags"] = value
			result[existedKey] = tags
		}

		if key == "rules" {
			var rules []string
			for _, v := range result["rules"].([]*m_asset.Bucket) {
				rules = append(rules, v.Key)
			}
			conditions := []interface{}{"product IN (?)", rules}
			list, err := asset.mysqlFactory.UserRule().GetList(0, 0, conditions...)
			if err != nil {
				return nil, err
			}

			result["database_rules"] = m_asset.UnmarshalJsonRawMessage(*rawMessage)
		}

	}

	//  查询 扫描类型的数量 ============
	var types []string
	boolQuery := elastic.NewBoolQuery()
	fetchSource := elastic.NewFetchSourceContext(true).Include("resolution") // 包含 resolution 字段

	// 执行搜索查询
	searchResult, err = asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfDomainAsset()). // 指定索引
		Query(boolQuery). // 查询条件
		FetchSourceContext(fetchSource). // 控制返回字段
		Do(ctx)
	if err != nil {
		return nil, err
	}

	// 遍历结果并提取 resolution.type
	for _, hit := range searchResult.Hits.Hits {
		var source map[string]interface{}
		hit_source, _ := json.Marshal(&hit.Source)
		if err := json.Unmarshal(hit_source, &source); err != nil {
			continue
		}

		// 提取 resolution 数组
		if resolutions, ok := source["resolution"].([]interface{}); ok {
			for _, res := range resolutions {
				if resMap, ok := res.(map[string]interface{}); ok {
					if resType, ok := resMap["type"].(string); ok {
						types = append(types, resType)
					}
				}
			}
		}
	}
	types = removeDuplicates(types)
	// 遍历输入数组
	var children []m_asset.Bucket
	for _, item := range types {

		// 创建 Child 对象并追加到切片
		children = append(children, m_asset.Bucket{
			Key:   item,
			Count: 1,
		})
	}
	result["type"] = children
	return result, nil
}

func removeDuplicates(elements []string) []string {
	seen := make(map[string]struct{})
	var result []string
	for _, elem := range elements {
		if _, exists := seen[elem]; !exists {
			seen[elem] = struct{}{}
			result = append(result, elem)
		}
	}
	return result
}

// GetAssetList get asset list from elasticsearch
func (asset *ESAssetDatabase) GetAssetList(
	ctx *gin.Context,
	q *r_asset.QueryListAndKeyword,
	portsMaps map[int]bool,
	ipRangeManager string, isLicenseMosaic bool,
) (int64, []*m_asset.Asset, error) {
	query := getQueryFromRequest(asset.configure, q, asset)
	if q.Type == "business" {
		query.MustNot(elastic.NewTermsQuery("business_app", ""))
	} else if q.Type == "domain" {
		query.Must(elastic.NewPrefixQuery("hosts", ""))
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := convertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	source := new(elastic.SearchSource)

	if q.Tab > 0 {
		q.Fields = append(q.Fields, "vuls", "custom_fields", "add_way", "_id", "state", "port_list", "ports", "protocols", "rule_infos")
		source.FetchSourceIncludeExclude(q.Fields, nil)
	} else {
		source.FetchSourceIncludeExclude(defaultSource, nil)
	}

	searchService := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		SearchSource(source).
		Query(query). // specify the query
		Pretty(true).
		SortBy(elastic.NewScriptSort(elastic.NewScript("doc['rule_tags'].values.size()"), "number").Desc()).
		Sort("lastupdatetime", false)

	if len(q.Ids) == 1 && q.Ids[0] == "" {
		searchService.Size(100000)
	} else {
		searchService.From((q.Number - 1) * q.Size).Size(q.Size)
	}
	searchResult, err := searchService. // take documents 0-9
		Pretty(true). // pretty print request and response JSON
		Do(ctx) // execute
	if err != nil {
		return 0, nil, err
	}
	total := searchResult.TotalHits()
	if total == 0 {
		return 0, nil, nil
	}
	// debuger.ShowContentForJSON(searchResult.Hits.Hits, true, true, true)

	var n int

	_, ipMaps := threats(asset)
	res := make([]*m_asset.Asset, 0)
	for _, hit := range searchResult.Hits.Hits {
		item := new(m_asset.Asset)
		err := json.Unmarshal(*hit.Source, &item)
		if err != nil {
			continue
		}

		// 处理漏洞详情去重_start
		if len(item.Vuls) > 0 {
			vulsNorepeats := make([]m_asset.VulsDetail, 0) // 存放不重复的数据
			VulsMap := make(map[string]bool, 0)
			for _, v := range item.Vuls {
				// k 已经存在，跳过此次循环继续下一次
				if _, ok := VulsMap[v.Name]; ok {
					continue
				}
				// key 不存在，加到map里面 并加到切片里面
				vulsNorepeats = append(vulsNorepeats, v)
				VulsMap[v.Name] = true
			}
			item.Vuls = vulsNorepeats
		}

		if isLicenseMosaic {
			item.Ip = inactivated.InactivatedAssetsAddMosaic(item.Ip, 1, n)
		}
		n++
		// 处理漏洞详情去重_end
		item.ConvertCommon()
		item.ConvertOpenProtocols(ipMaps)
		item.ConvertNoStandardPort(portsMaps)
		item.ConvertRemoteOpsAndProxyWareAndScannersAndIntegrationTools(sliceConvertMap(remoteOps), sliceConvertMap(proxyWares), sliceConvertMap(scanners), sliceConvertMap(integrationTools))
		res = append(res, item)
	}

	return total, res, nil
}

// DeleteAssets delete ip from Indexes
func (asset *ESAssetDatabase) DeleteAssets(ctx *gin.Context, query *r_asset.QueryListAndKeyword, ipRangeManager string) error {
	// 全部删除
	if query == nil {
		allQuery := elastic.NewMatchAllQuery()
		for _, index := range Indexes {
			_, err := asset.Instance.Client.DeleteByQuery(index).Query(allQuery).Refresh("true").Do(ctx)
			if err != nil {
				return err
			}
		}
		return nil
	}

	ids := query.CorrectIpV4()
	if len(ids) > 0 {
		for _, index := range Indexes {
			q := elastic.NewBoolQuery()
			q.Must(elastic.NewTermsQuery("ip", ids...))
			_, err := asset.Instance.Client.DeleteByQuery(index).Query(q).Refresh("true").Do(ctx)
			if err != nil {
				return err
			}
		}

		return nil
	} else if len(ids) == 0 && len(query.Ids) > 0 {
		return errors.New("wrong ip address")
	}
	q := &r_asset.QueryListAndKeyword{
		Search: query.Search,
	}
	queryRequest := getQueryFromRequest(asset.configure, q, asset)

	/*---------------------------分域---------------------------------*/
	ipQueryList := convertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		queryRequest.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	for _, index := range Indexes {
		_, err := asset.Instance.Client.DeleteByQuery(index).Query(queryRequest).Refresh("true").Do(ctx)
		if err != nil {
			return err
		}
	}
	return nil
}

// DeleteAssetsMux delete ip from Indexes
func (asset *ESAssetDatabase) DeleteAssetsMux(ctx *gin.Context, query *e_assets.RuquestQueryListAndKeywordDeleteAssetsMux) error {
	// 全部删除
	if query == nil {
		allQuery := elastic.NewMatchAllQuery()
		for _, index := range Indexes {
			_, err := asset.Instance.Client.DeleteByQuery(index).Query(allQuery).Refresh("true").Do(ctx)
			if err != nil {
				return err
			}
		}
		return nil
	}

	selecter := elastic.NewBoolQuery()
	switch query.Type {
	case "all":
		selecter.Must(elastic.NewMatchAllQuery())
		if query.Keyword != "" {
			selecter.Must(elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", query.Keyword)).Field("ip.ip_raw"))
		}
	case "query":
		selecter.Must(elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", query.Keyword)).Field("ip.ip_raw"))
	default:
		if query.Ids != nil && len(query.Ids) > 0 {
			ips := make([]interface{}, 0)
			for _, v := range query.Ids {
				ips = append(ips, v)
			}
			selecter.Must(elastic.NewTermsQuery("ip.ip_raw", ips...))
		}
	}

	for _, index := range Indexes {
		_, err := asset.Instance.Client.DeleteByQuery(index).Query(selecter).Refresh("true").Do(ctx)
		if err != nil {
			return err
		}
	}

	return nil
}

// UpdateAssetTags 编辑资产标签
func (asset *ESAssetDatabase) UpdateAssetTags(ctx *gin.Context, query *r_asset.QueryListAndKeyword, ipRangeManager string) error {
	ids := query.CorrectIpV4()

	script := make([]string, 0)
	params := make(map[string]interface{})
	for key, value := range query.UpdateFields {
		if strings.Contains(key, "custom_fields") {
			params[key[14:]] = value
			continue
		}
		s := fmt.Sprintf("ctx._source.%s = '%s'", key, value)
		script = append(script, s)
	}

	var ss *elastic.Script
	if len(params) > 0 {
		sc := ""
		for k := range params {
			sc += fmt.Sprintf("ctx._source.custom_fields.%s=params.%s;", k, k)
		}
		script = append(script, fmt.Sprintf("if (ctx._source.custom_fields==null){ctx._source.custom_fields=params.custom_fields}else{%s}", sc))
		tmp := make(map[string]interface{})
		tmp["custom_fields"] = params
		for k, v := range params {
			tmp[k] = v
		}
		ss = elastic.NewScript(strings.Join(script, ";")).Lang("painless").Params(tmp)
	} else {
		ss = elastic.NewScript(strings.Join(script, ";"))
	}

	allIndexes := []string{
		elasticx.IndexNameOfAsset(),
		elasticx.IndexNameOfThreat(),
		elasticx.IndexNameOfViolation(),
	}
	if len(ids) > 0 {
		for _, index := range allIndexes {
			q := elastic.NewBoolQuery()
			q.Must(elastic.NewTermsQuery("ip", ids...))
			_, err := asset.Instance.Client.UpdateByQuery(index).ProceedOnVersionConflict().Query(q).Refresh("true").Script(ss).Do(ctx)
			if err != nil {
				return err
			}
		}

		return nil
	}

	updateByQuery := getQueryFromRequest(asset.configure, query, asset)

	/*---------------------------分域---------------------------------*/
	ipQueryList := convertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		updateByQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	for _, index := range allIndexes {
		_, err := asset.Instance.Client.UpdateByQuery(index).ProceedOnVersionConflict().Query(updateByQuery).Refresh("true").Script(ss).Do(ctx)
		if err != nil {
			return err
		}
	}
	return nil
}

// UpdateCustomNames 编辑资产标签,同步es的custom_names
func (asset *ESAssetDatabase) UpdateCustomNames(query *r_asset.QueryListAndKeyword, ipRangeManager string, customTags map[string]string) error {
	ids := query.CorrectIpV4()
	params := make(map[string]interface{})
	for key, value := range query.UpdateFields {
		if strings.Contains(key, "custom_fields") {
			params[key[14:]] = value
		}
	}

	var ss *elastic.Script
	if len(params) > 0 {
		tmp := make(map[string]interface{})
		customNames := make(map[string]string)
		customNamesSc := ""
		for k, _ := range params {
			customNamesSc += fmt.Sprintf("ctx._source.custom_names.%s=params.%s;", k, k)
			tmp[k] = customTags[k]
			customNames[k] = customTags[k]
		}
		tmp["custom_names"] = customNames
		ss = elastic.NewScript(
			fmt.Sprintf("if (ctx._source.custom_names==null){ctx._source.custom_names=params.custom_names}else{%s}",
				customNamesSc)).
			Lang("painless").Params(tmp)

		allIndexes := []string{
			elasticx.IndexNameOfAsset(),
		}
		if len(ids) > 0 {
			for _, index := range allIndexes {
				q := elastic.NewBoolQuery()
				q.Must(elastic.NewTermsQuery("ip", ids...))
				_, err := asset.Instance.Client.UpdateByQuery(index).
					ProceedOnVersionConflict().
					Query(q).
					Refresh("true").
					Script(ss).
					Do(context.Background())
				if err != nil {
					return err
				}
			}
			return nil
		}

		updateByQuery := getQueryFromRequest(asset.configure, query, asset)

		/*---------------------------分域---------------------------------*/
		ipQueryList := convertIpContent(ipRangeManager)
		if len(ipQueryList) > 0 {
			updateByQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
		}
		/*---------------------------分域---------------------------------*/

		for _, index := range allIndexes {
			_, err := asset.Instance.Client.UpdateByQuery(index).
				ProceedOnVersionConflict().
				Query(updateByQuery).
				Refresh("true").
				Script(ss).
				Do(context.Background())
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// Get Vulnerability details by Ip.
func (asset *ESAssetDatabase) GetAssetVulesDetailsByIp(ctx *gin.Context, item *m_asset.AssetDetails) ([]*m_asset.AssetAulDetail, error) {
	boolQuery := elastic.NewBoolQuery().Must()
	if item.Id != "" {
		boolQuery.Must(elastic.NewTermQuery("ip", item.Id))
		boolQuery.Must(elastic.NewTermQuery("task_ids", 0))
	} else if item.TaskId != "" {
		if strings.Contains(item.TaskId, "_") {
			str := strings.Split(item.TaskId, "_")
			if len(str) == 2 && str[0] != "" && str[1] != "" {
				boolQuery.Must(elastic.NewTermQuery("ip", str[1]))
				boolQuery.Must(elastic.NewTermQuery("task_ids", str[0]))
			}
		}
	}

	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range aulSource {
		fsc.Include(v)
	}

	AulInfos := []*m_asset.AssetAulDetail{}
	result, err := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		Type(elasticx.TypeNameOfThreats).
		FetchSourceContext(fsc).
		Pretty(true).
		Query(boolQuery).
		Sort("lastupdatetime", false).
		Do(ctx)

	if err != nil {
		return AulInfos, err
	}

	for _, hit := range result.Hits.Hits {
		aul := new(m_asset.AulByIpResponse)
		err := json.Unmarshal(*hit.Source, &aul)

		if err == nil {
			aulModule := new(m_asset.AulModule)
			assetAul := new(m_asset.AssetAulDetail)
			for _, value := range aul.RuleInfos {
				if value.Version != "" {
					aulModule.Icon = value.Title + "/" + value.Version
				} else {
					aulModule.Icon = value.Title
				}
				aulModule.Title = value.Title
				aul.AulModule = append(aul.AulModule, *aulModule)
			}
			assetAul = &aul.AssetAulDetail
			var tmp map[string]interface{}
			if err = json.Unmarshal(*hit.Source, &tmp); err != nil {
				return AulInfos, err
			}
			if _, ok := tmp["vulfile"].(string); ok {
				assetAul.VulFile = tmp["vulfile"].(string)
			}
			if assetAul.State == 1 || assetAul.State == 2 {
				assetAul.FixStatus = "unfixed"
			} else {
				assetAul.FixStatus = "fixed"
			}
			AulInfos = append(AulInfos, assetAul)
		}
	}
	return AulInfos, nil
}

// Get AssetViolationList by Ip.
func (asset *ESAssetDatabase) GetAssetViolationListByIp(ctx *gin.Context, item *m_asset.AssetDetails) ([]*m_asset.AssetViolation, error) {
	boolQuery := elastic.NewBoolQuery().Must()
	if item.Id != "" {
		boolQuery.Must(elastic.NewTermQuery("ip", item.Id))
	}

	fsc := elastic.NewFetchSourceContext(true)
	for _, v := range violationSource {
		fsc.Include(v)
	}

	assetViolationList := []*m_asset.AssetViolation{}
	result, err := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfViolation()).
		FetchSourceContext(fsc).
		Pretty(true).
		Query(boolQuery).
		Sort(elasticx.FieldNameOfCreatedTime, false).
		Do(ctx)

	if err != nil {
		return assetViolationList, err
	}

	for _, hit := range result.Hits.Hits {
		violation := new(m_asset.AssetViolation)
		json.Unmarshal(*hit.Source, &violation)
		err := json.Unmarshal(*hit.Source, &violation)
		if err == nil {
			violation.MonitoringType = violation.ViolationType
			violation.ViolationType = ""
			// 增加检测前缀，前端用来和content拼接
			for _, complianceMonitorItemsIn := range m_compliance_monitor.ComplianceMonitorItemsIns {
				if complianceMonitorItemsIn.Value == violation.MonitoringType {
					violation.ContentPre = complianceMonitorItemsIn.Name
				}
			}
			assetViolationList = append(assetViolationList, violation)
		}
	}
	return assetViolationList, nil
}

// IsDomain 资产管理 - 域名维度 判断新老页面
func (asset *ESAssetDatabase) IsDomain(ctx *gin.Context) (bool, error) {

	boolQuery := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())

	searchResult, err := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfDomainAsset()).
		Query(boolQuery).
		Size(60000).
		TrackTotalHits(true).
		Pretty(true).
		Do(ctx)
	if err != nil {
		return false, err
	}

	// 获取 total 字段
	totalHits := searchResult.Hits.TotalHits

	// 判断是否有数据
	if totalHits == 0 { // 无数据
		// 匹配资产总库有无数据
		boolQuery.Filter(elastic.NewExistsQuery("hosts"))
		searchResult, err = asset.Instance.Client.Search().
			Index(elasticx.IndexNameOfAssets).
			Query(boolQuery).
			Size(60000).
			TrackTotalHits(true).
			Pretty(true).
			Do(ctx)
		if err != nil {
			return false, err
		}
		totalHits = searchResult.Hits.TotalHits
		if totalHits == 0 {
			return true, nil
		}
		return false, nil
	} else {
		return true, nil
	}

}

// GetDomainInfo 域名画像
func (asset *ESAssetDatabase) GetDomainInfo(ctx *gin.Context, item *m_asset.AssetDetails) (m_asset.Domain, error) {
	// 将查询结果赋值到结构体
	var domain m_asset.Domain

	docID := item.Id

	// 根据 ID 查询文档
	getResult, err := asset.Instance.Client.Get().
		Index(elasticx.IndexNameOfDomainAsset()). // 指定索引
		Id(docID). // 指定文档 ID
		Do(ctx) // 执行请求
	if err != nil {
		if elastic.IsNotFound(err) {
			return domain, errors.New("未查询到结果")
		}
	}

	if getResult.Source != nil {
		if err := json.Unmarshal(*getResult.Source, &domain); err != nil {
			return domain, errors.New("未查询到结果")
		}
	}

	for i := 0; i < len(domain.ResolutionInfo); i++ {
		rtt := time.Duration(domain.ResolutionInfo[i].Rtt).String()
		parts := strings.Split(rtt, ".")
		rttInt, _ := strconv.Atoi(parts[0])
		domain.ResolutionInfo[i].Rtt = rttInt
		domain.ResolutionInfo[i].Createtime = domain.CreateTime
		domain.ResolutionInfo[i].Lastupdatetime = domain.Lastupdatetime
	}
	domain.CustomTag = domain.CustomFields
	domain.CustomFields = domain.CustomNames
	return domain, nil
}

// GetRuleInfoByIp 通过ip获取产品名称
func (asset *ESAssetDatabase) GetRuleInfoByIp(ctx *gin.Context, ips []string) (map[string]int, error) {

	boolQuery := elastic.NewBoolQuery()
	r := stringSliceToInterface(ips)
	boolQuery.Must(elastic.NewTermsQuery("_id", r...))

	fetchSourceContext := elastic.NewFetchSourceContext(true).Include("rule_infos.title")

	// 执行查询，获取特定字段 rule_infos.title
	searchResult, err := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfAssets). // 使用索引名
		Query(boolQuery). // 设置查询条件
		FetchSourceContext(fetchSourceContext). // 只返回 rule_infos.title 字段
		Size(60000). // 返回最大数量，60000 只是示例
		TrackTotalHits(true). // 跟踪总命中数
		Pretty(true). // 格式化输出
		Do(ctx) // 执行查询
	if err != nil {
		return nil, err
	}

	var titles []string
	for _, hit := range searchResult.Hits.Hits {
		var doc map[string]interface{}
		err := json.Unmarshal(*hit.Source, &doc)
		if err != nil {
			// 解析错误，跳过该条记录
			continue
		}

		if ruleInfos, ok := doc["rule_infos"].([]interface{}); ok {
			for _, item := range ruleInfos {
				if ruleInfoMap, ok := item.(map[string]interface{}); ok {
					if title, ok := ruleInfoMap["title"].(string); ok {
						titles = append(titles, title)
					}
				}
			}
		}
	}
	titleMap := convertToTitleCountMap(titles)
	return titleMap, nil
}

func stringSliceToInterface(data []string) []interface{} {
	r := make([]interface{}, 0, len(data))
	for _, item := range data {
		if item != "" {
			r = append(r, item)
		}
	}

	return r
}

// 将切片转换为 map，key 为 title，value 为该 title 的数量
func convertToTitleCountMap(titles []string) map[string]int {
	titleCount := make(map[string]int)

	// 遍历切片，统计每个 title 出现的次数
	for _, title := range titles {
		titleCount[title]++
	}

	return titleCount
}
