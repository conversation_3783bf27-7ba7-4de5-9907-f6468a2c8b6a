package es_asset

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_second_categories"
	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/packages/cidrx"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/responses/r_asset"
)

var (
	proxyWares = []string{
		"LCX", "Htran", "Portmap", "EW", "reGeorg", "frp",
		"iox", "nps", "netsh", "icmptunnel", "dns2tcp", "pingtunnel",
		"ptunnel", "冰蝎", "metasploit", "cobaltstrike", "tuuna",
		"nc", "ssocks", "ssf", "EarthWorm",
	}
	remoteOps = []string{
		"pcAnywhere-CA", "Symantec-pcAnywhere", "VNC", "TeamViewer",
	}
	scanners = []string{
		"Invicti-AWVS", "RAPID7-NeXpose-Security-Console", "tenable-Nessus", "Greenbone", "Mars", "资产灯塔系统",
	}
	integrationTools     = []string{"COBALTSTRIKE-团队服务器", "RAPID7-Metasploit"}
	publicRiskyProtocols = []string{
		"FTP", "SSH", "Telnet", "DNS", "RPC",
		"SNMP", "LDAP", "SMB", "Rsync", "Lotus", "MSSQL", "Oracle",
		"Zookeeper", "Docker", "MySQL", "RDP", "Glassfish", "DB2",
		"SysBase", "PostgreSQL", "PCAnywhere", "VNC", "Redis",
		"AJP", "Memcacache", "MongoDB", "socks4", "socks5", "Weblogic T3",
	}
	publicHttpRuleNames = []string{
		"apache", "axis2", "couchdb",
		"elasticsearch", "glassfish",
		"jonas", "jboss", "jenkins",
		"jetty", "nginx", "resin",
		"tomcat", "websphere",
		"weblogic", "zabbix",
	}
)

// downcase 转为小写
func downcase(params []string) []interface{} {
	res := make([]interface{}, len(params))
	for i, param := range params {
		res[i] = strings.ToLower(param)
	}
	return res
}

func sliceConvertMap(params []string) map[string]bool {
	res := make(map[string]bool)
	for _, param := range params {
		res[strings.ToLower(param)] = true
	}
	return res
}

// getDataResult 从es中取聚合数据
func getDataResult(ctx *gin.Context, configure *config.Configure, asset *ESAssetDatabase, query elastic.Query, tagKeys []string) (*elastic.SearchResult, error) {
	portsAggregation := elastic.NewTermsAggregation().Field("ports").Size(65535)
	protocolAggregation := elastic.NewTermsAggregation().Field("protocols").Size(65535)
	companyAggregation := elastic.NewTermsAggregation().Field("rule_infos.company").Size(65535)
	companiesAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("company").Size(10000)
	managersAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("username").Size(10000)
	computerRoomAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("computer_room").Size(10000)
	businessAppAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("business_app").Size(10000)
	assetLevelAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field("asset_level").Size(10000)
	rulesAggregation := elastic.NewTermsAggregation().Field("rule_infos.title").Size(65535)

	firstNumAggs := elastic.NewSumAggregation().Field("first_tag_num.num")
	firstAggs := elastic.NewTermsAggregation().Field("first_tag_num.first_cat_tag").SubAggregation("sums", firstNumAggs).Size(10000)
	firstTagNum := elastic.NewNestedAggregation().Path("first_tag_num").SubAggregation("tag_nums", firstAggs)

	secondNumAggs := elastic.NewSumAggregation().Field("second_tag_num.num")
	secondAggs := elastic.NewTermsAggregation().Field("second_tag_num.second_cat_tag").SubAggregation("sums", secondNumAggs).Size(10000)
	secondTagNum := elastic.NewNestedAggregation().Path("second_tag_num").SubAggregation("tag_nums", secondAggs)

	SecondTagAggregation := elastic.NewTermsAggregation().Field("rule_infos.second_cat_tag").Size(65535)
	firstTagAggregation := elastic.NewTermsAggregation().Field("rule_infos.first_cat_tag").Size(65535)
	firstTagAggregation.SubAggregation("children", SecondTagAggregation)

	// 分类下面产品和厂商
	firstCategoriesAggregation := elastic.NewTermsAggregation().Field("rule_infos.first_cat_tag").Size(10000)
	firstCategoriesAggregation.SubAggregation("companies", companyAggregation).SubAggregation("rules", rulesAggregation)
	secondCategoriesAggregation := elastic.NewTermsAggregation().Field("rule_infos.second_cat_tag").Size(10000)
	secondCategoriesAggregation.SubAggregation("companies", companyAggregation).SubAggregation("rules", rulesAggregation)

	// 危险资产统计 Start
	// 漏洞
	vlusAggregation := elastic.NewRangeAggregation().Field("vuls_count").Gt(1)
	// 开放非标端口
	// todo
	ports := []interface{}{
		"1389",
	}
	noStdPortIpsAggregation := elastic.NewFilterAggregation().Filter(elastic.NewTermsQuery("ports", ports...))
	// 代理软件
	proxyAggregation := elastic.NewFilterAggregation().Filter(elastic.NewTermsQuery("rule_infos.title", downcase(proxyWares)...))
	//	// 高危端口及服务
	var protocolsAggregation *elastic.FilterAggregation
	// 内网 intranet
	if configure.Server.ScanType == "intranet" {
		unFixedIps, _ := threats(asset)
		protocolsAggregation = elastic.NewFilterAggregation().Filter(elastic.NewTermsQuery("ip", unFixedIps...))
	} else if configure.Server.ScanType == "extranet" {
		http := []interface{}{"http", "https"}
		boolQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("protocols", http...)).Must(elastic.NewTermsQuery("rule_infos.title", downcase(publicHttpRuleNames)...))
		protocolsAggregation = elastic.NewFilterAggregation().Filter(elastic.NewTermsQuery("protocols", downcase(publicRiskyProtocols)...)).Filter(boolQuery)
	}
	// 外网 extranet
	// 集成工具
	toolsAggregation := elastic.NewFilterAggregation().Filter(elastic.NewTermsQuery("rule_infos.title", downcase(integrationTools)...))
	// 扫描器
	scannersAggregation := elastic.NewFilterAggregation().Filter(elastic.NewTermsQuery("rule_infos.title", downcase(scanners)...))
	//	// 远程运维
	remoteAggregation := elastic.NewFilterAggregation().Filter(elastic.NewTermsQuery("rule_infos.title", downcase(remoteOps)...))
	// 危险资产统计 End

	searchService := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		Size(0).
		Query(query).
		Aggregation("ports", portsAggregation).
		Aggregation("protocols", protocolAggregation).
		Aggregation("rule_companies", companyAggregation).
		Aggregation("companies", companiesAggregation).
		Aggregation("managers", managersAggregation).
		Aggregation("business_app", businessAppAggregation).
		Aggregation("computer_room", computerRoomAggregation).
		Aggregation("asset_level", assetLevelAggregation).
		Aggregation("rules", rulesAggregation).
		Aggregation("rule_categories", firstTagAggregation).
		Aggregation("first_tags", firstTagNum).
		Aggregation("second_tags", secondTagNum).
		Aggregation("vuls_total", vlusAggregation).
		Aggregation("integration_tools", toolsAggregation).
		Aggregation("scanners", scannersAggregation).
		Aggregation("proxy_ware", proxyAggregation).
		Aggregation("no_standard_ports_ips", noStdPortIpsAggregation).
		Aggregation("remote_ops", remoteAggregation).
		Aggregation("protocol_ips", protocolsAggregation).
		Aggregation("first_categories", firstCategoriesAggregation).
		Aggregation("second_categories", secondCategoriesAggregation)
	if len(tagKeys) > 0 {
		for _, key := range tagKeys {
			fieldKey := fmt.Sprintf("custom_fields.%s", key)
			tagAggregation := elastic.NewTermsAggregation().Include("[^\\S]+").Field(fieldKey).Size(65535)
			searchService.Aggregation(fieldKey, tagAggregation)
		}
	}

	searchResult, err := searchService.
		Pretty(true).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	return searchResult, nil
}

// getQueryFromRequest 构造es的query查询条件
func getQueryFromRequest(configure *config.Configure, q *r_asset.QueryListAndKeyword, asset *ESAssetDatabase) *elastic.BoolQuery {
	ids := q.CorrectIpV4()
	query := elastic.NewBoolQuery()
	if len(ids) > 0 {
		query.Must(elastic.NewTermsQuery("ip", ids...))
	}

	conditionQuery, b := getFilterConditionQuery(configure, q, query, asset)
	if b {
		return conditionQuery
	}

	if q.Keyword != "" {
		var keyword string
		if strings.Contains(q.Keyword, " ") {
			str := strings.Split(q.Keyword, " ")
			if len(str) == 2 {
				keyword = str[0] + " AND " + str[1]
			}
		} else {
			keyword = Escape(q.Keyword)
		}

		field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", keyword)).
			Field("ip.ip_raw").
			Field("company").
			Field("name").
			Field("province").
			Field("city").
			Field("business_app").
			Field("computer_room").
			Field("mac").
			Field("username").
			Field("manager_mobile").
			Field("manager_email").
			Field("ports").
			Field("os").
			Field("os_version").
			Field("rule_infos.title").
			Field("rule_infos.first_cat_tag").
			Field("rule_infos.version").
			Field("protocols").
			Field("hosts").
			Field("asset_level")

		conditionQuery.Must(field)
	}
	return conditionQuery
}

// Escape special character convert to escape character.
func Escape(s string) string {
	ra := []string{"+", "-", "=", "&", "|", ">", "<", "!", "(", ")", "{", "}", "[", "]", "^", `~`, `*`, `?`, `:`, `\\`, `/`, `\`, ` `}
	exists := func(v string) bool {
		for _, s := range ra {
			if v == s {
				return true
			}
		}
		return false
	}
	buf := bytes.NewBuffer(nil)
	var prevBack bool
	for _, v := range s {
		if prevBack || !exists(string(v)) {
			buf.WriteString(string(v))
			prevBack = false
		} else {
			buf.WriteString(`\`)
			buf.WriteString(string(v))
			if string(v) == `\` {
				prevBack = true
			}
		}
	}
	return buf.String()
}

// getFilterConditionQuery 高级筛选query条件
func getFilterConditionQuery(configure *config.Configure, q *r_asset.QueryListAndKeyword, query *elastic.BoolQuery, asset *ESAssetDatabase) (*elastic.BoolQuery, bool) {
	filter := false // 高级筛选标志
	// query := elastic.NewBoolQuery()
	// Start 高级筛选
	// IP类型
	switch q.IpType {
	case "ipv4":
		filter = true
		query.Filter(elastic.NewTermQuery("is_ipv6", false))
	case "ipv6":
		filter = true
		query.Filter(elastic.NewTermQuery("is_ipv6", true))
	default:
	}

	// 高级筛选-规则类型(信创/非信创)
	if q.XcType != nil {
		if *q.XcType == 1 {
			filter = true
			query.Must(elastic.NewTermQuery("is_xc", m_task.IsXcType))
		} else if *q.XcType == 0 {
			filter = true
			query.MustNot(elastic.NewTermQuery("is_xc", m_task.IsXcType))
		}
	}

	// 资产状态
	switch q.AssetStatus {
	//case "offline":
	case "0":
		filter = true
		query.Filter(elastic.NewTermQuery("state", 0))
	case "1":
		//case "online":
		filter = true
		query.Filter(elastic.NewTermQuery("state", 1))
	default:
	}
	// 组件类型
	if q.SecondCategories != nil {
		filter = true
		query.Filter(elastic.NewTermsQuery("rule_infos.second_cat_tag", convertSliceStringToSliceInterface(q.SecondCategories)...))
	}
	// FilterRuleInfoTitles
	if len(q.FilterRuleInfoTitles) > 0 {
		filter = true
		query.Filter(elastic.NewTermsQuery("rule_infos.title", convertSliceStringToSliceInterface(q.FilterRuleInfoTitles)...))
	}
	// 开放端口
	if len(q.FilterPorts) > 0 {
		filter = true
		query.Filter(elastic.NewTermsQuery("port_list.port", convertSliceStringToSliceInterface(q.FilterPorts)...))
	}
	// 开放服务
	if len(q.FilterProtocols) > 0 {
		filter = true
		query.Filter(elastic.NewTermsQuery("port_list.protocol", convertSliceStringToSliceInterface(q.FilterProtocols)...))
	}
	// 厂商品牌
	if len(q.FilterRuleCompanies) > 0 {
		filter = true
		query.Filter(elastic.NewTermsQuery("rule_infos.company", convertSliceStringToSliceInterface(q.FilterRuleCompanies)...))
	}
	// 资产等级
	if len(q.AssetLevel) > 0 {
		filter = true
		query.Filter(elastic.NewTermsQuery("asset_level", convertSliceStringToSliceInterface(q.AssetLevel)...))
	}
	// 标签
	for key, items := range q.Tags {
		if len(items) > 0 {
			filter = true
			// fieldKey := fmt.Sprintf("custom_fields.%s", key)
			// log.Println("fieldKey", fieldKey, "fieldValue", convertSliceStringToSliceInterface(items))
			query.Filter(elastic.NewTermsQuery(key, convertSliceStringToSliceInterface(items)...))
		}
	}
	// 城市 city
	if len(q.FilterCity) != 0 {
		FilterCityQuery := elastic.NewTermsQuery("city", convertSliceStringToSliceInterface(q.FilterCity)...)
		query.Must(FilterCityQuery)
	}
	// 发现方式 add_way
	if len(q.AddWay) != 0 {
		AddWayQuery := elastic.NewTermsQuery("add_way", q.AddWay)
		query.Must(AddWayQuery)
	}
	// 发现时间
	if q.CreatedTimeRange != "" {
		if strings.Contains(q.CreatedTimeRange, ",") {
			filter = true
			strArr := strings.Split(q.CreatedTimeRange, ",")
			if len(strArr) == 2 {
				query.Must(elastic.NewRangeQuery("createtime").Gte(strArr[0] + " 00:00:00").Lte(strArr[1] + " 00:00:00"))
			}
		}
	}
	// IP段
	if q.IpRanges != "" {
		filter = true
		query.Filter(convertIpContent(q.IpRanges)...)
		if q.OtherIpRanges != "" {
			query.MustNot(convertIpContent(q.OtherIpRanges)...)
		}
	}
	// 高级筛选-IP地址段-去除
	if q.OtherIpRanges != "" {
		res := ConvertIpContent(q.OtherIpRanges)

		query.MustNot(res...)
	}
	// End 高级筛选

	// 危险资产查询条件 Start
	query = SetRiskFilter(configure, q, query, asset)
	// 危险资产查询条件 End

	return query, filter
}

func SetRiskFilter(configure *config.Configure, q *r_asset.QueryListAndKeyword, query *elastic.BoolQuery, asset *ESAssetDatabase) *elastic.BoolQuery {
	for _, risk := range q.RiskFilter {
		switch risk {
		// 漏洞
		case "vuls":
			query.Must(elastic.NewRangeQuery("vuls_count").Gt(0))
		// 代理软件
		case "proxy_ware":
			query.Must(elastic.NewTermsQuery("rule_infos.title", downcase(proxyWares)...))
		// 高危端口及服务
		case "protocol":
			query = riskyProtocolsQuery(configure, query, asset)
		// 非标端口
		case "port":
			// todo
			ports := []interface{}{
				"1389",
			}
			query.Must(elastic.NewTermsQuery("ports", ports...))
		// 集成工具
		case "integration_tools":
			query.Must(elastic.NewTermsQuery("rule_infos.title", downcase(integrationTools)...))
		// 扫描器
		case "scanners":
			query.Must(elastic.NewTermsQuery("rule_infos.title", downcase(scanners)...))
		// 远程运维
		case "remote_ops":
			query.Must(elastic.NewTermsQuery("rule_infos.title", downcase(remoteOps)...))
		default:
		}
	}
	return query
}

// threats 获取有未修复漏洞的ip
func threats(asset *ESAssetDatabase) ([]interface{}, map[string]bool) {
	query := elastic.NewBoolQuery().Must(elastic.NewMatchQuery("state", 1))
	source := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"ip"}, nil)
	search, err := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfThreat()).
		SearchSource(source).
		Query(query).
		From(0).
		Size(10000).
		Pretty(true).
		Do(context.TODO())
	if err != nil || search.TotalHits() == 0 {
		return nil, nil
	}
	ipMaps := make(map[string]bool)
	res := make([]interface{}, 0)
	for _, hit := range search.Hits.Hits {
		var h map[string]interface{}
		err = json.Unmarshal(*hit.Source, &h)
		if err != nil {
			return nil, nil
		}
		res = append(res, h["ip"])
		ipMaps[h["ip"].(string)] = true
	}

	return res, ipMaps
}

// riskyProtocolsQuery 开放高危端口及服务的ip 查询
func riskyProtocolsQuery(configure *config.Configure, query *elastic.BoolQuery, asset *ESAssetDatabase) *elastic.BoolQuery {
	// 内网
	if configure.Server.ScanType == "intranet" {
		unFixedIps, _ := threats(asset)
		query.Must(elastic.NewTermsQuery("ip", unFixedIps...))
		return query
	}
	// 外网 extranet
	query.Should(elastic.NewTermsQuery("protocols", downcase(publicRiskyProtocols)...))
	http := []interface{}{"http", "https"}
	boolQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("protocols", http...)).Must(elastic.NewTermsQuery("rule_infos.title", downcase(publicHttpRuleNames)...))
	query.Should(boolQuery)
	return query
}

// convertIpContent 处理前端传的ip段信息
func convertIpContent(ip string) (queries []elastic.Query) {
	if len(ip) == 0 {
		return
	}
	if strings.IndexByte(ip, ',') > 0 {
		split := strings.Split(ip, ",")
		if len(split) < 2 {
			return
		}
		for _, value := range split {
			queries = append(queries, convertIpContent(value)...)
		}
	} else if strings.Contains(ip, "\r\n") {
		split := strings.Split(ip, "\r\n")
		if len(split) < 2 {
			return
		}
		for _, value := range split {
			queries = append(queries, convertIpContent(value)...)
		}
	} else if strings.Contains(ip, "\n") {
		split := strings.Split(ip, "\n")
		if len(split) < 2 {
			return
		}
		for _, value := range split {
			queries = append(queries, convertIpContent(value)...)
		}
	} else if strings.IndexByte(ip, '-') > 0 {
		if strings.IndexByte(ip, '*') > 0 { //存在- 并且存在*
			//ip = "10.10.1-11.*"
			split := strings.Split(ip, ".")     //10 10 1-11 *
			tem := strings.Split(split[2], "-") //1 11
			var ipstart, ipend string
			ipstart = split[0] + "." + split[1] + "." + tem[0] + "." + strconv.Itoa(0)
			ipend = split[0] + "." + split[1] + "." + tem[1] + "." + strconv.Itoa(255)
			queries = append(queries, elastic.NewRangeQuery("ip").Gte(ipstart).Lte(ipend))
			//log.Info("Que:2^2:---->", ipstart, "   ", ipend, "   ", queries)
		} else { // 存在 - 但不存在*
			split := strings.Split(ip, "-") //split[0] = *********** split[1] = 20
			if len(split) != 2 {
				return
			}
			i := strings.Split(split[0], ".") //i= [10 ,10,10,10]
			i[len(i)-1] = split[1]            //i=[10,10,10,20]
			//NewRangeQuery(ip).Gte(***********).Lte(***********)
			queries = append(queries, elastic.NewRangeQuery("ip").Gte(split[0]).Lte(strings.Join(i, ".")))
		}
		//split := strings.Split(ip, "-")
		//if len(split) != 2 {
		//	return
		//}
		//i := strings.Split(split[0], ".")
		//i[len(i)-1] = split[1]
		//queries = append(queries, elastic.NewRangeQuery("ip").Gte(split[0]).Lte(strings.Join(i, ".")))
	} else {
		queries = append(queries, elastic.NewTermQuery("ip", ip))
	}
	return
}

// ConvertIpContent 处理前端传的ip段信息
func ConvertIpContent(ip string) (queries []elastic.Query) {
	if len(ip) == 0 {
		return
	}
	if strings.IndexByte(ip, ',') > 0 {
		split := strings.Split(ip, ",")
		if len(split) < 2 {
			return
		}
		for _, value := range split {
			queries = append(queries, ConvertIpContent(value)...)
		}
	} else if strings.Contains(ip, "\r\n") {
		split := strings.Split(ip, "\r\n")
		if len(split) < 2 {
			return
		}
		for _, value := range split {
			queries = append(queries, ConvertIpContent(value)...)
		}
	} else if strings.Contains(ip, "\n") {
		split := strings.Split(ip, "\n")
		if len(split) < 2 {
			return
		}
		for _, value := range split {
			queries = append(queries, ConvertIpContent(value)...)
		}
	} else if strings.IndexByte(ip, '-') > 0 {
		if strings.IndexByte(ip, '*') > 0 { //存在- 并且存在*
			//ip = "10.10.1-11.*"
			split := strings.Split(ip, ".")     //10 10 1-11 *
			tem := strings.Split(split[2], "-") //1 11
			var ipStart, ipEnd string
			ipStart = split[0] + "." + split[1] + "." + tem[0] + "." + strconv.Itoa(0)
			ipEnd = split[0] + "." + split[1] + "." + tem[1] + "." + strconv.Itoa(255)
			cidrRange, err := cidrx.IPv4RangeToCIDRRange(ipStart, ipEnd)
			if err != nil {
				return nil
			}
			res := convertSliceStringToSliceInterface(cidrRange)
			queries = append(queries, elastic.NewTermsQuery("ip", res...))
		} else { // 存在 - 但不存在*
			split := strings.Split(ip, "-") //split[0] = *********** split[1] = 20
			if len(split) != 2 {
				return
			}
			tem1 := strings.Split(split[0], ".") //i= [10 ,10,10,10]
			tem2 := strings.Split(split[1], ".")
			var ipStart, ipEnd string
			if len(tem2) == 4 { //解析 ***********-*********** 格式
				ipStart = split[0]
				ipEnd = split[1]
			} else {
				tem1[len(tem1)-1] = split[1] //i=[10,10,10,20]
				ipStart = split[0]
				ipEnd = strings.Join(tem1, ".")
			}
			cidrRange, err := cidrx.IPv4RangeToCIDRRange(ipStart, ipEnd)
			if err != nil {
				return nil
			}
			res := convertSliceStringToSliceInterface(cidrRange)
			queries = append(queries, elastic.NewTermsQuery("ip", res...))
		}
	} else {
		queries = append(queries, elastic.NewTermQuery("ip", ip))
	}
	return
}

func IsDomain(input string) bool {
	// 判断是否是域名：存在字母和点号，不是完全的 IP 格式
	if strings.ContainsAny(input, "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ") && strings.Contains(input, ".") {
		return true
	}
	return false
}

// ConvertIpContent 处理前端传的ip段信息
func ConvertIpContentString(ip string) (queries []string) {
	if len(ip) == 0 {
		return
	}

	if strings.IndexByte(ip, ',') > 0 {
		split := strings.Split(ip, ",")
		if len(split) < 2 {
			return
		}
		for _, value := range split {
			queries = append(queries, ConvertIpContentString(value)...)
		}
	} else if strings.Contains(ip, "\r\n") {
		split := strings.Split(ip, "\r\n")
		if len(split) < 2 {
			return
		}
		for _, value := range split {
			queries = append(queries, ConvertIpContentString(value)...)
		}
	} else if strings.Contains(ip, "\n") {
		split := strings.Split(ip, "\n")
		if len(split) < 2 {
			return
		}
		for _, value := range split {
			queries = append(queries, ConvertIpContentString(value)...)
		}
	} else if strings.IndexByte(ip, '-') > 0 {
		if strings.IndexByte(ip, '*') > 0 { //存在- 并且存在*
			//ip = "10.10.1-11.*"
			split := strings.Split(ip, ".")     //10 10 1-11 *
			tem := strings.Split(split[2], "-") //1 11
			var ipStart, ipEnd string
			ipStart = split[0] + "." + split[1] + "." + tem[0] + "." + strconv.Itoa(0)
			ipEnd = split[0] + "." + split[1] + "." + tem[1] + "." + strconv.Itoa(255)
			cidrRange, err := cidrx.IPv4RangeToCIDRRange(ipStart, ipEnd)
			if err != nil {
				return nil
			}

			queries = append(queries, cidrRange...)

		} else { // 存在 - 但不存在*
			split := strings.Split(ip, "-") //split[0] = *********** split[1] = 20
			if len(split) != 2 {
				return
			}

			tem1 := strings.Split(split[0], ".") //i= [10 ,10,10,10]
			tem2 := strings.Split(split[1], ".")
			var ipStart, ipEnd string
			if len(tem2) == 4 { //解析 ***********-*********** 格式
				ipStart = split[0]
				ipEnd = split[1]
			} else {
				tem1[len(tem1)-1] = split[1] //i=[10,10,10,20]
				ipStart = split[0]
				ipEnd = strings.Join(tem1, ".")
			}
			cidrRange, err := cidrx.IPv4RangeToCIDRRange(ipStart, ipEnd)
			if err != nil {
				return nil
			}
			queries = append(queries, cidrRange...)
		}
	} else {
		if strings.IndexByte(ip, '/') > 0 { //***********/24
			queries = append(queries, ip)
		} else {

			queries = append(queries, ip+"/32") //***********/32
		}

	}
	return
}

// convertSliceStringToSliceInterface es terms条件类型
func convertSliceStringToSliceInterface(arr []string) []interface{} {
	res := make([]interface{}, len(arr))
	for k, v := range arr {
		res[k] = v
	}
	return res
}

// ConvertComponents 构造组件层级
func ConvertComponents(firstTags, secondTags []*m_asset.NestedBucket,
	firstCategories, secondCategories []*m_asset.CategoryProductCompany,
	components []*m_second_categories.SecondCategories) (interface{}, interface{}, []*m_asset.CategoryWhitCategoryProductCompany, int64) {
	// 父类
	res := make(map[string]uint)
	for _, component := range components {
		if component.Ancestry != 0 {
			continue
		}
		res[component.Title] = component.ID
	}
	// 父类子类对应关系
	cpsArr := make(map[uint]map[string]int)
	for _, component := range components {
		r, ok := cpsArr[component.Ancestry]
		if component.Ancestry == 0 {
			continue
		}
		if ok {
			r[component.Title] = int(component.ID)
		} else {
			item := make(map[string]int)
			item[component.Title] = int(component.ID)
			cpsArr[component.Ancestry] = item
		}
	}

	secondRes := make(map[string]*m_asset.Bucket)
	for _, second := range secondTags {
		bucket := &m_asset.Bucket{Key: second.Bucket.Key, Count: int(second.Sums["value"])}
		secondRes[bucket.Key] = bucket
	}
	secondCategoryRes := make(map[string]*m_asset.CategoryProductCompany)
	for _, second := range secondCategories {
		secondCategoryRes[second.Key] = second
	}

	firstCategoryRes := make(map[string]*m_asset.CategoryProductCompany)
	for _, first := range firstCategories {
		firstCategoryRes[first.Key] = first
	}

	resultWithCompanyAndProduct := make([]*m_asset.CategoryWhitCategoryProductCompany, 0)
	result := make([]*m_asset.Category, 0)
	resultCondition := make([]interface{}, 0)
	var total int64
	for _, first := range firstTags {
		itemCondition := make(map[string]interface{})
		item := &m_asset.Category{
			Key:   first.Bucket.Key,
			Count: int(first.Sums["value"]),
		}
		id, ok := res[first.Bucket.Key]
		if !ok {
			continue
		}
		firstCategory, ok := firstCategoryRes[first.Bucket.Key]
		if !ok {
			continue
		}
		companyLen, rulesLen := 5, 5
		if len(firstCategory.Company.Keys) < 5 {
			companyLen = len(firstCategory.Company.Keys)
		}
		if len(firstCategory.Rules.Keys) < 5 {
			rulesLen = len(firstCategory.Rules.Keys)
		}
		itemWithCompanyProduct := &m_asset.CategoryWhitCategoryProductCompany{
			Key:     first.Bucket.Key,
			Count:   int(first.Sums["value"]),
			Company: firstCategory.Company.Keys[:companyLen],
			Rules:   firstCategory.Rules.Keys[:rulesLen],
		}

		itemCondition["id"] = id
		itemCondition["title"] = first.Bucket.Key
		m, b := cpsArr[id]
		if b {
			childrenWithCompanyProduct := make([]*m_asset.CategoryWhitCategoryProductCompany, 0)
			children := make([]*m_asset.Bucket, 0)
			childrenCondition := make([]map[string]interface{}, 0)
			for k, id := range m {
				s, o := secondRes[k]
				if !o {
					continue
				}
				ss, o := secondCategoryRes[k]
				if !o {
					continue
				}
				companyLen, rulesLen := 5, 5
				if len(ss.Company.Keys) < 5 {
					companyLen = len(ss.Company.Keys)
				}
				if len(ss.Rules.Keys) < 5 {
					rulesLen = len(ss.Rules.Keys)
				}
				category := &m_asset.CategoryWhitCategoryProductCompany{
					Key:      ss.Key,
					Count:    ss.Count,
					Children: nil,
					Company:  ss.Company.Keys[:companyLen],
					Rules:    ss.Rules.Keys[:rulesLen],
				}
				childrenWithCompanyProduct = append(childrenWithCompanyProduct, category)
				children = append(children, s)
				child := make(map[string]interface{})
				child["id"] = id
				child["title"] = s.Key
				child["name"] = s.Key
				childrenCondition = append(childrenCondition, child)
			}
			itemCondition["children"] = childrenCondition
			a := &m_asset.Buckets{Keys: children}
			sort.Sort(a)
			item.Children = a.Keys
			itemWithCompanyProduct.Children = childrenWithCompanyProduct
		}
		total += int64(first.Sums["value"])
		sort.Sort(itemWithCompanyProduct)
		resultWithCompanyAndProduct = append(resultWithCompanyAndProduct, itemWithCompanyProduct)
		result = append(result, item)
		resultCondition = append(resultCondition, itemCondition)
	}
	c := &m_asset.Categories{Keys: result}
	sort.Sort(c)

	return resultCondition, result, resultWithCompanyAndProduct, total
}

// Statistics 统计类型总和
func Statistics(buckets []*m_asset.Bucket) int {
	total := 0
	for _, bucket := range buckets {
		if bucket.Key != "" && bucket.Count > 0 {
			total += bucket.Count
		}
	}
	return total
}

// StatisticsTotal 统计类型总和,不对空值忽略
func StatisticsTotal(buckets []*m_asset.Bucket) int {
	total := 0
	for _, bucket := range buckets {
		if bucket.Count > 0 {
			total += bucket.Count
		}
	}
	return total
}
