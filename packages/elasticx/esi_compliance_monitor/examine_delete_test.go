package esi_compliance_monitor

import (
	"github.com/stretchr/testify/assert"

	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
)

func (suite *Suite) Test_DeleteDocuments_With_Keyword() {
	list, err := suite.db.DeleteDocuments(suite.ctx,
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
		elasticx.WithKeyword("大庆"),
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list)
}

func (suite *Suite) Test_DeleteDocuments_With_AdvancedSearch_IpRangeAndUsername() {
	list, err := suite.db.DeleteDocuments(suite.ctx,
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			IpRange:  "************",
			Username: []string{"大庆"},
		}),
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list)
}

func (suite *Suite) Test_DeleteDocuments_With_Ids() {
	list, err := suite.db.DeleteDocuments(suite.ctx,
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
		elasticx.WithIDs([]string{"YJsaeXwB0D8KRXJtPjQe", "WpsZeXwB0D8KRXJtKjSA"}),
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list)
}

func (suite *Suite) Test_DeleteDocuments_With_Keyword_AndIds() {
	list, err := suite.db.DeleteDocuments(suite.ctx,
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
		elasticx.WithKeyword("helloworld"),
		elasticx.WithIDs([]string{"WJsZeXwB0D8KRXJtAjTE", "V5sYeXwB0D8KRXJtwzQ6"}),
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list)
}
