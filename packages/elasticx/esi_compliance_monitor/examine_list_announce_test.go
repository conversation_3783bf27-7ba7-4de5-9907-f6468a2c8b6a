package esi_compliance_monitor

import (
	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/stretchr/testify/assert"
)

func (suite *Suite) Test_GetList_Announce() {
	list, err := suite.db.GetListWithAnnounce(suite.ctx, "")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list)
}

func (suite *Suite) Test_GetList_AnnounceWithIDs() {
	ids := []string{
		"iJtgK3wB0D8KRXJtOTN4",
		"epteK3wB0D8KRXJtuzNw",
	}
	list, err := suite.db.GetListWithAnnounce(suite.ctx, "", elasticx.WithIDs(ids))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list)
}

func (suite *Suite) Test_GetListWithAnnounce_AdvancedSearchQueryParams_IPRange() {

	list, err := suite.db.GetListWithAnnounce(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			IpRange: "**********/24",
		}),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

func (suite *Suite) Test_GetListWithAnnounce_AdvancedSearchQueryParams__Multiple_AdvancedSearch_Conditions() {

	list, err := suite.db.GetListWithAnnounce(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			IpRange:  "**********/24",
			Username: []string{"大庆"},
		}),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}
