package esi_compliance_monitor

import (
	"context"
	"encoding/json"
	"strings"

	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"

	"github.com/olivere/elastic"
)

// GetList 获取检测数据.
func (e *Store) GetList(ctx context.Context, ipRangeManager string, ops ...elasticx.Option) (*elastic.SearchResult, error) {
	options := elasticx.Options{
		Pretty: elasticx.IsPrettyOfFalse,
		Size:   elasticx.DefaultPaginationSize,
		Number: elasticx.DefaultPaginationNumber,
		Status: elasticx.ComplianceMonitorStatusOfNotRepair,
	}

	for _, o := range ops {
		o.Apply(&options)
	}

	// Prepare processing.
	//if options.Metadata != nil && options.Keyword != "" {
	//	options.Keyword = ""
	//}
	// Calc number.
	if options.Number <= 0 {
		options.Number = 1
	}
	number := options.Number - 1

	boolQuery := elastic.NewBoolQuery().Must()
	if options.Keyword != "" {
		boolQuery.Must(NewKeywordQuery(options.Keyword))
	}
	if options.Metadata != nil {
		e.advancedSearchMultipleCondition(options, boolQuery)
	}
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	//if options.Keyword == "" && options.Metadata == nil && len(ipQueryList) <= 0 {
	//	boolQuery = elastic.NewBoolQuery().Must()
	//}

	filter := e.Instance.Client.
		Search(e.IndexName).
		Query(boolQuery).
		PostFilter(elastic.NewTermQuery("status", options.Status))

	if options.Field != "" && options.Order == "asc" {
		filter = filter.Sort(options.Field, true)
	} else if options.Field != "" && options.Order == "desc" {
		filter = filter.Sort(options.Field, false)
	} else {
		filter = filter.Sort(elasticx.FieldNameOfCreatedTime, false)
	}

	result, err := filter.
		Sort(elasticx.FieldNameOfCreatedTime, false).
		From(number * options.Size).
		Size(options.Size).
		Pretty(options.Pretty).
		Do(ctx)

	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetListMux 获取检测数据.
func (e *Store) GetListMux(ctx context.Context, ipRangeManager string, oldRuleIds []interface{}, ops ...elasticx.Option) (*elastic.SearchResult, error) {
	options := elasticx.Options{
		Pretty: elasticx.IsPrettyOfFalse,
		Size:   elasticx.DefaultPaginationSize,
		Number: elasticx.DefaultPaginationNumber,
		Status: elasticx.ComplianceMonitorStatusOfNotRepair,
	}

	for _, o := range ops {
		o.Apply(&options)
	}

	// Prepare processing.
	if options.Metadata != nil && options.Keyword != "" {
		options.Keyword = ""
	}
	// Calc number.
	if options.Number <= 0 {
		options.Number = 1
	}
	number := options.Number - 1

	var query elastic.Query
	if options.Keyword != "" {
		query = NewKeywordQuery(options.Keyword)
	} else if options.Metadata != nil {
		boolQuery := elastic.NewBoolQuery()
		boolQuery = e.advancedSearchMultipleCondition(options, boolQuery)
		query = boolQuery
	} else {
		query = elastic.NewMatchAllQuery()
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		qq := elastic.NewBoolQuery().Must(elastic.NewBoolQuery().Should(ipQueryList...))
		query = elastic.NewBoolQuery().Must(qq, query)
	}
	/*---------------------------分域---------------------------------*/

	filter := e.Instance.Client.
		Search(e.IndexName).
		Query(query).
		PostFilter(elastic.NewTermQuery("status", options.Status))

	//filter:= e.Instance.Client.
	//	Search(e.IndexName).
	//	Query(query).Query(elastic.NewTermsQuery("compliance_monitoring_rule_id", oldRuleIds...)).
	//	PostFilter(elastic.NewTermQuery("status", options.Status))

	if options.Field != "" && options.Order == "asc" {
		filter = filter.Sort(options.Field, true)
	} else if options.Field != "" && options.Order == "desc" {
		filter = filter.Sort(options.Field, false)
	} else {
		filter = filter.Sort(elasticx.FieldNameOfCreatedTime, false)
	}
	result, err := filter.
		//Sort(elasticx.FieldNameOfCreatedTime, false).
		From(number * options.Size).
		Size(options.Size).
		Pretty(options.Pretty).
		Do(ctx)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (e *Store) advancedSearchMultipleCondition(options elasticx.Options, boolQuery *elastic.BoolQuery) *elastic.BoolQuery {
	params, ok := options.Metadata.(*m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams)
	if ok {
		if params.IpRange != "" {
			res := es_asset.ConvertIpContent(params.IpRange)
			boolQuery.Filter(res...)
		}
		if params.CreatedTimeRange != "" {
			createTimes := strings.Split(params.CreatedTimeRange, ",")
			if len(createTimes) == 2 {
				boolQuery.Must(elastic.NewRangeQuery("createtime").Gte(createTimes[0] + " 00:00:00").Lte(createTimes[1] + " 23:59:59"))
			}
		}
		if params.BusinessApp != nil {
			r := e.stringSliceToInterface(params.BusinessApp)

			boolQuery.Must(elastic.NewTermsQuery("business_app", r...))
		}
		if params.ComputerRoom != nil {
			r := e.stringSliceToInterface(params.ComputerRoom)

			boolQuery.Must(elastic.NewTermsQuery("computer_room", r...))
		}
		if params.Username != nil {
			r := e.stringSliceToInterface(params.Username)

			boolQuery.Must(elastic.NewTermsQuery("username", r...))
		}
		if params.Company != nil {
			r := e.stringSliceToInterface(params.Company)

			boolQuery.Must(elastic.NewTermsQuery("company", r...))
		}
		if params.AssetLevel != nil && len(params.AssetLevel) > 0 {
			r := e.stringSliceToInterface(params.AssetLevel)

			boolQuery.Must(elastic.NewTermsQuery("asset_level", r...))
		}
		if params.RuleTitle != "" {
			boolQuery.Must(elastic.NewTermsQuery("rule_title", params.RuleTitle))
		}
		if params.XcType != nil {
			if *params.XcType == 1 {
				boolQuery.Must(elastic.NewTermQuery("is_xc", 1))
			} else if *params.XcType == 0 {
				boolQuery.MustNot(elastic.NewTermQuery("is_xc", 1))
			}
		}
	}

	return boolQuery
}

func (e *Store) stringSliceToInterface(data []string) []interface{} {
	r := make([]interface{}, 0, len(data))
	for _, item := range data {
		r = append(r, item)
	}

	return r
}

func (e *Store) GetViolation(ip string, ruleID uint, violationType string, content []string) (*elastic.SearchResult, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("ip", ip))
	query.Must(elastic.NewTermQuery("status", "not_repair"))
	query.Must(elastic.NewTermQuery("compliance_monitoring_rule_id", ruleID))
	//query.Must(elastic.NewTermQuery("violation_type", violationType))
	//if violationType != "ip_occupied" && violationType != "ip_offline" {
	//	query.Must(elastic.NewTermQuery("content", strings.Join(content, ",")))
	//}

	res, err := e.Instance.Client.Search(e.IndexName).Type(e.TypeName).Query(query).Do(context.Background())

	return res, err
}

// GetListAll 获取符合条件的所有检测数据.
func (e *Store) GetListAll(ipRangeManager string, ops ...elasticx.Option) *elastic.ScrollService {
	options := elasticx.Options{
		Pretty: elasticx.IsPrettyOfTrue,
		Status: elasticx.ComplianceMonitorStatusOfNotRepair,
	}

	for _, o := range ops {
		o.Apply(&options)
	}

	// Prepare processing.
	if options.Metadata != nil && options.Keyword != "" {
		options.Keyword = ""
	}

	var query elastic.Query
	if options.Keyword != "" {
		query = NewKeywordQuery(options.Keyword)
	} else if options.Ids != nil {
		boolQuery := elastic.NewBoolQuery()
		boolQuery.Must(elastic.NewIdsQuery().Ids(options.Ids...))
		query = boolQuery
	} else if options.Metadata != nil {
		boolQuery := elastic.NewBoolQuery()
		boolQuery = e.advancedSearchMultipleCondition(options, boolQuery)
		query = boolQuery
	} else {
		query = elastic.NewMatchAllQuery()
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query = elastic.NewBoolQuery().Must(query, elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	filter := e.Instance.Client.
		Scroll(e.IndexName).
		Query(query).
		PostFilter(elastic.NewTermQuery("status", options.Status))

	result := filter.
		Size(1000).
		Pretty(options.Pretty)

	return result
}

// GetListAllMux 获取符合条件的所有检测数据.
func (e *Store) GetListAllMux(ipRangeManager string, newRuleIds []interface{}, ops ...elasticx.Option) *elastic.ScrollService {
	options := elasticx.Options{
		Pretty: elasticx.IsPrettyOfTrue,
		Status: elasticx.ComplianceMonitorStatusOfNotRepair,
	}

	for _, o := range ops {
		o.Apply(&options)
	}

	// Prepare processing.
	if options.Metadata != nil && options.Keyword != "" {
		options.Keyword = ""
	}

	var query elastic.Query
	if options.Keyword != "" {
		query = NewKeywordQuery(options.Keyword)
	} else if options.Metadata != nil {
		boolQuery := elastic.NewBoolQuery()
		boolQuery = e.advancedSearchMultipleCondition(options, boolQuery)
		query = boolQuery
	} else if options.Ids != nil {
		boolQuery := elastic.NewBoolQuery()
		boolQuery.Must(elastic.NewIdsQuery().Ids(options.Ids...))
		query = boolQuery
	} else {
		query = elastic.NewMatchAllQuery()
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query = elastic.NewBoolQuery().Must(query, elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	filter := e.Instance.Client.
		Scroll(e.IndexName).
		Query(query).
		PostFilter(elastic.NewTermQuery("status", options.Status))
	if newRuleIds != nil {
		q := elastic.NewBoolQuery().MustNot(elastic.NewTermsQuery("compliance_monitoring_rule_id", newRuleIds...))
		filter.Query(q)
	}
	result := filter.
		Size(1000).
		Pretty(options.Pretty)

	return result
}

// GetViolationById 获取fofaee_violations通过ID
func (e *Store) GetViolationById(violationID string) (*elastic.GetResult, error) {
	res, err := e.Instance.Client.Get().Index(e.IndexName).Type(e.TypeName).Id(violationID).Do(context.Background())
	return res, err
}

// GetViolationRanking 获取违规项排行
func (e *Store) GetViolationRanking(ipRangeManager string) ([]m_overviews.RankingList, error) {
	data := make([]m_overviews.RankingList, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("status", "not_repair"))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	ruleTitleAggregation := elastic.NewTermsAggregation().Field("rule_title").Size(10)

	searchResult, err := e.Instance.Client.Search().
		Index(elasticx.IndexNameOfViolation()).
		Size(0).
		Query(query).
		Aggregation("rule_titles", ruleTitleAggregation).
		Pretty(true).
		Do(context.Background())
	if err != nil {
		return data, err
	}
	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "rule_titles":
			tmp := m_overviews.Aggregations{}
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				return data, err
			}
			for _, bucket := range tmp.Buckets {
				data = append(data, m_overviews.RankingList{
					Name:  bucket.Key,
					Count: bucket.DocCount,
				})
			}
		}
	}
	return data, nil
}
