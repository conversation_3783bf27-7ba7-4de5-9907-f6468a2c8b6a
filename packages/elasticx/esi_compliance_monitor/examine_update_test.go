package esi_compliance_monitor

import (
	"encoding/json"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
)

func (suite *Suite) Test_UpdateComplianceMonitor() {
	res, err := suite.db.GetViolation("10.10.10.183", 2, "disabled_port_opened", []string{"80"})
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res.Hits.Hits)
	if res.Hits.TotalHits > 0 {
		tmp := make(map[string]interface{})
		err = json.Unmarshal(*res.Hits.Hits[0].Source, &tmp)
		assert.NoError(suite.T(), err)

		tmp["createtime"] = "2054-11-29 19:02:02"
		_, err = suite.db.UpdateComplianceMonitor(tmp, "AX1u82POZm_GDCG-6sE_")
		assert.NoError(suite.T(), err)
	}
}

func (suite *Suite) Test_PutAssetLevelMapping() {
	err := suite.db.PutAssetLevelMapping()
	assert.NoError(suite.T(), err)
}

func (suite *Suite) Test_BullUpdateStateByIP() {
	err := suite.db.BullUpdateStateByIP("***********", 1)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), false, elastic.IsNotFound(err))
}
