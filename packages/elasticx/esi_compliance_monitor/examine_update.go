package esi_compliance_monitor

import (
	"context"
	"fmt"

	"github.com/olivere/elastic"
	"github.com/thedevsaddam/gojsonq/v2"
)

// UpdateComplianceMonitor 更新合规检测未修复/已修复的项
func (e *Store) UpdateComplianceMonitor(data map[string]interface{}, violationID string) (*elastic.UpdateResponse, error) {
	res, err := e.Instance.Client.Update().Index(e.IndexName).Type(e.TypeName).Id(violationID).Doc(data).Refresh("true").Do(context.Background())
	return res, err
}

// PutAssetLevelMapping fofaee_violations的mapping添加资产等级
func (e *Store) PutAssetLevelMapping() error {
	aa := `{
			"properties": {
				"asset_level": {
        			"type": "keyword",
        			"index": true
      			}
		  	}
}`

	res, err := e.Instance.Client.GetFieldMapping().Field("asset_level").Index(e.IndexName).Do(context.Background())
	if err != nil {
		return err
	}
	gp := gojsonq.New().FromInterface(res)
	rr := gp.Find(fmt.Sprintf("%s.mappings.%s.asset_level.full_name", e.IndexName, e.TypeName))
	if rr == nil {
		_, err = e.Instance.Client.PutMapping().Index(e.IndexName).Type(e.TypeName).BodyString(aa).Do(context.Background())
		return err
	}
	return nil
}

// BullUpdateStateByIP 根据IP更新资产状态
func (store *Store) BullUpdateStateByIP(ip string, state int) error {

	_, err := store.Instance.Client.UpdateByQuery().
		Index(store.IndexName). // 设置索引名称
		Query(elastic.NewTermQuery("ip", ip)). // 查找 ip 字段匹配的文档
		Script(elastic.NewScriptInline(`
			if (ctx._source.containsKey("online")) {
				// 如果 online 字段存在，直接更新
				ctx._source.online = params.state;
			} else {
				// 如果 online 字段不存在，创建并赋值
				ctx._source.online = params.state;
			}`).
			Param("state", state)). // 设置 state 值
		Do(context.Background()) // 执行更新操作

	return err
}
