package esi_compliance_monitor

import (
	"bytes"
	"fmt"

	"github.com/olivere/elastic"
)

func NewKeywordQuery(keyword string) *elastic.QueryStringQuery {
	field := elastic.NewQueryStringQuery(fmt.Sprintf("*%s*", Escape(keyword))).
		Field("ip").
		Field("ip.ip_raw").
		Field("name").
		Field("content").
		Field("hosts").
		Field("rule_title").
		Field("rule_tags").
		Field("province").
		Field("city").
		Field("business_app").
		Field("company").
		Field("computer_room").
		Field("violation_type").
		Field("username").
		Field("asset_level")

	return field
}

// Escape special character convert to escape character.
func Escape(s string) string {
	ra := []string{"+", "-", "=", "&", "|", ">", "<", "!", "(", ")", "{", "}", "[", "]", "^", `~`, `*`, `?`, `:`, `\\`, `/`, `\`, ` `}
	exists := func(v string) bool {
		for _, s := range ra {
			if v == s {
				return true
			}
		}
		return false
	}
	buf := bytes.NewBuffer(nil)
	var prevBack bool
	for _, v := range s {
		if prevBack || !exists(string(v)) {
			buf.WriteString(string(v))
			prevBack = false
		} else {
			buf.WriteString(`\`)
			buf.WriteString(string(v))
			if string(v) == `\` {
				prevBack = true
			}
		}
	}
	return buf.String()
}
