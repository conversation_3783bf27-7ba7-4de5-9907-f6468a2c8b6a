package esi_compliance_monitor

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"testing"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/packages/backup_restore"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_index"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"
)

func TestElasticComplianceMonitorDatabaseSuite(t *testing.T) {
	suite.Run(t, new(Suite))
}

type Suite struct {
	suite.Suite
	ctx             context.Context
	db              *Store
	configure       *config.Configure
	elasticdatabase *elasticx.ElasticDatabase
	afterDropTables bool
}

var (
	indexes = map[string]string{
		elasticx.IndexNameOfAsset():     readDataFromJsonFile("asset_settings.json"),
		elasticx.IndexNameOfSubDomain(): readDataFromJsonFile("subdomain_settings.json"),
		elasticx.IndexNameOfService():   readDataFromJsonFile("service_settings.json"),
		elasticx.IndexNameOfThreat():    readDataFromJsonFile("threats_settings.json"),
		elasticx.IndexNameOfViolation(): readDataFromJsonFile("violations_settings.json"),
	}
)

func readDataFromJsonFile(filename string) string {
	bytes, err := ioutil.ReadFile("./../../../static/assets/asset/" + filename)
	if err != nil {
		panic(err)
	}
	return string(bytes)
}

func (suite *Suite) BeforeSuite() {
	gomonkey.ApplyFunc(elasticx.IndexNameOfAsset, func() string {
		return "fofaee_assets_compliance_test"
	})
	assert.Equal(suite.T(), "fofaee_assets_compliance_test", elasticx.IndexNameOfAsset())

	gomonkey.ApplyFunc(elasticx.IndexNameOfNetinfo, func() string {
		return "fofaee_netinfos_compliance_test"
	})
	assert.Equal(suite.T(), "fofaee_netinfos_compliance_test", elasticx.IndexNameOfNetinfo())

	gomonkey.ApplyFunc(elasticx.IndexNameOfService, func() string {
		return "fofaee_service_compliance_test"
	})
	assert.Equal(suite.T(), "fofaee_service_compliance_test", elasticx.IndexNameOfService())

	gomonkey.ApplyFunc(elasticx.IndexNameOfTaskAsset, func() string {
		return "fofaee_task_assets_compliance_test"
	})
	assert.Equal(suite.T(), "fofaee_task_assets_compliance_test", elasticx.IndexNameOfTaskAsset())

	gomonkey.ApplyFunc(elasticx.IndexNameOfSubDomain, func() string {
		return "fofaee_subdomain_compliance_test"
	})
	assert.Equal(suite.T(), "fofaee_subdomain_compliance_test", elasticx.IndexNameOfSubDomain())

	gomonkey.ApplyFunc(elasticx.IndexNameOfThreat, func() string {
		return "fofaee_threats_compliance_test"
	})
	assert.Equal(suite.T(), "fofaee_threats_compliance_test", elasticx.IndexNameOfThreat())

	gomonkey.ApplyFunc(elasticx.IndexNameOfViolation, func() string {
		return "fofaee_violations_compliance_test"
	})
	assert.Equal(suite.T(), "fofaee_violations_compliance_test", elasticx.IndexNameOfViolation())

	gomonkey.ApplyFunc(elasticx.IndexNameOfSiteurl, func() string {
		return "fofaee_siteurl_compliance_test"
	})
	assert.Equal(suite.T(), "fofaee_siteurl_compliance_test", elasticx.IndexNameOfSiteurl())

	gomonkey.ApplyGlobalVar(&es_asset.Indexes, map[string]string{
		"asset":     elasticx.IndexNameOfAsset(),
		"service":   elasticx.IndexNameOfService(),
		"subdomain": elasticx.IndexNameOfSubDomain(),
	})
}

func (suite *Suite) SetupSuite() {
	suite.BeforeSuite()

	var err error
	backup_restore.BaseDir = "./../../../"
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../../../"),
	)
	assert.NotNil(suite.T(), suite.configure)

	inst, err := elasticx.NewElasticDatabase(suite.configure)
	assert.NoError(suite.T(), err)
	suite.elasticdatabase = inst

	suite.db = NewStore(inst)
	suite.ctx = context.Background()
	suite.afterDropTables = true

	es_index.CreateIndex(suite.elasticdatabase.Client)

	str := `{ "business_app": "","city": "","company": "","compliance_monitoring_rule_id": 1,"computer_room": "","content": "IP被占用","country": "","createtime": "2022-07-08 15:42:52","custom_fields": { },"custom_names": { },"hosts": null,"ip": "***********","mac": "00:0c:29:83:79:0e","manager_email": "","manager_mobile": "","name": "网络资产测绘及风险分析系统","ports": [ "1099","443","3000","80","8080","111","22"],"protocols": [ "java-rmi","https","http","portmap","ssh"],"province": "局域网","rule_infos": [ { "belong_level": 0,"rule_id": 7512,"second_cat_tag": "其他支撑系统","soft_hard_code": 2,"first_cat_tag": "支撑系统","level_code": 3,"company": "其他","ports": [ 22],"title": "OpenSSH"},{ "belong_level": 0,"rule_id": 209,"second_cat_tag": "服务","soft_hard_code": 2,"first_cat_tag": "支撑系统","level_code": 3,"company": "Nginx","ports": [ 443,80],"title": "NGINX"},{ "belong_level": 0,"rule_id": 10000027,"second_cat_tag": "其他系统软件","soft_hard_code": 5,"first_cat_tag": "系统软件","level_code": 5,"company": "test","ports": [ 443,80,8080],"title": "test"},{ "belong_level": 0,"rule_id": 327133,"second_cat_tag": "开发框架","soft_hard_code": 2,"first_cat_tag": "支撑系统","level_code": 4,"company": "VMware, Inc.","ports": [ 8080],"title": "vmware-SpringBoot-Framework"},{ "belong_level": 0,"rule_id": 855434,"second_cat_tag": "组件","soft_hard_code": 2,"first_cat_tag": "支撑系统","level_code": 4,"company": "其他","ports": [ 8080],"title": "Log4j2"}],"rule_tags": [ "OpenSSH","NGINX","test","vmware-SpringBoot-Framework","Log4j2"],"rule_title": "test","status": "not_repair","user_id": 1,"username": "","violation_type": "ip_occupied"}`
	_, err = suite.elasticdatabase.Client.Index().Index(elasticx.IndexNameOfViolation()).Type("ips").BodyString(str).Do(context.Background())
	assert.NoError(suite.T(), err)
}

func (suite *Suite) TearDownSuite() {
	if suite.afterDropTables {
		suite.elasticdatabase.Client.DeleteIndex(
			elasticx.IndexNameOfAsset(),
			elasticx.IndexNameOfNetinfo(),
			elasticx.IndexNameOfService(),
			elasticx.IndexNameOfTaskAsset(),
			elasticx.IndexNameOfSubDomain(),
			elasticx.IndexNameOfThreat(),
			elasticx.IndexNameOfViolation(),
			elasticx.IndexNameOfSiteurl(),
		).Do(context.Background())
	}
}

func (suite *Suite) autoMigrateSystemPresetData() {
	// create index
	for index, settingsAndMappings := range indexes {
		_, err := suite.elasticdatabase.Client.CreateIndex(index).Body(settingsAndMappings).Do(context.Background())
		assert.NoError(suite.T(), err)
	}

	file, err := os.Open("./../../../static/assets/asset/asset_doc.txt")
	assert.NoError(suite.T(), err)
	defer file.Close()

	buf := bufio.NewReader(file)
	assetsBodyJsons := make([]string, 0)
	for {
		line, err := buf.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				assetsBodyJsons = append(assetsBodyJsons, line)
				break
			}
			break
		}
		assetsBodyJsons = append(assetsBodyJsons, line)
	}

	// insert data to assets
	for _, body := range assetsBodyJsons {
		asset := new(m_asset.Asset)
		assert.NoError(suite.T(), json.Unmarshal([]byte(body), &asset))

		_, err = suite.elasticdatabase.Client.Index().Index(elasticx.IndexNameOfAsset()).Type("ips").BodyJson(asset).Refresh("true").Do(context.Background())
		assert.NoError(suite.T(), err)
	}
}

func (suite *Suite) Test_GetList_Keyword_IP() {
	list, err := suite.db.GetList(suite.ctx, "")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
}

func (suite *Suite) Test_DeleteDocumentByID() {
	list, err := suite.db.DeleteDocumentByID(suite.ctx, "lDV24nsBwrGD6FlGY6Jw")
	assert.NotNil(suite.T(), err)
	assert.NotNil(suite.T(), list)
}

func (suite *Suite) Test_DeleteDocumentsByIDs() {
	list, err := suite.db.DeleteDocumentsByIDs(suite.ctx,
		"qjV24nsBwrGD6FlGu6JI",
		"oTV24nsBwrGD6FlGfaIK",
		"n5tZKnwB0D8KRXJt2DIC",
		"ojV24nsBwrGD6FlGfaIo",
	)
	// assert.NotNil(suite.T(), err)
	assert.NotNil(suite.T(), list)
	if err != nil {
		fmt.Println(err)
	}

	// fmt.Printf("%+v\n", list)
}

func (suite *Suite) Test_DeleteDocumentsByIDs_Index() {
	list, err := suite.db.DeleteDocumentsByIDs(suite.ctx)
	assert.NotNil(suite.T(), err)
	assert.Nil(suite.T(), list)
}

func (suite *Suite) Test_DeleteDocumentsByIDs_Match() {
	s := "互联网暴露资产风险动态监测系统@@@@@@@@@@@@@@@"
	list, err := suite.db.DeleteDocumentsByMatch(suite.ctx, &s)
	assert.Nil(suite.T(), err)
	assert.Equal(suite.T(), int64(0), list.Deleted)
}

func (suite *Suite) Test_DeleteDocumentsByIDs_MatchAll() {
	list, err := suite.db.DeleteDocumentsByMatch(suite.ctx, nil)
	assert.Nil(suite.T(), err)
	assert.NotNil(suite.T(), list)
}
