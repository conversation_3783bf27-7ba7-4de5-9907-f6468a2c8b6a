package esi_compliance_monitor

import (
	"context"

	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"git.gobies.org/foeye-dependencies/mosso"
	"github.com/olivere/elastic"
)

func (e *Store) DeleteDocuments(ctx context.Context, ops ...elasticx.Option) (*elastic.BulkIndexByScrollResponse, error) {
	options := elasticx.Options{
		Pretty: elasticx.IsPrettyOfFalse,
		Size:   elasticx.DefaultPaginationSize,
		Number: elasticx.DefaultPaginationNumber,
		Status: elasticx.ComplianceMonitorStatusOfNotRepair,
	}

	for _, o := range ops {
		o.Apply(&options)
	}

	mosso.DebugShowContentWithJSON(options)

	// Prepare processing.
	if options.Metadata != nil && options.Keyword != "" {
		options.Keyword = ""
	}

	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("status", options.Status))

	if options.Keyword != "" {
		query = query.Must(NewKeywordQuery(options.Keyword))
	}
	if options.Ids != nil && len(options.Ids) > 0 {
		query = query.Must(elastic.NewIdsQuery().Ids(options.Ids...))
	}
	if options.Metadata != nil {
		query = e.advancedSearchMultipleCondition(options, query)
	}

	if options.Keyword != "" && options.Ids != nil && options.Metadata != nil {
		query = query.Must(elastic.NewMatchAllQuery())
	}

	do, err := elastic.
		NewDeleteByQueryService(e.Instance.Client).
		Index(e.IndexName).
		Type(e.TypeName).
		Query(query).
		Refresh("true").
		Do(ctx)

	if err != nil {
		return nil, err
	}

	return do, nil
}

// DeleteDocumentByID delete document by id.
func (e *Store) DeleteDocumentByID(ctx context.Context, id string) (*elastic.DeleteResponse, error) {
	return e.Instance.Client.
		Delete().
		Index(e.IndexName).
		Type(e.TypeName).
		Id(id).
		Do(ctx)
}

// DeleteDocumentsByIDs delete docuemnts by ids.
func (e *Store) DeleteDocumentsByIDs(ctx context.Context, ids ...string) (*elastic.BulkResponse, error) {
	bulk := make([]elastic.BulkableRequest, 0)
	for _, id := range ids {
		bulk = append(bulk, elastic.NewBulkDeleteRequest().Index(e.IndexName).Type(e.TypeName).Id(id))
	}

	resp, err := e.Instance.Client.
		Bulk().
		Add(bulk...).
		Do(ctx)

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// DeleteDocumentsByMatch delete docuemnts by match.
func (e *Store) DeleteDocumentsByMatch(ctx context.Context, match *string) (*elastic.BulkIndexByScrollResponse, error) {

	var query elastic.Query
	if match != nil {
		query = NewKeywordQuery(*match)
	} else {
		query = elastic.NewMatchAllQuery()
	}

	do, err := elastic.
		NewDeleteByQueryService(e.Instance.Client).
		Index(e.IndexName).
		Type(e.TypeName).
		Query(query).
		Do(ctx)

	if err != nil {
		return nil, err
	}

	return do, err
}
