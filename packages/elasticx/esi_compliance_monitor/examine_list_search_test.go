package esi_compliance_monitor

import (
	"encoding/json"

	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
)

func (suite *Suite) Test_GetSearchList() {
	list, err := suite.db.GetSearchList(suite.ctx, "",
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list)
}

const searchMockResultOfNotRepair = `{
	"took": 3,
	"hits": {
		"total": 68,
		"max_score": 0
	},
	"aggregations": {
		"business_app": {
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [
				{
					"key": "helloworld",
					"doc_count": 14,
					"not_repair": {
						"doc_count": 13
					}
				}
			]
		},
		"company": {
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [
				{
					"key": "shaohua",
					"doc_count": 69,
					"not_repair": {
						"doc_count": 68
					}
				}
			]
		},
		"computer_room": {
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [
				{
					"key": "北京",
					"doc_count": 20,
					"not_repair": {
						"doc_count": 19
					}
				}
			]
		},
		"username": {
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [
				{
					"key": "武邵华",
					"doc_count": 55,
					"not_repair": {
						"doc_count": 55
					}
				},
				{
					"key": "大庆",
					"doc_count": 14,
					"not_repair": {
						"doc_count": 13
					}
				}
			]
		}
	},
	"_shards": {
		"total": 5,
		"successful": 5,
		"failed": 0
	}
}
`
const searchMockResultOfHasBeenRepaired = `{
	"took": 1,
	"hits": {
		"total": 1,
		"max_score": 0
	},
	"aggregations": {
		"business_app": {
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [
				{
					"key": "helloworld",
					"doc_count": 14,
					"has_been_repaired": {
						"doc_count": 1
					}
				}
			]
		},
		"company": {
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [
				{
					"key": "shaohua",
					"doc_count": 69,
					"has_been_repaired": {
						"doc_count": 1
					}
				}
			]
		},
		"computer_room": {
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [
				{
					"key": "北京",
					"doc_count": 20,
					"has_been_repaired": {
						"doc_count": 1
					}
				}
			]
		},
		"username": {
			"doc_count_error_upper_bound": 0,
			"sum_other_doc_count": 0,
			"buckets": [
				{
					"key": "武邵华",
					"doc_count": 55,
					"has_been_repaired": {
						"doc_count": 0
					}
				},
				{
					"key": "大庆",
					"doc_count": 14,
					"has_been_repaired": {
						"doc_count": 1
					}
				}
			]
		}
	},
	"_shards": {
		"total": 5,
		"successful": 5,
		"failed": 0
	}
}
`

func (suite *Suite) Test_aggregation_not_repair() {
	var r elastic.SearchResult
	err := json.Unmarshal([]byte(searchMockResultOfNotRepair), &r)
	assert.NoError(suite.T(), err)

	aggregation := suite.db.aggregation(&r, elasticx.ComplianceMonitorStatusOfNotRepair)
	assert.NotEmpty(suite.T(), aggregation)
	//mosso.DebugShowContentWithJSON(aggregation)
}

func (suite *Suite) Test_aggregation_has_been_repaired() {
	var r elastic.SearchResult
	err := json.Unmarshal([]byte(searchMockResultOfHasBeenRepaired), &r)
	assert.NoError(suite.T(), err)

	aggregation := suite.db.aggregation(&r, elasticx.ComplianceMonitorStatusOfRepaired)
	assert.NotEmpty(suite.T(), aggregation)
	//mosso.DebugShowContentWithJSON(aggregation)
}
