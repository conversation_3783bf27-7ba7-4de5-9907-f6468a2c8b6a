package esi_compliance_monitor

import (
	"context"
	"encoding/json"
	"fmt"

	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/responses/r_compliance_monitor"

	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_threats"

	"github.com/olivere/elastic"
	log "github.com/sirupsen/logrus"
)

// GetSummaryList 获取合规监测搜索列表数据.
func (e *Store) GetSummaryList(ctx context.Context, ipRangeManager string, ops ...elasticx.Option) (
	*elastic.SearchResult, error) {
	options := elasticx.Options{
		Status: elasticx.ComplianceMonitorStatusOfNotRepair,
	}

	for _, o := range ops {
		o.Apply(&options)
	}

	var query elastic.Query
	if options.Keyword != "" {
		query = NewKeywordQuery(options.Keyword)
	} else if options.Metadata != nil {
		boolQuery := elastic.NewBoolQuery()
		boolQuery = e.advancedSearchMultipleCondition(options, boolQuery)
		query = boolQuery
	} else {
		query = elastic.NewMatchAllQuery()
	}

	// ElasticSearch Query.
	aggregationNameWithMonitorType := "rule_title"
	status := elastic.NewTermQuery("status", options.Status)

	a := elastic.NewTermsAggregation().
		Field(aggregationNameWithMonitorType).
		Size(elasticx.DefaultScrollQueryBulkSize).
		OrderByCountDesc()
	a.SubAggregation(options.Status, elastic.NewFilterAggregation().Filter(status))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		qq := elastic.NewBoolQuery().Must(elastic.NewBoolQuery().Should(ipQueryList...))
		query = elastic.NewBoolQuery().Must(qq, query)
	}
	/*---------------------------分域---------------------------------*/

	optio, _ := json.Marshal(options)
	fmt.Println("GetSummaryList es optio", string(optio))

	qStr, _ := query.Source()
	dd, _ := json.Marshal(qStr)
	fmt.Println("GetSummaryList es query", string(dd))

	sr, err := e.Instance.Client.
		Search(e.IndexName).
		Query(query).
		PostFilter(status).
		Aggregation(aggregationNameWithMonitorType, a).
		Do(ctx)

	return sr, err
}

func (e *Store) GetSummaryListMenu(ctx context.Context, ipRangeManager string) ([]m_overviews.MenuIndex, error) {
	data := make([]m_overviews.MenuIndex, 0)
	data = append(data, m_overviews.MenuIndex{Name: "重要IP占用", Count: 0, Key: "violations_type", Value: "ip_occupied"})
	data = append(data, m_overviews.MenuIndex{Name: "重要IP离线", Count: 0, Key: "violations_type", Value: "ip_offline"})
	data = append(data, m_overviews.MenuIndex{Name: "禁用端口", Count: 0, Key: "violations_type", Value: "disabled_port_opened"})
	data = append(data, m_overviews.MenuIndex{Name: "禁用服务", Count: 0, Key: "violations_type", Value: "disabled_service_opened"})
	data = append(data, m_overviews.MenuIndex{Name: "禁用软件", Count: 0, Key: "violations_type", Value: "disabled_software_used"})
	data = append(data, m_overviews.MenuIndex{Name: "禁用硬件", Count: 0, Key: "violations_type", Value: "disabled_hardware_used"})
	//data = append(data, m_overviews.MenuIndex{Name: "端口开放监测", Count: 0, Key: "violations_type", Value: "port_open"})
	//data = append(data, m_overviews.MenuIndex{Name: "服务开放监测", Count: 0, Key: "violations_type", Value: "service_open"})

	query := elastic.NewBoolQuery()
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		qq := elastic.NewBoolQuery().Must(elastic.NewBoolQuery().Should(ipQueryList...))
		query = elastic.NewBoolQuery().Must(qq, query)
	}
	/*---------------------------分域---------------------------------*/
	query.Must(elastic.NewTermQuery("status", "not_repair"))
	violationAgg := elastic.NewTermsAggregation().Field("violation_type")
	xcAgg := elastic.NewTermsAggregation().Field("is_xc")
	serv, err := e.Instance.Client.Search().
		Index(e.IndexName).
		Query(query).
		Size(0).
		Pretty(true).
		Aggregation("violation_agg", violationAgg).
		Aggregation("xc_agg", xcAgg).
		Do(ctx)
	if err != nil {
		return data, err
	}

	for key, rawMessage := range serv.Aggregations {
		switch key {
		case "xc_agg":
			tmp := new(m_overviews.AggregationsPlus)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			if len(tmp.Buckets) > 0 {
				for _, item := range tmp.Buckets {
					keyString := fmt.Sprintf("%.f", item.Key)
					if keyString == "1" {
						data = append(data, m_overviews.MenuIndex{
							Name:  "xc",
							Key:   "xc_type",
							Value: keyString,
							Count: item.DocCount,
						})
					} else {
						data = append(data, m_overviews.MenuIndex{
							Name:  "nxc",
							Count: item.DocCount,
							Key:   "xc_type",
							Value: keyString,
						})
					}
				}
			} else {
				//没有聚合数据返回零
				data = append(data, m_overviews.MenuIndex{
					Name:  "nxc",
					Count: 0,
					Key:   "xc_type",
					Value: "0",
				})
				data = append(data, m_overviews.MenuIndex{
					Name:  "xc",
					Count: 0,
					Key:   "xc_type",
					Value: "1",
				})
			}
		case "violation_agg":
			tmp := new(m_overviews.Aggregations)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			for _, val := range tmp.Buckets {
				for index, tmpData := range data {
					if tmpData.Key == "violations_type" && tmpData.Value == val.Key {
						data[index].Count = val.DocCount
					}
				}

			}

		}
	}
	return data, nil
}

func (e *Store) GetXcSummaryList(ctx context.Context, ipRangeManager string, param *r_compliance_monitor.XcComplianceMonitorSummaryParam) (
	[]m_compliance_monitor.ComplianceMonitorSummary, int, error) {
	data := make([]m_compliance_monitor.ComplianceMonitorSummary, 0)
	total := 0
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("status", "not_repair"))
	if param.ViolationsType != "" {
		query.Must(elastic.NewTermQuery("violation_type", param.ViolationsType))
	}
	if param.XcType != "" {
		if param.XcType == "1" {
			query.Must(elastic.NewTermQuery("is_xc", 1))
		} else if param.XcType == "0" {
			query.MustNot(elastic.NewTermQuery("is_xc", 1))
		}
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		qq := elastic.NewBoolQuery().Must(elastic.NewBoolQuery().Should(ipQueryList...))
		query = elastic.NewBoolQuery().Must(qq, query)
	}
	/*---------------------------分域---------------------------------*/
	ruleTitleAgg := elastic.NewTermsAggregation().Field("rule_title")
	ruleTitleAgg.SubAggregation("page", elastic.NewBucketSortAggregation().From((param.Number-1)*param.Size).Size(param.Size))
	ruleCountAgg := elastic.NewCardinalityAggregation().Field("rule_title")

	serv, err := e.Instance.Client.Search().
		Index(e.IndexName).
		Query(query).
		Size(0).
		Pretty(true).
		Aggregation("rule_title", ruleTitleAgg).
		Aggregation("rule_count", ruleCountAgg).
		Do(context.Background())
	if err != nil {
		return data, 0, err
	}

	for key, rawMessage := range serv.Aggregations {
		switch key {
		case "rule_title":
			tmp := new(m_overviews.Aggregations)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, 0, err
			}
			for _, val := range tmp.Buckets {
				data = append(data, m_compliance_monitor.ComplianceMonitorSummary{
					Name:  val.Key,
					Value: int64(val.DocCount),
				})
			}

		case "rule_count":
			tmp := new(m_overviews.CardinalityAggregation)
			err := json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, 0, err
			}
			total = tmp.Value
		}
	}

	return data, total, nil
}

// GetSummaryListMux 获取合规监测搜索列表数据.
func (e *Store) GetSummaryListMux(ctx context.Context, ipRangeManager string, oldRuleIds []interface{}, ops ...elasticx.Option) (
	*elastic.SearchResult, error) {
	options := elasticx.Options{
		Status: elasticx.ComplianceMonitorStatusOfNotRepair,
	}

	for _, o := range ops {
		o.Apply(&options)
	}

	var query elastic.Query
	if options.Keyword != "" {
		query = NewKeywordQuery(options.Keyword)
	} else if options.Metadata != nil {
		boolQuery := elastic.NewBoolQuery()
		boolQuery = e.advancedSearchMultipleCondition(options, boolQuery)
		query = boolQuery
	} else {
		query = elastic.NewMatchAllQuery()
	}

	// ElasticSearch Query.
	aggregationNameWithMonitorType := "rule_title"
	status := elastic.NewTermQuery("status", options.Status)

	a := elastic.NewTermsAggregation().
		Field(aggregationNameWithMonitorType).
		Size(elasticx.DefaultScrollQueryBulkSize).
		OrderByCountDesc()
	a.SubAggregation(options.Status, elastic.NewFilterAggregation().Filter(status))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		qq := elastic.NewBoolQuery().Must(elastic.NewBoolQuery().Should(ipQueryList...))
		query = elastic.NewBoolQuery().Must(qq, query)
	}
	/*---------------------------分域---------------------------------*/

	sr, err := e.Instance.Client.
		Search(e.IndexName).
		Query(query).
		PostFilter(status).
		Aggregation(aggregationNameWithMonitorType, a).
		Do(ctx)

	return sr, err
}

// GetDashboardCategory get dashboard category.
func (e *Store) GetDashboardCategory(ctx context.Context, ipRangeManager string) ([]*m_threat.SystemBucket, int64, error) {
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("status", "not_repair")).
		MustNot(elastic.NewTermsQuery("status", ""))

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		boolQuery.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	serv := e.Instance.Client.Search().
		Index(e.IndexName).
		Type(e.TypeName).
		Query(boolQuery).
		Aggregation("rule_title", elastic.NewTermsAggregation().Field("rule_title").Size(6)).
		Size(0).
		Pretty(true)

	result, err := serv.Do(ctx)
	if err != nil {
		return nil, 0, err
	}

	if result.Hits.TotalHits < 1 {
		return nil, 0, nil
	}

	data := make([]*m_threat.SystemBucket, 0)
	terms, find := result.Aggregations.Terms("rule_title")
	if find {
		for _, bucket := range terms.Aggregations {
			agg, err := esi_threats.ConvertAggregationBucketToSystemBucket(bucket)
			if err != nil {
				continue
			}
			if len(agg) == 6 {
				var count int
				for _, v := range agg[:len(agg)-2] {
					count += v.DocCount
				}
				agg[len(agg)-2].Key = "其他"
				agg[len(agg)-2].DocCount = int(result.Hits.TotalHits) - count
				data = agg[:len(agg)-1]
			} else {
				data = agg
			}
		}
	}

	return data, result.Hits.TotalHits, nil
}
