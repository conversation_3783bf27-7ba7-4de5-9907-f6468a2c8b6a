package esi_compliance_monitor

import (
	"context"
	"encoding/json"

	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"

	"github.com/olivere/elastic"
)

// GetSearchList 获取合规监测搜索列表数据.
func (e *Store) GetSearchList(ctx context.Context, ipRangeManager string, ops ...elasticx.Option) (
	*m_compliance_monitor.ComplianceExamineAdvancedSearchDataResource, error) {
	options := elasticx.Options{
		Status: elasticx.ComplianceMonitorStatusOfNotRepair,
	}

	for _, o := range ops {
		o.Apply(&options)
	}

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	query := elastic.NewBoolQuery()
	if len(ipQueryList) > 0 {
		query = elastic.NewBoolQuery().Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/

	// ElasticSearch Query.
	business := e.NewTermsAggregation(m_tag.SystemPresetTagCategoryOfBusinessApp, elasticx.DefaultScrollQueryBulkSize, options.Status)
	company := e.NewTermsAggregation(m_tag.SystemPresetTagCategoryOfCompany, elasticx.DefaultScrollQueryBulkSize, options.Status)
	username := e.NewTermsAggregation(m_tag.SystemPresetTagCategoryOfResponsiblePerson, elasticx.DefaultScrollQueryBulkSize, options.Status)
	computeRoom := e.NewTermsAggregation(m_tag.SystemPresetTagCategoryOfComputerRoom, elasticx.DefaultScrollQueryBulkSize, options.Status)
	assetLevel := e.NewTermsAggregation(m_tag.SystemPresetTagCategoryOfAssetLevel, elasticx.DefaultScrollQueryBulkSize, options.Status)
	status := elastic.NewTermQuery(elasticx.FieldNameOfStatus, options.Status)

	sr, err := e.Instance.Client.
		Search(e.IndexName).
		Query(query).
		PostFilter(status).
		Size(0).
		Aggregation(m_tag.SystemPresetTagCategoryOfBusinessApp, business).
		Aggregation(m_tag.SystemPresetTagCategoryOfCompany, company).
		Aggregation(m_tag.SystemPresetTagCategoryOfResponsiblePerson, username).
		Aggregation(m_tag.SystemPresetTagCategoryOfComputerRoom, computeRoom).
		Aggregation(m_tag.SystemPresetTagCategoryOfAssetLevel, assetLevel).
		Do(ctx)

	aggregation := e.aggregation(sr, options.Status)

	return aggregation, err
}

func (e *Store) aggregation(sr *elastic.SearchResult, status string) *m_compliance_monitor.
	ComplianceExamineAdvancedSearchDataResource {

	r := &m_compliance_monitor.ComplianceExamineAdvancedSearchDataResource{}

	sets := []string{
		m_tag.SystemPresetTagCategoryOfBusinessApp,
		m_tag.SystemPresetTagCategoryOfCompany,
		m_tag.SystemPresetTagCategoryOfResponsiblePerson,
		m_tag.SystemPresetTagCategoryOfComputerRoom,
		m_tag.SystemPresetTagCategoryOfAssetLevel,
	}

	for _, set := range sets {
		terms, find := sr.Aggregations.Terms(set)

		if find {
			switch set {
			case m_tag.SystemPresetTagCategoryOfBusinessApp:
				if r.BusinessApp == nil {
					r.BusinessApp = make([]interface{}, 0)
				}
				r.BusinessApp = buckets(terms, r.BusinessApp.([]interface{}), status)
			case m_tag.SystemPresetTagCategoryOfCompany:
				if r.Company == nil {
					r.Company = make([]interface{}, 0)
				}
				r.Company = buckets(terms, r.Company.([]interface{}), status)
			case m_tag.SystemPresetTagCategoryOfResponsiblePerson:
				if r.Username == nil {
					r.Username = make([]interface{}, 0)
				}
				r.Username = buckets(terms, r.Username.([]interface{}), status)
			case m_tag.SystemPresetTagCategoryOfComputerRoom:
				if r.ComputerRoom == nil {
					r.ComputerRoom = make([]interface{}, 0)
				}
				r.ComputerRoom = buckets(terms, r.ComputerRoom.([]interface{}), status)
			case m_tag.SystemPresetTagCategoryOfAssetLevel:
				if r.AssetLevel == nil {
					r.AssetLevel = make([]interface{}, 0)
				}
				r.AssetLevel = buckets(terms, r.AssetLevel.([]interface{}), status)
			}
		}
	}

	return r
}

func buckets(terms *elastic.AggregationBucketKeyItems, arr []interface{}, status string) []interface{} {

	type docCount struct {
		DocCount int64 `json:"doc_count"`
	}

	for _, bucket := range terms.Buckets {
		var mess = &docCount{}

		if bucket.Aggregations != nil {
			if message, exists := bucket.Aggregations[status]; exists {
				marshalJSON, err := message.MarshalJSON()
				if err != nil {
					continue
				}

				err = json.Unmarshal(marshalJSON, &mess)
				if err != nil {
					continue
				}

				if mess.DocCount > 0 {
					if val, ok := bucket.Key.(string); ok && val != "" {
						arr = append(arr, bucket.Key)
					}
				}
			}
		}
	}

	return arr
}

func (e *Store) NewTermsAggregation(field string, size int, status string) *elastic.TermsAggregation {
	statusTermQuery := elastic.NewTermQuery("status", status)
	desc := elastic.NewTermsAggregation().
		Field(field).
		Size(size).
		OrderByCountDesc()

	desc.SubAggregation(status, elastic.NewFilterAggregation().Filter(statusTermQuery))

	return desc
}
