package esi_compliance_monitor

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/util/timer"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye-dependencies/mosso"
)

// GetViolationCount 计算违规数量
func (e *Store) GetViolationCount(ipRangeManager string) ([]m_overviews.Index, error) {
	tmpData := make(map[string]int)
	tmpData["add"] = 0
	tmpData["total"] = 0
	tmpData["ip"] = 0
	tmpData["port"] = 0
	tmpData["service"] = 0
	data := make([]m_overviews.Index, 0)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewMatchAllQuery())
	query.Must(elastic.NewTermQuery("status", "not_repair"))

	violationAgg := elastic.NewTermsAggregation().Field("violation_type")
	newViolation := elastic.NewFilterAggregation().Filter(elastic.NewRangeQuery("createtime").
		Gte(timer.CurrentDateStr() + " 00:00:00").
		Lte(time.Now().AddDate(0, 0, 1).Format("2006-01-02") + " 00:00:00"))
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	searchResult, err := e.Instance.Client.
		Search(e.IndexName).
		Type(e.TypeName).
		Query(query).Size(0).
		Aggregation("violation", violationAgg).
		Aggregation("new", newViolation).
		Do(context.Background())
	if err != nil {
		return data, err
	}

	for key, rawMessage := range searchResult.Aggregations {
		switch key {
		case "violation":
			tmp := m_overviews.Aggregations{}
			err = json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				return data, err
			}
			for _, bucket := range tmp.Buckets {
				if strings.Contains(bucket.Key, "ip_occupied") {
					tmpData["ip"] += bucket.DocCount
				} else if strings.Contains(bucket.Key, "disabled_port_opened") {
					tmpData["port"] += bucket.DocCount
				} else if strings.Contains(bucket.Key, "disabled_service_opened") {
					tmpData["service"] += bucket.DocCount
				}
			}
		case "new":
			mosso.DebugShowContentWithJSON(*rawMessage)
			tmp := new(m_overviews.Doc)
			err = json.Unmarshal(*rawMessage, &tmp)
			if err != nil {
				log.Println(err)
				return data, err
			}
			tmpData["add"] = tmp.DocCount
		}
	}
	tmpData["total"] = int(searchResult.Hits.TotalHits)
	for key, val := range tmpData {
		data = append(data, m_overviews.Index{
			Name:  key,
			Count: val,
		})
	}
	return data, nil
}

// CountViolationTotal 计算违规数量
func (e *Store) CountViolationTotal(repaired bool, ipRangeManager string, xcType string) int {
	query := elastic.NewBoolQuery()
	if repaired {
		query.Must(elastic.NewTermQuery("status", "has_been_repaired"))
	} else {
		query.Must(elastic.NewTermQuery("status", "not_repair"))
	}
	if xcType == "1" {
		query.Must(elastic.NewTermQuery("is_xc", 1))
	} else if xcType == "0" {
		query.MustNot(elastic.NewTermQuery("is_xc", 1))
	}
	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		query.Must(elastic.NewBoolQuery().Should(ipQueryList...))
	}
	/*---------------------------分域---------------------------------*/
	num, err := e.Instance.Client.Count(elasticx.IndexNameOfViolation()).Query(query).Do(context.Background())
	if err != nil {
		return 0
	}
	return int(num)
}
