package esi_compliance_monitor

import (
	"context"
	"io"
	"sync"

	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"

	"github.com/olivere/elastic"
)

var mutex sync.Mutex

// GetListWithAnnounce 获取合规监测违规通报列表数据.
func (e *Store) GetListWithAnnounce(ctx context.Context, ipRangeManager string, ops ...elasticx.Option) (*elastic.SearchResult, error) {
	options := elasticx.Options{
		Status: elasticx.ComplianceMonitorStatusOfNotRepair,
	}

	for _, o := range ops {
		o.Apply(&options)
	}

	// Prepare processing.
	if options.Keyword != "" && options.Metadata != nil {
		options.Keyword = ""
	}

	var query elastic.Query
	if options.Keyword != "" {
		query = NewKeywordQuery(options.Keyword)
	} else if options.Metadata != nil {
		boolQuery := elastic.NewBoolQuery()
		boolQuery = e.advancedSearchMultipleCondition(options, boolQuery)
		query = boolQuery
	} else {
		query = elastic.NewMatchAllQuery()
	}

	var r = new(elastic.SearchResult)

	mutex.Lock()
	defer mutex.Unlock()

	termQueryWithStatus := elastic.NewTermQuery(
		elasticx.FieldNameOfStatus,
		options.Status,
	)

	/*---------------------------分域---------------------------------*/
	ipQueryList := es_asset.ConvertIpContent(ipRangeManager)
	if len(ipQueryList) > 0 {
		qq := elastic.NewBoolQuery().Must(elastic.NewBoolQuery().Should(ipQueryList...))
		query = elastic.NewBoolQuery().Must(qq, query)
	}
	/*---------------------------分域---------------------------------*/

	service := e.Instance.Client.Scroll().
		Index(e.IndexName).
		Type(e.TypeName).
		Query(query).
		PostFilter(termQueryWithStatus)

	if options.Ids != nil {
		subQuery := elastic.NewIdsQuery().Ids(options.Ids...)

		service.Query(subQuery)
	}

	result, err := service.
		Sort(elasticx.FieldNameOfCreatedTime, false).
		Size(elasticx.DefaultScrollQueryBulkSize).
		Do(ctx)

	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil && len(result.Hits.Hits) > 0 {
			for _, item := range result.Hits.Hits {
				if r.Hits == nil {
					r.Hits = &elastic.SearchHits{Hits: make([]*elastic.SearchHit, 0)}
					r.Hits.Hits = append(r.Hits.Hits, item)
				} else {
					r.Hits.Hits = append(r.Hits.Hits, item)
				}
			}

			r.Hits.TotalHits += result.Hits.TotalHits
			result, err = e.Instance.Client.
				Scroll().
				Scroll(elasticx.DefaultScrollKeepAlive).
				ScrollId(result.ScrollId).
				Index(e.IndexName).
				Type(e.TypeName).
				Query(query).
				Size(elasticx.DefaultScrollQueryBulkSize).
				Do(ctx)

			if err != nil {
				break
			}
		} else {
			break
		}
	}

	if err != nil && err != io.EOF {
		return nil, err
	}

	return r, nil
}
