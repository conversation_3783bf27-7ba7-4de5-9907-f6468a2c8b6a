package esi_compliance_monitor

import (
	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/stretchr/testify/assert"
)

//func (suite *Suite) Test_GetSummaryList() {
//	list, err := suite.db.GetSummaryList(suite.ctx, "")
//	assert.NoError(suite.T(), err)
//	assert.NotNil(suite.T(), list)
//	terms, find := list.Aggregations.Terms("violation_type")
//	assert.True(suite.T(), find)
//	mosso.DebugShowContentWithJSON(terms)
//}

func (suite *Suite) Test_GetSummaryList_WithStatus() {
	list, err := suite.db.GetSummaryList(suite.ctx, "", elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfRepaired))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}
