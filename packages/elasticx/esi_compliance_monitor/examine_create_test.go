package esi_compliance_monitor

import (
	"encoding/json"
	"io/ioutil"

	"github.com/stretchr/testify/assert"
)

func (suite *Suite) Test_CreateComplianceMonitor() {

	data, err := ioutil.ReadFile("./../../../static/assets/asset/violations_doc.json")
	assert.NoError(suite.T(), err)

	tmp := make(map[string]interface{})
	err = json.Unmarshal(data, &tmp)
	assert.NoError(suite.T(), err)

	_, err = suite.db.CreateComplianceMonitor(tmp)
	assert.NoError(suite.T(), err)
}
