package esi_compliance_monitor

import (
	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/stretchr/testify/assert"
)

func (suite *Suite) Test_GetList() {
	list, err := suite.db.GetList(suite.ctx, "", elasticx.WithNumber(1), elasticx.WithSize(-1))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

func (suite *Suite) Test_GetList_With_AdvancedSearchQueryParams_IPRange() {

	list, err := suite.db.GetList(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			IpRange: "**********/24",
		}),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

func (suite *Suite) Test_GetList_With_AdvancedSearchQueryParams_IPRange_SingleIP() {

	list, err := suite.db.GetList(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			IpRange: "************",
		}),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

// 未修复
func (suite *Suite) Test_GetListNew_ComplianceMonitorStatusOfNotRepair_IpRange() {

	list, err := suite.db.GetList(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			IpRange: "***********/24",
		}),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

// 未修复
func (suite *Suite) Test_GetListNew_ComplianceMonitorStatusOfNotRepair_Username() {

	list, err := suite.db.GetList(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			Username: []string{"大庆"},
		}),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

// 未修复，存在Keyword时并且存在高级查询时，只生效高级查询，会清空关键字内容.
func (suite *Suite) Test_GetListNew_ComplianceMonitorStatusOfNotRepair_Username_And_Has_Keyword() {

	list, err := suite.db.GetList(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
		elasticx.WithKeyword("Aruba"),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			Username: []string{"大庆"},
		}),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

// 未修复，存在Keyword时并且存在高级查询时，只生效高级查询，会清空关键字内容.
func (suite *Suite) Test_GetListNew_ComplianceMonitorStatusOfNotRepair_Keyword() {

	list, err := suite.db.GetList(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
		elasticx.WithKeyword("Aruba"),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

// 未修复，多个高级查询条件.
func (suite *Suite) Test_GetListNew_ComplianceMonitorStatusOfNotRepair_Multiple_AdvancedSearch_Conditions() {

	list, err := suite.db.GetList(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfNotRepair),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			IpRange:  "***********/24",
			Username: []string{"大庆"},
		}),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

// 已修复
func (suite *Suite) Test_GetListNew_ComplianceMonitorStatusOfRepaired_Username() {

	list, err := suite.db.GetList(suite.ctx, "",
		elasticx.WithNumber(1),
		elasticx.WithSize(-1),
		elasticx.WithStatus(elasticx.ComplianceMonitorStatusOfRepaired),
		elasticx.WithMetadata(&m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams{
			Username: []string{"大庆"},
		}),
	)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), list)
	//mosso.DebugShowContentWithJSON(list, mosso.WithSpecificOfWriteFile(true))
}

func (suite *Suite) Test_GetListAll() {
	res := suite.db.GetListAll("************", elasticx.WithIDs([]string{"123"}))
	assert.NotNil(suite.T(), res)
}
