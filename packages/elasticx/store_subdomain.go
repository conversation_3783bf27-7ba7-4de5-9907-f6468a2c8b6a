package elasticx

import (
	"context"

	"git.gobies.org/foeye/foeye3/api/risk/asset/exclusive"

	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/model/m_product_inspection"
	"git.gobies.org/foeye/foeye3/model/m_user_rules"

	"github.com/olivere/elastic"
)

// ElasticSearch index related constant definition for {subdomain}.
const (
	IndexNameOfSubDomains = "fofaee_subdomain"
	TypeNameOfSubDomain   = "subdomain"
)

type IndexSubDomainStore interface {
	GetListAllWriteToFile(ctx context.Context, filename string, ops ...Option) (string, error)
	CountSubdomainTotal(query elastic.Query) (int, error)
	GetSubdomainByFofa(query string, state bool) (map[string]*m_user_rules.ServiceSubdomain, error)
	UpdateUserRule(data map[string]*m_user_rules.ServiceSubdomain) error
	GetSubdomainByRuleId(ruleId uint) (map[string]*m_user_rules.ServiceSubdomain, error)
	GetSubdomainFeatureID() ([]m_product_inspection.FeatureID, error)
	GetSubdomainAssetFeature() ([]m_product_inspection.Subdomain, error)
	CreateBulkAssetFeature(data []m_product_inspection.Subdomain) error
	CreateBulkFeatureID(data []m_product_inspection.FeatureID) error
	AssetOverviewCountSubdomain(ipRangeManager string) ([]m_overviews.Index, error)
	AssetOverviewCountDomain(ipRangeManager string) ([]m_overviews.Index, error)
	GetServiceSubdomainInfo(searchSource *elastic.SearchSource) ([]*exclusive.RiskLoginListData, int, error)
	GetServiceSubdomainAllInfo(searchSource *elastic.SearchSource) ([]*exclusive.RiskLoginListData, int, error)
}

func IndexNameOfSubDomain() string {
	return IndexNameOfSubDomains
}
