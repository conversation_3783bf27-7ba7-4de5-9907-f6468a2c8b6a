package elasticx

import (
	"context"

	"git.gobies.org/foeye/foeye3/internal/mux/packages/exchange/e_task"
)

// ElasticSearch index related constant definition for {subdomain}.
const (
	IndexNameOfTaskAssets = "fofaee_task_assets"
	TypeNameOfTaskAssets  = "ips"
)

type IndexTaskAssetsStore interface {
	GetListAllWithTaskIdWriteToFile(ctx context.Context,
		id int, filename string, ops ...Option) (string, error)
	DeleteAssetCount(ctx context.Context,
		taskID []uint) error
	DeleteDocumentsByTaskId(ctx context.Context,
		taskID int) error
	DeleteDocumentsByTaskIds(ctx context.Context,
		taskIds []interface{}) error

	// GetAssetServerByTaskFinished API 2.0
	GetAssetServerByTaskFinished(ctx context.Context,
		QL *e_task.QueryTaskFinishedListContainExtra) (interface{}, int64, error)
	PutAssetLevelMapping() error
}

func IndexNameOfTaskAsset() string {
	return IndexNameOfTaskAssets
}
