package esi_task_assets

import (
	"context"
	"testing"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/backup_restore"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_index"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"
)

func TestElasticIndexAssetsSuite(t *testing.T) {
	suite.Run(t, new(ElasticIndexAssetsSuite))
}

type ElasticIndexAssetsSuite struct {
	suite.Suite
	ins             *Store
	elasticdatabase *elasticx.ElasticDatabase
	configure       *config.Configure
	afterDropTables bool
}

func (suite *ElasticIndexAssetsSuite) BeforeSuite() {
	gomonkey.ApplyFunc(elasticx.IndexNameOfAsset, func() string {
		return "fofaee_assets_task4_test"
	})
	assert.Equal(suite.T(), "fofaee_assets_task4_test", elasticx.IndexNameOfAsset())

	gomonkey.ApplyFunc(elasticx.IndexNameOfNetinfo, func() string {
		return "fofaee_netinfos_task4_test"
	})
	assert.Equal(suite.T(), "fofaee_netinfos_task4_test", elasticx.IndexNameOfNetinfo())

	gomonkey.ApplyFunc(elasticx.IndexNameOfService, func() string {
		return "fofaee_service_task4_test"
	})
	assert.Equal(suite.T(), "fofaee_service_task4_test", elasticx.IndexNameOfService())

	gomonkey.ApplyFunc(elasticx.IndexNameOfTaskAsset, func() string {
		return "fofaee_task_assets_task4_test"
	})
	assert.Equal(suite.T(), "fofaee_task_assets_task4_test", elasticx.IndexNameOfTaskAsset())

	gomonkey.ApplyFunc(elasticx.IndexNameOfSubDomain, func() string {
		return "fofaee_subdomain_task4_test"
	})
	assert.Equal(suite.T(), "fofaee_subdomain_task4_test", elasticx.IndexNameOfSubDomain())

	gomonkey.ApplyFunc(elasticx.IndexNameOfThreat, func() string {
		return "fofaee_threats_task4_test"
	})
	assert.Equal(suite.T(), "fofaee_threats_task4_test", elasticx.IndexNameOfThreat())

	gomonkey.ApplyFunc(elasticx.IndexNameOfViolation, func() string {
		return "fofaee_violations_task4_test"
	})
	assert.Equal(suite.T(), "fofaee_violations_task4_test", elasticx.IndexNameOfViolation())

	gomonkey.ApplyFunc(elasticx.IndexNameOfSiteurl, func() string {
		return "fofaee_siteurl_task4_test"
	})
	assert.Equal(suite.T(), "fofaee_siteurl_task4_test", elasticx.IndexNameOfSiteurl())

	gomonkey.ApplyGlobalVar(&es_asset.Indexes, map[string]string{
		"asset":     elasticx.IndexNameOfAsset(),
		"service":   elasticx.IndexNameOfService(),
		"subdomain": elasticx.IndexNameOfSubDomain(),
	})
}

func (suite *ElasticIndexAssetsSuite) SetupSuite() {
	suite.BeforeSuite()
	var err error
	backup_restore.BaseDir = "./../../../"
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../../../"),
	)
	assert.NotNil(suite.T(), suite.configure)

	suite.elasticdatabase, err = elasticx.NewElasticDatabase(suite.configure)
	assert.NoError(suite.T(), err)

	suite.ins = NewStore(suite.elasticdatabase)

	es_index.CreateIndex(suite.elasticdatabase.Client)
	suite.afterDropTables = true
}

func (suite *ElasticIndexAssetsSuite) TearDownSuite() {
	if suite.afterDropTables {
		suite.elasticdatabase.Client.DeleteIndex(
			elasticx.IndexNameOfAsset(),
			elasticx.IndexNameOfNetinfo(),
			elasticx.IndexNameOfService(),
			elasticx.IndexNameOfTaskAsset(),
			elasticx.IndexNameOfSubDomain(),
			elasticx.IndexNameOfThreat(),
			elasticx.IndexNameOfViolation(),
			elasticx.IndexNameOfSiteurl(),
		).Do(context.Background())
	}
}
