package esi_task_assets

import (
	"context"
	"fmt"
	"io"
	"os"
	"strconv"

	log "github.com/sirupsen/logrus"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_task"
)

func (e *Store) GetListAllWithTaskIdWriteToFile(
	ctx context.Context,
	id int,
	filename string,
	ops ...elasticx.Option,
) (string, error) {
	var err error

	defer func() {
		if err != nil && err != io.EOF {
			os.Remove(filename)
		}
	}()

	file, err := elasticx.MustOpenFile(filename)
	if err != nil {
		return "", err
	}
	defer file.Close()

	search := e.Instance.Client.
		Scroll().
		Scroll(elasticx.DefaultScrollKeepAlive).
		Index(e.IndexName).
		Type(e.TypeName).
		Query(elastic.NewTermQuery("task_id", strconv.Itoa(id))).
		Size(elasticx.DefaultScrollQueryBulkSize)

	result, err := search.Do(ctx)

	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
			for _, hit := range result.Hits.Hits {
				if hit.Source != nil {
					cur, err := hit.Source.MarshalJSON()
					if err != nil {
						continue
					}

					cur = append(cur[0:len(cur)-1],
						[]byte(fmt.Sprintf(`,"_id":"%s"}`, hit.Id))...)
					cur = append(cur, '\n')
					if _, err := file.Write(cur); err != nil {
						log.Printf("failed write context to file: %+v\n", err)
					}
				}
			}

			// Proceed to the next read.
			result, err = search.ScrollId(result.ScrollId).Do(ctx)
			if err != nil {
				break
			}
		} else {
			break
		}
	}

	if err != nil && err != io.EOF {
		return "", err
	}

	return filename, nil
}

// DeleteDocumentsByTaskIds delete documents by task id list.
func (e *Store) DeleteDocumentsByTaskIds(ctx context.Context, taskIds []interface{}) error {

	var query elastic.Query

	if taskIds == nil {
		query = elastic.NewMatchAllQuery()
	} else {
		query = elastic.NewTermsQuery("task_id", taskIds...)
	}

	_, err := e.Instance.Client.
		DeleteByQuery(e.IndexName).
		Type(e.TypeName).
		Conflicts("proceed").
		WaitForCompletion(false).
		Refresh("true").
		Query(query).
		Do(ctx)
	if err != nil {
		return err
	}

	return nil
}

// DeleteDocumentsByTaskId delete document by task id.
func (e *Store) DeleteDocumentsByTaskId(ctx context.Context, taskID int) error {
	_, err := e.Instance.Client.
		DeleteByQuery(e.IndexName).
		Type(e.TypeName).
		Conflicts("proceed").
		WaitForCompletion(false).
		Refresh("true").
		Query(elastic.NewTermsQuery("task_id", taskID)).
		Do(ctx)
	if err != nil {
		return err
	}

	return nil
}

// DeleteAssetCount 删除es有关资产数据
func (e *Store) DeleteAssetCount(ctx context.Context, taskID []uint) error {
	List, err := es_task.ToInterfaceSlice(taskID)
	if err != nil {
		return err
	}

	termQuery := elastic.NewTermsQuery("task_id", List...)
	_, err = e.Instance.Client.DeleteByQuery(elasticx.IndexNameOfTaskAsset()).Query(termQuery).Do(ctx)
	if err != nil {
		return err
	}
	return nil
}
