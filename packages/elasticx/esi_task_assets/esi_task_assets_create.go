package esi_task_assets

import (
	"context"
	"fmt"

	"github.com/thedevsaddam/gojsonq/v2"
)

func (e *Store) PutAssetLevelMapping() error {
	aa := `{
			"properties": {
				"asset_level": {
        			"type": "keyword",
        			"index": true
      			}
		  	}
}`

	res, err := e.Instance.Client.GetFieldMapping().Field("asset_level").Index(e.IndexName).Do(context.Background())
	if err != nil {
		return err
	}
	gp := gojsonq.New().FromInterface(res)
	rr := gp.Find(fmt.Sprintf("%s.mappings.%s.asset_level.full_name", e.IndexName, e.TypeName))
	if rr == nil {
		_, err = e.Instance.Client.PutMapping().Index(e.IndexName).Type(e.TypeName).BodyString(aa).Do(context.Background())
		return err
	}
	return nil
}
