package esi_task_assets

import (
	"context"

	"github.com/stretchr/testify/assert"
)

func (suite *ElasticIndexAssetsSuite) Test_GetListAllWriteToFile() {
	actual, err := suite.ins.GetListAllWithTaskIdWriteToFile(
		context.TODO(),
		848,
		"test_data/task_assets/task_assets_2020_0000026.txt",
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), actual)
}

func (suite *ElasticIndexAssetsSuite) Test_DeleteDocumentsByTaskId() {
	err := suite.ins.DeleteDocumentsByTaskId(context.TODO(), 11)
	assert.NoError(suite.T(), err)
}

func (suite *ElasticIndexAssetsSuite) Test_DeleteDocumentsByTaskIds_DeleteAll() {
	err := suite.ins.DeleteDocumentsByTaskIds(context.TODO(), nil)
	assert.NoError(suite.T(), err)
}

func (suite *ElasticIndexAssetsSuite) Test_DeleteDocumentsByTaskIds_WithSameIds() {
	err := suite.ins.DeleteDocumentsByTaskIds(context.TODO(), []interface{}{54})
	assert.NoError(suite.T(), err)
}
