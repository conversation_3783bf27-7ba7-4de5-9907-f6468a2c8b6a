package esi_task_assets

import (
	"context"
	"encoding/json"

	"git.gobies.org/foeye/foeye3/internal/mux/model/m_task"
	"git.gobies.org/foeye/foeye3/internal/mux/packages/exchange/e_task"

	"github.com/olivere/elastic"
)

// GetAssetServerByTaskFinished  get asset data by task_id.
func (e *Store) GetAssetServerByTaskFinished(ctx context.Context, QL *e_task.QueryTaskFinishedListContainExtra) (interface{}, int64, error) {

	boolQuery := elastic.NewBoolQuery().Must()
	boolQuery.Must(elastic.NewTermQuery("task_id", QL.Id))

	service := e.Instance.Client.Search().
		Index(e.IndexName).
		Type(e.TypeName).
		Pretty(true).
		Query(boolQuery).
		From((QL.Page-1)*QL.PerPage).
		Size(QL.PerPage).
		Sort("lastupdatetime", false)

	result, err := service.Do(ctx)
	if err != nil {
		return nil, 0, err
	}

	//mosso.DebugShowContentWithJSON(result)
	return taskAssetServerResultDataByDefault(result), result.Hits.TotalHits, nil
}

// taskAssetServerResultDataByDefault es data convert To interface{}.
func taskAssetServerResultDataByDefault(sr *elastic.SearchResult) interface{} {
	data := make([]*m_task.Assets, 0)
	for _, source := range sr.Hits.Hits {
		if source != nil {
			marshalJSON, err := source.Source.MarshalJSON()
			if err != nil {
				continue
			}
			info := new(m_task.Assets)
			err = json.Unmarshal(marshalJSON, &info)
			if err != nil {
				continue
			}
			data = append(data, info)
		}
	}
	return data
}
