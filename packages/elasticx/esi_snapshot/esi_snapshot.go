package esi_snapshot

import (
	"context"
	"sync"

	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/packages/elasticx"
)

type Store struct {
	Instance  *elasticx.ElasticDatabase
	IndexName string
	TypeName  string
	fileLock  sync.Mutex
}

func NewStore(elasticdatabase *elasticx.ElasticDatabase) *Store {
	return &Store{
		Instance:  elasticdatabase,
		IndexName: elasticx.IndexNameOfAsset(),
		TypeName:  elasticx.TypeNameOfAssets,
	}
}

// CrateSnapshotRepository 创建共享文件系统仓库
func (store *Store) CrateSnapshotRepository(repository string, settings map[string]interface{}) error {
	_, err := store.Instance.Client.
		SnapshotCreateRepository(repository).
		Type("fs").
		Settings(settings).
		Do(context.Background())
	return err
}

// CreateSnapshot 创建快照
func (store *Store) CreateSnapshot(repository string, snapshot string) (*elastic.SnapshotCreateResponse, error) {
	scr, err := store.Instance.Client.
		SnapshotCreate(repository, snapshot).
		WaitForCompletion(true).
		Do(context.Background())
	return scr, err
}

// DeleteSnapshot 删除快照
func (store *Store) DeleteSnapshot(repository string, snapshot string) error {
	_, err := store.Instance.Client.SnapshotDelete(repository, snapshot).Do(context.Background())
	return err
}

// GetAllSnapshot 获取所有快照
func (store *Store) GetAllSnapshot(repository string) ([]*elastic.Snapshot, error) {
	sgr, err := store.Instance.Client.SnapshotGet(repository).Do(context.Background())
	if err != nil {
		return nil, err
	}
	return sgr.Snapshots, err
}

// GetCatIndices 获取索引的状态和健康情况
func (store *Store) GetCatIndices() ([]elastic.CatIndicesResponseRow, error) {
	cir, err := store.Instance.Client.CatIndices().Do(context.Background())
	return cir, err
}

// VerifyRepository 验证仓库
func (store *Store) VerifyRepository(repository string) bool {
	svrr, err := store.Instance.Client.SnapshotVerifyRepository(repository).Do(context.Background())
	return svrr != nil && err == nil
}
