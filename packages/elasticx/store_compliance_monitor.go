package elasticx

import (
	"context"

	"git.gobies.org/foeye/foeye3/responses/r_compliance_monitor"

	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/model/m_overviews"
	"git.gobies.org/foeye/foeye3/model/m_threat"

	"github.com/olivere/elastic"
)

// ElasticSearch index related constant definition for {violations}.
const (
	// Compliance monitor module correspondence index name.
	IndexNameOfViolations       = "fofaee_violations"
	TypeNameOfComplianceMonitor = "ips"
)

// 核查状态相关常量定义.
const (
	ComplianceMonitorStatusOfNotRepair = "not_repair"
	ComplianceMonitorStatusOfRepaired  = "has_been_repaired"
)

type IndexComplianceMonitorStore interface {
	GetList(ctx context.Context, ipRangeManager string, ops ...Option) (*elastic.SearchResult, error)
	GetListMux(ctx context.Context, ipRangeManager string, oldRuleIds []interface{}, ops ...Option) (*elastic.SearchResult, error)
	GetSearchList(ctx context.Context, ipRangeManager string, ops ...Option) (*m_compliance_monitor.ComplianceExamineAdvancedSearchDataResource, error)
	GetSummaryList(ctx context.Context, ipRangeManager string, ops ...Option) (*elastic.SearchResult, error)
	GetXcSummaryList(ctx context.Context, ipRangeManager string, param *r_compliance_monitor.XcComplianceMonitorSummaryParam) ([]m_compliance_monitor.ComplianceMonitorSummary, int, error)
	GetSummaryListMenu(ctx context.Context, ipRangeManager string) ([]m_overviews.MenuIndex, error)
	GetSummaryListMux(ctx context.Context, ipRangeManager string, oldRuleIds []interface{}, ops ...Option) (*elastic.SearchResult, error)
	GetListWithAnnounce(ctx context.Context, ipRangeManager string, ops ...Option) (*elastic.SearchResult, error)
	DeleteDocuments(ctx context.Context, ops ...Option) (*elastic.BulkIndexByScrollResponse, error)
	DeleteDocumentByID(ctx context.Context, id string) (*elastic.DeleteResponse, error)
	DeleteDocumentsByIDs(ctx context.Context, ids ...string) (*elastic.BulkResponse, error)

	// DeleteDocumentsByMatch Delete single or more documents.
	DeleteDocumentsByMatch(ctx context.Context, match *string) (*elastic.BulkIndexByScrollResponse, error)

	// 获取监测的数据
	GetViolation(ip string, ruleID uint, violationType string, content []string) (*elastic.SearchResult, error)
	CreateComplianceMonitor(data map[string]interface{}) (*elastic.IndexResponse, error)
	UpdateComplianceMonitor(data map[string]interface{}, violationID string) (*elastic.UpdateResponse, error)
	GetListAll(ipRangeManager string, ops ...Option) *elastic.ScrollService
	GetListAllMux(ipRangeManager string, newRuleIds []interface{}, ops ...Option) *elastic.ScrollService
	GetViolationById(violationID string) (*elastic.GetResult, error)
	GetViolationRanking(ipRangeManager string) ([]m_overviews.RankingList, error)
	GetViolationCount(ipRangeManager string) ([]m_overviews.Index, error)
	PutAssetLevelMapping() error
	CountViolationTotal(repaired bool, ipRangeManager string, xcType string) int
	GetDashboardCategory(ctx context.Context, ipRangeManager string) ([]*m_threat.SystemBucket, int64, error)

	BullUpdateStateByIP(ip string, state int) error
}

func IndexNameOfViolation() string {
	return IndexNameOfViolations
}
