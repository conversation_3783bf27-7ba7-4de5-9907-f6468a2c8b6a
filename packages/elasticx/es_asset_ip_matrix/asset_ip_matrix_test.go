package es_asset_ip_matrix

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"testing"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye-dependencies/fsfire"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_second_categories"

	"git.gobies.org/foeye/foeye3/packages/backup_restore"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_index"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestMatrixSuite(t *testing.T) {
	suite.Run(t, &MatrixSuite{})
}

type MatrixSuite struct {
	suite.Suite

	ins             ESAssetIpMatrix
	esaim           esAssetIpMatrix
	componentDB     *db_second_categories.GormSecondCategoriesDatabase
	configure       *config.Configure
	elasticdatabase *elasticx.ElasticDatabase
	afterDropTables bool
	baseDir         string
	testBaseDir     string
}

func (suite *MatrixSuite) Test_IPMatrixItem() {
	expected := "{\"id\":\"1\",\"name\":\"kitty\",\"is_xc\":0,\"port_size\":0,\"icon_background\":\"\",\"lastchecktime\":\"\",\"statistics\":{\"online\":0,\"offline\":0,\"unscanned\":0,\"undiscovered\":0,\"has_vul\":0}}"
	actual := IPMatrixItem{ID: "1", Name: "kitty"}
	assert.Equal(suite.T(), expected, actual.String())
}

func (suite *MatrixSuite) SetupSuite() {
	var err error
	suite.baseDir = "./../../../"
	backup_restore.BaseDir = "./../../../"
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath(suite.baseDir),
	)
	assert.NotNil(suite.T(), suite.configure)

	suite.elasticdatabase, err = elasticx.NewElasticDatabase(suite.configure)
	assert.NoError(suite.T(), err)

	suite.ins = NewESAssetIpMatrix(suite.elasticdatabase)
	assert.NotNil(suite.T(), suite.ins)

	suite.esaim = esAssetIpMatrix{Instance: suite.elasticdatabase}

	gormDatabase, err := database.NewGormDatabase(suite.configure)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), gormDatabase)
	suite.componentDB = &db_second_categories.GormSecondCategoriesDatabase{Instance: gormDatabase}

	suite.testBaseDir, err = fsfire.GetFilePathWithFileSystemPath(suite.baseDir, fsfire.WithSpecificFileSystemPath("test"))
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), suite.testBaseDir)

	suite.afterDropTables = true
	es_index.CreateIndex(suite.elasticdatabase.Client)
	suite.autoMigrateSystemPresetData()
}

func (suite *MatrixSuite) autoMigrateSystemPresetData() {
	testBaseDir, err := fsfire.GetFilePathWithFileSystemPath(suite.baseDir, fsfire.WithSpecificFileSystemPath("test"))
	fileName := "asset_doc.txt"
	filePath := filepath.Join(testBaseDir, "data", fileName)

	slice, err := fsfire.GetFileContentStringSliceWithFilename(filePath)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), slice)

	// insert data to assets
	for _, body := range slice {
		asset := make(map[string]interface{})
		assert.NoError(suite.T(), json.Unmarshal([]byte(body), &asset))
		_, err = suite.elasticdatabase.Client.Index().Index(elasticx.IndexNameOfAsset()).Type("ips").BodyJson(asset).Refresh("true").Do(context.Background())
		assert.NoError(suite.T(), err)
	}
}

func (suite *MatrixSuite) TearDownSuite() {
	defer database.Clone(suite.componentDB.Instance.DB)
	if suite.afterDropTables {
		suite.elasticdatabase.Client.DeleteIndex(elasticx.IndexNameOfAsset()).Do(context.Background())
	}
}

func (suite *MatrixSuite) Test_GetListOfSegments() {
	segments, err := suite.ins.GetListOfSegments(context.Background(), "")
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), segments)
	fmt.Printf("%#v\n", segments)
}

func (suite *MatrixSuite) Test_GetAssetId() {
	id := suite.esaim.GetAssetId("b-*********")
	assert.Equal(suite.T(), "*********", id)

	id = suite.esaim.GetAssetId("c-*********")
	assert.Equal(suite.T(), "*********", id)
}

func (suite *MatrixSuite) Test_GetIPMatrix() {
	var ctx context.Context

	jsonData := `{
			"ip_b_nets": {
				"doc_count_error_upper_bound": 0,
				"sum_other_doc_count": 0,
				"buckets": [
					{
						"key": "*********/16",
						"doc_count": 157,
						"ip_c_nets": {
							"doc_count_error_upper_bound": 0,
							"sum_other_doc_count": 0,
							"buckets": [
								{
									"key": "**********/24",
									"doc_count": 102
								},
								{
									"key": "**********/24",
									"doc_count": 55
								}
							]
						}
					},
					{
						"key": "**********/16",
						"doc_count": 4,
						"ip_c_nets": {
							"doc_count_error_upper_bound": 0,
							"sum_other_doc_count": 0,
							"buckets": [
								{
									"key": "***********/24",
									"doc_count": 3
								},
								{
									"key": "***********/24",
									"doc_count": 1
								}
							]
						}
					},
					{
						"key": "",
						"doc_count": 3,
						"ip_c_nets": {
							"doc_count_error_upper_bound": 0,
							"sum_other_doc_count": 0,
							"buckets": [
								{
									"key": "",
									"doc_count": 3
								}
							]
						}
					},
					{
						"key": "*********/16",
						"doc_count": 2,
						"ip_c_nets": {
							"doc_count_error_upper_bound": 0,
							"sum_other_doc_count": 0,
							"buckets": [
								{
									"key": "**********/24",
									"doc_count": 2
								}
							]
						}
					}
				]
			}
		
	}`
	searchResult := elastic.SearchResult{}
	err := json.Unmarshal([]byte(jsonData), &searchResult.Aggregations)

	ipBAggs := elastic.NewTermsAggregation().Field("ip_b_net").Size(255).SubAggregation("ip_c_nets", elastic.NewTermsAggregation().Field("ip_c_net").Size(255))
	defer gomonkey.ApplyMethodReturn(suite.esaim.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		Type("ips").
		Size(0).
		Query(elastic.NewBoolQuery().Must(elastic.NewTermQuery("is_ipv6", false))).
		Aggregation("ip_b_nets", ipBAggs), "Do", &searchResult, nil).Reset()

	result, err := suite.esaim.GetIPMatrix(ctx)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), result, 3)

	assert.Equal(suite.T(), "*********/16", result[0].Name)
	assert.Equal(suite.T(), "**********/24", result[0].SubItems[0].Name)

	result2, err := suite.esaim.GetMatrixStatistics(result)
	assert.Equal(suite.T(), int64(163), result2.IPAmount)
	assert.Equal(suite.T(), uint(5), result2.CAmount)
	assert.Equal(suite.T(), uint(3), result2.BAmount)
}
