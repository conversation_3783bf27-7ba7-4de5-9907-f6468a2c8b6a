package es_asset_ip_matrix

import (
	"context"
	"encoding/json"
	"io"
	"net"
	"strings"

	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_assets"
	"git.gobies.org/foeye/foeye3/packages/segment"
	"git.gobies.org/foeye/foeye3/responses/r_asset"

	"github.com/nytr0gen/go-cidr"
	"github.com/olivere/elastic"
)

// IPStateStatistics IP 段下IP在线状态统计
type IPStateStatistics struct {
	Online       int `json:"online"`
	Offline      int `json:"offline"`
	UnScanned    int `json:"unscanned"`
	UnDiscovered int `json:"undiscovered"`
	HasVul       int `json:"has_vul"`
}

// IPMatrixItem IP矩阵详情
// swagger:model IPMatrixItem
type IPMatrixItem struct {
	// ID 资产ID
	ID string `json:"id,omitempty"`
	// Name 资产名称，通常是IP
	Name string `json:"name,omitempty"`
	// Parent 父级ID，如果是B段则为空
	Parent string `json:"parent,omitempty"`
	// Type 类型，b表示B段，c表示C段，ip表示IP
	Type string `json:"type,omitempty"`
	// State 资产状态。在线：1, 离线：2, 未发现：3, 未扫描: 4
	State int `json:"state,omitempty"`
	// Value 获取IP资产下子IP的数量
	Value int `json:"value,omitempty"`
	// IsXc 获取IP资产信创类型
	IsXc int `json:"is_xc"`
	// RuleInfos 资产信息分层列表
	RuleInfos []*m_asset.RuleInfos `json:"rule_infos,omitempty"`
	// ThreatStatus 威胁情况
	// ThreatStatus *m_asset.ThreatStatus `json:"threat_status,omitempty"`
	// VulsAllCount 漏洞数量
	VulsAllCount int `json:"vuls_all_count,omitempty"`

	// SubItems 列表模式下，获取B段下所有C段列表
	SubItems []IPMatrixItem `json:"sub_items,omitempty"`

	// 开放端口数量.
	//
	// required: true
	// read only: false
	// example: 10
	PortSize int `json:"port_size"`

	// 卡片的背景图片.
	//
	// required: true
	// read only: false
	// example: "路由器"
	IconBackground string `json:"icon_background"`

	// 上次探测时间.
	//
	// required: true
	// read only: false
	// example: "2022-03-27 09:35:38"
	LastCheckTime string `json:"lastchecktime"`

	// IP 段下IP在线状态统计，仅IP段下有效
	Statistics IPStateStatistics `json:"statistics,omitempty"`

	// C段分类(仅C段会存在该字段,video-monitor:视频监控,data-center:数据中心,business-support:业务支持,normal-office:普通办公).
	//
	// required: true
	// read only: false
	// example: video-monitor
	Category segment.Category `json:"category,omitempty"`
}

func (item IPMatrixItem) String() string {
	data, _ := json.Marshal(&item)
	return string(data)
}

// todo:
// "lastupdatetime" : "2022-03-27 09:35:38",
// "lastchecktime" : "2022-03-27 09:35:38",

// IPInfo json decode for ip_info
type IPInfo struct {
	ID               string             `json:"id,omitempty"`
	IP               string             `json:"ip,omitempty"`
	State            int                `json:"state,omitempty"`
	StateDescription string             `json:"state_description,omitempty"`
	IPBNet           string             `json:"ip_b_net,omitempty"`
	IPCNet           string             `json:"ip_c_net,omitempty"`
	RuleInfos        []m_asset.RuleInfo `json:"rule_infos,omitempty"`
	VulsAllCount     int                `json:"vuls_all_count,omitempty"`
	IconBackground   string             `json:"icon_background"`
	PortSize         int                `json:"port_size"`
	LastCheckTime    string             `json:"lastchecktime"`
}

// ESAssetIpMatrix interface for es ip matrix methods
type ESAssetIpMatrix interface {
	GetIPMatrixStatistics(ctx context.Context) (IPMatrixStatistics, error)
	GetMatrixStatistics(ipMatrixItems []IPMatrixItem) (IPMatrixStatistics, error)
	GetIPMatrix(ctx context.Context) ([]IPMatrixItem, error)
	GetIPMatrixOfB(ctx context.Context, b string) ([]IPMatrixItem, error)
	GetIPMatrixOfC(ctx context.Context, c string) ([]IPMatrixItem, error)
	GetListOfSegments(ctx context.Context, ipNet string) (*segment.Segment, error)
}

// esAssetIpMatrix ESAssetIpMatrix instance
type esAssetIpMatrix struct {
	Instance *elasticx.ElasticDatabase
}

// IPMatrixStatistics IP统计数据
// swagger:model IPMatrixStatistics
type IPMatrixStatistics struct {
	// B段数量
	BAmount uint `json:"b_amount,omitempty"`
	// C段数量
	CAmount uint `json:"c_amount,omitempty"`
	// IP总数量
	IPAmount int64 `json:"ip_amount,omitempty"`
}

// B段
type IPBNet struct {
	Buckets []IPBNets `json:"buckets"`
}

type IPBNets struct {
	Key      string  `json:"key"`
	DocCount int     `json:"doc_count"`
	IPCNets  IPCNets `json:"ip_c_nets"`
}

type IPCNets struct {
	Buckets []IPCBucket `json:"buckets"`
}

type IPCBucket struct {
	Key      string `json:"key"`
	DocCount int    `json:"doc_count"`
}

const matrixSize = 100000

// todo: packages/elasticx/esi_assets/esi_assets_default_list.go@AggAssetDefault 有数据包装器

// NewESAssetIpMatrix create a new ESAssetIpMatrix instance
func NewESAssetIpMatrix(es *elasticx.ElasticDatabase) ESAssetIpMatrix {
	return &esAssetIpMatrix{Instance: es}
}

// getItemFromID retrive item from es by id
func (search *esAssetIpMatrix) getItemFromID(ctx context.Context, id string) (IPInfo, error) {
	id = search.GetAssetId(id)
	searchResult, err := search.Instance.Client.Get().
		Index(elasticx.IndexNameOfAsset()).
		Id(id).
		Do(context.Background())
	if err != nil {
		return IPInfo{}, err
	}

	b, _ := searchResult.Source.MarshalJSON()

	var r IPInfo
	err = json.Unmarshal(b, &r)
	return r, err
}

func (search *esAssetIpMatrix) GetAssetId(id string) string {
	if strings.HasPrefix(id, "c-") || strings.HasPrefix(id, "b-") {
		id = id[2:]
	}
	return id
}

// GetIPMatrixStatistics get ip matrix statistics
func (search *esAssetIpMatrix) GetIPMatrixStatistics(ctx context.Context) (IPMatrixStatistics, error) {
	searchResult, err := search.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		Type("ips").
		Size(0).
		Query(elastic.NewBoolQuery().Must(elastic.NewTermQuery("is_ipv6", false))).
		Aggregation("ip_b_net", elastic.NewCardinalityAggregation().Field("ip_b_net")).
		Aggregation("ip_c_net", elastic.NewCardinalityAggregation().Field("ip_c_net")).
		Do(context.Background())
	if err != nil {
		return IPMatrixStatistics{}, err
	}

	data := IPMatrixStatistics{
		IPAmount: searchResult.Hits.TotalHits,
	}
	for k, v := range searchResult.Aggregations {
		var d struct {
			Value uint `json:"value"`
		}
		b, _ := v.MarshalJSON()
		err = json.Unmarshal(b, &d)
		switch k {
		case "ip_b_net":
			data.BAmount = d.Value
		case "ip_c_net":
			data.CAmount = d.Value
		}
	}

	return data, err
}

// GetMatrixStatistics get matrix statistics
func (search *esAssetIpMatrix) GetMatrixStatistics(ipMatrixItems []IPMatrixItem) (IPMatrixStatistics, error) {
	data := IPMatrixStatistics{
		BAmount: uint(len(ipMatrixItems)),
	}
	cAmount := 0
	ipAmount := 0
	for _, v := range ipMatrixItems {
		cAmount += len(v.SubItems)
		for _, c := range v.SubItems {
			ipAmount += c.Value
		}
	}
	data.CAmount = uint(cAmount)
	data.IPAmount = int64(ipAmount)

	return data, nil
}

// GetIPMatrix get ip matrix of b block
func (search *esAssetIpMatrix) GetIPMatrix(ctx context.Context) ([]IPMatrixItem, error) {
	ipBAggs := elastic.NewTermsAggregation().Field("ip_b_net").Size(255).SubAggregation("ip_c_nets", elastic.NewTermsAggregation().Field("ip_c_net").Size(255))
	searchResult, err := search.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		Type("ips").
		Size(0).
		Query(elastic.NewBoolQuery().Must(elastic.NewTermQuery("is_ipv6", false))).
		Aggregation("ip_b_nets", ipBAggs).
		Do(context.Background())
	if err != nil {
		return nil, err
	}

	ips := []IPMatrixItem{}
	for _, rawMessage := range searchResult.Aggregations {
		var ipB *IPBNet
		if err := json.Unmarshal(*rawMessage, &ipB); err != nil {
			return nil, err
		}

		for _, ipBNet := range ipB.Buckets {
			if ipBNet.Key != "" {
				subItems := []IPMatrixItem{}
				for _, ipc := range ipBNet.IPCNets.Buckets {
					subItems = append(subItems, IPMatrixItem{
						ID:     "c-" + strings.ReplaceAll(ipc.Key, "/24", ""),
						Type:   "c",
						Name:   ipc.Key,
						Parent: ipBNet.Key,
						Value:  ipc.DocCount,
					})
				}

				ips = append(ips, IPMatrixItem{
					ID:       strings.ReplaceAll(ipBNet.Key, "/16", ""),
					Type:     "b",
					Name:     ipBNet.Key,
					Parent:   "",
					Value:    ipBNet.DocCount,
					SubItems: subItems,
				})
			}
		}

	}

	return ips, nil
}

// GetIPMatrixOfB get ip c block matrix of b block
func (search *esAssetIpMatrix) GetIPMatrixOfB(ctx context.Context, id string) ([]IPMatrixItem, error) {
	id = search.GetAssetId(id)
	if !strings.HasSuffix(id, "/16") {
		id = id + "/16"
	}

	ipBAggs := elastic.NewTermsAggregation().Field("ip_c_net").Size(255)
	searchResult, err := search.Instance.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		Type("ips").
		Size(0).
		Query(elastic.NewBoolQuery().Must(elastic.NewMatchQuery("ip_b_net", id)).
			Must(elastic.NewTermQuery("is_ipv6", false))).
		Aggregation("ip_c_nets", ipBAggs).
		Do(context.Background())
	if err != nil {
		return nil, err
	}

	ips := []IPMatrixItem{}
	for _, rawMessage := range searchResult.Aggregations {
		var ipC *IPCNets
		if err := json.Unmarshal(*rawMessage, &ipC); err != nil {
			return nil, err
		}

		for _, ipc := range ipC.Buckets {
			ips = append(ips, IPMatrixItem{
				ID:     "c-" + strings.ReplaceAll(ipc.Key, "/24", ""),
				Type:   "c",
				Name:   ipc.Key,
				Parent: id,
				Value:  ipc.DocCount,
			})
		}

	}

	segments, err := search.GetListOfSegments(ctx, id)
	if err != nil {
		return nil, err
	}

	for idx, item := range ips {
		category, err := segments.ComputeWithKey(item.Name)
		if err == nil {
			item.Category = category
			ips[idx] = item
		}
	}
	return ips, nil
}

// GetIPMatrixOfC get all ip of c block matrix with statistics/vul/ruleinfo data
func (search *esAssetIpMatrix) GetIPMatrixOfC(ctx context.Context, id string) ([]IPMatrixItem, error) {
	id = search.GetAssetId(id)
	if !strings.HasSuffix(id, "/24") {
		id = id + "/24"
	}

	searchResult, err := search.Instance.Client.Search().Index(elasticx.IndexNameOfAsset()).Type("ips").
		Size(matrixSize).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(
			"ip_c_net", "ip", "ip_b_net", "rule_infos", "state", "vuls_all_count", "lastchecktime", "port_size", "is_xc")).
		Query(elastic.NewBoolQuery().Must(elastic.NewMatchQuery("ip_c_net", id)).
			Must(elastic.NewTermQuery("is_ipv6", false))).
		Do(context.Background())
	if err != nil {
		return nil, err
	}

	ql := &r_asset.QueryListAndKeyword{}
	ql.TaskID = 0
	items := esi_assets.AggAssetDefault(searchResult, ql)

	ipCidr, err := cidr.NewRange(id)
	if err != nil {
		return nil, err
	}

	// fill ip list
	ips := make([]IPMatrixItem, 255)
	for i := 0; ipCidr.Next(); i++ {
		ips[i] = IPMatrixItem{
			ID:        "",
			Name:      ipCidr.String(),
			Parent:    id,
			Type:      id,
			State:     4,
			RuleInfos: nil,
		}
	}

	// modify target item
	for i, item := range items {
		var r IPInfo
		b, _ := searchResult.Hits.Hits[i].Source.MarshalJSON()
		// fmt.Println(string(b))
		json.Unmarshal(b, &r)

		// 底层数据：0：offline, 1:online
		// 在线：1, 离线：2, 未发现：3, 未扫描: 4
		state := 1
		if item.State == 0 {
			state = 2
		}

		parseIP := net.ParseIP(r.IP)

		idx := int(parseIP[15])

		if idx > 0 {
			idx = idx - 1
		}

		ips[idx] = IPMatrixItem{
			ID:             "ip-" + item.ID,
			Name:           item.IP,
			Parent:         "c-" + id,
			Type:           id,
			State:          state,
			Value:          0,
			RuleInfos:      item.RuleInfos,
			VulsAllCount:   r.VulsAllCount,
			SubItems:       []IPMatrixItem{},
			PortSize:       item.PortSize,
			IconBackground: item.IconBackground,
			LastCheckTime:  r.LastCheckTime,
			IsXc:           item.IsXc,
		}
	}

	// 需要返回时在redis bloomfilter 中过滤一次获取未扫描的IP状态
	return ips, nil

}

func (search *esAssetIpMatrix) GetListOfSegments(ctx context.Context, ipNet string) (*segment.Segment, error) {
	include := []string{"ip", "state", "ip_b_net", "ip_c_net", "cat_tags"}
	fields := elastic.NewSearchSource().FetchSourceIncludeExclude(include, nil)
	segments := segment.NewSegment()

	var query elastic.Query
	if len(ipNet) > 0 {
		query = elastic.NewBoolQuery().Must(elastic.NewTermQuery("ip_b_net", ipNet))
	} else {
		query = elastic.NewMatchAllQuery()
	}

	builder := search.Instance.Client.
		Scroll().
		Scroll("5m").
		Index(elasticx.IndexNameOfAsset()).
		Type(elasticx.TypeNameOfAssets).
		SearchSource(fields).
		Query(query).
		Size(elasticx.DefaultScrollQueryBulkSize)
	result, err := builder.Do(ctx)

	for {
		if result != nil && result.Hits != nil && result.Hits.Hits != nil {
			for _, hit := range result.Hits.Hits {
				if hit.Source != nil {
					data, err := hit.Source.MarshalJSON()
					if err != nil {
						continue
					}

					var r segment.Document
					err = json.Unmarshal(data, &r)
					if err != nil {
						continue
					}
					segments.SetValues(r.IPCNet, r)
				}
			}

			// Proceed to the next read.
			result, err = builder.ScrollId(result.ScrollId).Do(ctx)
			if err != nil {
				break
			}
		} else {
			break
		}
	}

	if err != nil && err != io.EOF {
		return nil, err
	}

	return segments, nil
}
