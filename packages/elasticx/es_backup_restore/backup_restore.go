package es_backup_restore

import (
	"context"
	"fmt"
	"strings"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/packages/backup_restore"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/fsx"
	"git.gobies.org/foeye/foeye3/packages/jsoner"

	"github.com/olivere/elastic"
)

type ElasticBackupRestoreDatabase struct {
	Instance *elasticx.ElasticDatabase
}

func NewElasticBackupRestoreDatabase(instance *elasticx.ElasticDatabase) *ElasticBackupRestoreDatabase {
	return &ElasticBackupRestoreDatabase{Instance: instance}
}

func (e *ElasticBackupRestoreDatabase) GetESScrollService(indexName string) *elastic.ScrollService {
	return e.Instance.Client.Scroll(indexName).Size(1000)
}

func (e *ElasticBackupRestoreDatabase) DeleteAllDataOfDCC(taskID uint) {
	indices := map[string]string{
		elasticx.IndexNameOfAsset():     "task_id",
		elasticx.IndexNameOfTaskAsset(): "task_id",
	}

	for indice, field := range indices {
		e.Instance.Client.DeleteByQuery().Index(indice).Query(elastic.NewTermQuery(field, taskID)).Do(context.Background())
	}

	deleteAll := []string{
		elasticx.IndexNameOfAsset(),
		elasticx.IndexNameOfService(),
		elasticx.IndexNameOfSubDomain(),
	}
	for _, indice := range deleteAll {
		_, err := e.Instance.Client.DeleteByQuery(indice).Query(elastic.NewMatchAllQuery()).Refresh("true").Do(context.Background())
		if err != nil {
			log.Println(err)
		}
	}

	_, _ = e.Instance.Client.DeleteIndex(elasticx.IndexNameOfThreat()).Do(context.Background())

}

func (e *ElasticBackupRestoreDatabase) DeleteAllData() {
	indices := []string{
		elasticx.IndexNameOfIndexTag(),
		elasticx.IndexNameOfAsset(),
		elasticx.IndexNameOfService(),
		elasticx.IndexNameOfSubDomain(),
		elasticx.IndexNameOfSiteurl(),
		elasticx.IndexNameOfTaskAsset(),
		elasticx.IndexNameOfThreat(),
		elasticx.IndexNameOfViolation(),
		elasticx.IndexNameOfNetinfo(),
		elasticx.IndexNameOfDomainAsset(),
		elasticx.IndexNameOfTaskDomainAsset(),
	}
	for _, indice := range indices {
		_, err := e.Instance.Client.DeleteByQuery(indice).Query(elastic.NewMatchAllQuery()).Refresh("true").Do(context.Background())
		if err != nil {
			log.Println(err)
		}
	}
}

func (e *ElasticBackupRestoreDatabase) CreateIndex() {
	backup_restore.InitVar()
	exists, _ := e.Instance.Client.IndexExists(elasticx.IndexNameOfSubDomain()).Do(context.Background())
	if !exists {
		e.Instance.Client.CreateIndex(elasticx.IndexNameOfSubDomain()).BodyString(fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`, backup_restore.SubdomainSettings, backup_restore.SubdomainMappings)).Do(context.Background())
	}
	exists, _ = e.Instance.Client.IndexExists(elasticx.IndexNameOfService()).Do(context.Background())
	if !exists {
		e.Instance.Client.CreateIndex(elasticx.IndexNameOfService()).BodyString(fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`, backup_restore.ServiceESSettings, backup_restore.ServiceESMappings)).Do(context.Background())
	}

	exists, _ = e.Instance.Client.IndexExists(elasticx.IndexNameOfTaskAsset()).Do(context.Background())
	if !exists {
		e.Instance.Client.CreateIndex(elasticx.IndexNameOfTaskAsset()).BodyString(fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`, backup_restore.TaskAssetSettings, backup_restore.TaskAssetMappings)).Do(context.Background())
	}
	exists, _ = e.Instance.Client.IndexExists(elasticx.IndexNameOfAsset()).Do(context.Background())
	if !exists {
		e.Instance.Client.CreateIndex(elasticx.IndexNameOfAsset()).BodyString(fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`, backup_restore.AssetSettings, backup_restore.AssetMappings)).Do(context.Background())
	}
	exists, _ = e.Instance.Client.IndexExists(elasticx.IndexNameOfThreat()).Do(context.Background())
	if !exists {
		e.Instance.Client.CreateIndex(elasticx.IndexNameOfThreat()).BodyString(fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`, backup_restore.ThreatESSettings, backup_restore.ThreatESMappings)).Do(context.Background())
	}
	exists, _ = e.Instance.Client.IndexExists(elasticx.IndexNameOfViolation()).Do(context.Background())
	if !exists {
		e.Instance.Client.CreateIndex(elasticx.IndexNameOfViolation()).BodyString(fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`, backup_restore.ViolationSettings, backup_restore.ViolationMappings)).Do(context.Background())
	}
	exists, _ = e.Instance.Client.IndexExists(elasticx.IndexNameOfNetinfo()).Do(context.Background())
	if !exists {
		e.Instance.Client.CreateIndex(elasticx.IndexNameOfNetinfo()).BodyString(fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`, backup_restore.NetinfoSettings, backup_restore.NetinfoMappings)).Do(context.Background())
	}
	exists, _ = e.Instance.Client.IndexExists(elasticx.IndexNameOfSiteurl()).Do(context.Background())
	if !exists {
		e.Instance.Client.CreateIndex(elasticx.IndexNameOfSiteurl()).BodyString(fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`, backup_restore.SiteUrlSettings, backup_restore.SiteUrlMappings)).Do(context.Background())
	}

	exists, _ = e.Instance.Client.IndexExists(elasticx.IndexNameOfIndexTag()).Do(context.Background())
	if !exists {
		e.Instance.Client.CreateIndex(elasticx.IndexNameOfIndexTag()).Do(context.Background())
	}
}

func (e *ElasticBackupRestoreDatabase) CreateTemplates() {
	e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfService()).BodyString(fmt.Sprintf(`{ "index_patterns": %s, "settings": %s, "mappings": %s }`, "fofaee_service*", backup_restore.ServiceESSettings, backup_restore.ServiceESMappings)).Do(context.Background())
	e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfSubDomain()).BodyString(fmt.Sprintf(`{ "index_patterns": %s, "settings": %s, "mappings": %s }`, "fofaee_subdomain*", backup_restore.SubdomainSettings, backup_restore.SubdomainMappings)).Do(context.Background())
	e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfAsset()).BodyString(fmt.Sprintf(`{ "index_patterns": %s, "settings": %s, "mappings": %s }`, "fofaee_assets*", backup_restore.AssetSettings, backup_restore.AssetMappings)).Do(context.Background())
	e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfTaskAsset()).BodyString(fmt.Sprintf(`{ "index_patterns": %s, "settings": %s, "mappings": %s }`, "fofaee_task_assets*", backup_restore.TaskAssetSettings, backup_restore.TaskAssetMappings)).Do(context.Background())
	e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfViolation()).BodyString(fmt.Sprintf(`{ "index_patterns": %s, "settings": %s, "mappings": %s }`, "fofaee_violations*", backup_restore.ViolationSettings, backup_restore.ViolationMappings)).Do(context.Background())
}

func (e *ElasticBackupRestoreDatabase) DeleteIndexOrCreate(ctx context.Context, baseDir, index string) error {
	if _, err := e.Instance.Client.DeleteIndex(index).Do(ctx); err != nil {
		return fmt.Errorf("delete index failed, err: %v", err)
	}

	if index == elasticx.IndexNameOfIndexTag() {
		if _, err := e.Instance.Client.CreateIndex(elasticx.IndexNameOfIndexTag()).
			Do(ctx); err != nil {
			return fmt.Errorf("create index failed, err: %v", err)
		}
	} else if index == ".tasks" || !strings.HasPrefix(index, "fofaee_") {
		return nil
	} else {
		if _, err := e.Instance.Client.CreateIndex(index).
			BodyString(fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`,
				jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(baseDir, index)),
				jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(baseDir, index)))).
			Do(ctx); err != nil {
			return fmt.Errorf("create index failed, err: %v", err)
		}
	}

	switch index {
	case elasticx.IndexNameOfService():
		if _, err := e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfService()).
			BodyString(fmt.Sprintf(`{ "index_patterns": ["%s*"], "settings": %s, "mappings": %s }`,
				elasticx.IndexNameOfService(), backup_restore.ServiceESSettings, backup_restore.ServiceESMappings)).
			Do(context.Background()); err != nil {
			return fmt.Errorf("create template %s failed, err: %v", elasticx.IndexNameOfService(), err)
		}

	case elasticx.IndexNameOfSubDomain():
		if _, err := e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfSubDomain()).
			BodyString(fmt.Sprintf(`{ "index_patterns": ["%s*"], "settings": %s, "mappings": %s }`,
				elasticx.IndexNameOfSubDomain(), backup_restore.SubdomainSettings, backup_restore.SubdomainMappings)).
			Do(context.Background()); err != nil {
			return fmt.Errorf("create template %s failed, err: %v", elasticx.IndexNameOfSubDomain(), err)
		}

	case elasticx.IndexNameOfAsset():
		if _, err := e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfAsset()).
			BodyString(fmt.Sprintf(`{ "index_patterns": ["%s*"], "settings": %s, "mappings": %s }`,
				elasticx.IndexNameOfAsset(), backup_restore.AssetSettings, backup_restore.AssetMappings)).
			Do(context.Background()); err != nil {
			return fmt.Errorf("create template %s failed, err: %v", elasticx.IndexNameOfAsset(), err)
		}

	case elasticx.IndexNameOfTaskAsset():
		if _, err := e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfTaskAsset()).
			BodyString(fmt.Sprintf(`{ "index_patterns": ["%s*"], "settings": %s, "mappings": %s }`,
				elasticx.IndexNameOfTaskAsset(), backup_restore.TaskAssetSettings, backup_restore.TaskAssetMappings)).
			Do(context.Background()); err != nil {
			return fmt.Errorf("create template %s failed, err: %v", elasticx.IndexNameOfTaskAsset(), err)
		}

	case elasticx.IndexNameOfViolation():
		if _, err := e.Instance.Client.IndexPutTemplate(elasticx.IndexNameOfViolation()).
			BodyString(fmt.Sprintf(`{ "index_patterns": ["%s*"], "settings": %s, "mappings": %s }`,
				elasticx.IndexNameOfViolation(), backup_restore.ViolationSettings, backup_restore.ViolationMappings)).
			Do(context.Background()); err != nil {
			return fmt.Errorf("create template %s failed, err: %v", elasticx.IndexNameOfViolation(), err)
		}
	}

	return nil
}
