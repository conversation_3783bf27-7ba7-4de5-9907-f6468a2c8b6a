package es_backup_restore

import (
	"context"
	"testing"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/backup_restore"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_index"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"
)

func TestElasticBackupRestoreSuiteSuite(t *testing.T) {
	suite.Run(t, new(ElasticBackupRestoreSuite))
}

type ElasticBackupRestoreSuite struct {
	suite.Suite
	ctx             *gin.Context
	db              ElasticBackupRestoreDatabase
	configure       *config.Configure
	elasticSearch   *elasticx.ElasticDatabase
	afterDropTables bool
}

func (suite *ElasticBackupRestoreSuite) BeforeSuite() {
	gomonkey.ApplyFunc(elasticx.IndexNameOfAsset, func() string {
		return "fofaee_assets_backup_test"
	})
	assert.Equal(suite.T(), "fofaee_assets_backup_test", elasticx.IndexNameOfAsset())

	gomonkey.ApplyFunc(elasticx.IndexNameOfNetinfo, func() string {
		return "fofaee_netinfos_backup_test"
	})
	assert.Equal(suite.T(), "fofaee_netinfos_backup_test", elasticx.IndexNameOfNetinfo())

	gomonkey.ApplyFunc(elasticx.IndexNameOfService, func() string {
		return "fofaee_service_backup_test"
	})
	assert.Equal(suite.T(), "fofaee_service_backup_test", elasticx.IndexNameOfService())

	gomonkey.ApplyFunc(elasticx.IndexNameOfTaskAsset, func() string {
		return "fofaee_task_assets_backup_test"
	})
	assert.Equal(suite.T(), "fofaee_task_assets_backup_test", elasticx.IndexNameOfTaskAsset())

	gomonkey.ApplyFunc(elasticx.IndexNameOfSubDomain, func() string {
		return "fofaee_subdomain_backup_test"
	})
	assert.Equal(suite.T(), "fofaee_subdomain_backup_test", elasticx.IndexNameOfSubDomain())

	gomonkey.ApplyFunc(elasticx.IndexNameOfThreat, func() string {
		return "fofaee_threats_backup_test"
	})
	assert.Equal(suite.T(), "fofaee_threats_backup_test", elasticx.IndexNameOfThreat())

	gomonkey.ApplyFunc(elasticx.IndexNameOfViolation, func() string {
		return "fofaee_violations_backup_test"
	})
	assert.Equal(suite.T(), "fofaee_violations_backup_test", elasticx.IndexNameOfViolation())

	gomonkey.ApplyFunc(elasticx.IndexNameOfSiteurl, func() string {
		return "fofaee_siteurl_backup_test"
	})
	assert.Equal(suite.T(), "fofaee_siteurl_backup_test", elasticx.IndexNameOfSiteurl())

	gomonkey.ApplyGlobalVar(&es_asset.Indexes, map[string]string{
		"asset":     elasticx.IndexNameOfAsset(),
		"service":   elasticx.IndexNameOfService(),
		"subdomain": elasticx.IndexNameOfSubDomain(),
	})
}

func (suite *ElasticBackupRestoreSuite) SetupSuite() {
	suite.BeforeSuite()
	var err error
	backup_restore.BaseDir = "./../../../"
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../../../"),
	)
	assert.NotNil(suite.T(), suite.configure)

	elasticdatabase, err := elasticx.NewElasticDatabase(suite.configure)
	assert.NoError(suite.T(), err)
	suite.db.Instance = elasticdatabase

	suite.elasticSearch = elasticdatabase

	es_index.CreateIndex(elasticdatabase.Client)
	suite.afterDropTables = true
}

func (suite *ElasticBackupRestoreSuite) TearDownSuite() {
	if suite.afterDropTables {
		suite.elasticSearch.Client.DeleteIndex(
			elasticx.IndexNameOfAsset(),
			elasticx.IndexNameOfNetinfo(),
			elasticx.IndexNameOfService(),
			elasticx.IndexNameOfTaskAsset(),
			elasticx.IndexNameOfSubDomain(),
			elasticx.IndexNameOfThreat(),
			elasticx.IndexNameOfViolation(),
			elasticx.IndexNameOfSiteurl(),
		).Do(context.Background())
	}
}

func (suite *ElasticBackupRestoreSuite) Test_DeleteAllData() {
	_, err := suite.elasticSearch.Client.Index().Index(elasticx.IndexNameOfAsset()).Type("ips").BodyString(`{"ip":"***********"}`).Refresh("true").Do(context.Background())
	assert.NoError(suite.T(), err)
	n, err := suite.elasticSearch.Client.Count(elasticx.IndexNameOfAsset()).Do(context.Background())
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1, int(n))
	indices := []string{
		elasticx.IndexNameOfIndexTag(),
		elasticx.IndexNameOfAsset(),
		elasticx.IndexNameOfService(),
		elasticx.IndexNameOfSubDomain(),
		elasticx.IndexNameOfSiteurl(),
		elasticx.IndexNameOfTaskAsset(),
		elasticx.IndexNameOfThreat(),
		elasticx.IndexNameOfViolation(),
		elasticx.IndexNameOfNetinfo(),
		elasticx.IndexNameOfDomainAsset(),
		elasticx.IndexNameOfTaskDomainAsset(),
	}
	suite.db.DeleteAllData()
	isExist, err := suite.db.Instance.Client.IndexExists(indices...).Do(context.Background())
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), true, isExist)
	n, err = suite.elasticSearch.Client.Count(elasticx.IndexNameOfAsset()).Do(context.Background())
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 0, int(n))
}

func (suite *ElasticBackupRestoreSuite) Test_GetESScrollService() {
	res := suite.db.GetESScrollService(elasticx.IndexNameOfAsset())
	assert.NotNil(suite.T(), res)
}

func (suite *ElasticBackupRestoreSuite) Test_DeleteAllDataOfDCC() {
	suite.db.DeleteAllDataOfDCC(1)
}

func (suite *ElasticBackupRestoreSuite) Test_CreateIndex() {
	suite.db.CreateIndex()
}

func (suite *ElasticBackupRestoreSuite) Test_CreateTemplates() {
	suite.db.CreateTemplates()
}
