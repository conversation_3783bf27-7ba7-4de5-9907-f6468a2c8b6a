package es_asset_search

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http/httptest"
	"path/filepath"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/backup_restore"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/responses/r_asset_search"
	"git.gobies.org/foeye/foeye3/responses/r_common"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye-dependencies/fsfire"
)

func TestSearchSuite(t *testing.T) {
	suite.Run(t, new(SearchSuite))
}

type SearchSuite struct {
	suite.Suite
	baseDir         string
	testDataDir     string
	configure       *config.Configure
	elasticdatabase *elasticx.ElasticDatabase
	search          ESAssetSearchDatabaseInter
	searchDb        *ESAssetSearchDatabase
	afterDropTables bool
}

func (suite *SearchSuite) SetupSuite() {
	var err error
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../../../"),
	)
	assert.NotNil(suite.T(), suite.configure)

	suite.baseDir = "./../../../"
	backup_restore.BaseDir = "./../../../"
	backup_restore.InitVar()

	suite.testDataDir, err = fsfire.GetFilePathWithFileSystemPath(suite.baseDir, fsfire.WithSpecificFileSystemPath("test"))
	assert.NoError(suite.T(), err)

	suite.elasticdatabase, err = elasticx.NewElasticDatabase(suite.configure)
	assert.NoError(suite.T(), err)

	suite.searchDb = &ESAssetSearchDatabase{Instance: suite.elasticdatabase}
	suite.search = suite.searchDb
	suite.afterDropTables = true

	suite.createIndex()

}

func (suite *SearchSuite) createIndex() {
	tmpStr, err := ioutil.ReadFile(filepath.Join(suite.testDataDir, "data", "subdomain.json"))
	assert.NoError(suite.T(), err)
	suite.elasticdatabase.Client.CreateIndex(elasticx.IndexNameOfSubDomain()).BodyString(
		fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`,
			backup_restore.SubdomainSettings,
			backup_restore.SubdomainMappings)).Do(context.Background())
	suite.elasticdatabase.Client.IndexPutTemplate(elasticx.IndexNameOfSubDomain()).BodyString(
		fmt.Sprintf(`{ "index_patterns": %s, "settings": %s, "mappings": %s }`,
			"fofaee_subdomain*",
			backup_restore.SubdomainSettings,
			backup_restore.SubdomainMappings)).Do(context.Background())

	suite.elasticdatabase.Client.CreateIndex(elasticx.IndexNameOfService()).BodyString(
		fmt.Sprintf(`{"settings" : %s,"mappings" : %s}`,
			backup_restore.ServiceESSettings,
			backup_restore.ServiceESMappings)).Do(context.Background())
	suite.elasticdatabase.Client.IndexPutTemplate(elasticx.IndexNameOfService()).BodyString(
		fmt.Sprintf(`{ "index_patterns": %s, "settings": %s, "mappings": %s }`,
			"fofaee_service*",
			backup_restore.ServiceESSettings,
			backup_restore.ServiceESMappings)).Do(context.Background())
	tmp := make([]map[string]interface{}, 0)
	err = json.Unmarshal(tmpStr, &tmp)
	assert.NoError(suite.T(), err)

	_, err = suite.elasticdatabase.Client.Index().Index(elasticx.IndexNameOfSubDomain()).Type("subdomain").BodyJson(tmp[0]).Do(context.Background())
	assert.NoError(suite.T(), err)
}

func (suite *SearchSuite) TearDownSuite() {
	if suite.afterDropTables {
		suite.elasticdatabase.Client.Delete().Index(elasticx.IndexNameOfSubDomain())
		suite.elasticdatabase.Client.Delete().Index(elasticx.IndexNameOfService())
	}
}

func (suite *SearchSuite) Test_GetClusterSearchList() {
	var q r_asset_search.QueryClusteringListAndKeyword
	q.QueryList = &r_common.QueryList{
		Number: 1,
		Size:   32,
		All:    false,
	}
	q.ClusteringType = "not_recorded"
	q.SortType = "asc"
	res, err := suite.search.GetClusterSearchList(q, []string{"612416514f0fe88ec3dc201b13a0a7b8"}, true, false)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res)
}

func (suite *SearchSuite) Test_GetClusterSearchListASCForUpdate() {
	var q r_asset_search.QueryClusteringListAndKeyword
	q.QueryList = &r_common.QueryList{
		Number: 1,
		Size:   32,
		All:    false,
	}
	q.ClusteringType = "not_recorded"
	q.SortType = "asc"
	q.SortField = "update_time"
	res, err := suite.search.GetClusterSearchList(q, []string{"612416514f0fe88ec3dc201b13a0a7b8"}, true, false)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res)
}

func (suite *SearchSuite) Test_GetClusterSearchListASCForAssetNum() {
	var q r_asset_search.QueryClusteringListAndKeyword
	q.QueryList = &r_common.QueryList{
		Number: 1,
		Size:   32,
		All:    false,
	}
	q.ClusteringType = "not_recorded"
	q.SortType = "asc"
	q.SortField = "asset_num"
	res, err := suite.search.GetClusterSearchList(q, []string{"612416514f0fe88ec3dc201b13a0a7b8"}, true, false)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res)
}

func (suite *SearchSuite) Test_GetAssetSearchStatistics() {
	rec := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(rec)
	var q r_asset_search.QueryListAndKeyword
	jsonData := `{
	"os_num": {
		"doc_count_error_upper_bound": 0,
		"sum_other_doc_count": 1,
		"buckets": [{
				"key": "ubuntu",
				"doc_count": 14
			},
			{
				"key": "windows 10, version 2004/windows server, version 2004",
				"doc_count": 7
			},
			{
				"key": "windows",
				"doc_count": 6
			},
			{
				"key": "windows server 2019, version 1809/windows 10, version 1809",
				"doc_count": 4
			},
			{
				"key": "windows 7, service pack 1/windows server 2008 r2, service pack 1",
				"doc_count": 3
			}
		]
	},
	"server_num": {
		"doc_count_error_upper_bound": 4,
		"sum_other_doc_count": 82,
		"buckets": [{
				"key": "",
				"doc_count": 157
			},
			{
				"key": "nginx",
				"doc_count": 97
			},
			{
				"key": "cloudflare",
				"doc_count": 11
			},
			{
				"key": "nginx/1.10.2",
				"doc_count": 10
			},
			{
				"key": "openresty/1.19.9.1",
				"doc_count": 8
			}
		]
	},
	"port_num": {
		"doc_count_error_upper_bound": 17,
		"sum_other_doc_count": 758,
		"buckets": [{
				"key": "80",
				"doc_count": 194
			},
			{
				"key": "443",
				"doc_count": 148
			},
			{
				"key": "22",
				"doc_count": 141
			},
			{
				"key": "111",
				"doc_count": 93
			},
			{
				"key": "8080",
				"doc_count": 92
			}
		]
	},
	"domain_num": {
		"doc_count_error_upper_bound": 5,
		"sum_other_doc_count": 364,
		"buckets": [{
			"key": "10.10.10.101",
			"doc_count": 1
		}]
	},
	"countries_num": {
		"doc_count_error_upper_bound": 0,
		"sum_other_doc_count": 0,
		"buckets": [{
				"key": "",
				"doc_count": 1387
			},
			{
				"key": "US",
				"doc_count": 19
			},
			{
				"key": "CN",
				"doc_count": 14
			},
			{
				"key": "JP",
				"doc_count": 4
			},
			{
				"key": "SG",
				"doc_count": 2
			}
		]
	},
	"companies_num": {
		"doc_count_error_upper_bound": 0,
		"sum_other_doc_count": 0,
		"buckets": []
	}
}`
	searchResult := elastic.SearchResult{}
	searchResult.Hits = &elastic.SearchHits{TotalHits: int64(0), Hits: nil}
	err := json.Unmarshal([]byte(jsonData), &searchResult.Aggregations)

	defer gomonkey.ApplyFuncReturn(getDataResult, &searchResult, nil).Reset()
	data, err := suite.search.GetAssetSearchStatistics(
		ctx,
		q,
		[]string{},
		true, true,
	)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}
