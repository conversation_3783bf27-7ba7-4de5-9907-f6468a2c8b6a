package es_asset_search

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/packages/stringsx"

	parsec "github.com/prataprc/goparsec"
)

func (fqp *FofaQueryParser) processNotEqToES6(left parsec.Queryable, right parsec.Queryable) string {
	if fqp.keyIsKeyword(left) {
		return `{
				"bool": { 
					"must_not": {
						"term": {
							"` + fqp.parseValue(left) + `": "` + fqp.parseValue(right) + `"
						}
					}
				}
			}`
	} else {
		return `{
				"bool": { 
					"must_not":{
						"match_phrase": {
							"` + fqp.parseValue(left) + `": {
								"query" : "` + fqp.parseValue(right) + `"
							}
						}
					}
				}
			}`
	}

}

func (fqp *FofaQueryParser) processEqToES6(left parsec.Queryable, right parsec.Queryable) string {
	switch left.GetValue() {
	case "app":
		// log.Println("[WARNING] app is not supported right now!")
		queryStr := `{
			"bool": {
				"filter": {
					"match_phrase": {
						"product": { 
							"query": "` + fqp.parseValue(right) + `"
						}
					}
				}
			}
		}`

		log.Println("query_string", queryStr)
		return queryStr
		// log.Println("[WARNING] app is not supported right now!")
	case "after":
		return dateToQuery(right.GetValue(), "gte")
	case "before":
		return dateToQuery(right.GetValue(), "lte")
	default:
		return `{
			"bool": {
				"filter": {
					"match_phrase": {
						"` + fqp.getTranslateField(left.GetValue()) + `": { 
							"query": "` + fqp.parseValue(right) + `"
						}
					}
				}
			}
		}`
	}
	return ""
}

// dateToQuery("2019", "gte")
func dateToQuery(dateStr, oper string) string {
	dateStr = strings.Trim(dateStr, "\"")
	dateRegex := regexp.MustCompile(`\d{4}(?:\-\d{2}(?:\-\d{2})?)?`)
	if !dateRegex.MatchString(dateStr) {
		panic("error format of date query")
	}

	d := dateStr
	switch len(d) {
	case 4:
		d += "-01-01 00:00:00" // year
	case 7:
		d += "-01 00:00:00" // year-month
	case 10:
		d += " 00:00:00" // year-month-day
	}

	return `{
				"bool": {
					"filter": {
						"range": {
							"lastupdatetime": {
								"` + oper + `": "` + d + `" 
							}
						}
					}
				}
			}`
}

func (fqp *FofaQueryParser) processFullEqToES6(left parsec.Queryable, right parsec.Queryable) string {
	return `
{
	"bool": {
		"filter": {
			"term": {
				"` + fqp.getTranslateField(fqp.getRawField(left)) + `": "` + fqp.parseValue(right) + `"
			}
		}
	}
}
`
}

func (fqp *FofaQueryParser) processMatchesToES6(left parsec.Queryable, right parsec.Queryable) string {
	switch right.GetName() {
	case "DOUBLEQUOTESTRING":
		return left.GetValue() + " REGEXP '" + right.GetValue()[1:len(right.GetValue())-1] + "'"
	case "literal":
		return left.GetValue() + " REGEXP '" + right.GetValue() + "'"
	default:
		log.Println("[WARNING]", fmt.Sprintf("%s is not valid for regex match", right.GetName()))
	}
	return left.GetValue() + "=\"" + right.GetValue() + "\""
}

func (fqp *FofaQueryParser) processMathToES6(left parsec.Queryable, right parsec.Queryable, oper string) string {
	return left.GetValue() + oper + right.GetValue()
}

// 每一个都分成三部分，left，operation，right
func (fqp *FofaQueryParser) parseAstToES6(q []parsec.Queryable) (ret string) {
	left, operation, right := parseLOR(q)

	for _, node := range q {
		if node.GetPosition() > fqp.position {
			fqp.position = node.GetPosition()
			fqp.positionResult = node.GetValue()
		}
		switch node.GetName() {
		case "missing":
			continue
		case "left_parenthesis":
			// ret += "("
		case "right_parenthesis":
			// ret += ")"
		case "parenthesis":
			ret += fqp.parseAstToES6(node.GetChildren())
		case "compare_eq", "compare_not_eq", "compare_matches", "compare_fulleq",
			"compare_lt", "compare_lteq", "compare_gt", "compare_gteq":
			ret += fqp.parseAstToES6(node.GetChildren())
		case "eq":
			if fqp.keyIsKeyword(left) {
				ret += fqp.processFullEqToES6(left, right)
			} else {
				ret += fqp.processEqToES6(left, right)
			}
		case "not_eq":
			ret += fqp.processNotEqToES6(left, right)
		case "matches":
			ret += fqp.processMatchesToES6(left, right)
		case "fulleq":
			ret += fqp.processFullEqToES6(left, right)
		case "lt":
			ret += fqp.processMathToES6(left, right, "<")
		case "lteq":
			ret += fqp.processMathToES6(left, right, "<=")
		case "gt":
			ret += fqp.processMathToES6(left, right, ">")
		case "gteq":
			ret += fqp.processMathToES6(left, right, ">=")
		case "and_operation_item":
			ret += fqp.parseAstToES6(operation.GetChildren())
		case "and_operator":
			// ret += " and "
			return `
			{
				"bool": { 
					"must": [
						` + fqp.parseAstToES6(left.GetChildren()) + `,
						` + fqp.parseAstToES6(right.GetChildren()) + `
					]
				}
			}`
			break
		case "or_operation_item":
			ret += fqp.parseAstToES6(operation.GetChildren())
		case "or_operator":
			return `
			{
				"bool": { 
					"should":[
						` + fqp.parseAstToES6(left.GetChildren()) + `,
						` + fqp.parseAstToES6(right.GetChildren()) + `
					]
				}
			}`
		case "literal":
			if !stringsx.IsContains(node.GetValue(), fqp.keywords) && fqp.result == "" {
				fqp.result = fmt.Sprintf("规则内容里不能包含未知字段: %s", node.GetValue())
			}
			if node == left && right == nil && operation == nil {
				// 模糊查询
				ret += `{
          "bool" : {
            "should": [
              {"match_phrase": {"body": {"query": "` + node.GetValue() + `"}}},
              {"match_phrase": {"appserver": {"query": "` + node.GetValue() + `"}}},
              {"match_phrase": {"cert": {"query": "` + node.GetValue() + `"}}},
              {"match_phrase": {"domain": {"query": "` + node.GetValue() + `"}}},
              {"match_phrase": {"server": {"query": "` + node.GetValue() + `"}}},
              {"match_phrase": {"title": {"query": "` + node.GetValue() + `"}}},
              {"match_phrase": {"banner": {"query": "` + node.GetValue() + `"}}},
              {"match_phrase": {"header": {"query": "` + node.GetValue() + `"}}},
              {"match_phrase": {"host": {"query": "` + node.GetValue() + `"}}}
            ]
          }
        }`
			}
		case "number", "DOUBLEQUOTESTRING", "null", "true", "false":
		default:
			if !stringsx.IsContains(node.GetValue(), fqp.keywords) && fqp.result == "" {
				fqp.result = fmt.Sprintf("规则内容里不能包含未知字段: %s", node.GetValue())
			}
			log.Println("[WARNING]", fmt.Sprintf("Unknown operation of %s", node.GetName()))
		}
	}

	// if ret == "" {
	//	ret = `{"query_string": {"query": "*"}}`
	// }

	return
}

func (fqp *FofaQueryParser) ParseToES6(query string) (string, error) {
	r := fqp.parse(query)
	if r == nil {
		return "", errors.New("数据解析错误")
	}
	log.Println("children", r.GetChildren(), r.GetName(), r.GetAttributes())
	nodes := r.GetChildren()
	return fqp.parseAstToES6(nodes), nil
}

// FofaProgramCheck 检查fofa语法
func (fqp *FofaQueryParser) FofaProgramCheck(fofaQuery string) string {
	defer func() {
		// 清空状态
		fqp.result = ""
		fqp.positionResult = ""
		fqp.position = 0
	}()
	res, err := fqp.ParseToES6(fofaQuery)
	if err != nil {
		return "规则内容语法错误"
	}

	tmp := make(map[string]interface{})
	err = json.Unmarshal([]byte(res), &tmp)
	if err != nil {
		return "规则内容语法错误"
	}
	q, _ := fqp.AST.Parsewith(fqp.RootParser, parsec.NewScanner([]byte(fofaQuery)).TrackLineno())
	if q == nil {
		return "规则内容语法错误"
	}
	fqp.parseAstToES6(q.GetChildren())
	if fqp.position+len(fqp.positionResult) != len(fofaQuery) {
		return "规则内容语法错误"
	}
	return fqp.result
}
