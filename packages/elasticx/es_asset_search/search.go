package es_asset_search

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"sort"
	"sync"

	"git.gobies.org/fofa-backend/fofacore"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_asset_search"
	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/responses/r_asset_search"
)

var (
	defaultSource = []string{
		"ip",
		"domain",
		"asn",
		"subdomain",
		"isdomain",
		"host",
		"body",
		"title",
		"banner",
		"header",
		"lastchecktime",
		"port",
		"product",
		"protocol",
		"server",
		"cert",
		"certs",
		"geoip",
		"is_honeypot",
		"is_fraud",
		"is_xc",
		"rule_tags",
	}
)

// ESAssetSearchDatabaseInter interface
type ESAssetSearchDatabaseInter interface {
	GetAssetSearchList(ctx *gin.Context, q r_asset_search.QueryListAndKeyword, state bool) (int64, int64, []*m_asset_search.AssetSearch, error)
	GetAssetSearchStatistics(ctx *gin.Context, q r_asset_search.QueryListAndKeyword, fids []string, isFeatureEngine, state bool) (interface{}, error)
	GetAssetSearchTotalData(ctx *gin.Context) (*m_asset_search.TotalData, error)
	GetClusterSearchList(q r_asset_search.QueryClusteringListAndKeyword, fids []string, state, isXc bool) (*m_asset_search.ClusteringSearch, error)
	GetAssetSearchListAll(q r_asset_search.QueryListAndKeyword, state bool) ([]*m_asset_search.AssetSearch, error)
	GetAssetSearchIconList(queryBase64 string, state bool) ([]*m_asset_search.Icon, error)
	GetInvalidFids(ctx *gin.Context) ([]*m_asset_search.FidGroup, error)
}

// ESAssetSearchDatabase operation instance of module.
type ESAssetSearchDatabase struct {
	Instance *elasticx.ElasticDatabase
}

// GetAssetSearchTotalData ...
func (search *ESAssetSearchDatabase) GetAssetSearchTotalData(ctx *gin.Context) (*m_asset_search.TotalData, error) {
	domainAggregation := elastic.NewTermsAggregation().Field("domain").Size(1000000)

	searchResult, err := search.Instance.Client.Search().
		Index(elasticx.IndexNameOfSubDomain()).
		Size(0).
		Aggregation("domain_num", domainAggregation).
		Pretty(true).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	totalData := &m_asset_search.TotalData{}
	for _, rawMessage := range searchResult.Aggregations {
		domains := m_asset.UnmarshalJsonRawMessage(*rawMessage)
		count := es_asset.StatisticsTotal(domains)
		totalData.Domain = count
	}

	protocolsAggregation := elastic.NewTermsAggregation().Field("protocol").Size(1000000)
	result, err := search.Instance.Client.Search().
		Index(elasticx.IndexNameOfService()).
		Size(0).
		Aggregation("protocols_num", protocolsAggregation).
		Pretty(true).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	for key, rawMessage := range result.Aggregations {
		switch key {
		case "protocols_num":
			protocols := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			count := es_asset.Statistics(protocols)
			totalData.Protocols = count
		default:
		}
	}

	return totalData, nil
}

// GetAssetSearchStatistics 资产空间左侧统计
func (search *ESAssetSearchDatabase) GetAssetSearchStatistics(
	ctx *gin.Context,
	q r_asset_search.QueryListAndKeyword,
	fids []string,
	isFeatureEngine, state bool) (interface{}, error) {
	searchResult, err := getDataResult(ctx, search, q, state)
	if err != nil {
		return nil, err
	}
	map2 := make(map[string]interface{})
	if len(searchResult.Hits.Hits) > 0 {
		err = json.Unmarshal(*searchResult.Hits.Hits[0].Source, &map2)
		if err != nil {
			return nil, err
		}
	}
	took := searchResult.TookInMillis
	// res := make([]map[string]interface{}, 0)
	res := make(map[int]map[string]interface{}, 0)

	res = search.SetSearchResultItem(searchResult.Aggregations, res)

	searchResult1, err := getDataResult1(ctx, search, q, state)
	if err != nil {
		return nil, err
	}
	took1 := searchResult.TookInMillis
	for key, rawMessage := range searchResult1.Aggregations {
		item := make(map[string]interface{})
		switch key {
		case "protocols_num":
			protocols := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			item["doc_count"] = es_asset.Statistics(protocols)
			item["title"] = "协议"
			item["key"] = "service"
			res[7] = item
		default:
		}
	}
	moduleNum := 10
	if isFeatureEngine {
		moduleNum++
		searchResultFid, err := getDataResultFid(ctx, search, q, fids, state)
		if err != nil {
			return nil, err
		}
		for key, rawMessage := range searchResultFid.Aggregations {
			item := make(map[string]interface{})
			switch key {
			case "fid_num":
				fidList := m_asset.UnmarshalJsonRawMessage(*rawMessage)
				item["title"] = "资产特征聚类"
				item["key"] = "fid"
				if len(fidList) >= 5 {
					fidList = fidList[0:5]
				}
				item["children"] = fidList
				res[10] = item
			default:
			}
		}
	}

	if took < took1 {
		took = took1
	}
	res[8] = map[string]interface{}{
		"key":       "took",
		"doc_count": took,
	}
	if len(map2) == 0 {
		map2["lastchecktime"] = "no datas"
	}
	res[9] = map[string]interface{}{
		"key":           "lastchecktime",
		"lastchecktime": map2["lastchecktime"],
	}
	// 将map转换成切片
	resData := make([]map[string]interface{}, 0)
	for i := 0; i < moduleNum; i++ {
		if len(res[i]) != 0 {
			resData = append(resData, res[i])
		}
	}
	return resData, nil
}

func (search *ESAssetSearchDatabase) SetSearchResultItem(searchResult elastic.Aggregations, res map[int]map[string]interface{}) map[int]map[string]interface{} {
	for key, rawMessage := range searchResult {
		item := make(map[string]interface{})
		switch key {
		case "domain_num":
			count, err := search.Instance.Client.Count(elasticx.IndexNameOfSubDomain()).Do(context.TODO())
			if err != nil {
				count = 0
			}
			item["doc_count"] = count
			item["title"] = "网站"
			item["key"] = "subdomain"
			// res = append(res, item)
			res[0] = item
		case "companies_num":
			companies := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			item["title"] = "公司排名"
			item["key"] = "company"
			item["children"] = companies
			res[1] = item
		case "server_num":
			server := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			item["title"] = "Server排名"
			item["key"] = "server"
			item["children"] = server
			res[2] = item
		case "countries_num":
			countries := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			item["title"] = "国家排名"
			// 处理国家
			chinaNum := 0
			var countryData = make([]interface{}, 0)
			for _, v := range countries {
				if v.Key == "TW" || v.Key == "HK" || v.Key == "MO" || v.Key == "CN" {
					chinaNum += v.Count
					continue
				}
				var con = make(map[string]interface{})
				con["key"] = v.Key
				con["doc_count"] = v.Count
				con["title"] = v.Key
				if len(v.Key) > 0 {
					if val, ok := CountriesTranslate[v.Key]; ok {
						con["title"] = val
					}
				}
				countryData = append(countryData, con)
			}
			if chinaNum > 0 {
				var con = make(map[string]interface{})
				con["key"] = "CN"
				con["doc_count"] = chinaNum
				con["title"] = "CN"
				con["title"] = CountriesTranslate["CN"]
				countryData = append(countryData, con)
			}
			sort.Slice(countryData, func(i, j int) bool {
				docCountI := countryData[i].(map[string]interface{})["doc_count"].(int)
				docCountJ := countryData[j].(map[string]interface{})["doc_count"].(int)
				return docCountI > docCountJ // 降序排序
			})
			item["key"] = "country"
			item["children"] = countryData
			if len(countryData) >= 5 {
				item["children"] = countryData[:5]
			}
			res[3] = item
		case "port_num":
			port := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			item["title"] = "端口排名"
			item["key"] = "port"
			item["children"] = port
			res[4] = item
		case "protocol_num":
			protocol := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			item["title"] = "协议排名"
			item["key"] = "protocol"
			item["children"] = protocol
			res[5] = item
		case "os_num":
			os := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			item["title"] = "操作系统排名"
			item["key"] = "os"
			item["children"] = os
			res[6] = item
		default:
		}
	}
	return res
}

// GetAssetSearchList ...
func (search *ESAssetSearchDatabase) GetAssetSearchList(ctx *gin.Context, q r_asset_search.QueryListAndKeyword, state bool) (int64, int64, []*m_asset_search.AssetSearch, error) {
	searchService := search.Instance.Client.Search().
		Index(elasticx.IndexNameOfService()).
		Index(elasticx.IndexNameOfSubDomain())
	var num, size int = 0, q.Size
	// 处理分页数据
	if q.Number == 0 {
		size = 1000
	} else {
		num = q.Number - 1
		num = num * q.Size
	}
	if q.SearchType == "advanced" {
		query := elastic.NewBoolQuery()
		for key, val := range q.Asset {
			if val == "" {
				continue
			}
			query.Must(elastic.NewTermQuery(key, val))
		}
		searchService.Query(query)
	} else if q.KeywordBase64 == "" {
		query := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())
		source := elastic.NewSearchSource().FetchSourceIncludeExclude(defaultSource, nil)
		if state {
			query.MustNot(elastic.NewTermQuery("is_honeypot", true))
			query.Must(elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("is_fraud")),
				elastic.NewTermQuery("is_fraud", false)))
		}
		searchService.
			SearchSource(source).
			Query(query)
	} else {
		// base64解码
		keyword, err := base64.StdEncoding.DecodeString(q.KeywordBase64)
		// url解码
		enEscapeUrl, _ := url.QueryUnescape(string(keyword))
		if err != nil {
			return 0, 0, nil, err
		}
		qi := fofacore.QueryInput{
			Query: enEscapeUrl,
			Full:  true,
			Fraud: !state,
		}
		out, err := fofacore.ParseFEQuery(&qi)
		if err != nil {
			return 0, 0, nil, err
		}

		qr, ok := out.Query["query"]
		if !ok {
			return 0, 0, nil, errors.New("query is nil")
		}

		body, err := json.MarshalIndent(qr, "", "  ")
		if err != nil {
			return 0, 0, nil, err
		}
		fmt.Println("convert fofaquery:", string(body))
		fetchSourceContext := elastic.NewFetchSourceContext(true).Include(defaultSource...)
		searchService.Query(elastic.NewRawStringQuery(string(body))).FetchSourceContext(fetchSourceContext)
	}

	searchResult, err := searchService. // specify the query
		Sort("lastchecktime", false).
		From(num).Size(size). // take documents 0-9
		Pretty(true). // pretty print request and response JSON
		Do(ctx) // execute

	if err != nil {
		return 0, 0, nil, err
	}
	total := searchResult.TotalHits()
	if total == 0 {
		return searchResult.TookInMillis, 0, nil, nil
	}

	res := make([]*m_asset_search.AssetSearch, 0)
	for _, hit := range searchResult.Hits.Hits {
		assetSearch := new(m_asset_search.AssetSearch)
		err = json.Unmarshal(*hit.Source, &assetSearch)
		if err != nil {
			return 0, 0, nil, err
		}
		for _, rule := range assetSearch.RuleTags {
			if rule.IsXc == "1" {
				assetSearch.IsXc = m_task.IsXcType
			}
		}
		res = append(res, assetSearch)
	}

	return searchResult.TookInMillis, searchResult.TotalHits(), res, nil
}

// GetAssetSearchListAll 获取全部资产空间搜索的数据
func (search *ESAssetSearchDatabase) GetAssetSearchListAll(q r_asset_search.QueryListAndKeyword, state bool) ([]*m_asset_search.AssetSearch, error) {
	data := make([]*m_asset_search.AssetSearch, 0)

	searchService := search.Instance.Client.Scroll().
		Index(elasticx.IndexNameOfService()).
		Index(elasticx.IndexNameOfSubDomain())

	if q.SearchType == "advanced" {
		query := elastic.NewBoolQuery()
		for key, val := range q.Asset {
			if val == "" {
				continue
			}
			query.Must(elastic.NewTermQuery(key, val))
		}
		searchService.Query(query)
	} else if q.KeywordBase64 == "" {
		query := elastic.NewMatchAllQuery()
		source := elastic.NewSearchSource().FetchSourceIncludeExclude(defaultSource, nil)
		searchService.
			SearchSource(source).
			Query(query)
	} else {
		// base64解码
		keyword, err := base64.StdEncoding.DecodeString(q.KeywordBase64)
		// url解码
		enEscapeUrl, _ := url.QueryUnescape(string(keyword))
		if err != nil {
			return data, err
		}

		qi := fofacore.QueryInput{
			Query: enEscapeUrl,
			Full:  true,
			Fraud: !state,
		}
		out, err := fofacore.ParseFEQuery(&qi)
		if err != nil {
			return data, err
		}

		qr, ok := out.Query["query"]
		if !ok {
			return data, errors.New("query is nil")
		}

		body, err := json.MarshalIndent(qr, "", "  ")
		if err != nil {
			return data, err
		}

		fetchSourceContext := elastic.NewFetchSourceContext(true).Include(defaultSource...)
		searchService.Query(elastic.NewRawStringQuery(string(body))).FetchSourceContext(fetchSourceContext)
	}
	searchService. // specify the query
		Sort("lastchecktime", false).
		Size(10000).
		Pretty(true)
	//20240526封包改为上限1w条，如需全部导出，考虑后续做异步
	//for {
	sr, err := searchService.Do(context.Background())
	//if err == io.EOF {
	//	break
	//}
	if err != nil {
		return data, err
	}

	for _, v := range sr.Hits.Hits {
		assetSearch := new(m_asset_search.AssetSearch)
		err := json.Unmarshal(*v.Source, &assetSearch)
		if err != nil {
			return data, err
		}
		data = append(data, assetSearch)
	}
	//}
	return data, nil
}

// GetClusterSearchList 获取资产特征库数据
func (search *ESAssetSearchDatabase) GetClusterSearchList(q r_asset_search.QueryClusteringListAndKeyword, fids []string, state, isXc bool) (*m_asset_search.ClusteringSearch, error) {
	searchService := search.Instance.Client.Search().
		Index(elasticx.IndexNameOfService()).
		Index(elasticx.IndexNameOfSubDomain())

	data := new(m_asset_search.ClusteringSearch)
	fidTmp := make([]interface{}, len(fids))
	for i, v := range fids {
		fidTmp[i] = v
	}

	query := elastic.NewBoolQuery()
	if q.ClusteringType == "not_recorded" {
		query.MustNot(elastic.NewTermsQuery("dom.foid.raw", fidTmp...))
	} else {
		query.Must(elastic.NewTermsQuery("dom.foid.raw", fidTmp...))
	}
	if state {
		query.MustNot(elastic.NewTermQuery("is_honeypot", true))
		query.Must(elastic.NewBoolQuery().Should(
			elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("is_fraud")),
			elastic.NewTermQuery("is_fraud", false),
		))
	}

	lastchecktimeOrder := false
	foidOrder := false
	if q.SortType == "asc" {
		switch q.SortField {
		case "update_time":
			lastchecktimeOrder = true
		case "asset_num":
			foidOrder = true
		}
	}

	foidAgg := elastic.NewTermsAggregation().Field("dom.foid.raw").Size(10000000)
	maxLastCheckTimeAgg := elastic.NewMaxAggregation().Field("lastchecktime")
	foidAgg.SubAggregation("max_lastchecktime", maxLastCheckTimeAgg)

	searchResult, err := searchService.Query(query).Size(0).Aggregation("foid", foidAgg).Pretty(true).Do(context.Background())
	if err != nil {
		return nil, err
	}

	agg, found := searchResult.Aggregations.Terms("foid")
	if !found {
		return nil, errors.New("没有找到聚合数据")
	}

	if foidOrder && q.SortField == "asset_num" {
		buckets := agg.Buckets
		sort.Slice(buckets, func(i, j int) bool {
			return buckets[i].DocCount < buckets[j].DocCount
		})
	}

	if q.SortField == "update_time" {
		bucketMaxCheckTimes := make(map[string]float64)
		for _, bucket := range agg.Buckets {
			maxLastCheckTimeAggResult, _ := bucket.Aggregations.Max("max_lastchecktime")
			if maxLastCheckTimeAggResult != nil {
				bucketMaxCheckTimes[bucket.Key.(string)] = *maxLastCheckTimeAggResult.Value
			}
		}

		buckets := agg.Buckets
		sort.Slice(buckets, func(i, j int) bool {
			key1 := buckets[i].Key.(string)
			key2 := buckets[j].Key.(string)

			maxCheckTime1, exists1 := bucketMaxCheckTimes[key1]
			maxCheckTime2, exists2 := bucketMaxCheckTimes[key2]

			if exists1 && exists2 {
				if lastchecktimeOrder {
					return maxCheckTime1 < maxCheckTime2
				}
				return maxCheckTime1 > maxCheckTime2
			}
			return false
		})
	}

	clusteringTmp := make(map[string]*m_asset_search.ClusteringSearchInfo)
	var assetsTotalNum int64
	fidList := make([]string, 0, len(agg.Buckets))
	for _, bucket := range agg.Buckets {
		assetsTotalNum += bucket.DocCount
		clusteringTmp[bucket.Key.(string)] = new(m_asset_search.ClusteringSearchInfo)
		clusteringTmp[bucket.Key.(string)].AssetsNum += int(bucket.DocCount)
		fidList = append(fidList, bucket.Key.(string))
	}

	end := q.Number * q.Size
	if end > len(fidList) {
		end = len(fidList)
	}
	fidList = fidList[(q.Number-1)*q.Size : end]

	var wg sync.WaitGroup
	var mu sync.Mutex
	errorsChan := make(chan error, len(fidList))

	if len(fidList) > 0 {
		for _, fidVul := range fidList {
			wg.Add(1)
			go GetFidChildren(fidVul, &wg, lastchecktimeOrder, errorsChan, mu, clusteringTmp, search)
		}
		wg.Wait()
	}

	close(errorsChan)
	for err := range errorsChan {
		return nil, err
	}

	data.AssetsTotalNum = int(assetsTotalNum)
	data.FeatureTotalNum = len(agg.Buckets) + int(agg.SumOfOtherDocCount)
	if q.ClusteringType == "recorded" {
		fidList = fids
	}

	zeroListData := make([]m_asset_search.ClusteringSearchInfo, 0)
	for _, fidTmp := range fidList {
		if v, exists := clusteringTmp[fidTmp]; exists {
			if v.Fid != "" {
				data.List = append(data.List, *v)
			}
		} else if q.ClusteringType == "recorded" {
			zeroListData = append(zeroListData, m_asset_search.ClusteringSearchInfo{
				Fid:        fidTmp,
				Foid:       fidTmp,
				Title:      "",
				Server:     "",
				AssetsNum:  0,
				UpdateTime: "",
				RuleInfo:   m_asset_search.ClusteringRuleInfo{},
				Children:   []m_asset_search.AssetSearch{},
			})
		}
	}
	data.List = append(data.List, zeroListData...)
	return data, nil
}

func GetFidChildren(fidVul string, wg *sync.WaitGroup, lastchecktimeOrder bool, errorsChan chan error, mu sync.Mutex, clusteringTmp map[string]*m_asset_search.ClusteringSearchInfo, search *ESAssetSearchDatabase) {
	defer wg.Done()
	query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("dom.foid.raw", fidVul))
	res, err := search.Instance.Client.Search().
		Index(elasticx.IndexNameOfService()).
		Index(elasticx.IndexNameOfSubDomain()).
		Query(query).
		Size(1).
		Sort("lastchecktime", lastchecktimeOrder).
		Pretty(true).
		Do(context.TODO())

	if err != nil {
		errorsChan <- err
		return
	}
	if res == nil || res.Hits == nil {
		errorsChan <- errors.New("expected results != nil; got nil")
		return
	}

	for _, hit := range res.Hits.Hits {
		tmp := new(m_asset_search.ClusteringEs)
		if err := json.Unmarshal(*hit.Source, tmp); err != nil {
			errorsChan <- err
			return
		}

		mu.Lock()
		if v, exists := clusteringTmp[tmp.Dom.Foid]; exists {
			if v.Foid == "" {
				v.Fid = tmp.Fid
				v.Server = tmp.Server
				v.Title = tmp.Title
				v.UpdateTime = tmp.LastCheckTime
				v.Foid = tmp.Dom.Foid
			}

			if tmp.Protocol == "https" && tmp.Host != "" {
				tmp.JumpLink = tmp.Host
			} else if tmp.Protocol == "http" && tmp.Host != "" {
				tmp.JumpLink = fmt.Sprintf("http://%s", tmp.Host)
			}

			v.Children = append(v.Children, tmp.AssetSearch)
		}
		mu.Unlock()
	}
}

// GetAssetSearchIconList 获取资产空间搜索中的icon信息
func (search *ESAssetSearchDatabase) GetAssetSearchIconList(queryBase64 string, state bool) ([]*m_asset_search.Icon, error) {
	data := make([]*m_asset_search.Icon, 0)

	searchService := search.Instance.Client.Search().
		Index(elasticx.IndexNameOfService()).
		Index(elasticx.IndexNameOfSubDomain())
	if queryBase64 == "" {
		query := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())
		source := elastic.NewSearchSource().FetchSourceIncludeExclude([]string{"favicon"}, nil)
		if state {
			query.MustNot(elastic.NewTermQuery("is_honeypot", true))
			query.Must(elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("is_fraud")),
				elastic.NewTermQuery("is_fraud", false)))
		}
		searchService.
			SearchSource(source).
			Query(query)
	} else {
		// base64解码
		keyword, err := base64.StdEncoding.DecodeString(queryBase64)
		if err != nil {
			return data, err
		}
		// url解码
		enEscapeUrl, err := url.QueryUnescape(string(keyword))
		if err != nil {
			return data, err
		}
		qi := fofacore.QueryInput{
			Query: enEscapeUrl,
			Full:  true,
			Fraud: !state,
		}
		out, err := fofacore.ParseFEQuery(&qi)
		if err != nil {
			return data, err
		}

		qr, ok := out.Query["query"]
		if !ok {
			return data, errors.New("query is nil")
		}

		body, err := json.MarshalIndent(qr, "", "  ")
		if err != nil {
			return data, err
		}
		searchService.
			Query(elastic.NewRawStringQuery(string(body)))
	}
	iconAgg := elastic.NewTermsAggregation().Field("favicon.hash").Size(50)

	fetchSearch := elastic.NewFetchSourceContext(true).Include("favicon")
	iconTop := elastic.NewTopHitsAggregation().FetchSourceContext(fetchSearch).Size(1)

	iconAgg.SubAggregation("icon_top", iconTop)
	searchResult, err := searchService.
		Size(0).
		Aggregation("icon", iconAgg).
		Pretty(true).
		Do(context.Background())
	if err != nil {
		return data, err
	}

	agg, find := searchResult.Aggregations.Terms("icon")
	if find {
		for _, bucket := range agg.Buckets {
			hash := bucket.KeyNumber.String()
			tops, find := bucket.TopHits("icon_top")
			if find {
				for _, hit := range tops.Hits.Hits {
					tmpTop := new(m_asset_search.EsIcon)
					err := json.Unmarshal(*hit.Source, &tmpTop)
					if err != nil {
						continue
					}
					data = append(data, &m_asset_search.Icon{
						Count:      bucket.DocCount,
						Hash:       hash,
						IconUrl:    tmpTop.Favicon.Url,
						IconBase64: tmpTop.Favicon.Base64,
					})
					break
				}
			}
		}
	}

	return data, nil
}
