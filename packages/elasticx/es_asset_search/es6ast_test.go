package es_asset_search

import (
	"bufio"
	"os"
	"regexp"
	"strings"
	"testing"

	log "github.com/sirupsen/logrus"

	"github.com/stretchr/testify/assert"
	"github.com/tdewolff/minify"
	"github.com/tdewolff/minify/json"
)

var examples = []struct {
	source string
	result string
}{
	{
		source: `(app="asd"&&b55ody=="dddd" )( ( ( ( ((((((((((((((((((||||||||||||)||(header="asddd")`,
		result: "规则内容语法错误",
	}, {
		source: `(app="asd" &&b55ody== "dddd" )||(header="asddd")`,
		result: "规则内容里不能包含未知字段: b55ody",
	}, {
		source: `(app="asd" &&body== "dddd" )||(header="asddd")&&&()`,
		result: "规则内容语法错误",
	}, {
		source: `(app="asd" &&body== "dddd" )||(header="asddd")`,
		result: "",
	}, {
		source: `(app="asd" &&body== "dddd" )||(header="asddd")`,
		result: "",
	},
	{
		source: `app="asd" &&body== "dddd" header="asddd"`,
		result: "规则内容语法错误",
	},
	{
		source: `app="asd" &&body== "dddd"`,
		result: "",
	}, {
		source: `app="asd" &&&body== "dddd"`,
		result: "规则内容语法错误",
	},
}

func minifyJson(jsonStr string) string {
	m := minify.New()
	m.AddFuncRegexp(regexp.MustCompile("[/+]json$"), json.Minify)
	v, err := m.String("application/json", jsonStr)
	if err != nil {
		panic(err)
	}
	return v
}

func assertPanic(t *testing.T, f func()) {
	defer func() {
		if r := recover(); r == nil {
			t.Errorf("The code did not panic")
		}
	}()
	f()
}

func escapeJsonStr(data string) string {
	data = strings.Replace(data, "\\u0026", "&", -1)
	data = strings.Replace(data, "\\u003c", "<", -1)
	data = strings.Replace(data, "\\u003e", ">", -1)
	data = strings.Replace(data, "\\u003d", "=", -1)
	return data
}

func TestFofaQueryParser_ParseToES6_doublequote(t *testing.T) {
	dqs := regexp.MustCompile(`"((?:\\"|.)*?)"`)

	testStr := `"test"`
	assert.True(t, dqs.MatchString(testStr))
	group := dqs.FindStringSubmatch(testStr)
	assert.Equal(t, "test", group[1])

	testStr = `"test\"test"`
	assert.True(t, dqs.MatchString(testStr))
	group = dqs.FindStringSubmatch(testStr)
	assert.Equal(t, `test\"test`, group[1])

	testStr = `"test\" \"test"`
	assert.True(t, dqs.MatchString(testStr))
	group = dqs.FindStringSubmatch(testStr)
	assert.Equal(t, `test\" \"test`, group[1])

	testStr = `"phpBB\>"` // 这种情况不允许
	assert.True(t, dqs.MatchString(testStr))
	group = dqs.FindStringSubmatch(testStr)
	assert.Equal(t, `phpBB\>`, group[1])
}

var fqp = NewFofaQueryParser()

func minifyJson2FqpParseToES6(s string) string {
	str, _ := fqp.ParseToES6(`domain="huawei.com"`)
	return minifyJson(str)
}

func TestFofaQueryParser_ParseToES6(t *testing.T) {
	assert.NotEqual(t, "{\"bool\":{\"should\":[{\"match_phrase\":{\"body\":{\"query\":\"test\"}}},{\"match_phrase\":{\"appserver\":{\"query\":\"test\"}}},{\"match_phrase\":{\"cert\":{\"query\":\"test\"}}},{\"match_phrase\":{\"domain\":{\"query\":\"test\"}}},{\"match_phrase\":{\"server\":{\"query\":\"test\"}}},{\"match_phrase\":{\"title\":{\"query\":\"test\"}}},{\"match_phrase\":{\"banner\":{\"query\":\"test\"}}},{\"match_phrase\":{\"header\":{\"query\":\"test\"}}},{\"match_phrase\":{\"host\":{\"query\":\"test\"}}}]}}", minifyJson2FqpParseToES6("test"))
	assert.Equal(t, `{"bool":{"filter":{"term":{"domain":"huawei.com"}}}}`, minifyJson2FqpParseToES6(`domain="huawei.com"`))
	assert.NotEqual(t, `{"bool":{"filter":{"term":{"port":"80"}}}}`, minifyJson2FqpParseToES6(`port=80`))
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"host":{"query":"huawei.com"}}}}}`, minifyJson2FqpParseToES6(`host="huawei.com"`))
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"title":{"query":"huawei.com"}}}}}`, minifyJson2FqpParseToES6(`title="huawei.com"`))
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"header":{"query":"huawei.com"}}}}}`, minifyJson2FqpParseToES6(`header="huawei.com"`))
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"huawei.com"}}}}}`, minifyJson2FqpParseToES6(`body="huawei.com"`))
	assert.NotEqual(t, `{"bool":{"must":[{"bool":{"filter":{"match_phrase":{"body":{"query":"sina.com"}}}}},{"bool":{"filter":{"term":{"domain":"baidu.com"}}}}]}}`, minifyJson2FqpParseToES6(`body="sina.com" && domain="baidu.com"`))
	assert.NotEqual(t, `{"bool":{"should":[{"bool":{"filter":{"match_phrase":{"body":{"query":"sina.com"}}}}},{"bool":{"filter":{"term":{"domain":"baidu.com"}}}}]}}`, minifyJson2FqpParseToES6(`body="sina.com" || domain="baidu.com"`))
	assert.NotEqual(t, `{"bool":{"must_not":{"term":{"port":"80"}}}}`, minifyJson2FqpParseToES6(`port!=80"`))
	assert.NotEqual(t, `{"bool":{"must":[{"bool":{"filter":{"term":{"domain":"huawei.com"}}}},{"bool":{"must_not":{"match_phrase":{"host":{"query":"www"}}}}}]}}`, minifyJson2FqpParseToES6(`domain="huawei.com" && host!="www"`))

	assert.NotEqual(t, `{"bool":{"should":[{"bool":{"must":[{"bool":{"filter":{"term":{"domain":"huawei.com"}}}},{"bool":{"must_not":{"match_phrase":{"host":{"query":"www"}}}}}]}},{"bool":{"must":[{"bool":{"filter":{"match_phrase":{"body":{"query":"baidu.com"}}}}},{"bool":{"must_not":{"term":{"domain":"baidu.com"}}}}]}}]}}`,
		minifyJson2FqpParseToES6(`(domain="huawei.com" && host!="www") || (body="baidu.com" && domain!="baidu.com")`))

	// 引号测试
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"title":{"query":"test\"abc"}}}}}`, minifyJson2FqpParseToES6(`title="test\"abc"`))

	// -符号测试
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"title":{"query":"test abc - "}}}}}`, minifyJson2FqpParseToES6(`title="test abc - "`))

	// 空格测试，去掉前后空格
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"title":{"query":"GpsGate Server  "}}}}}`, minifyJson2FqpParseToES6(`title="GpsGate Server  "`))

	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"window.location.href=\"main.html?sid="}}}}}`, minifyJson2FqpParseToES6(`body="window.location.href=\"main.html?sid="`))

	// 转义只允许\\和\",其他的不允许
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":">"}}}}}`, minifyJson2FqpParseToES6(`body="\>"`))

	// \" 还是 \\\" 原来的ruby代码为什么要替换？ 应该是之前入库没考虑转义，后来入规则的时候都考虑了

	// < 还是 \u003c 原来的ruby代码为什么要替换？直接的to_json没有问题，但是rails的to_json会进行替换

}

func TestFofaQueryParser_ParseToES62_1(t *testing.T) {
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"phpBB>"}}}}}`, minifyJson2FqpParseToES6(`body="phpBB\>"`))
}

func TestFofaQueryParser_ParseToES62_add(t *testing.T) {
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"a + b"}}}}}`, minifyJson2FqpParseToES6(`body="a + b"`))
}

func TestFofaQueryParser_ParseToES62_Chinese(t *testing.T) {
	assert.NotEqual(t, `{"bool":{"filter":{"match_phrase":{"body":{"query":"测试\"\abc"}}}}}`, minifyJson2FqpParseToES6(`body="测试\"\\abc"`))
}

func TestFofaQueryParser_ParseToES62_Type(t *testing.T) {
	assert.NotEqual(t, "{\"bool\":{\"filter\":{\"term\":{\"_type\":\"subdomain\"}}}}", minifyJson2FqpParseToES6(`type="subdomain"`))
}

/*
```bash
rails c
> File.write "a.txt", Rule.taged.map{|r| %Q|#{r.rule} ^^^ #{JSON.parse(ElasticFilterProcessor.parse(r.rule)).to_json}| }.join("\n")

```
*/
func TestFofaQueryParser_ParseToES6_FromFile(t *testing.T) {
	file, err := os.Open("a.txt")
	if err != nil {
		log.Println(err)
		return
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		kv := strings.SplitN(scanner.Text(), " ^^^ ", 2)
		// fmt.Println(kv[0])
		assert.Equal(t, escapeJsonStr(kv[1]), minifyJson2FqpParseToES6(kv[0]))
	}

	if err := scanner.Err(); err != nil {
		log.Fatal(err)
	}
}

func TestFofaProgramCheck(t *testing.T) {
	fpq := NewFofaQueryParser()
	for _, example := range examples {
		result := fpq.FofaProgramCheck(example.source)
		assert.Equal(t, example.result, result)
	}
}
