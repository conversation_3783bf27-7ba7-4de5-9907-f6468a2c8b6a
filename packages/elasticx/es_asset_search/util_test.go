package es_asset_search

import (
	"git.gobies.org/foeye/foeye3/responses/r_asset_search"
	"git.gobies.org/foeye/foeye3/responses/r_common"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func (suite *SearchSuite) Test_getDataResultFid() {
	ctx := &gin.Context{}
	var q r_asset_search.QueryListAndKeyword
	q.QueryList = &r_common.QueryList{
		Number: 1,
		Size:   32,
		All:    false,
	}
	res, err := getDataResultFid(ctx, suite.searchDb, q, []string{}, true)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), res)
	//mosso.DebugShowContentWithJSON(res)
}
