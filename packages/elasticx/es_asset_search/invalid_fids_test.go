package es_asset_search

import (
	"encoding/json"
	"fmt"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
)

func (suite *SearchSuite) Test_GetInvalidFids() {
	rec := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(rec)
	jsonData := `{
	"os_num": {
		"doc_count_error_upper_bound": 0,
		"sum_other_doc_count": 1,
		"buckets": [{
				"key": "ubuntu",
				"doc_count": 14
			},
			{
				"key": "windows 10, version 2004/windows server, version 2004",
				"doc_count": 7
			},
			{
				"key": "windows",
				"doc_count": 6
			},
			{
				"key": "windows server 2019, version 1809/windows 10, version 1809",
				"doc_count": 4
			},
			{
				"key": "windows 7, service pack 1/windows server 2008 r2, service pack 1",
				"doc_count": 3
			}
		]
	},
	"server_num": {
		"doc_count_error_upper_bound": 4,
		"sum_other_doc_count": 82,
		"buckets": [{
				"key": "",
				"doc_count": 157
			},
			{
				"key": "nginx",
				"doc_count": 97
			},
			{
				"key": "cloudflare",
				"doc_count": 11
			},
			{
				"key": "nginx/1.10.2",
				"doc_count": 10
			},
			{
				"key": "openresty/1.19.9.1",
				"doc_count": 8
			}
		]
	},
	"port_num": {
		"doc_count_error_upper_bound": 17,
		"sum_other_doc_count": 758,
		"buckets": [{
				"key": "80",
				"doc_count": 194
			},
			{
				"key": "443",
				"doc_count": 148
			},
			{
				"key": "22",
				"doc_count": 141
			},
			{
				"key": "111",
				"doc_count": 93
			},
			{
				"key": "8080",
				"doc_count": 92
			}
		]
	},
	"domain_num": {
		"doc_count_error_upper_bound": 5,
		"sum_other_doc_count": 364,
		"buckets": [{
			"key": "10.10.10.101",
			"doc_count": 1
		}]
	},
	"countries_num": {
		"doc_count_error_upper_bound": 0,
		"sum_other_doc_count": 0,
		"buckets": [{
				"key": "",
				"doc_count": 1387
			},
			{
				"key": "US",
				"doc_count": 19
			},
			{
				"key": "CN",
				"doc_count": 14
			},
			{
				"key": "JP",
				"doc_count": 4
			},
			{
				"key": "SG",
				"doc_count": 2
			}
		]
	},
	"companies_num": {
		"doc_count_error_upper_bound": 0,
		"sum_other_doc_count": 0,
		"buckets": []
	}
}`
	searchResult := elastic.SearchResult{}
	searchResult.Hits = &elastic.SearchHits{TotalHits: int64(0), Hits: nil}
	err := json.Unmarshal([]byte(jsonData), &searchResult.Aggregations)
	defer gomonkey.ApplyFuncReturn(getDataResult, &searchResult, nil).Reset()

	data, err := suite.search.GetInvalidFids(ctx)
	assert.NoError(suite.T(), err)
	fmt.Println("data", data)
	assert.NotNil(suite.T(), data)
}
