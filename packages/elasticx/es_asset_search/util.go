package es_asset_search

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"

	"git.gobies.org/fofa-backend/fofacore"

	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/responses/r_asset_search"
)

// getDataResult 从es中取聚合数据
func getDataResult(ctx *gin.Context, asset *ESAssetSearchDatabase, q r_asset_search.QueryListAndKeyword, state bool) (*elastic.SearchResult, error) {
	searchService := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfService()).
		Index(elasticx.IndexNameOfSubDomain()).
		Size(0)
	portsAggregation := elastic.NewTermsAggregation().Field("port").Size(5)
	osAggregation := elastic.NewTermsAggregation().Field("os").Size(5)
	domainAggregation := elastic.NewTermsAggregation().Field("host.host_raw").Size(1) // 此数没有使用，在后面后用subdomain count代替
	companiesAggregation := elastic.NewTermsAggregation().Field("company.raw").Size(5)
	serverAggregation := elastic.NewTermsAggregation().Field("server.server_raw").Size(5)
	countriesAggregation := elastic.NewTermsAggregation().Field("geoip.country_code2.raw").Size(10000).ShardSize(10000)
	if q.SearchType == "advanced" {
		query := elastic.NewBoolQuery()
		for key, val := range q.Asset {
			if val == "" {
				continue
			}
			query.Must(elastic.NewTermQuery(key, val))
		}
		if state {
			query.MustNot(elastic.NewTermQuery("is_honeypot", true))
			query.Must(elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("is_fraud")),
				elastic.NewTermQuery("is_fraud", false)))
		}
		searchService.Query(query).
			Aggregation("domain_num", domainAggregation).
			Aggregation("companies_num", companiesAggregation).
			Aggregation("server_num", serverAggregation).
			Aggregation("countries_num", countriesAggregation).
			Aggregation("port_num", portsAggregation).
			Aggregation("os_num", osAggregation)
	} else if q.KeywordBase64 == "" {
		query := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())
		if state {
			query.MustNot(elastic.NewTermQuery("is_honeypot", true))
			query.Must(elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("is_fraud")),
				elastic.NewTermQuery("is_fraud", false)))
		}
		source := elastic.NewSearchSource().FetchSourceIncludeExclude(defaultSource, nil)
		searchService.
			SearchSource(source).
			Query(query).
			Aggregation("domain_num", domainAggregation).
			Aggregation("companies_num", companiesAggregation).
			Aggregation("server_num", serverAggregation).
			Aggregation("countries_num", countriesAggregation).
			Aggregation("port_num", portsAggregation).
			Aggregation("os_num", osAggregation)
	} else {
		// base 64解码
		keyword, err := base64.StdEncoding.DecodeString(q.KeywordBase64)
		// url解码
		enEscapeUrl, _ := url.QueryUnescape(string(keyword))
		if err != nil {
			return nil, err
		}
		qi := fofacore.QueryInput{
			Query: enEscapeUrl,
			Full:  true,
			Fraud: !state,
		}
		out, err := fofacore.ParseFEQuery(&qi)
		if err != nil {
			return nil, err
		}

		qr, ok := out.Query["query"]
		if !ok {
			return nil, errors.New("query is nil")
		}

		body, err := json.MarshalIndent(qr, "", "  ")
		if err != nil {
			return nil, err
		}
		aggs := `
{
    "server_num": {
      "terms": {
        "field": "server.server_raw",
        "size": 5
      }
    },
    "os_num": {
      "terms": {
        "field": "os",
        "size": 5
      }
    },
    "domain_num": {
      "terms": {
        "field": "host.host_raw",
        "size": 500000
      }
    },
    "countries_num": {
      "terms": {
        "field": "geoip.country_code2.raw",
        "size": 10000,
        "shard_size": 10000
      }
    },
    "port_num": {
      "terms": {
        "field": "port.port_raw",
        "size": 5
      }
    },
    "companies_num" : {
      "terms": {
        "field": "company.raw",
        "size": 5
      }
    }
  }
`
		source := fmt.Sprintf(`{"query":%s, "aggs": %s }`, body, aggs)
		searchService.Source(source)
	}

	searchResult, err := searchService.
		Pretty(true).
		Sort("lastchecktime", false).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	return searchResult, nil
}

// getDataResult1 从es中取聚合数据 单独取protocol
func getDataResult1(ctx *gin.Context, asset *ESAssetSearchDatabase, q r_asset_search.QueryListAndKeyword, state bool) (*elastic.SearchResult, error) {
	searchService := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfService()).
		Size(0)
	protocolsAggregation := elastic.NewTermsAggregation().Field("protocol").Size(5000)
	if q.SearchType == "advanced" {
		query := elastic.NewBoolQuery()
		if state {
			query.MustNot(elastic.NewTermQuery("is_honeypot", true))
			query.Must(elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("is_fraud")),
				elastic.NewTermQuery("is_fraud", false)))
		}
		for key, val := range q.Asset {
			if val == "" {
				continue
			}
			query.Must(elastic.NewTermQuery(key, val))
		}
		searchService.Query(query)
		searchService.
			Query(query).
			Aggregation("protocols_num", protocolsAggregation)
	} else if q.KeywordBase64 == "" {
		query := elastic.NewBoolQuery().Must(elastic.NewMatchAllQuery())
		if state {
			query.MustNot(elastic.NewTermQuery("is_honeypot", true))
			query.Must(elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("is_fraud")),
				elastic.NewTermQuery("is_fraud", false)))
		}
		source := elastic.NewSearchSource().FetchSourceIncludeExclude(defaultSource, nil)
		searchService.
			SearchSource(source).
			Query(query)
		searchService.
			Query(query).
			Aggregation("protocols_num", protocolsAggregation)
	} else {
		keyword, err := base64.StdEncoding.DecodeString(q.KeywordBase64)
		if err != nil {
			return nil, err
		}
		// url解码
		enEscapeUrl, _ := url.QueryUnescape(string(keyword))
		qi := fofacore.QueryInput{
			Query: enEscapeUrl,
			Full:  true,
			Fraud: !state,
		}
		out, err := fofacore.ParseFEQuery(&qi)
		if err != nil {
			return nil, err
		}

		qr, ok := out.Query["query"]
		if !ok {
			return nil, errors.New("query is nil")
		}

		body, err := json.MarshalIndent(qr, "", "  ")
		if err != nil {
			return nil, err
		}
		source := fmt.Sprintf(`{"query":%s, "aggs": {"protocols_num":{"terms":{"field":"protocol","size":5000}}} }`, body)
		searchService.Source(source)
	}

	searchResult, err := searchService.
		Pretty(true).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	return searchResult, nil
}

// getDataResultFid 从es中取聚合数据 单独取fid
func getDataResultFid(ctx *gin.Context, asset *ESAssetSearchDatabase, q r_asset_search.QueryListAndKeyword, fidRecordList []string, state bool) (*elastic.SearchResult, error) {
	searchService := asset.Instance.Client.Search().
		Index(elasticx.IndexNameOfService()).
		Index(elasticx.IndexNameOfSubDomain()).
		Size(0)
	fidList := make([]interface{}, 0)
	for _, val := range fidRecordList {
		fidList = append(fidList, val)
	}
	//mosso.DebugShowContentWithJSON(fidRecordList)

	fidAggregation := elastic.NewTermsAggregation().Field("dom.foid.raw").Size(10000000)
	if q.SearchType == "advanced" {
		query := elastic.NewBoolQuery()
		if state {
			query.MustNot(elastic.NewTermQuery("is_honeypot", true))
			query.Must(elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("is_fraud")),
				elastic.NewTermQuery("is_fraud", false)))
		}
		query.MustNot(elastic.NewTermsQuery("dom.foid.raw", fidList...))
		for key, val := range q.Asset {
			if val == "" {
				continue
			}
			query.Must(elastic.NewTermQuery(key, val))
		}
		searchService.Query(query)
		searchService.
			Query(query).
			Aggregation("fid_num", fidAggregation)
	} else if q.KeywordBase64 == "" {
		query := elastic.NewBoolQuery()
		if state {
			query.MustNot(elastic.NewTermQuery("is_honeypot", true))
			query.Must(elastic.NewBoolQuery().Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("is_fraud")),
				elastic.NewTermQuery("is_fraud", false)))
		}
		query.MustNot(elastic.NewTermsQuery("dom.foid.raw", fidList...))
		source := elastic.NewFetchSourceContext(true).Include(defaultSource...)
		searchService.
			FetchSourceContext(source).
			Query(query).
			Aggregation("fid_num", fidAggregation)
	} else {
		keyword, err := base64.StdEncoding.DecodeString(q.KeywordBase64)
		if err != nil {
			return nil, err
		}
		// url解码
		enEscapeUrl, _ := url.QueryUnescape(string(keyword))
		qi := fofacore.QueryInput{
			Query: enEscapeUrl,
			Full:  true,
			Fraud: !state,
		}
		out, err := fofacore.ParseFEQuery(&qi)
		if err != nil {
			return nil, err
		}

		qr, ok := out.Query["query"]
		if !ok {
			return nil, errors.New("query is nil")
		}

		body, err := json.MarshalIndent(qr, "", "  ")
		if err != nil {
			return nil, err
		}
		boolQuery := elastic.NewBoolQuery()
		boolQuery.Must(elastic.NewRawStringQuery(string(body)))
		boolQuery.MustNot(elastic.NewTermsQuery("dom.foid.raw", fidList...))
		searchService.Query(boolQuery).
			Aggregation("fid_num", fidAggregation)
	}

	searchResult, err := searchService.Size(0).
		Pretty(true).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	return searchResult, nil
}
