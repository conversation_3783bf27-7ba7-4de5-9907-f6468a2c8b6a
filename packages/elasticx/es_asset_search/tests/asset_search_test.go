package tests

import (
	"testing"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset_search"
	"git.gobies.org/foeye/foeye3/responses/r_asset_search"
	"git.gobies.org/foeye/foeye3/responses/r_common"

	"git.gobies.org/foeye-dependencies/configure"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestAssetSearchSuite(t *testing.T) {
	suite.Run(t, &AssetSearchSuite{})
}

type AssetSearchSuite struct {
	suite.Suite

	configure     *config.Configure
	elasticsearch *elasticx.ElasticDatabase
}

func (suite *AssetSearchSuite) SetupSuite() {
	var err error

	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../../../../"),
	)
	assert.NotNil(suite.T(), suite.configure)

	suite.elasticsearch, err = elasticx.NewElasticDatabase(suite.configure)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), suite.elasticsearch)
}

func (suite *AssetSearchSuite) TestGetSearchList() {
	// TODO: 可测试性非常差，不连接测试机难道就有测试不了了？
	// suite.configure.Elastic.Host = "http://***********:9200"
	suite.configure.Elastic.Host = "http://127.0.0.1:9200"

	esd := &es_asset_search.ESAssetSearchDatabase{Instance: suite.elasticsearch}
	assert.NotNil(suite.T(), esd)

	ctx := &gin.Context{}
	// r_asset_search.QueryListAndKeyword{}
	took, total, res, err := esd.GetAssetSearchList(ctx, r_asset_search.QueryListAndKeyword{
		QueryList: &r_common.QueryList{
			Number: 0,
			Size:   20,
			All:    false,
		},
		Asset:         nil,
		Keyword:       "YXBwJTNEJTIyQVBBQ0hFLVdlYi1TZXJ2ZXIlMjI=",
		KeywordBase64: "YXBwJTNEJTIyQVBBQ0hFLVdlYi1TZXJ2ZXIlMjI=",
		SearchType:    "",
	}, true)

	log.Println("took", took, "total", total, "res", res, err)
}
