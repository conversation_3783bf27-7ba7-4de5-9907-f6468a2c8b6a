package es_asset_search

import (
	"encoding/json"
	"fmt"

	"git.gobies.org/fofa-backend/fofacore"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/model/m_asset_search"
)

func (search *ESAssetSearchDatabase) GetInvalidFids(ctx *gin.Context) ([]*m_asset_search.FidGroup, error) {
	pageSize := 100
	maxDocsPerFoid := 100

	agg := elastic.NewTermsAggregation().
		Field("dom.foid.raw").
		Size(pageSize). // 最多 100 个不同 foid
		OrderByKeyAsc().
		SubAggregation("top_hit", elastic.NewTopHitsAggregation().
			Size(maxDocsPerFoid).
			FetchSourceContext(elastic.NewFetchSourceContext(true).
				Include("ip", "host", "port", "title", "domain", "dom.foid")),
		)

	// 构造 FOFA 查询
	qi := fofacore.QueryInput{
		Query: `header="301 Moved Permanently" || body="301 Moved Permanently" || header="302 Found" || body="302 Found" || header="407 Proxy Authentication Required" || body="407 Proxy Authentication Required" || header="401 Authentication Required" || body="401 Authentication Required" || body="Default Web Site Page" || body="無効なURLです" || body="Stranica u fazi izrade!!!" || body="Welcome to nginx!" || body="Welcome to OpenResty!" || body="cPanel 登录" || body="Webmail 登录" || body="FRITZ!Box" || body="503 Service Temporarily Unavailable" || body="IIS Windows Server" || body="IIS7" || body="ASUS Login" || body="阿里云 Web应用防火墙" || body="速御安全高防CDN默认页" || header="401 Unauthorized" || body="401 Unauthorized" || header="403 Forbidden" || body="403 Forbidden" || header="404 Not Found" || body="404 Not Found" || header="500 Internal Server Error" || body="500 Internal Server Error" || header="502 Bad Gateway" || body="502 Bad Gateway" || header="503 Service Unavailable" || body="503 Service Unavailable" || header="504 Gateway Timeout" || body="504 Gateway Timeout" || header="400 Bad Request" || body="400 Bad Request" || body="403 Forbidden - Access denied" || body="This site can’t be reached" || body="Web Server's Default Page"`,
		Full:  true,
		Fraud: false,
	}
	out, _ := fofacore.ParseFEQuery(&qi)
	qr, ok := out.Query["query"]
	if !ok {
		return nil, fmt.Errorf("Query parsing failed")
	}
	body, err := json.MarshalIndent(qr, "", "  ")
	if err != nil {
		return nil, err
	}

	query := elastic.NewRawStringQuery(string(body))

	// 执行聚合查询
	searchResult, err := search.Instance.Client.Search().
		Index("fofaee_subdomain").
		Query(query).
		Size(0). // 不返回 hits
		Aggregation("foid_terms", agg).
		Do(ctx)
	if err != nil {
		return nil, err
	}

	// 提取聚合结果
	aggRes, found := searchResult.Aggregations.Terms("foid_terms")
	if !found {
		return nil, fmt.Errorf("aggregation foid_terms not found")
	}

	// {"fid": "", "children": [m_asset_search.InvalidFids}]}
	var result []*m_asset_search.FidGroup

	for _, bucket := range aggRes.Buckets {
		foid := bucket.Key.(string)

		topHits, ok := bucket.TopHits("top_hit")
		if !ok || len(topHits.Hits.Hits) == 0 {
			continue
		}

		group := &m_asset_search.FidGroup{
			Fid:      foid,
			Children: []*m_asset_search.InvalidFids{},
		}

		for _, hit := range topHits.Hits.Hits {
			invalidFids := m_asset_search.InvalidFids{}
			if err := json.Unmarshal(*hit.Source, &invalidFids); err != nil {
				continue
			}

			uid, _ := uuid.NewRandom()
			invalidFids.Uuid = uid.String()

			group.Children = append(group.Children, &invalidFids)
		}
		result = append(result, group)
	}

	return result, nil
}
