package dccapiserver

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestDccAPIServerSuite(t *testing.T) {
	suite.Run(t, &DccAPIServerSuite{})
}

type DccAPIServerSuite struct {
	suite.Suite
	host string
	mid  string
	key  string
	path string
}

func (suite *DccAPIServerSuite) BeforeTest(suiteName, testName string) {
	suite.host = "localhost"
	suite.mid = "MID000c2915d9e5"
	suite.key = "0eadf69942fa2ef94c3c46861591ae7c"
}

func (suite *DccAPIServerSuite) Test_CallAuth() {
	suite.path = "/foeyeApi/auth"
	actual, err := CallAuth(suite.host, suite.mid, suite.key, suite.path)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), actual)
}

func (suite *DccAPIServerSuite) Test_CallAuthByGet() {
	suite.path = "/foeyeApi/auth/sftpPasswd"
	actual, err := CallAuthSftpByGet(suite.host, suite.mid, suite.key, suite.path)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), actual)
}
