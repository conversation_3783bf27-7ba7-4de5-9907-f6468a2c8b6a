package dccapiserver

import "encoding/json"

type AuthParameter struct {
	Mid string `json:"mid"`
	Key string `json:"key"`
}

func (a *AuthParameter) String() string {
	marshal, _ := json.Marshal(&a)
	return string(marshal)
}

type AuthResponse struct {
	Code    int    `json:"statusCode"`
	Message string `json:"messages"`
}

type AuthResponseSftpData struct {
	SftpPassword string `json:"sftp_password"`
	SftpPort     uint   `json:"sftp_port"`
}

type AuthSftpResponse struct {
	Code    int                  `json:"statusCode"`
	Message string               `json:"messages"`
	Data    AuthResponseSftpData `json:"data"`
}

func (response *AuthResponse) String() string {
	marshal, _ := json.Marshal(&response)
	return string(marshal)
}

func (response *AuthSftpResponse) String() string {
	marshal, _ := json.Marshal(&response)
	return string(marshal)
}
