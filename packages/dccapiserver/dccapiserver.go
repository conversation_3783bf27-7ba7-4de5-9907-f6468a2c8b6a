package dccapiserver

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"git.gobies.org/foeye-dependencies/httpclient"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
)

func CallAuth(host, mid, key, path string) (*AuthResponse, error) {
	queries, dst, err := commonReq(host, mid, key, path)
	if err != nil {
		return nil, err
	}

	resp, err := httpclient.Post(dst).
		QueryParams(queries).
		Timeout(5 * time.Second).
		InsecureSkipVerify(true).
		Debug(true).
		Do()
	if err != nil {
		return nil, errors.Wrap(err, "post failed")
	}
	defer resp.Body.Close()

	bytes, err := commonResp(resp)
	if err != nil {
		return nil, err
	}

	response := &AuthResponse{}
	if err = json.Unmarshal(bytes, response); err != nil {
		return nil, errors.Wrap(err, "json unmarshal failed.")
	}

	if response.Code != http.StatusOK {
		return response, fmt.Errorf(response.Message)
	}

	return response, nil
}

func CallAuthSftpByGet(host, mid, key, path string) (*AuthSftpResponse, error) {
	queries, dst, err := commonReq(host, mid, key, path)
	if err != nil {
		return nil, err
	}

	resp, err := httpclient.Get(dst).
		QueryParams(queries).
		Timeout(5 * time.Second).
		InsecureSkipVerify(true).
		Debug(true).
		Do()
	if err != nil {
		return nil, errors.Wrap(err, "get failed.")
	}
	defer resp.Body.Close()

	bytes, err := commonResp(resp)
	if err != nil {
		return nil, err
	}

	response := &AuthSftpResponse{}
	if err = json.Unmarshal(bytes, response); err != nil {
		return nil, errors.Wrap(err, "json unmarshal failed.")
	}

	if response.Code != http.StatusOK {
		return response, fmt.Errorf(response.Message)
	}

	return response, nil
}

func commonReq(host string, mid string, key string, path string) (httpclient.Params, string, error) {
	host = strings.TrimSpace(host)
	mid = strings.TrimSpace(mid)
	key = strings.TrimSpace(key)
	path = strings.TrimSpace(path)
	if len(host) == 0 || len(mid) == 0 || len(key) == 0 || path == "" {
		return nil, "", fmt.Errorf("host or mid or key or path invalid")
	}

	url, err := FixedURL(host)
	if err != nil {
		return nil, "", errors.Wrap(err, "fixed url failed.")
	}

	url.Path = path
	dst := url.String()
	logrus.Infof("request url is %s", dst)

	queries := httpclient.Params{
		httpclient.Param{
			Key:   "mid",
			Value: mid,
		},
		httpclient.Param{
			Key:   "key",
			Value: key,
		},
	}
	return queries, dst, nil
}

func commonResp(resp *http.Response) ([]byte, error) {
	bytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "read response body failed.")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("response status code is %d, data= %s", resp.StatusCode, string(bytes))
	}

	return bytes, nil
}

func FixedURL(rawurl string) (*url.URL, error) {
	// Check if the URL has a valid scheme.
	if u, err := url.ParseRequestURI(rawurl); err == nil && u.Scheme != "" {
		return u, nil
	}

	// Add the HTTPS scheme if the URL doesn't have a valid scheme.
	u, err := url.Parse("https://" + rawurl)
	if err != nil {
		return nil, err
	}

	return u, nil
}
