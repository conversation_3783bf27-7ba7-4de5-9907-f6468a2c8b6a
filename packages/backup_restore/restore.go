package backup_restore

import (
	"fmt"
	"io/ioutil"
	"os"
	"path"
	"path/filepath"

	"git.gobies.org/foeye/foeye3/packages/fsx"
	"git.gobies.org/foeye/foeye3/packages/systemx"
)

// ClearFiles 删除之前的恢复文件
func ClearFiles() {
	place, _ := fsx.GetRestoreSavePathForDatabase("")
	if systemx.PathExist(place) {
		os.RemoveAll(place)
	}
	os.MkdirAll(place, os.ModePerm)
}

// CpFile 复制恢复文件到指定路径并重命名
func CpFile() string {
	place, _ := fsx.GetRestoreSavePath("")
	filePath := path.Join(place, "import_data.zip")
	if systemx.PathExist(filePath) {
		newPath := filepath.Join(place, "restore.zip")
		tmp, _ := ioutil.ReadFile(filePath)
		ioutil.WriteFile(newPath, tmp, os.ModePerm)
		return newPath
	}
	return ""
}

// DecryptTar 解压解密
func DecryptTar() bool {
	place, _ := fsx.GetRestoreSavePath("")
	cmd := fmt.Sprintf("cd %s; dd if=restore.zip |openssl des3 -d -k %s|tar zxf -", place, EncryptKey)
	if _, err := systemx.ExecuteSystemCommand(cmd); err != nil {
		return false
	}
	return true
}
