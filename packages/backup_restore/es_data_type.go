package backup_restore

import (
	"git.gobies.org/foeye/foeye3/packages/fsx"
	"git.gobies.org/foeye/foeye3/packages/jsoner"
)

const (
	IndexTagFileName        = "index_tag"
	SubdomainFileName       = "fofaee_subdomain"
	ServiceESFileName       = "fofaee_service"
	TaskAssetFileName       = "fofaee_task_assets"
	AssetFileName           = "fofaee_assets"
	DomainAssetFileName     = "fofaee_domain_assets"
	TaskDomainAssetFileName = "fofaee_task_domain_assets"
	ThreatESFileName        = "fofaee_threats"
	ViolationFileName       = "fofaee_violations"
	NetinfoFileName         = "fofaee_netinfos"
	SiteUrlFileName         = "fofaee_siteurl"
)

var (
	BaseDir           = "."
	SubdomainSettings string
	SubdomainMappings string

	ServiceESSettings string
	ServiceESMappings string

	TaskAssetSettings string
	TaskAssetMappings string

	AssetSettings string
	AssetMappings string

	DomainAssetSettings string
	DomainAssetMappings string

	TaskDomainAssetSettings string
	TaskDomainAssetMappings string

	ThreatESSettings string
	ThreatESMappings string

	ViolationSettings string
	ViolationMappings string

	NetinfoSettings string
	NetinfoMappings string

	SiteUrlSettings string
	SiteUrlMappings string
)

func InitVar() {
	SubdomainSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, SubdomainFileName))
	SubdomainMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, SubdomainFileName))

	ServiceESSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, ServiceESFileName))
	ServiceESMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, ServiceESFileName))

	TaskAssetSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, TaskAssetFileName))
	TaskAssetMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, TaskAssetFileName))

	AssetSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, AssetFileName))
	AssetMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, AssetFileName))

	DomainAssetSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, DomainAssetFileName))
	DomainAssetMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, DomainAssetFileName))

	TaskDomainAssetSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, TaskDomainAssetFileName))
	TaskDomainAssetMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, TaskDomainAssetFileName))

	ThreatESSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, ThreatESFileName))
	ThreatESMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, ThreatESFileName))

	ViolationSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, ViolationFileName))
	ViolationMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, ViolationFileName))

	NetinfoSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, NetinfoFileName))
	NetinfoMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, NetinfoFileName))

	SiteUrlSettings = jsoner.ReadJsonFileToString(fsx.GetIndexForSettingsFilePath(BaseDir, SiteUrlFileName))
	SiteUrlMappings = jsoner.ReadJsonFileToString(fsx.GetIndexForMappingsFilePath(BaseDir, SiteUrlFileName))
}
