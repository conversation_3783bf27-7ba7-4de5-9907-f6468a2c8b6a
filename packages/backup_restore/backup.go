package backup_restore

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math/rand"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"git.gobies.org/foeye/foeye3/store"

	"git.gobies.org/foeye/foeye3/config"

	"github.com/go-redis/redis"

	"git.gobies.org/foeye/foeye3/packages/fsx"
	"git.gobies.org/foeye/foeye3/packages/systemx"
)

// EncryptKey 备份数据库的加密key
var EncryptKey = "80cd0a127ff3d041a3b957f8b88fb9f2"

// GetBackupFilename 获取备份文件名称
func GetBackupFilename(client *redis.Client) string {
	filenameRaw, _ := client.Get("db_backup_filename").Bytes()
	filename := filepath.Base(string(bytes.Trim(filenameRaw, "\"")))
	return filename
}

// Backup 备份
type Backup struct {
	Path   string
	Folder string
}

// ClearFiles 删除旧的备份文件
func (backup *Backup) ClearFiles() {
	//place := filepath.Join(backup.Path, backup.Folder)
	//place, _ := fsx.GetBackupSavePathOfMysql("")
	fsx.DeleteDirFileForPath(backup.Path, "", true)

	zipPath, _ := fsx.GetBackupSavePath("")
	files, _ := ioutil.ReadDir(zipPath)
	for _, file := range files {
		if strings.HasSuffix(file.Name(), ".zip") {
			fsx.DeleteDirFileForPath(zipPath, file.Name(), false)
		}
	}
}

//CreateFile 创建目录
func (backup *Backup) CreateFile(filename, prefolder string) string {
	var place string
	if prefolder == "mysql_backup" {
		place, _ = fsx.GetBackupSavePathOfMysql("")
	} else {
		place, _ = fsx.GetBackupSavePathOfES("")
	}
	//place := path.Join(backup.Path, prefolder)
	if !systemx.PathExist(place) {
		os.MkdirAll(place, os.ModePerm)
	}
	return path.Join(place, filename)
}

/*mysql数据库备份*/
type MysqlBackup struct {
	Backup
	exceptTables []string
	folder       string
	RecordChan   chan []map[string]interface{}

	configure *config.Configure
	storage   store.Factory
}

// NewMysqlBackup 创建mysql备份实例
func NewMysqlBackup(configure *config.Configure, storage store.Factory) *MysqlBackup {

	c := &MysqlBackup{configure: configure, storage: storage}
	c.Path, _ = fsx.GetBackupSavePathOfMysql("")

	return c
}

// SaveJson 保存mysql数据
func (mysqlBackup *MysqlBackup) SaveJson(table string, count int, i int, tablesCount int) {
	var page int
	for records := range mysqlBackup.RecordChan {
		page++
		var pageContent string
		for _, record := range records {
			resByte, err := json.Marshal(record)
			if err != nil {
				continue
			}
			pageContent += string(resByte) + "\n"
		}
		//保存一页数据
		filePlace := mysqlBackup.CreateFile(fmt.Sprintf("%s_%d.json", table, page), "mysql_backup")
		file, err := os.OpenFile(filePlace, os.O_CREATE|os.O_APPEND|os.O_RDWR, 0666)
		if err != nil {
			continue
		}
		file.WriteString(pageContent)
		file.Close()
		//计算进度
		var progress float64
		if count > 0 {
			progress = ((float64(i)+1)*100 + (float64(page)*float64(len(records))*100)/float64(count)) / float64(tablesCount)
		} else {
			progress = (float64(i) + 1) * 100 / float64(tablesCount)
		}
		if progress >= 99.99 {
			progress = 99.99
		}
		progress, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", progress), 64)
		myProgress, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", (progress/2)), 64)
		mysqlBackup.storage.Redis().Client().Set("system_db_backup_progress", myProgress, 0)
		mysqlBackup.storage.Redis().Client().Set("buckuping", 1, 30*time.Second)
	}
}

/*es数据库备份*/
type ESBackup struct {
	Backup
	IndexNames []string
}

// NewESBackup 创建es备份实例
func NewESBackup() *ESBackup {
	tmp := new(ESBackup)
	tmp.Path, _ = fsx.GetBackupSavePathOfES("")
	tmp.IndexNames = []string{
		elasticx.IndexNameOfSiteurl(),
		elasticx.IndexNameOfAsset(),
		elasticx.IndexNameOfTaskAsset(),
		elasticx.IndexNameOfService(),
		elasticx.IndexNameOfSubDomain(),
		elasticx.IndexNameOfThreat(),
		elasticx.IndexNameOfViolation(),
	}
	return tmp
}

// EncryptTar 加密备份数据
func EncryptTar() string {
	backupPath, _ := fsx.GetBackupSavePath("")
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	filename := fmt.Sprintf("db_backup_%s-%d.zip", time.Now().Format("2006-01-02-15-04-05"), r.Intn(8999)+1000)
	cmd := fmt.Sprintf("cd %s; tar -zcvf - files|openssl des3 -salt -k %s | dd of=%s", backupPath, EncryptKey, filename)
	systemx.ExecuteSystemCommand(cmd)
	return filename
}
