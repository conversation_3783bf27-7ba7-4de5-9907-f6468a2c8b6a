package backup_restore

import (
	"fmt"
	"testing"

	"git.gobies.org/foeye/foeye3/packages/elasticx"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestBackupRestoreTool(t *testing.T) {
	suite.Run(t, new(BackupRestoreTool))
}

type BackupRestoreTool struct {
	backup      Backup
	mysqlBackup MysqlBackup
	es          ESBackup
	suite.Suite
}

func (suite *BackupRestoreTool) BeforeTest(suiteName, testName string) {

	suite.backup = Backup{Path: "/tmp"}
	suite.mysqlBackup = MysqlBackup{
		Backup:       Backup{},
		exceptTables: []string{"schema_migrations", "sessions", "system_networks", "system_routes"},
		folder:       "mysql_backup",
	}
	suite.es = ESBackup{
		Backup: Backup{
			Path:   "",
			Folder: "",
		},
		IndexNames: []string{elasticx.IndexNameOfAsset(), elasticx.IndexNameOfTaskAsset(), elasticx.IndexNameOfService(), elasticx.IndexNameOfSubDomain(), elasticx.IndexNameOfThreat(), elasticx.IndexNameOfViolation()},
	}

}

func (suite *BackupRestoreTool) Test_ClearFile() {
	fmt.Println(suite.backup.Path)
}

func (suite *BackupRestoreTool) Test_CreateFile() {
	res := suite.backup.CreateFile("result.txt", "test")
	assert.NotEmpty(suite.T(), res)
	fmt.Println(res)
}
