package netinfox

import (
	"context"
	"time"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/packages/redis_lock"

	"github.com/go-redis/redis"

	"git.gobies.org/foeye/foeye3/model/m_scan_port"
	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/packages/iprange"

	"golang.org/x/sync/errgroup"

	"git.gobies.org/foeye/foeye3/model/m_netinfo"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_assets"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
)

// RunAllFlowWorkers 资产流量分析
func RunAllFlowWorkers(configure *config.Configure, db database.Factory, es elasticx.Factory, redis *redis.Client) {
	l, err := redis_lock.GetRedisMutex(redis, "RunAllFlowWorkers", 30*time.Second, true)
	if err != nil {
		log.Printf("create GetRedisMutex failed: %v\n", err)
		return
	}
	ok, err := l.Lock()
	if err != nil {
		log.Printf("create GetRedisMutex.Lock failed: %v\n", err)
		return
	}
	if !ok {
		return
	}
	defer l.Unlock()
	work, err := NewNetInfoBackendAPI(configure, db, es)
	if err != nil {
		log.Printf("create NewNetInfoBackendAPI failed: %v\n", err)
		return
	}
	if !work.IsFlowSwitchOn() {
		log.Println("flow-analysis is not open")
		return
	}
	if err = work.CreateNetInfosIndex(); err != nil {
		log.Printf("fofaee_netinfos not created: %v\n", err)
		return
	}
	if err = work.LimitNetInfos(); err != nil {
		log.Println(err.Error())
		return
	}
	work.NetInfoRun()
	work.NetHttpkRun()
}

// RunUpdateInfoWorkers 修改未识别和非标状态
func RunUpdateInfoWorkers(configure *config.Configure, db database.Factory, es elasticx.Factory, redis *redis.Client) {
	l, err := redis_lock.GetRedisMutex(redis, "RunUpdateInfoWorkers", 30*time.Second, true)
	if err != nil {
		log.Printf("create GetRedisMutex failed: %v\n", err)
		return
	}
	ok, err := l.Lock()
	if err != nil {
		log.Printf("create GetRedisMutex.Lock failed: %v\n", err)
		return
	}
	if !ok {
		return
	}
	defer l.Unlock()
	work, err := NewNetInfoBackendAPI(configure, db, es)
	if err != nil {
		log.Printf("Create NewNetInfoBackendAPI failed: %v\n", err)
		return
	}
	if !work.IsFlowSwitchOn() {
		log.Println("flow-analysis is not open")
		return
	}
	if err = work.CreateNetInfosIndex(); err != nil {
		log.Printf("fofaee_netinfos not created: %v\n", err)
		return
	}
	// 获取未识别的资产
	unIdentifyAssetMap, err := es.NetInfo().GetUnIdentifyAssets2(context.Background(), int(m_netinfo.MaxCountLimit))
	if err != nil {
		return
	}

	if len(unIdentifyAssetMap) <= 0 {
		return
	}
	sourceIPs := make([]string, 0)
	destIPs := make([]string, 0)
	for _, v := range unIdentifyAssetMap {
		sourceIPs = append(sourceIPs, v.SourceIp)
		destIPs = append(destIPs, v.DestIp)
	}
	var (
		eg                   errgroup.Group
		identifySourceAssets interface{}
		identifyDestAssets   interface{}
		identifySourceMap    = make(map[string]bool, 0)
		identifyDestMap      = make(map[string]bool, 0)
		scanPortMap          = make(map[string]bool, 0)
	)

	// 获取资产信息
	eg.Go(func() error {
		identifySourceAssets, err = es.Assets().GetList(context.Background(), sourceIPs)
		if err != nil {
			return err
		}
		for _, v := range identifySourceAssets.([]esi_assets.Document) {
			if _, exi := identifySourceMap[v.IP]; !exi {
				identifySourceMap[v.IP] = true
			}
		}
		return nil
	})
	eg.Go(func() error {
		identifyDestAssets, err = es.Assets().GetList(context.Background(), destIPs)
		if err != nil {
			return err
		}
		for _, v := range identifyDestAssets.([]esi_assets.Document) {
			if _, exi := identifyDestMap[v.IP]; !exi {
				identifyDestMap[v.IP] = true
			}
		}
		return nil
	})
	// 获取非预置端口
	eg.Go(func() error {
		scanPortMap = work.getSystemPorts()
		return nil
	})
	if err := eg.Wait(); err != nil {
		return
	}

	// 修改未识别资产信息
	for k, v := range unIdentifyAssetMap {
		// 资产
		if v.SourceIp == "" {
			unIdentifyAssetMap[k].SourceIpTag = "-"
		} else if identifySourceMap[v.SourceIp] {
			unIdentifyAssetMap[k].SourceIpTag = "已识别"
		} else {
			unIdentifyAssetMap[k].SourceIpTag = "未识别"
		}
		if v.DestIp == "" {
			unIdentifyAssetMap[k].DestIpTag = "-"
		} else if identifyDestMap[v.DestIp] {
			unIdentifyAssetMap[k].DestIpTag = "已识别"
		} else {
			unIdentifyAssetMap[k].DestIpTag = "未识别"
		}
		// 端口
		if v.SourcePort == "" {
			unIdentifyAssetMap[k].SourcePortTag = "-"
		} else if scanPortMap[v.SourcePort] {
			unIdentifyAssetMap[k].SourcePortTag = "预置"
		} else {
			unIdentifyAssetMap[k].SourcePortTag = "非标"
		}
		if v.DestPort == "" {
			unIdentifyAssetMap[k].DestPortTag = "-"
		} else if scanPortMap[v.DestPort] {
			unIdentifyAssetMap[k].DestPortTag = "预置"
		} else {
			unIdentifyAssetMap[k].DestPortTag = "非标"
		}
	}

	es.NetInfo().BatchUpdateUnIdentifyAssets(context.Background(), unIdentifyAssetMap)
}

// RunCreateTaskByConfigWorkers　通过配置创建任务
func RunCreateTaskByConfigWorkers(configure *config.Configure, db database.Factory, es elasticx.Factory, redis *redis.Client) {
	l, err := redis_lock.GetRedisMutex(redis, "RunCreateTaskByConfigWorkers", 30*time.Second, true)
	if err != nil {
		log.Printf("create GetRedisMutex failed: %v\n", err)
		return
	}
	ok, err := l.Lock()
	if err != nil {
		log.Printf("create GetRedisMutex.Lock failed: %v\n", err)
		return
	}
	if !ok {
		return
	}
	defer l.Unlock()
	work, err := NewNetInfoBackendAPI(configure, db, es)
	if err != nil {
		log.Printf("Create NewNetInfoBackendAPI failed: %v\n", err)
		return
	}
	conf := work.GetFlowConfig()
	if !(conf.FlowSwitch && conf.FlowAutoScan) {
		return
	}
	if err = work.CreateNetInfosIndex(); err != nil {
		log.Printf("fofaee_netinfos not created: %v\n", err)
		return
	}

	// 获取未识别资产
	ctx := context.Background()
	ipsUnIdentify, err := work.ES.NetInfo().GetUnIdentifyAssetsMap(ctx)
	if err != nil {
		return
	}
	if len(ipsUnIdentify) == 0 {
		return
	}
	if len(ipsUnIdentify) < conf.FlowAssetNum {
		return
	}

	// 获取ip_ranges
	ipRangeMap, err := work.GetIpMapFromIprangeMap()
	if err != nil {
		return
	}
	if len(ipRangeMap) == 0 {
		return
	}

	// 获取用户信息
	user, err := work.DB.User().GetAdminIds()
	if err != nil {
		return
	}
	if len(user) <= 0 {
		return
	}

	taskIps := make([]string, 0)
	esIDs := make([]string, 0)
	for ip, ids := range ipsUnIdentify {
		if _, ok := ipRangeMap[ip]; ok {
			taskIps = append(taskIps, ip)
			esIDs = append(esIDs, ids...)
		}
	}
	// 验证TaskIps 为空不允许下发任务
	if len(taskIps) == 0 {
		return
	}
	// 根据全部常用端口名获取全部常用端口id
	PortTem, err := work.DB.ScanPortTemplate().GetPortItemForName("全部常用端口")
	if err != nil {
		return
	}
	PortTemInfo := PortTem.(m_scan_port.AdditionScanPortTemplateData)

	// 获取带宽
	state := m_task.TaskStateOfWaiting
	addition := m_task.AdditionTask{
		Title:              "流量任务扫描",
		TaskType:           1,
		UserID:             user[0],
		IPRangeType:        "user_input",
		Bandwidth:          conf.FlowBandwidth,
		ScanPortTemplateID: int(PortTemInfo.ID),
		Category:           "flow_task",
		OtherConfig: m_task.OtherConfig{
			IsDeepScan:           false,
			IsTreckRecognition:   false,
			IsPingRecognition:    false,
			IsCycleProtocol:      false,
			IsDeepRecognition:    false,
			IsCrackDns:           false,
			IsVersionRecognition: false,
		},
		ScanType:         "quick",
		IPType:           1,
		SelectedIpRanges: taskIps,
	}
	_, err = iprange.ValidationIPs(
		addition.SelectedIpRanges,
		iprange.IPAddressType(addition.IPType),
	)
	if err != nil {
		return
	}

	internal, err := m_task.AdditionTaskConvertToInternal(user[0], &addition)
	internal.State = &state

	id, err := work.DB.Task().CreateAddTask(internal, 0)
	work.DB.Task().CreateIpRangeAndCondition(addition.SelectedIpRanges, addition.IPRangeType, addition.SelectedPocs, user[0], uint(addition.IPType), uint(id))
	work.ES.NetInfo().UpdateNetInfoIsPush(ctx, esIDs)

}
