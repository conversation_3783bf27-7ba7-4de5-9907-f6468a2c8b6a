package netinfox

import (
	"context"
	"fmt"
	"os"
	"time"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	msyqldatabase "git.gobies.org/foeye/foeye3/database/factory"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	elasticstorage "git.gobies.org/foeye/foeye3/packages/elasticx/factory"
	"git.gobies.org/foeye/foeye3/packages/fsx"
	"git.gobies.org/foeye/foeye3/packages/redisclient"

	"git.gobies.org/foeye-dependencies/configure"
	"github.com/go-redis/redis"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

//func TestWorkAllSuite(t *testing.T) {
//	suite.Run(t, new(WorkAllSuite))
//}

type WorkAllSuite struct {
	suite.Suite
	configure *config.Configure
	db        database.Factory
	es        elasticx.Factory
	redis     *redis.Client
}

func (suite *WorkAllSuite) BeforeTest(suiteName, testName string) {
	var err error
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfDevelop.String()),
		configure.WithSpecificConfigPath("./../../"),
	)
	assert.NotNil(suite.T(), suite.configure)
	suite.redis, err = redisclient.NewRedisClientOr(suite.configure)
	if err != nil {
		panic(fmt.Errorf("failed to initialize redis client: %+v", err))
	}
	assert.NotNil(suite.T(), suite.redis)
	// NewElasticDatabase database
	gormDatabase, err := database.NewGormDatabase(suite.configure)
	if err != nil {
		panic(fmt.Errorf("database new has error:%+v", err))
	}

	suite.db = msyqldatabase.NewMysqlDatabaseFactoryOr(gormDatabase)
	assert.NotNil(suite.T(), suite.db)
	// NewGormDatabase elasticx client instance.
	elastic, err := elasticx.NewElasticDatabase(suite.configure)
	if err != nil {
		panic(fmt.Errorf("failed to initialize elasticx client: %+v", err))
	}

	suite.es = elasticstorage.NewElasticDatabaseFactoryOr(elastic)
	assert.NotNil(suite.T(), suite.es)
}

func (suite *WorkAllSuite) TestRunAllFlowWorkers() {
	err := fsx.NotExistsMkDir("/dev/shm/")
	if err != nil {
		panic(fmt.Errorf("create a folder failed: %v", err))
	}
	// 没有测试数据创建模拟数据
	//if fsx.IsNotExists(ConnectInfoFilename) {
	// 创建模拟文件
	err = CreateFileAndWrite(ConnectInfoFilename, []byte(connectInfoAnalogData))
	if err != nil {
		panic(fmt.Errorf("write analog data failed: %v", err))
	}
	//}
	//if fsx.IsNotExists(HttpInfoFilename) {
	// 创建模拟文件
	err = CreateFileAndWrite(HttpInfoFilename, []byte(curlAnalogData))
	if err != nil {
		panic(fmt.Errorf("write analog data failed: %v", err))
	}
	//}

	RunAllFlowWorkers(suite.configure, suite.db, suite.es, suite.redis)
	time.Sleep(5 * time.Second)
	fmt.Println("--------success----------")
}

// TestRunUpdateInfoWorkers
func (suite *WorkAllSuite) TestRunUpdateInfoWorkers() {
	RunUpdateInfoWorkers(suite.configure, suite.db, suite.es, suite.redis)
	time.Sleep(5 * time.Second)
	fmt.Println("--------success----------")
}

// TestRunCreateTaskByConfigWorkers
func (suite *WorkAllSuite) TestRunCreateTaskByConfigWorkers() {
	RunCreateTaskByConfigWorkers(suite.configure, suite.db, suite.es, suite.redis)
	time.Sleep(5 * time.Second)
	fmt.Println("--------success----------")
}

// 创建模拟数据文件
func CreateFileAndWrite(fileName string, content []byte) error {
	f, err := os.Create(fileName)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.Write(content)
	if err != nil {
		return err
	}
	return nil
}

func (suite *WorkAllSuite) TestAddTestData() {
	elastics, err := elasticx.NewElasticDatabase(suite.configure)
	if err != nil {
		panic(fmt.Errorf("failed to initialize elastics client: %+v", err))
	}
	es := elasticstorage.NewElasticDatabaseFactoryOr(elastics)
	bulkRequest := es.Assets().GetEsClient().Bulk()
	doc := map[string]interface{}{
		"source_ip": "**********",
	}
	bulkRequest.Add(elastic.NewBulkUpdateRequest().Index(elasticx.IndexNameOfNetinfo()).Type(elasticx.TypeNameOfNetinfos).Id("3PR4tn8BGSblAU-bRAmK").Doc(doc))
	resp, err := bulkRequest.Do(context.Background())
	if err != nil {
		panic(err)
	}
	fmt.Println("---------success----------", resp)
}

var connectInfoAnalogData = `{"timestamp":"1646190995", "source_ip":"************","source_port":"41092","dest_ip":"************","dest_port":"8222"}
{"timestamp":"1646190995", "source_ip":"************","source_port":"46987","dest_ip":"************","dest_port":"22"}
{"timestamp":"1646190995", "source_ip":"************","source_port":"48566","dest_ip":"***********","dest_port":"22"}
{"timestamp":"1646190995", "source_ip":"************","source_port":"55655","dest_ip":"***********","dest_port":"389"}
{"timestamp":"1646190995", "source_ip":"************","source_port":"55656","dest_ip":"***********","dest_port":"88"}
{"timestamp":"1646190995", "source_ip":"************","source_port":"55657","dest_ip":"***********","dest_port":"389"}
{"timestamp":"1646190996", "source_ip":"************","source_port":"43766","dest_ip":"************","dest_port":"22"}
{"timestamp":"1646190996", "source_ip":"************","source_port":"41093","dest_ip":"************","dest_port":"8222"}
{"timestamp":"1646190996", "source_ip":"************","source_port":"48567","dest_ip":"***********","dest_port":"22"}
{"timestamp":"1646190996", "source_ip":"************","source_port":"51710","dest_ip":"************","dest_port":"443"}
{"timestamp":"1646190996", "source_ip":"************","source_port":"57478","dest_ip":"***********","dest_port":"8100"}
{"timestamp":"1646190996", "source_ip":"************","source_port":"57478","dest_ip":"***********","dest_port":"427"}
{"timestamp":"1646190996", "source_ip":"************","source_port":"51711","dest_ip":"************","dest_port":"443"}`

var curlAnalogData = `{"host":"***********:5000", "ip":"***********", "source_ip":"************", "method":"GET", "url":"http://***********:5000/cgi-bin/authLogin.cgi?sid=zvgdzorc&_dc=1646191010871", "post_data":"", "headers":"Host: ***********:5000\r\nConnection: keep-alive\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36\r\nX-Requested-With: XMLHttpRequest\r\nAccept: */*\r\nReferer: http://***********:5000/cgi-bin/\r\nAccept-Encoding: gzip, deflate\r\nAccept-Language: zh-CN,zh;q=0.9\r\nCookie: DESKTOP=1; nas_wfm_tree_x=200; QSYNC_USER=wangjing; QSYNC_SID=wz49w9ik; WINDOW_MODE=1; treeRootPathwangjing=/Public; NAS_PW_STATUS=0; NAS_USER=wangjing; NAS_SID=zvgdzorc; home=1; QT=1646190917869", "user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36", "cookie":""}
{"host":"************:9200", "ip":"************", "source_ip":"***********", "method":"GET", "url":"http://************:9200/fofaee_task_assets/ips/_search", "post_data":"", "headers":"Content-Type: application/json\r\nUser-Agent: Faraday v0.15.2\r\nAccept-Encoding: gzip;q=1.0,deflate;q=0.6,identity;q=0.3\r\nAccept: */*\r\nConnection: close\r\nHost: ************:9200\r\nContent-Length: 151", "user_agent":"Faraday v0.15.2", "cookie":""}
{"host":"************", "ip":"************", "source_ip":"************", "method":"POST", "url":"http://************/fofapro_subdomain%2Cfofapro_history_subdomain/_search?from=0&size=1000&_source=host%2Cdomain%2Cip%2Cport%2Cbanner%2Ctitle%2Cheader%2Cserver%2Clastchecktime%2Clastupdatetime%2Cbody%2Cgeoip%2Cos%2Cappserver%2Ctags%2Csubdomain%2Clanguage%2Ccert%2Cmac%2Cnetbios_name%2Cmiddleware%2Cprotocol%2Casn", "post_data":"7b22736f7274223a7b226970223a2264657363227d2c227175657279223a7b22636f6e7374616e745f73636f7265223a7b2266696c746572223a7b22626f6f6c223a7b226d757374223a5b7b226d617463685f706872617365223a7b22686f7374223a22666f66612e696e666f227d7d2c7b22626f6f6c223a7b2273686f756c64223a5b7b2272616e6765223a7b226c61737475706461746574696d65223a7b22677465223a22323031362d30312d30312030313a30303a3030227d7d7d2c7b22736372697074223a7b22736372697074223a7b22696e6c696e65223a22646f635b276c617374636865636b74696d65275d2e76616c7565203d3d20646f635b276c61737475706461746574696d65275d2e76616c7565227d7d7d5d7d7d5d7d7d7d7d7d", "headers":"Host: ************\r\nContent-Type: application/json\r\nAccept: application/json\r\nUser-Agent: elasticsearch-php/7.11.0 (Linux 3.10.0-327.el7.x86_64; PHP 7.2.1)\r\nx-elastic-client-meta: es=7.11.0,php=7.2.1,t=7.11.0,a=0,cu=7.29.0\r\nContent-Length: 292", "user_agent":"elasticsearch-php/7.11.0 (Linux 3.10.0-327.el7.x86_64; PHP 7.2.1)", "cookie":""}
{"host":"************:9200", "ip":"************", "source_ip":"***********", "method":"GET", "url":"http://************:9200/fofaee_task_assets/ips/_count", "post_data":"", "headers":"Content-Type: application/json\r\nUser-Agent: Faraday v0.15.2\r\nAccept-Encoding: gzip;q=1.0,deflate;q=0.6,identity;q=0.3\r\nAccept: */*\r\nConnection: close\r\nHost: ************:9200\r\nContent-Length: 33", "user_agent":"Faraday v0.15.2", "cookie":""}`
