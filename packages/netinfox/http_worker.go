package netinfox

import (
	"encoding/json"
	"net/url"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/model/m_netinfo"
	"git.gobies.org/foeye/foeye3/packages/util/timer"
)

// HttpRequestData 资产流量分析: url
type HttpRequestData struct {
	Host      string `json:"host"`
	IP        string `json:"ip"`
	SourceIP  string `json:"source_ip"`
	Method    string `json:"method"`
	Url       string `json:"url"`
	PostData  string `json:"post_data"`
	Headers   string `json:"headers"`
	UserAgent string `json:"user_agent"`
	Cookie    string `json:"cookie"`
}

// NetHttpkRun http-worker
func (info *NetInfoBackendAPI) NetHttpkRun() error {
	return info.Run(HttpInfoFilename, dealHttpLineConvertNetInfo)
}

// dealHttpLineConvertNetInfo 文件数据行处理
func dealHttpLineConvertNetInfo(line []byte, ips, ports map[string]bool) *m_netinfo.NetInfo {
	var data *HttpRequestData
	err := json.Unmarshal(line, &data)
	if err != nil || data.IP == "" || data.SourceIP == "" || data.Host == "" {
		log.Printf("  http-info proc-flowdata invalid json line:%s\n", string(line))
		return nil
	}
	parse, err := url.Parse(data.Url)
	if err != nil {
		log.Printf(" http-info proc-flowdata invalid json line:%s\n", string(line))
		return nil
	}
	netInfo := &m_netinfo.NetInfo{
		DestIp:        data.IP,
		SourcePort:    "",
		SourcePortTag: "-",
		Protocol:      "",
		ProtocolTag:   "-",
	}

	switch parse.Scheme {
	case "http":
		netInfo.DestPort = "80"
	case "https":
		netInfo.DestPort = "443"
	}

	netInfo.SourceIp = data.SourceIP
	if ips[data.SourceIP] {
		netInfo.SourceIpTag = "已识别"
	} else {
		netInfo.SourceIpTag = "未识别"
	}

	if data.IP == "" {
		netInfo.DestIpTag = "-"
	} else if ips[data.IP] {
		netInfo.DestIpTag = "已识别"
	} else {
		netInfo.DestIpTag = "未识别"
	}
	if netInfo.DestPort == "" {
		netInfo.DestPortTag = "-"
	} else {
		netInfo.DestPortTag = "预置"
	}
	if netInfo.SourcePort == "" {
		netInfo.SourcePortTag = "-"
	} else {
		netInfo.SourcePortTag = "预置"
	}

	netInfo.Date = timer.CurrentDatetimeStr()
	return netInfo
}
