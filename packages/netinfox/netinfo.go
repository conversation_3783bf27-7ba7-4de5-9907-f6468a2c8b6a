package netinfox

import (
	"bufio"
	"bytes"
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"net"
	"os"
	"os/exec"
	"strconv"
	"strings"

	"github.com/3th1nk/cidr"
	"github.com/olivere/elastic"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/model/m_ip_range"
	"git.gobies.org/foeye/foeye3/model/m_netinfo"
	"git.gobies.org/foeye/foeye3/model/m_system"
	"git.gobies.org/foeye/foeye3/packages/cidrx"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/fsx"
)

const (
	ConnectInfoFilename = "/dev/shm/connect_info_file" // 文件路径: "/dev/shm/connect_info_file"
	HttpInfoFilename    = "/dev/shm/url_file"          // 文件路径: "/dev/shm/url_file"
	ConnectESBulkNum    = 200                          // es批量插入数量/次
	DealChanNum         = 10                           // chan
)

type convertToNetInfo func(link []byte, ips, ports map[string]bool) *m_netinfo.NetInfo

// NetInfoBackendAPI provides handlers for net infos backend.
type NetInfoBackendAPI struct {
	DB        database.Factory
	ES        elasticx.Factory
	configure *config.Configure
}

// NewNetInfoBackendAPI creates a new NewNetInfoBackendAPI instance.
func NewNetInfoBackendAPI(configure *config.Configure, db database.Factory, es elasticx.Factory) (*NetInfoBackendAPI, error) {
	return &NetInfoBackendAPI{
		DB: db,
		ES: es,

		configure: configure,
	}, nil
}

func (info *NetInfoBackendAPI) IsNotExists(filename string) bool {
	return fsx.IsNotExists(filename)
}

// getSystemPorts 非预置端口
func (info *NetInfoBackendAPI) getSystemPorts() map[string]bool {
	list, _ := info.DB.ScanPort().GetItemsByCondition([]interface{}{"add_way = ?", "system"}...)
	if len(list) == 0 {
		return nil
	}
	ports := make(map[string]bool)
	for _, i := range list {
		if i.AddWay != "system" {
			continue
		}
		portStr := strconv.Itoa(int(i.Port))
		ports[portStr] = true
	}
	return ports
}

// getAssetIps 资产ip
func (info *NetInfoBackendAPI) getAssetIps() map[string]bool {
	ips, err := info.ES.Assets().GetSourceIPs()
	if err != nil || len(ips) == 0 {
		return nil
	}
	res := make(map[string]bool)
	for _, ip := range ips {
		res[ip] = true
	}
	return res
}

// readLineFromFileConvertToNetInfo 按行读取数据源文件
func readLineFromFileConvertToNetInfo(reader io.Reader, ips, ports map[string]bool, covertFunc convertToNetInfo, netInfoChan chan *m_netinfo.NetInfo) {
	br := bufio.NewReader(reader)
	for {
		line, _, err := br.ReadLine()
		if err == io.EOF {
			break
		}
		netInfo := covertFunc(line, ips, ports)
		if netInfo == nil {
			continue
		}
		netInfoChan <- netInfo
	}
	close(netInfoChan)
}

func sudoRenameFile(oldName, newName string) error {
	cmd := exec.Command("sudo", "mv", oldName, newName)
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Start()
	if err != nil {
		return err
	}
	//fmt.Println(cmd.Args)
	return cmd.Wait()
}

func sudoDeleteFile(name string) error {
	cmd := exec.Command("sudo", "rm", "-rf", name)
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Start()
	if err != nil {
		return err
	}
	//fmt.Println(cmd.Args)
	return cmd.Wait()
}

// Run 执行
func (info *NetInfoBackendAPI) Run(fileName string, covertFunc convertToNetInfo) error {
	newFilename := fmt.Sprintf("%s.txt", fileName)
	if info.IsNotExists(newFilename) {
		if info.IsNotExists(fileName) {
			return fmt.Errorf("file %s not exists", fileName)
		}
		err := sudoRenameFile(fileName, newFilename)
		//err := os.Rename(fileName, newFilename)
		if err != nil {
			return fmt.Errorf("move old file %s Failed, error: %v", fileName, err)
		}
	}
	sourceInfo, err := os.Open(newFilename)
	if err != nil {
		return fmt.Errorf("open file %s Failed, error: %v", newFilename, err)
	}
	defer func() {
		sourceInfo.Close()
		_ = sudoDeleteFile(newFilename)
		//os.Remove(newFilename)
	}()

	netInfoDataChannels := make(chan *m_netinfo.NetInfo, DealChanNum)
	go readLineFromFileConvertToNetInfo(sourceInfo, info.getAssetIps(), info.getSystemPorts(), covertFunc, netInfoDataChannels)

	bulkRequest := info.ES.Assets().GetEsClient().Bulk().Index(elasticx.IndexNameOfNetinfo()).Type(elasticx.TypeNameOfNetinfos)
	bulkNum := 0
	for infoDataChannel := range netInfoDataChannels {
		str := strings.Join([]string{
			infoDataChannel.DestIp,
			infoDataChannel.DestPort,
			infoDataChannel.SourcePort,
			infoDataChannel.SourceIp,
		}, ":")
		id := fmt.Sprintf("%x", md5.Sum([]byte(str))) // 指定id，有则update,无则create
		bulkRequest.Add(elastic.NewBulkIndexRequest().Id(id).Doc(infoDataChannel))
		if bulkNum > ConnectESBulkNum {
			bulkResponse, err := bulkRequest.Do(context.Background())
			if err != nil {
				return err
			}
			if bulkResponse == nil {
				return fmt.Errorf("bulk fofaee_netinfos failed: %v", err)
			}
			bulkRequest = info.ES.Assets().GetEsClient().Bulk()
			bulkNum = 0
			continue
		}
		bulkNum++
	}
	if bulkRequest.NumberOfActions() == 0 {
		return nil
	}
	bulkResponse, err := bulkRequest.Do(context.Background())
	if err != nil {
		return err
	}
	if bulkResponse == nil {
		return fmt.Errorf("bulk fofaee_netinfos failed: %v", err)
	}
	return nil
}

// CreateNetInfosIndex ...
func (info *NetInfoBackendAPI) CreateNetInfosIndex() error {
	// Exists
	indexExists, err := info.ES.Assets().GetEsClient().IndexExists(elasticx.IndexNameOfNetinfo()).Do(context.TODO())
	if err != nil {
		return err
	}
	// index不存在
	if !indexExists {
		// create index
		_, err = info.ES.Assets().GetEsClient().CreateIndex(elasticx.IndexNameOfNetinfo()).Body(settingsAndMappings).Do(context.Background())
		if err != nil {
			return err
		}
	}
	return nil
}

// LimitNetInfos 流量数据是否达到阈值
func (info *NetInfoBackendAPI) LimitNetInfos() error {
	searchResult, err := info.ES.Assets().GetEsClient().Search().Index(elasticx.IndexNameOfNetinfo()).Query(elastic.NewMatchAllQuery()).Do(context.Background())
	if err != nil {
		return err
	}
	if searchResult.TotalHits() > m_netinfo.MaxCountLimit {
		return fmt.Errorf("flow data reached max limit: 100 0000")
	}
	return nil
}

// IsFlowSwitchOn 是否开启流量
func (info *NetInfoBackendAPI) IsFlowSwitchOn() bool {
	flowSwitch, err := info.DB.System().GetSystemWorkerIp("flow_switch", "netinfo")
	if err != nil {
		return false
	}
	if flowSwitch.(*m_system.Systems).Value == "on" {
		return true
	}
	return false
}

type FlowConfig struct {
	FlowAutoScan  bool
	FlowSwitch    bool
	FlowAssetNum  int
	FlowBandwidth int
}

func (info *NetInfoBackendAPI) GetFlowConfig() *FlowConfig {
	m := make(map[string]string)
	m["flow_auto_scan"] = ""
	m["flow_bandwidth"] = ""
	m["flow_asset_num"] = ""
	m["flow_switch"] = ""
	for k, _ := range m {
		flow, err := info.DB.System().GetSystemWorkerIp(k, "netinfo")
		if err != nil {
			continue
		}
		m[k] = flow.(*m_system.Systems).Value
	}

	r := &FlowConfig{}
	if m["flow_switch"] != "" && m["flow_switch"] == "on" {
		r.FlowSwitch = true
	}
	if m["flow_auto_scan"] != "" && m["flow_auto_scan"] == "on" {
		r.FlowAutoScan = true
	}
	if m["flow_asset_num"] != "" {
		num, err := strconv.Atoi(m["flow_asset_num"])
		if err == nil {
			r.FlowAssetNum = 100
		} else {
			r.FlowAssetNum = num
		}
	}
	if m["flow_bandwidth"] != "" {
		num, err := strconv.Atoi(m["flow_asset_num"])
		if err != nil {
			r.FlowBandwidth = 300
		} else {
			r.FlowBandwidth = num
		}
	}

	return r
}

// GetIpMapFromIprangeMap ip ranges
func (info *NetInfoBackendAPI) GetIpMapFromIprangeMap() (map[string]bool, error) {
	list, err := info.DB.IPRange().GetList(0, 0)
	if err != nil {
		return nil, err
	}
	arr := make([]string, 0)
	for _, info := range list.([]*m_ip_range.IpRange) {
		// 验证IP地址是否带"/"，并处理带"/"的数据
		if strings.Contains(info.IpRange, "/") { // 验证IP地址是否带"/"
			cidr, err := cidr.ParseCIDR(info.IpRange)
			if err != nil {
				continue
			}
			if err := cidr.ForEachIP(func(ip string) error {
				arr = append(arr, ip)
				return nil
			}); err != nil {
				continue
			}
		} else if strings.Contains(info.IpRange, "-") { // 验证IP地址是否带"-"
			//处理 带-的IP段
			s := strings.Split(info.IpRange, "-")
			if len(s) != 2 {
				continue
			}
			ipS := strings.Split(s[0], ".")
			if len(ipS) != 4 {
				continue
			}
			ipS[3] = s[1]
			endIP := strings.Join(ipS, ".")
			cidrs, err := cidrx.IPv4RangeToCIDRRange(s[0], endIP)
			if err != nil {
				continue
			}
			for _, v := range cidrs {
				c, err := cidr.ParseCIDR(v)
				if err != nil {
					continue
				}
				if err := c.ForEachIP(func(ip string) error {
					arr = append(arr, ip)
					return nil
				}); err != nil {
					continue
				}
			}
		} else { // 单纯iｐ
			ip := net.ParseIP(info.IpRange)
			if ip.To4() != nil || ip.To16() != nil {
				arr = append(arr, info.IpRange)
			}
		}
	}

	m := make(map[string]bool)
	for _, v := range arr {
		if _, exi := m[v]; exi {
			continue
		}
		m[v] = true
	}

	return m, nil
}

var settingsAndMappings = `
{
  "settings": {
    "index": {
      "number_of_shards": "5",
      "analysis": {
        "analyzer": {
          "fofa_dot_analyzer": {
            "type": "custom",
            "char_filter": [
              "fofa_dot_to_space"
            ],
            "tokenizer": "fofa_dot_tokenizer"
          },
          "case_sensitive": {
            "filter": "lowercase",
            "type": "custom",
            "tokenizer": "keyword"
          }
        },
        "char_filter": {
          "fofa_dot_to_space": {
            "pattern": "[.:/]",
            "type": "pattern_replace",
            "replacement": " "
          }
        },
        "tokenizer": {
          "fofa_dot_tokenizer": {
            "type": "whitespace"
          }
        }
      },
      "number_of_replicas": "1"
    }
  },
  "mappings": {
    "netinfos": {
      "dynamic_templates": [
      {
        "string_fields": {
          "match": "*",
          "match_mapping_type": "string",
          "mapping": {
            "index": true,
            "norms": false,
            "type": "keyword"
          }
        }
      }
    ],
      "properties": {
        "date": {
          "type": "date",
          "format": "YYYY-MM-dd HH:mm:ss"
        },
        "dest_ip": {
          "type": "ip",
          "fields": {
            "ip_raw": {
              "type": "keyword"
            },
            "ipstr": {
              "type": "text",
              "analyzer": "fofa_dot_analyzer"
            }
          }
        },
        "dest_ip_tag": {
          "type": "keyword"
        },
        "dest_port": {
          "type": "keyword"
        },
        "dest_port_tag": {
          "type": "keyword"
        },
        "protocol": {
          "type": "keyword"
        },
        "protocol_tag": {
          "type": "keyword"
        },
        "source_ip": {
          "type": "ip",
          "fields": {
            "ip_raw": {
              "type": "keyword"
            },
            "ipstr": {
              "type": "text",
              "analyzer": "fofa_dot_analyzer"
            }
          }
        },
        "source_ip_tag": {
          "type": "keyword"
        },
        "source_port": {
          "type": "keyword"
        },
        "source_port_tag": {
          "type": "keyword"
        }
      }
    }
  }
}
`
