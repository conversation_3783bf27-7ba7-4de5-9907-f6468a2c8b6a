package netinfox

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/model/m_netinfo"
	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/util/timer"
)

const (
	DefaultEmptyValue = "-"
)

// ConnectData 资产流量分析: connect-info
type ConnectData struct {
	SourceIP   string `json:"source_ip"`
	SourcePort string `json:"source_port"`
	DestIP     string `json:"dest_ip"`
	DestPort   string `json:"dest_port"`
	DateStr    string `json:"timestamp"`
}

// NetInfoRun connect-worker
func (info *NetInfoBackendAPI) NetInfoRun() error {
	return info.Run(ConnectInfoFilename, dealConnectLineConvertNetInfo)
}

// dealConnectLineConvertNetInfo 文件数据行处理
func dealConnectLineConvertNetInfo(line []byte, ips, ports map[string]bool) *m_netinfo.NetInfo {
	var data *ConnectData
	err := json.Unmarshal(line, &data)
	if err != nil || data.SourcePort == "" || data.SourceIP == "" || data.DestIP == "" || data.DestPort == "" {
		log.Printf("http-info proc-flowdata invalid json line:%s\n", string(line))
		return nil
	}
	netInfo := &m_netinfo.NetInfo{
		DestIp:     data.DestIP,
		DestPort:   data.DestPort,
		SourcePort: data.SourcePort,
		SourceIp:   data.SourceIP,
	}

	if ips[data.SourceIP] {
		netInfo.SourceIpTag = "已识别"
	} else {
		netInfo.SourceIpTag = "未识别"
	}
	if ips[data.DestIP] {
		netInfo.DestIpTag = "已识别"
	} else {
		netInfo.DestIpTag = "未识别"
	}

	// 识别判断标签
	distTag := func(s string, ps map[string]bool) string {
		if s == "" {
			return DefaultEmptyValue
		} else if ps[s] {
			return "预置"
		} else {
			return "非标"
		}
	}
	if netInfo.SourcePort == "" {
		fmt.Println("-------------------")
	}
	netInfo.SourcePortTag = distTag(netInfo.SourcePort, ports)
	netInfo.DestPortTag = distTag(netInfo.DestPort, ports)
	netInfo.ProtocolTag = distTag(netInfo.Protocol, ports)

	if data.DateStr == "" {
		netInfo.Date = timer.CurrentDatetimeStr()
	} else {
		dataUnix, err := strconv.ParseInt(data.DateStr, 10, 64)
		if err != nil {
			netInfo.Date = timer.CurrentDatetimeStr()
		} else {
			netInfo.Date = time.Unix(dataUnix, 0).Format(constant.FormatChina24DateTime)
		}
	}
	return netInfo
}
