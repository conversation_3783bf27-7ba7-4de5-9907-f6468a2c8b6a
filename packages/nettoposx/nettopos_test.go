package nettoposx

import (
	"encoding/json"
	"fmt"
	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	elastic2 "git.gobies.org/foeye/foeye3/store/elastic"
	mysql2 "git.gobies.org/foeye/foeye3/store/mysql"
	"git.gobies.org/foeye/foeye3/store/storage"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"math/rand"
	"testing"
)

//func TestNetToposBackendAPI_Run(t *testing.T) {
//	// NewElasticDatabase database
//	db, err := database.NewGormDatabase()
//	if err != nil {
//		panic(errors.New(fmt.Sprintf("Database new has error:%+v", err)))
//	}
//
//	// NewGormDatabase elasticx client instance.
//	elastic, err := elasticx.NewElasticDatabase()
//	if err != nil {
//		panic(errors.New(fmt.Sprintf("failed to initialize elasticx client: %+v", err)))
//	}
//
//	nettopos, err := NewNetToposBackendAPI(db, elastic)
//	if err != nil {
//		panic(errors.New(fmt.Sprintf("failed to NewNetToposBackendAPI")))
//	}
//
//	nettopos.Run()
//}

func TestNetToposBackendAPI_Run(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()
	t.Run("GetIpArrayFromIprange err", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		store := &database.GormDatabase{DB: mockDb.MockGorm}

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		api, err := NewNetToposBackendAPI(store, esDB)
		defer gomonkey.ApplyMethodReturn(api, "GetIpArrayFromIprange", nil).Reset()
		err = api.Run()
		assert.EqualError(t, err, "no result from IP range")
	})
	t.Run("success case", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		store := &database.GormDatabase{DB: mockDb.MockGorm}

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		api, err := NewNetToposBackendAPI(store, esDB)
		defer gomonkey.ApplyMethodReturn(api, "GetIpArrayFromIprange", []string{"**********"}).Reset()
		defer gomonkey.ApplyMethod(api, "Assets", func() {}).Reset()
		err = api.Run()
		assert.NoError(t, err)
	})
}

func TestNetToposBackendAPI_Assets(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()
	t.Run("search err", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		store := &database.GormDatabase{DB: mockDb.MockGorm}

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		api, err := NewNetToposBackendAPI(store, esDB)
		defer gomonkey.ApplyMethodReturn(api.AssetDB.Client.Search(), "Do", nil, fmt.Errorf("mock err")).Reset()
		api.Assets("**********")
	})
	t.Run("search nothing", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		store := &database.GormDatabase{DB: mockDb.MockGorm}

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		api, err := NewNetToposBackendAPI(store, esDB)
		defer gomonkey.ApplyMethodReturn(api.AssetDB.Client.Search(), "Do", &elastic.SearchResult{
			Hits: &elastic.SearchHits{
				TotalHits: int64(0),
			},
		}, nil).Reset()
		api.Assets("**********")
	})
	t.Run("success case", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		store := &database.GormDatabase{DB: mockDb.MockGorm}

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		api, err := NewNetToposBackendAPI(store, esDB)
		value := m_asset.Buckets{
			Keys: []*m_asset.Bucket{
				{
					Key:   "视频监控",
					Count: 10,
				},
				{
					Key:   "企业资源计划系统（ERP）",
					Count: 10,
				},
			},
		}
		bucketData, err := json.Marshal(value)
		assert.NoError(t, err)

		item := m_asset.Asset{
			Ip: "**********",
		}
		itemData, err := json.Marshal(item)
		assert.NoError(t, err)

		defer gomonkey.ApplyMethodReturn(api.AssetDB.Client.Search(), "Do", &elastic.SearchResult{
			Hits: &elastic.SearchHits{
				TotalHits: int64(2),
				Hits: []*elastic.SearchHit{
					{
						Source: (*json.RawMessage)(&itemData),
					},
				},
			},
			Aggregations: elastic.Aggregations{
				"second_cat_tags": (*json.RawMessage)(&bucketData),
			},
		}, nil).Reset()
		api.Assets("**********")
	})
}

func Test_getRandSliceInt(t *testing.T) {
	idx := -1
	rint := []int{1, 2, 3, 4}
	defer gomonkey.ApplyFunc(rand.Intn, func(int) int {
		idx++
		return rint[idx]
	}).Reset()
	res := getRandSliceInt(10)
	assert.Equal(t, []int{1, 2, 3}, res)
}

func TestNetToposBackendAPI_dealData(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()
	t.Run("success case 1", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		store := &database.GormDatabase{DB: mockDb.MockGorm}

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		api, err := NewNetToposBackendAPI(store, esDB)

		rows := []string{"**********", "**********", "服务器ip"}
		ip := "**********"
		gateway := "**********"
		image := "test_image"
		ipRangeInfo := "test_iprange"
		targetTitle := "test_title"

		defer gomonkey.ApplyMethodReturn(api.DB, "CreateItemByInstance", nil).Reset()

		api.dealData(rows, ip, gateway, image, ipRangeInfo, targetTitle)

	})
	t.Run("success case 2", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		store := &database.GormDatabase{DB: mockDb.MockGorm}

		mockServer := elastic2.NewMockServer()
		defer mockServer.Close()
		esClient, _ := mockServer.NewElasticClient()
		esDB := &elasticx.ElasticDatabase{Client: esClient}

		api, err := NewNetToposBackendAPI(store, esDB)

		rows := []string{"**********", "**********", "服务器ip"}
		ip := "**********"
		gateway := "**********"
		image := "test_image"
		ipRangeInfo := "test_iprange"
		targetTitle := "test_title"

		defer gomonkey.ApplyMethodReturn(api.DB, "CreateItemByInstance", nil).Reset()

		api.dealData(rows, ip, gateway, image, ipRangeInfo, targetTitle)

	})
}
