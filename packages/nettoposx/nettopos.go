package nettoposx

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net"
	"os/exec"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/packages/cidrx"

	"git.gobies.org/foeye/foeye3/api/a_net_topos"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_ip_range"
	"git.gobies.org/foeye/foeye3/database/db_net_topos"
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_ip_range"
	"git.gobies.org/foeye/foeye3/model/m_net_topos"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"

	"github.com/3th1nk/cidr"
	"github.com/olivere/elastic"
)

var (
	categoryReverse = map[string]string{"视频监控": "视频专用网段", "数据库系统": "数据中心网段", "服务器": "数据中心网段", "企业资源计划系统（ERP）": "业务支撑网段", "办公自动化系统（OA）": "业务支撑网段", "财务管理系统（FMS）": "业务支撑网段", "人力资源管理系统（HRM）": "业务支撑网段", "客户关系管理系统（CRM）": "业务支撑网段", "供应链管理系统（SCM）": "业务支撑网段", "项目管理系统": "业务支撑网段", "电子邮件系统": "业务支撑网段"}

	categoryCount = map[string]int{"普通办公网段": 0, "视频专用网段": 0, "数据中心网段": 0, "业务支撑网段": 0}

	yewuCategory = map[string]bool{"企业资源计划系统（ERP）": true, "办公自动化系统（OA）": true, "财务管理系统（FMS）": true, "人力资源管理系统（HRM）": true, "客户关系管理系统（CRM）": true, "供应链管理系统（SCM）": true, "项目管理系统": true, "电子邮件系统": true}
)

func init() {
	rand.Seed(time.Now().Unix())
}

// AssetElasticsearchInter interface
type AssetElasticsearchInter interface {
	es_asset.ESAssetDatabaseInter
}

// NetToposBackendAPI provides handlers for net topos backend.
type NetToposBackendAPI struct {
	IpRangeDB database.GormIPRangeDatabaseStore
	DB        *db_net_topos.GormNetToposDatabase
	AssetDB   *elasticx.ElasticDatabase
}

// NewNetToposBackendAPI creates a new NewNetToposBackendAPI instance.
func NewNetToposBackendAPI(DB *database.GormDatabase, ES *elasticx.ElasticDatabase) (*NetToposBackendAPI, error) {
	db := new(db_net_topos.GormNetToposDatabase)
	db.Instance = DB

	ipRangeDB := new(db_ip_range.GormIPRangeDatabase)
	ipRangeDB.Instance = DB

	return &NetToposBackendAPI{
		DB:        db,
		IpRangeDB: ipRangeDB,
		AssetDB:   ES,
	}, nil
}

// Run ...
func (topos *NetToposBackendAPI) Run() error {
	ipArray := topos.GetIpArrayFromIprange()
	if ipArray == nil {
		return errors.New("no result from IP range")
	}
	_ = topos.DB.DeleteItems()

	for _, s := range ipArray {
		topos.Assets(s)
	}

	return nil
}

// Assets 根据ip 查资产
func (topos *NetToposBackendAPI) Assets(ip string) {
	query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("ip", ip))
	secondCategories := elastic.NewTermsAggregation().Field("rule_infos.second_cat_tag").Size(256)
	categories := elastic.NewTermsAggregation().Field("rule_infos.first_cat_tag").Size(1)
	searchResult, err := topos.AssetDB.Client.Search().
		Index(elasticx.IndexNameOfAsset()).
		Query(query).
		Aggregation("second_cat_tags", secondCategories).
		Aggregation("first_cat_tags", categories).
		Pretty(true).
		Size(256).
		Do(context.Background())
	if err != nil {
		log.Println(err.Error())
		return
	}
	totalHits := searchResult.TotalHits()
	if totalHits == 0 {
		return
	}
	secondCatTags := make([]string, 0)
	for key, rawMessage := range searchResult.Aggregations {
		if key == "second_cat_tags" {
			value := m_asset.UnmarshalJsonRawMessage(*rawMessage)
			if value == nil {
				continue
			}
			for _, bucket := range value {
				if v, ok := categoryReverse[bucket.Key]; ok {
					categoryCount[v] += bucket.Count
				}
				if _, ok := yewuCategory[bucket.Key]; ok {
					secondCatTags = append(secondCatTags, bucket.Key)
				}
			}
		}
	}
	// buckets := &m_asset.Buckets{Keys: nil}
	buckets := make([]*m_asset.Bucket, 0)
	for key, value := range categoryCount {
		buckets = append(buckets, &m_asset.Bucket{
			Key:   key,
			Count: value,
		})
	}
	b := &m_asset.Buckets{Keys: buckets}
	sort.Sort(b)
	ipRangeInfo, image := "", ""
	if len(secondCatTags) > 0 && float64(b.Keys[0].Count/int(totalHits)) > 0.4 && (b.Keys[0].Key == "视频专用网段" || b.Keys[0].Key == "数据中心网段") {
		ipRangeInfo = b.Keys[0].Key
	} else {
		if len(secondCatTags) > 5 {
			ipRangeInfo = "业务支撑网段"
		} else {
			ipRangeInfo = "普通办公网段"
		}
	}
	if v, ok := a_net_topos.TextToImage[ipRangeInfo]; ok {
		image = v
	} else {
		image = "/topo/wd-others.png"
	}
	ips := make([]string, 0)
	if totalHits <= 3 {
		for _, hit := range searchResult.Hits.Hits {
			var item *m_asset.Asset
			_ = json.Unmarshal(*hit.Source, &item)
			ips = append(ips, item.Ip)
		}
	} else {
		ipsIds := getRandSliceInt(int(totalHits))
		for _, id := range ipsIds {
			var item *m_asset.Asset
			_ = json.Unmarshal(*searchResult.Hits.Hits[id].Source, &item)
			ips = append(ips, item.Ip)
		}
	}
	topos.traceroute(ips, "", image, ipRangeInfo, ip)
	return
}

// getRandSliceInt 随机取3个数
func getRandSliceInt(max int) (res []int) {
	noRepeatMaps := make(map[int]bool)
	for i := 0; ; i++ {
		intn := rand.Intn(max)
		if len(noRepeatMaps) == 3 {
			break
		}
		if _, ok := noRepeatMaps[intn]; !ok {
			noRepeatMaps[intn] = true
			res = append(res, intn)
		}
	}
	return
}

// traceroute ...
func (topos *NetToposBackendAPI) traceroute(ips []string, gateway, image, ipRangeInfo, targetTitle string) {
	for _, ip := range ips {
		cmdLine := fmt.Sprintf(`traceroute -n -m15 %s | grep -v '\* \* \*' | grep -v '!H'`, ip)
		outputLines := tracerouteExec(cmdLine)

		reg := regexp.MustCompile(`(?:\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(?:\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(?:\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})`)
		findString := reg.FindString(outputLines[1])
		rows := []string{
			"服务器ip", findString,
		}
		if len(outputLines[1:]) > 1 {
			for _, s := range outputLines[1:] {
				findString = reg.FindString(s)
				if findString == "" {
					continue
				}
				rows = append(rows, findString)
			}
		}
		topos.dealData(rows, ip, gateway, image, ipRangeInfo, targetTitle)
		if len(outputLines) > 1 {
			break
		}
	}
}

// dealData ...
func (topos *NetToposBackendAPI) dealData(rows []string, ip, gateway, image, ipRangeInfo, targetTitle string) {
	if len(rows) <= 2 {
		return
	}
	targetTitleCopy := dealIp(ip)
	if targetTitle != targetTitleCopy {
		targetTitle = targetTitleCopy
	}
	if rows[0] == ip {
		sourceIp := rows[len(rows)-1]
		targetIp := ip
		sourceInfo := "路由"
		if sourceIp == "服务器ip" {
			sourceInfo = "中心"
		}
		toposModel := &m_net_topos.NetTopos{
			Source:       sourceIp,
			Target:       targetIp,
			PathCategory: "resolved",
			IsLast:       1,
			Owner:        ip,
			SourceInfo:   sourceInfo,
			TargetInfo:   ipRangeInfo,
			TargetTitle:  targetTitle,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}
		_ = topos.DB.CreateItemByInstance(toposModel)
		return
	}
	for i := 1; i < len(rows); i++ {
		sourceIp := rows[i-1]
		targetIp := rows[i]
		if sourceIp == targetIp {
			continue
		}
		sourceInfo := "路由"
		if sourceIp == "服务器ip" {
			sourceInfo = "中心"
		}
		isLast := 0
		targetInfo := "路由"

		if targetIp == ip {
			isLast = 1
			targetInfo = ipRangeInfo
		}
		toposModel := &m_net_topos.NetTopos{
			Source:       sourceIp,
			Target:       targetIp,
			PathCategory: "licensing",
			IsLast:       isLast,
			Owner:        ip,
			SourceInfo:   sourceInfo,
			TargetInfo:   targetInfo,
			TargetTitle:  targetTitle,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}
		_ = topos.DB.CreateItemByInstance(toposModel)
	}
	return
}

// dealIp fake ip to cidr
func dealIp(ip string) string {
	split := strings.Split(ip, ".")
	split[len(split)-1] = "0"
	return strings.Join(split, ".") + "/24"
}

// tracerouteExec exec traceroute
func tracerouteExec(cmdLine string) (res []string) {
	ctx, cancelFunc := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancelFunc()

	resp := make(chan struct{}, 1)
	go func(ctx context.Context) {
		cmd := exec.Command("bash", "-c", cmdLine)
		var stdout, stderr bytes.Buffer
		cmd.Stdout = &stdout // 标准输出
		cmd.Stderr = &stderr // 标准错误
		err := cmd.Run()
		if err != nil {
			log.Printf("cmd.Run() failed with %s\n", err)
			return
		}
		outStr, _ := string(stdout.Bytes()), string(stderr.Bytes())

		res = strings.Split(outStr, "\n")
		resp <- struct{}{}
	}(ctx)

	select {
	case <-ctx.Done():
		return nil
	case <-resp:
		return
	}
}

// GetIpArrayFromIprange ip range
func (topos *NetToposBackendAPI) GetIpArrayFromIprange() (cidrArr []string) {
	list, err := topos.IpRangeDB.GetList(0, 0)
	if err != nil {
		return
	}
	ipArr := make([]string, 0)
	for _, info := range list.([]*m_ip_range.IpRange) {
		cidr1, ipNet, err := net.ParseCIDR(info.IpRange)
		if err != nil {
			ip := net.ParseIP(info.IpRange)
			if ip == nil {
				ips := ipRangeToIp(info.IpRange)
				ipArr = append(ipArr, ips...)
			} else if ip.To4() != nil || ip.To16() != nil {
				ipArr = append(ipArr, info.IpRange)
			}
		} else {
			size, _ := ipNet.Mask.Size()
			if size == 24 {
				ipArr = append(ipArr, info.IpRange)
				cidrArr = append(cidrArr, info.IpRange)
			} else {
				ipArr = append(ipArr, cidr1.String())
			}
		}
	}
	if len(ipArr) > 0 {
		ipMaps := make(map[string]bool)
		for _, s := range ipArr {
			if ipMaps[s] {
				continue
			} else {
				ipMaps[s] = true
			}
		}
		for _, v := range cidrArr {
			c, _ := cidr.ParseCIDR(v)
			if err := c.ForEachIP(func(ip string) error {
				if ipMaps[ip] {
					delete(ipMaps, ip)
				}
				return nil
			}); err != nil {
				continue
			}
		}
		for key, _ := range ipMaps {
			cidrArr = append(cidrArr, key)
		}
		return
	}
	return
}

// GetIpMapFromIprange ip range
func (topos *NetToposBackendAPI) GetIpMapFromIprange() (map[string]bool, map[string]bool, error) {
	list, err := topos.IpRangeDB.GetList(0, 0)
	if err != nil {
		return nil, nil, err
	}
	ipArr := make([]string, 0)
	cidrArr := make([]string, 0)
	// 获取纯ip地址
	ips := make(map[string]bool)
	for _, info := range list.([]*m_ip_range.IpRange) {
		// 验证IP地址是否带/  处理带/的数据
		cidr1, ipNet, err := net.ParseCIDR(info.IpRange)

		if err != nil {
			// 验证IP地址是否带-,带-返回nil  处理
			ip := net.ParseIP(info.IpRange)
			if ip == nil {
				// 处理 带-的IP段
				ips := ipRangeToIp(info.IpRange)
				ipArr = append(ipArr, ips...)
			} else if ip.To4() != nil || ip.To16() != nil {
				ipArr = append(ipArr, info.IpRange)
				ips[info.IpRange] = true
			}
		} else {
			// 处理 带/的IP段
			// fmt.Println("带/的处理", info.IpRange, cidr1, ipNet) // ************/24 ************ **********/24
			size, _ := ipNet.Mask.Size()
			if size == 24 {
				ipArr = append(ipArr, info.IpRange)
				cidrArr = append(cidrArr, info.IpRange) // 将************/24 数据添加到数组中
			} else {
				ipArr = append(ipArr, cidr1.String())
			}
		}
	}
	// fmt.Println(ipArr, cidrArr) //[************ *********** *********** ************/24] [************/24]
	ipMaps := make(map[string]bool)
	if len(ipArr) > 0 {

		for _, s := range ipArr {
			if ipMaps[s] {
				continue
			} else {
				ipMaps[s] = true
			}
		}
		for _, v := range cidrArr {
			c, _ := cidr.ParseCIDR(v)
			// fmt.Println("c", *c) //  ************ **********/24
			if err := c.ForEachIP(func(ip string) error {
				if !ipMaps[ip] {
					ipMaps[ip] = true
				}
				return nil
			}); err != nil {
				continue
			}
		}
	}
	return ipMaps, ips, nil
}

// GetIpMapFromIprange2 ip range 2.0
func (topos *NetToposBackendAPI) GetIpMapFromIprange2() (map[string]bool, error) {
	list, err := topos.IpRangeDB.GetList(0, 0)
	if err != nil {
		return nil, err
	}
	arr := make([]string, 0)

	// 判断是否为一个ip
	isNotIP := func(ip string) bool {
		return net.ParseIP(ip) == nil
	}

	for _, info := range list.([]*m_ip_range.IpRange) {
		// 验证IP地址是否带"/"，并处理带"/"的数据
		if strings.Contains(info.IpRange, "/") { // 验证IP地址是否带"/"
			cidr, err := cidr.ParseCIDR(info.IpRange)
			if err != nil {
				continue
			}
			if err := cidr.ForEachIP(func(ip string) error {
				arr = append(arr, ip)
				return nil
			}); err != nil {
				continue
			}
		} else if strings.Contains(info.IpRange, "-") { // 验证IP地址是否带"-"
			// 判断尾部是否为* (192.168.1-10*)
			l := len(info.IpRange)
			// 处理 带-的IP段
			s := strings.Split(info.IpRange, "-")
			if len(s) != 2 {
				continue
			}
			if info.IpRange[l-1:] == "*" && isNotIP(s[0]) && len(s[0]) == 3 {
				ip1 := s[0] + ".0"
				if isNotIP(ip1) {
					continue
				}
				endStrS := strings.Split(s[1], ".")
				if len(endStrS) != 2 {
					continue
				}
				ip2 := s[0] + "." + endStrS[0]
				if isNotIP(ip2) {
					continue
				}
				cidrs, err := cidrx.IPv4RangeToCIDRRange(ip1, ip2)
				if err != nil {
					continue
				}
				arr = append(arr, cidrx.CIDRs2IPs(cidrs)...)

			} else {
				if isNotIP(s[0]) {
					continue
				}
				endIP := ""
				if isNotIP(s[1]) {
					ipS := strings.Split(s[0], ".")
					if len(ipS) != 4 {
						continue
					}
					ipS[3] = s[1]
					endIP = strings.Join(ipS, ".")
				} else {
					endIP = s[1]
				}
				if isNotIP(endIP) {
					continue
				}
				cidrs, err := cidrx.IPv4RangeToCIDRRange(s[0], endIP)
				if err != nil {
					continue
				}
				arr = append(arr, cidrx.CIDRs2IPs(cidrs)...)
			}

		} else { // 单纯iｐ
			ip := net.ParseIP(info.IpRange)
			if ip.To4() != nil || ip.To16() != nil {
				arr = append(arr, info.IpRange)
			}
		}
	}

	m := make(map[string]bool)
	for _, v := range arr {
		if _, exi := m[v]; exi {
			continue
		}
		m[v] = true
	}

	return m, nil
}

// ipRangeToIp ...
func ipRangeToIp(ipRange string) (res []string) {
	if strings.Contains(ipRange, "-") {
		ips := strings.Split(ipRange, "-")
		ip0, ip1 := net.ParseIP(ips[0]), net.ParseIP(ips[1])
		if ip0 == nil {
			return nil
		}
		end := 0
		ips0 := strings.Split(ips[0], ".")
		start, err := strconv.Atoi(ips0[len(ips0)-1])
		if err != nil {
			return nil
		}
		if ip1 == nil {
			end, err = strconv.Atoi(ips[1])
			if err != nil {
				return nil
			}
		} else {
			ips1 := strings.Split(ips[1], ".")
			end, err = strconv.Atoi(ips1[len(ips1)-1])

		}
		prefix := strings.Join(ips0[:len(ips0)-1], ".")
		for i := start; i <= end; i++ {
			ip := prefix + "." + strconv.Itoa(i)
			res = append(res, ip)
		}
	}
	return res
}
