package permission

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/api/a_common"
	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_ip_range"
	"git.gobies.org/foeye/foeye3/database/db_user"
	"git.gobies.org/foeye/foeye3/model/m_ip_range"
	"git.gobies.org/foeye/foeye3/model/m_user"
	"git.gobies.org/foeye/foeye3/packages/ipx"
	"git.gobies.org/foeye/foeye3/packages/secure/token"
	"git.gobies.org/foeye/foeye3/packages/stringsx"

	"github.com/casbin/casbin/v2"
	"github.com/casbin/casbin/v2/model"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	"gorm.io/gorm"
)

// 角色==权限
const (
	AssetScan           = "asset_scan"
	VulScan             = "vul_scan"
	Compliance          = "compliance"
	Base                = "base"
	Administrator       = "administrator"
	SysAdministrator    = "sys_administrator"
	SauditAdministrator = "saudit_administrator"
)

// permission 实体
var permission *casbin.Enforcer
var gormDB *gorm.DB

// NewPermission 初始化model和策略
func NewPermission(configure *config.Configure, db *gorm.DB) error {
	// 初始化模型
	m := initModel()
	// 初始化策略
	gormDB = db
	p, err := initPolicy(db)
	if err != nil {
		return err
	}
	permission, err = casbin.NewEnforcer(m, p)
	if err != nil {
		return err
	}
	// 初始化管理员权限
	permission.AddRoleForUser(configure.Database.DefaultUsername, Administrator)
	permission.AddRoleForUser(configure.Database.DefaultServiceUsername, SysAdministrator)
	permission.AddRoleForUser("saudit", SauditAdministrator)
	return nil
}

// InitModel 初始化模型
func initModel() model.Model {
	m := model.NewModel()
	m.AddDef("r", "r", "sub, obj, act")
	m.AddDef("p", "p", "sub, obj, act")
	m.AddDef("g", "g", "_, _")
	m.AddDef("e", "e", "some(where (p.eft == allow))")
	m.AddDef("m", "m", "g(r.sub,p.sub) && keyMatch2(r.obj,p.obj) && regexMatch(r.act,p.act) || r.sub == 'administrator'")
	return m
}

// InitPolicy 初始化策略
func initPolicy(db *gorm.DB) (*gormadapter.Adapter, error) {

	a, err := gormadapter.NewAdapterByDB(db)
	if err != nil {
		return nil, err
	}
	// 初始化管理员权限
	initAdministratorPolicy(a)
	// 初始化只读权限
	initBasePolicy(a)
	// 初始化资产扫描权限
	initAssetScanPolicy(a)
	// 初始化漏洞扫描权限
	initVulScanPolicy(a)
	// 初始化合规检测权限
	initCompliancePolicy(a)
	// 初始化服务管理员权限
	initSysAdministratorPolicy(a)
	// 初始化审计管理员权限
	initSauditAdministratorPolicy(a)

	return a, nil
}

// initAdministratorPolicy 初始化管理员策略
func initAdministratorPolicy(p *gormadapter.Adapter) {
	// 管理员角色
	p.AddPolicy("p", "p", []string{Administrator, ".*?", ".*?"})
}

// initBasePolicy 初始化基础策略
func initBasePolicy(p *gormadapter.Adapter) {
	// 只读角色
	policys := make([][]string, 0)
	policys = append(policys, []string{Base, "/v3/captcha", "GET"})
	policys = append(policys, []string{Base, "/v3/systems/expiration_times", "GET"})
	policys = append(policys, []string{Base, "/v3/systems/hardware_info_change", "GET"})
	policys = append(policys, []string{Base, "/v3/network/is_docker", "GET"})
	policys = append(policys, []string{Base, "/v3/user_auth/info", "GET"})
	policys = append(policys, []string{Base, "/v3/user_auth/login", "GET"})
	policys = append(policys, []string{Base, "/v3/users/update_key", "POST"})
	policys = append(policys, []string{Base, "/v3/user_auth/logout", "POST"})
	policys = append(policys, []string{Base, "/v3/user_auth/change_password", "POST"})
	policys = append(policys, []string{Base, "/v3/systems/hardware_info", "GET"})
	policys = append(policys, []string{Base, "/v3/systems/get_blocktime", "GET"})
	policys = append(policys, []string{Base, "/v3/tasks/running", "GET"})
	policys = append(policys, []string{Base, "/v3/tasks/summaries/*", "GET"})
	policys = append(policys, []string{Base, "/v3/tasks/templates*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Base, "/v3/tasks/templates/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Base, "/v3/overviews", "GET"})
	policys = append(policys, []string{Base, "/v3/overviews/assets_pie", "GET"})
	policys = append(policys, []string{Base, "/v3/overviews/asset_trend_statistics*", "GET"})
	policys = append(policys, []string{Base, "/v3/overviews/asset_vulnerability_statistics*", "GET"})
	policys = append(policys, []string{Base, "/v3/overviews/body", "GET"})
	policys = append(policys, []string{Base, "/v3/overviews/header", "GET"})
	policys = append(policys, []string{Base, "/v3/assets", "GET"})
	policys = append(policys, []string{Base, "/v3/assets/ip*", "GET"})
	policys = append(policys, []string{Base, "/v3/assets/details/*", "GET"})
	policys = append(policys, []string{Base, "/v3/assets/details*", "GET"})
	policys = append(policys, []string{Base, "/v3/assets/subdomain_body*", "GET"})
	policys = append(policys, []string{Base, "/v3/assets/business*", "GET"})
	policys = append(policys, []string{Base, "/v3/assets/domain*", "GET"})
	policys = append(policys, []string{Base, "/v3/assets/statistics", "GET"})
	policys = append(policys, []string{Base, "/v3/assets/filter_condition", "GET"})
	policys = append(policys, []string{Base, "/v3/threats/fields", "(GET)|(PUT)"})
	policys = append(policys, []string{Base, "/v3/threats*", "GET"})
	policys = append(policys, []string{Base, "/v3/threats/repair_status", "GET"})
	policys = append(policys, []string{Base, "/v3/threats/ip", "GET"})
	policys = append(policys, []string{Base, "/v3/threats/threat", "GET"})
	policys = append(policys, []string{Base, "/v3/threats/search*", "GET"})
	policys = append(policys, []string{Base, "/v3/areas", "GET"})
	policys = append(policys, []string{Base, "/v3/static/*", "GET"})
	policys = append(policys, []string{Base, "/v3/violations/get_repair_status", "GET"})
	policys = append(policys, []string{Base, "/v3/compliance_monitors*", "GET"})
	policys = append(policys, []string{Base, "/v3/compliance_monitors/*", "GET"})
	policys = append(policys, []string{Base, "/v3/compliance_examine/*", "GET"})
	policys = append(policys, []string{Base, "/v3/config", "GET"})
	policys = append(policys, []string{Base, "/v3/tags", "GET"})
	policys = append(policys, []string{Base, "/v3/public/manual/*", "GET"})
	policys = append(policys, []string{Base, "/v3/down_load_records*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Base, "/v3/down_load_records/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Base, "/v3/user_rules/software_hardware", "GET"})
	policys = append(policys, []string{Base, "/v3/scan_ports/protocols", "GET"})
	for _, policy := range policys {
		p.AddPolicy("p", "p", policy)
	}
}

// initAssetScanPolicy 初始化资产扫描策略
func initAssetScanPolicy(p *gormadapter.Adapter) {
	// 资产扫描角色
	// 基础权限
	policys := make([][]string, 0)
	policys = append(policys, []string{AssetScan, "/v3/user_auth/login", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/systems/expiration_times", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/captcha", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/user_auth/info", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/network/is_docker", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/systems/hardware_info", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/systems/get_blocktime", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/running", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/overviews", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/overviews/assets_pie", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/overviews/asset_trend_statistics*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/overviews/asset_vulnerability_statistics*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/overviews/body", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/overviews/header", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/systems/hardware_info_change", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/users/update_key", "POST"})
	policys = append(policys, []string{AssetScan, "/v3/user_auth/change_password", "POST"})
	policys = append(policys, []string{AssetScan, "/v3/user_auth/logout", "POST"})
	policys = append(policys, []string{AssetScan, "/v3/areas", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/static/*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/systems/auto_del_tasks_settings", "(GET)|(POST)"})
	policys = append(policys, []string{AssetScan, "/v3/config", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/public/manual/*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/report_types*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{AssetScan, "/v3/report_types/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{AssetScan, "/v3/reports*", "(GET)|(POST)|(DELETE)"})
	policys = append(policys, []string{AssetScan, "/v3/reports/*", "(GET)|(POST)|(DELETE)"})
	policys = append(policys, []string{AssetScan, "/v3/task_report/*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/public/report/*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/assets*", "(GET)|(DELETE)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/assets/*", "(GET)|(DELETE)"})
	policys = append(policys, []string{AssetScan, "/v3/assets/table_view", "PUT"})
	policys = append(policys, []string{AssetScan, "/v3/assets/export*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/assets/export/ip_port*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/assets/subdomain_body*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/assets/business*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/assets/domain*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/assets/tags", "PUT"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/finished*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/finished/*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/import_example*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/ip_ranges*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/ports*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/position_change*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/running*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/task_timers*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/task_timers/*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/taskings*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/taskings/*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/upload*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/\\d+", "DELETE"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/summaries/*", "GET"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/templates*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{AssetScan, "/v3/tasks/templates/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{AssetScan, "/v3/down_load_records*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{AssetScan, "/v3/down_load_records/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	for _, policy := range policys {
		p.AddPolicy("p", "p", policy)
	}
}

// initVulScanPolicy 初始化漏洞扫描策略
func initVulScanPolicy(p *gormadapter.Adapter) {
	// 漏洞扫描角色
	// 基础权限
	policys := make([][]string, 0)
	policys = append(policys, []string{VulScan, "/v3/user_auth/login", "GET"})
	policys = append(policys, []string{VulScan, "/v3/systems/expiration_times", "GET"})
	policys = append(policys, []string{VulScan, "/v3/captcha", "GET"})
	policys = append(policys, []string{VulScan, "/v3/user_auth/info", "GET"})
	policys = append(policys, []string{VulScan, "/v3/network/is_docker", "GET"})
	policys = append(policys, []string{VulScan, "/v3/systems/hardware_info", "GET"})
	policys = append(policys, []string{VulScan, "/v3/systems/get_blocktime", "GET"})
	policys = append(policys, []string{VulScan, "/v3/tasks/running", "GET"})
	policys = append(policys, []string{VulScan, "/v3/overviews", "GET"})
	policys = append(policys, []string{VulScan, "/v3/overviews/assets_pie", "GET"})
	policys = append(policys, []string{VulScan, "/v3/overviews/asset_trend_statistics*", "GET"})
	policys = append(policys, []string{VulScan, "/v3/overviews/asset_vulnerability_statistics*", "GET"})
	policys = append(policys, []string{VulScan, "/v3/overviews/body", "GET"})
	policys = append(policys, []string{VulScan, "/v3/overviews/header", "GET"})
	policys = append(policys, []string{VulScan, "/v3/systems/hardware_info_change", "GET"})
	policys = append(policys, []string{VulScan, "/v3/users/update_key", "POST"})
	policys = append(policys, []string{VulScan, "/v3/user_auth/change_password", "POST"})
	policys = append(policys, []string{VulScan, "/v3/user_auth/logout", "POST"})
	policys = append(policys, []string{VulScan, "/v3/systems/auto_del_tasks_settings", "(GET)|(POST)"})
	policys = append(policys, []string{VulScan, "/v3/config", "GET"})
	policys = append(policys, []string{VulScan, "/v3/public/manual/*", "GET"})
	policys = append(policys, []string{VulScan, "/v3/report_types*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{VulScan, "/v3/report_types/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{VulScan, "/v3/reports*", "(GET)|(POST)|(DELETE)"})
	policys = append(policys, []string{VulScan, "/v3/reports/*", "(GET)|(POST)|(DELETE)"})
	policys = append(policys, []string{VulScan, "/v3/task_report/*", "GET"})
	policys = append(policys, []string{VulScan, "/v3/public/report/*", "GET"})
	policys = append(policys, []string{VulScan, "/v3/threats*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/threats/*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/ports*", "GET"})
	policys = append(policys, []string{VulScan, "/v3/tasks/finished*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/finished/*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/import_example*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/ip_ranges*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/pocs*", "GET"})
	policys = append(policys, []string{VulScan, "/v3/tasks/position_change*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/running*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/task_timers*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/task_timers/*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/taskings*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/taskings/*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/upload*", "(GET)|(DELETE)|(POST)|(PUT)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/\\d+", "DELETE"})
	policys = append(policys, []string{VulScan, "/v3/tasks/summaries/*", "GET"})
	policys = append(policys, []string{VulScan, "/v3/tasks/templates*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{VulScan, "/v3/tasks/templates/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{VulScan, "/v3/down_load_records*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{VulScan, "/v3/down_load_records/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	for _, policy := range policys {
		p.AddPolicy("p", "p", policy)
	}
}

// initCompliancePolicy 初始化合规检测策略
func initCompliancePolicy(p *gormadapter.Adapter) {
	// 合规检测角色
	// 基础权限
	policys := make([][]string, 0)
	policys = append(policys, []string{Compliance, "/v3/user_auth/login", "GET"})
	policys = append(policys, []string{Compliance, "/v3/systems/expiration_times", "GET"})
	policys = append(policys, []string{Compliance, "/v3/captcha", "GET"})
	policys = append(policys, []string{Compliance, "/v3/user_auth/info", "GET"})
	policys = append(policys, []string{Compliance, "/v3/network/is_docker", "GET"})
	policys = append(policys, []string{Compliance, "/v3/systems/hardware_info", "GET"})
	policys = append(policys, []string{Compliance, "/v3/systems/get_blocktime", "GET"})
	policys = append(policys, []string{Compliance, "/v3/tasks/running", "GET"})
	policys = append(policys, []string{Compliance, "/v3/overviews", "GET"})
	policys = append(policys, []string{Compliance, "/v3/overviews/assets_pie", "GET"})
	policys = append(policys, []string{Compliance, "/v3/overviews/asset_trend_statistics*", "GET"})
	policys = append(policys, []string{Compliance, "/v3/overviews/asset_vulnerability_statistics*", "GET"})
	policys = append(policys, []string{Compliance, "/v3/overviews/body", "GET"})
	policys = append(policys, []string{Compliance, "/v3/overviews/header", "GET"})
	policys = append(policys, []string{Compliance, "/v3/systems/hardware_info_change", "GET"})
	policys = append(policys, []string{Compliance, "/v3/users/update_key", "POST"})
	policys = append(policys, []string{Compliance, "/v3/user_auth/change_password", "POST"})
	policys = append(policys, []string{Compliance, "/v3/user_auth/logout", "POST"})
	policys = append(policys, []string{Compliance, "/v3/config", "GET"})
	policys = append(policys, []string{Compliance, "/v3/public/manual/*", "GET"})
	policys = append(policys, []string{Compliance, "/v3/compliance_monitors*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Compliance, "/v3/compliance_monitors/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Compliance, "/v3/compliance_examine/*", "(GET)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Compliance, "/v3/violations/*", "(GET)|(POST)"})
	policys = append(policys, []string{Compliance, "/v3/tags", "GET"})
	policys = append(policys, []string{Compliance, "/v3/ip_ranges", "GET"})
	policys = append(policys, []string{Compliance, "/v3/tasks/templates*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Compliance, "/v3/tasks/templates/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Compliance, "/v3/down_load_records*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Compliance, "/v3/down_load_records/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{Compliance, "/v3/user_rules/software_hardware", "GET"})
	policys = append(policys, []string{Compliance, "/v3/scan_ports/protocols", "GET"})
	for _, policy := range policys {
		p.AddPolicy("p", "p", policy)
	}
}

// initSysAdministratorPolicy 初始化服务管理员策略
func initSysAdministratorPolicy(p *gormadapter.Adapter) {
	policys := make([][]string, 0)
	policys = append(policys, []string{SysAdministrator, "/v3/log/download_logs", "GET"})
	policys = append(policys, []string{SysAdministrator, "/v3/network/is_docker", "GET"})
	policys = append(policys, []string{SysAdministrator, "/v3/systems/hardware_info", "GET"})
	policys = append(policys, []string{SysAdministrator, "/v3/systems/hardware_info_change", "GET"})
	policys = append(policys, []string{SysAdministrator, "/v3/systems/get_blocktime", "GET"})
	policys = append(policys, []string{SysAdministrator, "/v3/tasks/running", "GET"})
	policys = append(policys, []string{SysAdministrator, "/v3/systems/upgrade_log", "GET"})
	policys = append(policys, []string{SysAdministrator, "/v3/systems/info", "(GET)|(PUT)"})
	policys = append(policys, []string{SysAdministrator, "/v3/tcpdump*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/tcpdump/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/tcpdump/elastic/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/tcpdump/start/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/microkernels*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/microkernels/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/product_inspection*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/product_inspection/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/services*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/users*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/services/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/users/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SysAdministrator, "/v3/user_auth/change_password", "POST"})
	policys = append(policys, []string{SysAdministrator, "/v3/tasks/taskings/\\d+/force", "DELETE"})
	for _, policy := range policys {
		p.AddPolicy("p", "p", policy)
	}
}

// initSysAdministratorPolicy 初始化服务管理员策略
func initSauditAdministratorPolicy(p *gormadapter.Adapter) {
	policys := make([][]string, 0)

	policys = append(policys, []string{SauditAdministrator, "/v3/traces*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SauditAdministrator, "/v3/traces/*", "(GET)|(POST)|(PUT)|(DELETE)"})
	policys = append(policys, []string{SauditAdministrator, "/v3/user_auth/change_password", "POST"})
	policys = append(policys, []string{SauditAdministrator, "/v3/network/is_docker", "GET"})
	policys = append(policys, []string{SauditAdministrator, "/v3/systems/hardware_info", "GET"})
	policys = append(policys, []string{SauditAdministrator, "/v3/systems/hardware_info_change", "GET"})
	policys = append(policys, []string{SauditAdministrator, "/v3/systems/get_blocktime", "GET"})
	policys = append(policys, []string{SauditAdministrator, "/v3/tasks/running", "GET"})

	for _, policy := range policys {
		p.AddPolicy("p", "p", policy)
	}
}

// CheckPermission 检查权限
func CheckPermission(username, url, option string) bool {
	res, err := permission.Enforce(username, url, option)
	if err != nil {
		log.Println(err)
		return false
	}
	return res
}

// GrantRole 给用户分配角色
func GrantRole(username string, roles ...string) error {
	// 授予基础权限
	if len(roles) == 0 {
		roles = append(roles, Base)
	}
	_, err := permission.AddRolesForUser(username, roles)
	permission.LoadPolicy()
	return err
}

// UpdateGrantRole 更新用户和角色的绑定
func UpdateGrantRole(oldUsername, newUsername string, roles ...string) error {
	// 删除旧的用户和角色绑定
	_, err := permission.DeleteUser(oldUsername)
	if err != nil {
		return err
	}
	if len(roles) == 0 {
		roles = append(roles, Base)
	}
	// 新增新用户和角色的绑定
	_, err = permission.AddRolesForUser(newUsername, roles)
	permission.LoadPolicy()
	return err
}

// DeleteGrantRole 删除用户和角色的绑定
func DeleteGrantRole(username string) error {
	_, err := permission.DeleteUser(username)
	permission.LoadPolicy()
	return err
}

// GetIpRange 获取IP管理范围
func GetIpRange(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client) (ipRangeContent []string, ipRangeType string, ipRangeIds []string, ipRangeTagIds []string, err error) {
	// 获取用户基本信息
	instance := new(database.GormDatabase)
	instance.DB = gormDB
	userDB := db_user.NewGormUserDatabase(configure, instance)
	ipRangeConditionDB := db_ip_range.GormIPRangeDatabase{instance}

	claims, err := token.GetUserInfo(ctx, configure, redisclient)
	if success := a_common.SuccessOrAbort(ctx, http.StatusBadRequest, err); !success {
		return nil, "", nil, nil, err
	}

	user, err := userDB.GetUserByID(claims.ID)
	if err != nil {
		return nil, "", nil, nil, err
	}

	if user.IPRangeType == "all_ips" {
		// //全部ip
		// ipRangesRaw, err := ipRangeConditionDB.GetList(0, 0)
		// if err != nil {
		//	return nil, "", nil, err
		// }
		// ipRanges, ok := ipRangesRaw.([]*m_ip_range.IpRange)
		// if !ok {
		//	return nil, "", nil, errors.New("类型错误")
		// }
		// for _, ipRange := range ipRanges {
		//	ipRangeContent = append(ipRangeContent, ipRange.IpRange)
		// }
	} else if user.IPRangeType == "ip_ranges" {
		// 根据ip段
		// 从ip_range_conditions中获取
		// ip_range_conditionable_type:ComplianceMonitoring::Rule
		// ip_range_conditionable_id:规则ID
		//category:"ip_ranges"
		ipRangeConditionsRaw, err := ipRangeConditionDB.GetIpRangeConditions("User", user.IPRangeType, user.ID)
		if err != nil {
			return nil, "", nil, nil, err
		}
		ipRangeConditions, ok := ipRangeConditionsRaw.([]m_ip_range.IPRangeConditions)
		if !ok {
			return nil, "", nil, nil, errors.New("数据类型错误")
		}
		for _, val := range ipRangeConditions {
			ipRangeIds = append(ipRangeIds, val.ValId)
			if ipRangeID, err := strconv.Atoi(val.ValId); err == nil {
				ipRangeRaw, err := ipRangeConditionDB.GetItemByID(uint(ipRangeID))
				if err != nil {
					continue
				}
				ipRange, ok := ipRangeRaw.(*m_ip_range.IpRange)
				if !ok {
					continue
				}
				ipRangeContent = append(ipRangeContent, ipRange.IpRange)
			}
		}
	} else {
		// 标签
		// 根据【规则ID】【category】【ip_range_conditionable_type】从ip_range_conditions中找到tag_id
		ipRangeConditionsRaw, err := ipRangeConditionDB.GetIpRangeConditions("User", user.IPRangeType, user.ID)
		if err != nil {
			return nil, "", nil, nil, err
		}
		ipRangeConditions, ok := ipRangeConditionsRaw.([]m_ip_range.IPRangeConditions)
		if !ok {
			return nil, "", nil, nil, errors.New("数据类型错误")
		}
		for _, val := range ipRangeConditions {
			ipRangeTagIds = append(ipRangeTagIds, val.ValId)
			if ipRangeTagsID, err := strconv.Atoi(val.ValId); err == nil {
				// 根据【tag_id】从ip_range_tags中找到ip_range的id
				ipRangeTagsRaw, err := ipRangeConditionDB.GetIpRangeTagsByTagID(uint(ipRangeTagsID))
				if err != nil {
					continue
				}
				ipRangeTags, ok := ipRangeTagsRaw.([]m_ip_range.IpRangeTags)
				if !ok {
					continue
				}
				for _, ipRangeTag := range ipRangeTags {
					// 根据【ip_range的id】从ip_ranges中获取ip_range
					ipRangeIds = append(ipRangeIds, fmt.Sprintf("%d", ipRangeTag.IpRangeID))
					ipRangeRaw, err := ipRangeConditionDB.GetItemByID(uint(ipRangeTag.IpRangeID))
					if err != nil {
						continue
					}
					if ipRange, ok := ipRangeRaw.(*m_ip_range.IpRange); ok {
						ipRangeContent = append(ipRangeContent, ipRange.IpRange)
					}
				}
			}
		}
	}
	return ipRangeContent, user.IPRangeType, ipRangeIds, ipRangeTagIds, nil
}

// CheckIpRange 检查IP管理范围
func CheckIpRange(ipRangeList []string, userId uint) bool {
	managerIpRanges, err := GetIpRangeByUser(userId)
	if err != nil {
		log.Println("获取IP段范围失败", err)
		return true
	}
	managerSignalIpList := make([]string, 0)
	for _, tmp := range managerIpRanges {
		tmpList, err := ipx.Handler(tmp)
		if err != nil {
			log.Println("IP段转单个IP的列表", err)
			continue
		}
		managerSignalIpList = append(managerSignalIpList, tmpList...)
	}
	signalIpList := make([]string, 0)
	for _, tmp := range ipRangeList {
		tmpList, err := ipx.Handler(tmp)
		if err != nil {
			log.Println("IP段转单个IP的列表", err)
			continue
		}
		signalIpList = append(signalIpList, tmpList...)
	}

	res := stringsx.Difference(signalIpList, managerSignalIpList)
	if len(res) > 0 {
		return false
	}
	return true
}

// GetIpRangeByUser 通过用户名称获取管理范围
func GetIpRangeByUser(userId uint) ([]string, error) {
	ipRangeList := make([]string, 0)
	user := new(m_user.User)
	err := gormDB.Find(&user, userId).Error
	if err != nil {
		return nil, err
	}
	switch user.IPRangeType {
	case "all_ips":
		return []string{}, nil
	case "ip_ranges":
		return ipRangeList, gormDB.Table("ip_ranges").
			Select("ip_range").
			Where("id in (?)",
				gormDB.Table("ip_range_conditions").
					Select("val_id").
					Where("ip_range_conditionable_type = ? and category = ?",
						"User", "ip_ranges").
					Where("ip_range_conditionable_id = ?", userId)).
			Find(&ipRangeList).Error
	default:
		return ipRangeList, gormDB.Table("ip_ranges").
			Select("ip_range").
			Where("id in (?)",
				gormDB.Table("ip_range_tags").
					Select("ip_range_id").
					Where("tag_id in (?)",
						gormDB.Table("ip_range_conditions").
							Select("val_id").
							Where("ip_range_conditionable_type = ? and category = ?",
								"User", user.IPRangeType).
							Where("ip_range_conditionable_id = ?", userId))).
			Find(&ipRangeList).Error
	}
}
