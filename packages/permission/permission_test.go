package permission

import (
	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_ip_range"
	"git.gobies.org/foeye/foeye3/model/m_ip_range"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"
)

func TestPermissionSuite(t *testing.T) {
	suite.Run(t, new(PermissionSuite))
}

type PermissionSuite struct {
	suite.Suite
	configure      *config.Configure
	db             *database.GormDatabase
	afterDropTable bool
}

func (suite *PermissionSuite) SetupSuite() {
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../../"),
	)
	assert.NotNil(suite.T(), suite.configure)

	var err error
	suite.db, err = database.NewGormDatabase(suite.configure)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), suite.db)

	err = NewPermission(suite.configure, suite.db.DB)
	assert.NoError(suite.T(), err)

	ipRangesDb := db_ip_range.GormIPRangeDatabase{Instance: suite.db}
	err = ipRangesDb.CreateTable()
	assert.NoError(suite.T(), err)

	suite.afterDropTable = true
}

func (suite *PermissionSuite) TearDownSuite() {
	defer database.Clone(suite.db.DB)
	if suite.afterDropTable {
		suite.db.DB.Migrator().DropTable(new(m_ip_range.IpRange))
	}
}

func (suite *PermissionSuite) Test_NewPermission() {
	db, err := database.NewGormDatabase(suite.configure)
	assert.NoError(suite.T(), err)
	err = NewPermission(suite.configure, db.DB)
	assert.NoError(suite.T(), err)
}

func (suite *PermissionSuite) Test_CheckPermission() {
	flag := CheckPermission("admin", "/", "GET")
	assert.Equal(suite.T(), true, flag)
}

func (suite *PermissionSuite) Test_GrantRole() {
	err := GrantRole("admin", Administrator)
	assert.NoError(suite.T(), err)
}

func (suite *PermissionSuite) Test_UpdateGrantRole() {
	err := UpdateGrantRole("admin", "asd", AssetScan)
	assert.NoError(suite.T(), err)
}

func (suite *PermissionSuite) Test_DeleteGrantRole() {
	err := DeleteGrantRole("admin")
	assert.NoError(suite.T(), err)
}

func (suite *PermissionSuite) Test_GetIpRangeByUser() {
	data, err := GetIpRangeByUser(1)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), data)
}

func (suite *PermissionSuite) Test_CheckIpRange() {
	data := []string{"*******"}
	flag := CheckIpRange(data, 1)
	assert.Equal(suite.T(), false, flag)
}
