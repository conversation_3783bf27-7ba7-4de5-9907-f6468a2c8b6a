package permission

import (
	"fmt"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/packages/stringsx"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// FilterTag 标签IP管理范围检测
func FilterTag(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client, data []*m_tag.TagBaseList) []*m_tag.TagBaseList {
	if configure.Server.IsCloseIpRange {
		return data
	}
	_, ipRangeType, _, ipRangeTagIds, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return data
	}
	if ipRangeType == "all_ips" {
		return data
	}

	tmp := make([]*m_tag.TagBaseList, 0)
	for _, val := range data {
		if val.Realname == ipRangeType || stringsx.IsContains(fmt.Sprintf("%d", val.ID), ipRangeTagIds) {
			tmp = append(tmp, val)
		}
	}

	return tmp
}
