package permission

import (
	"fmt"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/model/m_tag"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/model/m_user"
	"git.gobies.org/foeye/foeye3/model/m_user_ability"
	"git.gobies.org/foeye/foeye3/packages/ipx"
	"git.gobies.org/foeye/foeye3/packages/secure/token"
	"git.gobies.org/foeye/foeye3/packages/stringsx"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// CheckIpRangeCreateTask 创建任务IP管理范围检测
func CheckIpRangeCreateTask(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client,
	addition m_task.AdditionTask, userInfo *m_user.User, userAbility *m_user_ability.UserAbility) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	ipRanges, ipRangeType, ipRangeIds, ipRangeTagIds, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return true, err
	}

	switch addition.TaskType {
	case 1:
		if !userInfo.CanScanAsset(userAbility.AssetTask) {
			return false, nil
		}
	case 2:
		if !userInfo.CanScanVul(userAbility.ThreatTask) {
			return false, nil
		}
	case 3:
		if !userInfo.CanScanVul(userAbility.ThreatTask) || !userInfo.CanScanAsset(userAbility.AssetTask) {
			return false, nil
		}
	}

	if ipRangeType == "all_ips" {
		return true, nil
	}
	if ipRangeType != addition.IPRangeType && ipRangeType != "all_ips" { // ip
		b, err := CheckIpRanges(ipRangeType, addition, ipRanges)
		return b, err
	} else {
		if ipRangeType == "ip_ranges" {
			res := stringsx.Intersect(ipRangeIds, addition.SelectedIpRanges)
			if len(res) != len(addition.SelectedIpRanges) {
				return false, nil
			}
		} else {
			res := stringsx.Intersect(ipRangeTagIds, addition.SelectedIpRanges)
			if len(res) != len(addition.SelectedIpRanges) {
				return false, nil
			}
		}
	}
	return true, nil
}

func CheckIpRanges(ipRangeType string, addition m_task.AdditionTask, ipRanges []string) (bool, error) {
	if ipRangeType == "ip_ranges" && addition.IPRangeType == "user_input" {
		rangeType, err := CheckIpRangeByIPRangeType(ipRanges, addition)
		return rangeType, err
	} else if addition.IPRangeType == "user_input" && ipRangeType == "company" {
		rangeType, err := CheckIpRangeByIPRangeType(ipRanges, addition)
		return rangeType, err
	} else if addition.IPRangeType == "user_input" && ipRangeType == "business_app" {
		rangeType, err := CheckIpRangeByIPRangeType(ipRanges, addition)
		return rangeType, err
	} else if addition.IPRangeType == "user_input" && ipRangeType == "computer_room" {
		rangeType, err := CheckIpRangeByIPRangeType(ipRanges, addition)
		return rangeType, err
	} else if addition.IPRangeType == "user_input" && ipRangeType == "as set_level" {
		rangeType, err := CheckIpRangeByIPRangeType(ipRanges, addition)
		return rangeType, err
	} else if addition.IPRangeType == "user_input" && ipRangeType == "t_zidingyibiaoqian" {
		rangeType, err := CheckIpRangeByIPRangeType(ipRanges, addition)
		return rangeType, err
	} else if addition.IPRangeType == "user_input" && ipRangeType == "t_zidingyibiaoqianJtlF2I" {
		rangeType, err := CheckIpRangeByIPRangeType(ipRanges, addition)
		return rangeType, err
	} else {
		return false, nil
	}
	return false, nil
}

// 对传入段iprangetype 进行解析
func CheckIpRangeByIPRangeType(ipRanges []string, addition m_task.AdditionTask) (bool, error) {
	taskIpList := make([]string, 0)
	for _, ipRange := range addition.SelectedIpRanges {
		res, err := ipx.Handler(ipRange) //[***********-50]
		if err != nil {
			log.Printf("%+v\n", err)
			continue
		}
		taskIpList = append(taskIpList, res...)
	}
	userIpList := make([]string, 0)
	for _, ipRange := range ipRanges {
		res, err := ipx.Handler(ipRange)
		if err != nil {
			log.Printf("%+v\n", err)
			continue
		}
		userIpList = append(userIpList, res...)
	}
	res := stringsx.Intersect(userIpList, taskIpList)
	if len(res) != len(taskIpList) {
		log.Info("Que^25:----->")
		return false, nil
	}
	return true, nil
}

// CheckIpRangeDeleteFinishedTasks 通过ID删除已完成任务IP管理范围检测
func CheckIpRangeDeleteFinishedTasks(configure *config.Configure, batch []*m_task.Tasks, userInfo *m_user.User) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	for _, task := range batch {
		if task.UserID != userInfo.ID && !userInfo.IsAdmin() && task.ID != 0 {
			return false, nil
		}
	}
	return true, nil
}

// CheckIpRangeDeleteTaskings 删除等待队列中的任务IP管理范围检测
func CheckIpRangeDeleteTaskings(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client,
	has interface{}, userInfo *m_user.User) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	info, err := token.GetUserInfo(ctx, configure, redisclient)
	if err != nil {
		return false, err
	}

	if task, ok := has.(*m_task.Tasks); ok {
		if task.UserID != info.ID && !userInfo.IsAdmin() {
			return false, nil
		}
	}
	return true, nil
}

func GetIpRangeDeleteTaskingForUser(taskList []*m_task.Tasks, userInfo *m_user.User) (delTaskList []*m_task.Tasks, err error) {

	if userInfo.IsAdmin() {
		for _, tmp := range taskList {
			delTaskList = append(delTaskList, tmp)
		}
	} else {
		for _, tmp := range taskList {
			if tmp.UserID == userInfo.ID {
				delTaskList = append(delTaskList, tmp)
			}
		}
	}
	return delTaskList, nil
}

// CheckIpRangeGetIPRangeList 获取IP段和tags列表IP管理范围检测
func CheckIpRangeGetIPRangeList(configure *config.Configure, id uint, ipRangeType string, ipRangeIds []string) bool {
	if configure.Server.IsCloseIpRange {
		return false
	}
	if ipRangeType != "all_ips" && !stringsx.IsContains(fmt.Sprintf("%d", int(id)), ipRangeIds) {
		return true
	}

	return false
}

// FilterTasksIpRange 添加任务页面的获取IP段和tags列表IP管理范围检测
func FilterTasksIpRange(
	ctx *gin.Context,
	configure *config.Configure,
	redisclient *redis.Client,
	userInfo *m_user.User,
	ipv4RangesBase []*m_task.GetIpRangeListBase,
	ipv6RangesBase []*m_task.GetIpRangeListBase,
	ipRangesBase []*m_task.GetIpRangeListBase,
	tagList []m_tag.TagOptions,
	ipv4List []m_tag.TagOptions,
	ipv6List []m_tag.TagOptions) (
	ipv4RangesBaseExternal []*m_task.GetIpRangeListBase,
	ipv6RangesBaseExternal []*m_task.GetIpRangeListBase,
	ipRangesBaseExternal []*m_task.GetIpRangeListBase,
	tagListExternal []m_tag.TagOptions,
	ipv4ListExternal []m_tag.TagOptions,
	ipv6ListExternal []m_tag.TagOptions) {
	if configure.Server.IsCloseIpRange {
		return ipv4RangesBase, ipv6RangesBase, ipRangesBase, tagList, ipv4List, ipv6List
	}

	if userInfo.IsAdmin() {
		return ipv4RangesBase, ipv6RangesBase, ipRangesBase, tagList, ipv4List, ipv6List
	}
	_, ipRangeType, ipRangeIds, ipRangeTagIds, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return ipv4RangesBase, ipv6RangesBase, ipRangesBase, tagList, ipv4List, ipv6List
	}

	if ipRangeType == "all_ips" {
		return ipv4RangesBase, ipv6RangesBase, ipRangesBase, tagList, ipv4List, ipv6List
	}

	for _, tmp := range ipv4RangesBase {
		if stringsx.IsContains(fmt.Sprintf("%d", int(tmp.ID)), ipRangeIds) {
			ipv4RangesBaseExternal = append(ipv4RangesBaseExternal, tmp)
		}
	}

	for _, tmp := range ipv6RangesBase {
		if stringsx.IsContains(fmt.Sprintf("%d", int(tmp.ID)), ipRangeIds) {
			ipv6RangesBaseExternal = append(ipv6RangesBaseExternal, tmp)
		}
	}
	for _, tmp := range ipRangesBase {
		if stringsx.IsContains(fmt.Sprintf("%d", int(tmp.ID)), ipRangeIds) {
			ipRangesBaseExternal = append(ipRangesBaseExternal, tmp)
		}
	}
	for _, tmp := range tagList {
		if tmp.RealName == ipRangeType {
			tmpTagList := make([]*m_tag.TagOptionList, 0)
			for _, tmpTag := range tmp.List {
				if stringsx.IsContains(fmt.Sprintf("%d", int(tmpTag.ID)), ipRangeTagIds) {
					tmpTagList = append(tmpTagList, tmpTag)
				}
			}
			tmp.List = tmpTagList
			tagListExternal = append(tagListExternal, tmp)
		}

	}
	for _, tmp := range ipv4List {
		if tmp.RealName == ipRangeType {
			tmpTagList := make([]*m_tag.TagOptionList, 0)
			for _, tmpTag := range tmp.List {
				if stringsx.IsContains(fmt.Sprintf("%d", int(tmpTag.ID)), ipRangeTagIds) {
					tmpTagList = append(tmpTagList, tmpTag)
				}
			}
			tmp.List = tmpTagList
			ipv4ListExternal = append(ipv4ListExternal, tmp)
		}
	}

	for _, tmp := range ipv6List {
		if tmp.RealName == ipRangeType {
			tmpTagList := make([]*m_tag.TagOptionList, 0)
			for _, tmpTag := range tmp.List {
				if stringsx.IsContains(fmt.Sprintf("%d", int(tmpTag.ID)), ipRangeTagIds) {
					tmpTagList = append(tmpTagList, tmpTag)
				}
			}
			tmp.List = tmpTagList
			ipv6ListExternal = append(ipv6ListExternal, tmp)
		}
	}

	return
}

// FilterGetTasking 等待任务队列IP管理范围检测
func FilterGetTasking(configure *config.Configure, result *m_task.GetTaskingResult,
	userInfo *m_user.User) *m_task.GetTaskingResult {
	if configure.Server.IsCloseIpRange {
		return result
	}

	if userInfo.IsAdmin() {
		return result
	}
	tmp := make([]*m_task.GetTaskings, 0)
	for _, val := range result.Task {
		if val.UserID == userInfo.ID {
			tmp = append(tmp, val)
		}
	}
	result.Task = tmp

	return result
}

// CheckTaskById 检查任务ID
func CheckTaskById(configure *config.Configure, task *m_task.Tasks, userInfo *m_user.User) bool {
	if configure.Server.IsCloseIpRange {
		return true
	}

	if !userInfo.IsAdmin() && task.UserID != userInfo.ID {
		return false
	}
	return true
}

// FilterTaskFinished 完成任务IP管理范围
func FilterTaskFinished(configure *config.Configure,
	userInfo *m_user.User,
	taskBase []*m_task.FinishedTasksBase,
	userList []*m_task.UsersListBase) (
	[]*m_task.FinishedTasksBase,
	[]*m_task.UsersListBase) {
	if configure.Server.IsCloseIpRange {
		return taskBase, userList
	}
	if userInfo.IsAdmin() {
		return taskBase, userList
	}

	tmpTaskBase := make([]*m_task.FinishedTasksBase, 0)
	tmpUserList := make([]*m_task.UsersListBase, 0)
	for _, val := range taskBase {
		if val.UserName == userInfo.Username {
			tmpTaskBase = append(tmpTaskBase, val)
		}
	}
	for _, val := range userList {
		if val.ID == userInfo.ID {
			tmpUserList = append(tmpUserList, val)
		}
	}
	return tmpTaskBase, tmpUserList
}
