package permission

import (
	"strings"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/ipx"
	"git.gobies.org/foeye/foeye3/packages/stringsx"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// CheckIpRangeDetails 资产和漏洞详情IP管理范围检测
func CheckIpRangeDetails(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client,
	ipId string) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}

	ipRanges, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return false, err
	}
	if ipRangeType == "all_ips" {
		return true, nil
	}

	ipRangeList := make([]string, 0)
	for _, val := range ipRanges {
		tmp, err := ipx.Handler(val)
		if err != nil {
			log.Println(err)
			continue
		}
		ipRangeList = append(ipRangeList, tmp...)
	}
	if !stringsx.IsContains(ipId, ipRangeList) {
		return false, nil
	}
	return true, nil
}

// CheckAssets 资产IP管理范围检测
func CheckAssets(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client) (string, error) {
	if configure.Server.IsCloseIpRange {
		return "", nil
	}

	ipRanges, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return "", err
	}
	if ipRangeType == "all_ips" {
		return "", nil
	}

	return strings.Join(ipRanges, "\n"), nil
}
