package permission

import (
	"fmt"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_ip_range"
	"git.gobies.org/foeye/foeye3/packages/stringsx"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// FilterIpRange 获取IP段列表-IP管理范围检测
func FilterIpRange(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client, list []*m_ip_range.IpRangeBase) []*m_ip_range.IpRangeBase {
	if configure.Server.IsCloseIpRange {
		return list
	}
	_, ipRangeType, ipRangeIds, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		log.Println(err)
		return list
	}
	if ipRangeType == "all_ips" {
		return list
	}

	tmpList := make([]*m_ip_range.IpRangeBase, 0)

	for _, sourceIpRange := range list {
		if stringsx.IsContains(fmt.Sprintf("%d", sourceIpRange.ID), ipRangeIds) {
			tmpList = append(tmpList, sourceIpRange)
		}
	}

	return tmpList
}
