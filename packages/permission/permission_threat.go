package permission

import (
	"strings"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/ipx"
	"git.gobies.org/foeye/foeye3/packages/stringsx"
	"git.gobies.org/foeye/foeye3/responses/r_threat"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// CheckIpRangeThreats 漏洞IP管理范围检测
func CheckIpRangeThreats(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client, params *r_threat.QueryListAndKeyword, ipRangeContent []string) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	ipRanges, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {

		return false, err
	}
	if ipRangeType == "all_ips" {
		return true, nil
	}

	if len(params.Ids) > 0 {
		ipRangeList := make([]string, 0)
		for _, val := range ipRanges {
			tmp, err := ipx.Handler(val)
			if err != nil {
				log.Println(err)
				continue
			}
			ipRangeList = append(ipRangeList, tmp...)
		}
		for _, external := range ipRangeContent {
			if !stringsx.IsContains(external, ipRangeList) {
				return false, nil
			}
		}
	}

	ipRanges = append(ipRanges, params.IpRange)
	ipRange := strings.Join(ipRanges, ",")
	params.IpRange = ipRange
	return true, nil
}

// CheckIpRangeThreatGetList 漏洞管理-默认维度IP管理范围检测
func CheckIpRangeThreatGetList(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client, params *r_threat.QueryListAndKeyword) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	ipRanges, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return false, err
	}
	if ipRangeType == "all_ips" {
		return true, nil
	}
	ipRanges = append(ipRanges, params.IpRange)
	ipRange := strings.Join(ipRanges, ",")
	params.IpRange = ipRange
	return true, nil
}

// CheckIpRangeGetThreatXcList  信创风险资产管理-漏洞概览-IP管理范围检测
func CheckIpRangeGetThreatXcList(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client, params *r_threat.QueryListForXcThreat) (string, error) {
	if configure.Server.IsCloseIpRange {
		return "", nil
	}
	ipRanges, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return "", err
	}
	if ipRangeType == "all_ips" {
		return "", nil
	}

	return strings.Join(ipRanges, "\n"), nil
}
