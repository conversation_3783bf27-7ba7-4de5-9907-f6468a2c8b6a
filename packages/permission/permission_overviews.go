package permission

import (
	"strings"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/responses/r_asset"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// CheckIpRangeOverviews 资产总览IP管理范围检测
func CheckIpRangeOverviews(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client, query *r_asset.QueryListAndKeyword) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	ipRanges, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return false, err
	}
	if ipRangeType == "all_ips" {
		return true, nil
	}

	ipRange := strings.Join(ipRanges, ",")
	query.Search.IpRanges = ipRange
	return true, nil
}
