package permission

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye-dependencies/address"
	"git.gobies.org/foeye-dependencies/mosso"
	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/packages/ipx"
	"git.gobies.org/foeye/foeye3/packages/stringsx"
	"git.gobies.org/foeye/foeye3/responses/r_compliance_monitor"
)

// CheckIpRangeComplianceMonitorCreate 创建规则IP管理范围检测
func CheckIpRangeComplianceMonitorCreate(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client,
	ipRangeTypeExternal string, content []string) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	// 判断IP管理范围
	userIpRangeContent, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return true, err
	}
	if ipRangeType == "all_ips" {
		return true, nil
	}
	mosso.DebugShowContentWithJSON(userIpRangeContent)
	userIpListIns := address.ConvertIPsToCIDR(userIpRangeContent)

	switch ipRangeTypeExternal {
	case "all_ips":
		return false, nil
	default:
		inputIpListIns := address.ConvertIPsToCIDR(content)
		mosso.DebugShowContentWithJSON(stringsx.RemoveDuplicationSlice(userIpListIns.ToIPRanges()))
		mosso.DebugShowContentWithJSON(stringsx.RemoveDuplicationSlice(inputIpListIns.ToIPRanges()))
		if len(stringsx.Difference(stringsx.RemoveDuplicationSlice(inputIpListIns.ToIPRanges()), stringsx.RemoveDuplicationSlice(userIpListIns.ToIPRanges()))) > 0 {
			return false, nil
		}
	}
	return true, nil
}

// CheckIpRangeComplianceMonitorCheck 整改核查IP管理范围检测
func CheckIpRangeComplianceMonitorCheck(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client,
	params *m_compliance_monitor.RepairCheckRequest, ipRangeContent []string) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	ipRanges, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return true, err
	}
	if ipRangeType == "all_ips" {
		return true, nil
	}
	if len(params.IDs) > 0 {
		ipRangeList := make([]string, 0)
		for _, val := range ipRanges {
			tmp, err := ipx.Handler(val)
			if err != nil {
				log.Println(err)
				continue
			}
			ipRangeList = append(ipRangeList, tmp...)
		}
		for _, external := range ipRangeContent {
			if !stringsx.IsContains(external, ipRangeList) {
				return false, nil
			}
		}
	}
	return true, nil
}

// CheckIpRangeExamineDelete 删除未修复和已修复违规项IP管理范围检测
func CheckIpRangeExamineDelete(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client,
	ql *r_compliance_monitor.QueryListDeleteExtra, ipRangeContent []string) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	ipRanges, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return true, err
	}
	if ipRangeType == "all_ips" {
		return true, nil
	}
	if len(ql.IDs) > 0 {
		ipRangeList := make([]string, 0)
		for _, val := range ipRanges {
			tmp, err := ipx.Handler(val)
			if err != nil {
				log.Println(err)
				continue
			}
			ipRangeList = append(ipRangeList, tmp...)
		}
		for _, external := range ipRangeContent {
			if !stringsx.IsContains(external, ipRangeList) {
				return false, nil
			}
		}
	}

	return true, nil
}

// CheckList 违规IP管理范围检测
func CheckList(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client) (string, error) {
	if configure.Server.IsCloseIpRange {
		return "", nil
	}
	ipRanges, ipRangeType, _, _, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return "", err
	}
	if ipRangeType == "all_ips" {
		return "", nil
	}

	return strings.Join(ipRanges, "\n"), nil
}
