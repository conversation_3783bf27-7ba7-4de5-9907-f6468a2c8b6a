package permission

import (
	"fmt"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_report"
	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/model/m_user"
	"git.gobies.org/foeye/foeye3/model/m_user_ability"
	"git.gobies.org/foeye/foeye3/packages/stringsx"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// CheckIpRangeCreateReportType 创建报告模板IP管理范围检测
func CheckIpRangeCreateReportType(ctx *gin.Context, configure *config.Configure, redisclient *redis.Client,
	params m_report.AdditionReportParam, userInfo *m_user.User, userAbility *m_user_ability.UserAbility) (bool, error) {
	if configure.Server.IsCloseIpRange {
		return true, nil
	}
	_, ipRangeType, ipRangeIds, ipRangeTagIds, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return true, err
	}

	switch params.Category {
	case "assets_threats":
		if !userInfo.CanScanAsset(userAbility.AssetTask) || !userInfo.CanScanVul(userAbility.ThreatTask) {
			return false, nil
		}
	case "threats":
		if !userInfo.CanScanVul(userAbility.ThreatTask) {
			return false, nil
		}
	case "assets":
		if !userInfo.CanScanAsset(userAbility.AssetTask) {
			return false, nil
		}
	default:
		return false, nil
	}
	if ipRangeType == "all_ips" {
		return true, nil
	}

	if ipRangeType == params.Query {
		if ipRangeType == "ip_ranges" {
			for _, external := range params.SelectedIpRanges {
				if !stringsx.IsContains(fmt.Sprintf("%d", external), ipRangeIds) {
					return false, nil
				}
			}
		} else {
			for _, external := range params.SelectedIpRanges {
				if !stringsx.IsContains(fmt.Sprintf("%d", external), ipRangeTagIds) {
					return false, nil
				}
			}
		}
	}

	return true, nil
}

// FilterGetReportTypeIpRagesOrTags 获取报告范围IP段和标签列表IP管理范围
func FilterGetReportTypeIpRagesOrTags(
	ctx *gin.Context,
	configure *config.Configure,
	redisclient *redis.Client,
	ipRangesBase []*m_task.GetIpRangeListBase,
	tagList []*m_task.Taglist) (ipRangesBaseExternal []*m_task.GetIpRangeListBase, tagListExternal []*m_task.Taglist) {
	if configure.Server.IsCloseIpRange {
		return ipRangesBase, tagList
	}
	_, ipRangeType, ipRangeIds, ipRangeTagIds, err := GetIpRange(ctx, configure, redisclient)
	if err != nil {
		return ipRangesBase, tagList
	}
	if ipRangeType == "all_ips" {
		return ipRangesBase, tagList
	}

	for _, tmp := range ipRangesBase {
		if stringsx.IsContains(fmt.Sprintf("%d", tmp.ID), ipRangeIds) {
			ipRangesBaseExternal = append(ipRangesBaseExternal, tmp)
		}
	}
	for _, tmp := range tagList {
		if tmp.RealName == ipRangeType {
			tmpTagList := make([]*m_task.GetTagsListBase, 0)
			for _, tmpTag := range tmp.List {
				if stringsx.IsContains(fmt.Sprintf("%d", tmpTag.ID), ipRangeTagIds) {
					tmpTagList = append(tmpTagList, tmpTag)
				}
			}
			tmp.List = tmpTagList
			tagListExternal = append(tagListExternal, tmp)
		}
	}

	return
}
