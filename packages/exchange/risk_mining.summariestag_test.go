package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRiskMiningSummariesTags_Increment(t *testing.T) {
	var settings = RiskMiningSummariesTags{}
	settings.Increment("负责人", "张三")
	settings.Increment("负责人", "李四")
	settings.Increment("负责人", "张三")
	settings.Increment("负责人", "李四")
	settings.Increment("负责人", "张三")
	settings.Increment("资产等级", "一般")
	settings.Increment("资产等级", "一般")
	settings.Increment("资产等级", "一般")
	settings.Increment("资产等级", "一般")
	settings.Increment("资产等级", "重要")
	settings.Increment("资产等级", "一般")
	settings.Increment("资产等级", "重要")
	settings.Increment("资产等级", "重要")
	settings.Increment("资产等级", "重要")
	settings.Increment("资产等级", "重要")
	settings.Increment("资产等级", "重要")
	settings.Increment("资产等级", "次要")
	settings.Increment("资产等级", "一般")
	settings.Increment("资产等级", "一般")
	settings.Increment("资产等级", "次要")

	for _, item := range settings {
		if item.Name == "负责人" {
			assert.Len(t, item.Data, 2)
		}
		if item.Name == "资产等级" {
			assert.Len(t, item.Data, 3)
		}
	}
}
