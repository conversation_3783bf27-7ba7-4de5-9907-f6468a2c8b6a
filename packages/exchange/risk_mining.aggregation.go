package exchange

// AggregationValueItem
// Used to represent key-value pair types.
// swagger:type
// example: {"key": "value"}
type AggregationValueItem map[string]int

// AggregationCustomFieldsItem
// Used to represent key-value pair types.
// swagger:type
type AggregationCustomFieldsItem struct {
	// 自定义标签名称
	//
	// required: true
	// read only: false
	// example: 自定义标签1
	Field string

	// 自定义标签名称
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/AggregationValueItem"
	Value []AggregationValueItem
}

// RiskMiningOfAggregation
// Used to return a risk mining aggregation structure information.
// swagger:model
type RiskMiningOfAggregation struct {
	// 挖矿风险名称
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/AggregationValueItem"
	Title RiskMiningAggregationValueItems `json:"title"`

	// 资产等级
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/AggregationValueItem"
	AssetLevel RiskMiningAggregationValueItems `json:"asset_level"`

	// 负责人
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/AggregationValueItem"
	Username RiskMiningAggregationValueItems `json:"username"`

	// 管理单元
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/AggregationValueItem"
	Company RiskMiningAggregationValueItems `json:"company"`

	// 业务系统
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/AggregationValueItem"
	BusinessApp RiskMiningAggregationValueItems `json:"business_app"`

	// 机房信息
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/AggregationValueItem"
	ComputerRoom RiskMiningAggregationValueItems `json:"computer_room"`

	// 其它自定义标签
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/AggregationCustomFieldsItem"
	CustomFields []AggregationCustomFieldsItem `json:"custom_fields"`
}

type RiskMiningAggregationValueItems []AggregationValueItem

func (r *RiskMiningAggregationValueItems) Increment(key string) {
	if !r.exists(key) {
		*r = append(*r, AggregationValueItem{key: 1})
	} else {
		r.incr(key)
	}
}

func (r *RiskMiningAggregationValueItems) incr(key string) {
	for idx, item := range *r {
		if _, ok := item[key]; ok {
			item[key]++
			(*r)[idx] = item
		}
	}
}

func (r *RiskMiningAggregationValueItems) exists(key string) bool {
	for _, item := range *r {
		if _, ok := item[key]; ok {
			return true
		}
	}
	return false
}
