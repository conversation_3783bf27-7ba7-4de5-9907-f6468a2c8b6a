package exchange

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

func TestUpdateIPRangeFileSuite(t *testing.T) {
	suite.Run(t, &UpdateIPRangeFileSuite{})
}

type UpdateIPRangeFileSuite struct {
	suite.Suite
	inst *TaskImportIpRangeResult
}

func (suite *UpdateIPRangeFileSuite) BeforeTest(suiteName, testName string) {
	suite.inst = &TaskImportIpRangeResult{}
}

func (suite *UpdateIPRangeFileSuite) Test_Set() {
	suite.inst.SetAlready(nil)
	suite.inst.SetNewness(nil)
	suite.inst.SetFail(3)
}
