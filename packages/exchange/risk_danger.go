package exchange

// RiskDangerParams Model.
//
// 数据模型 RuleDesc 危险资产管理列表查询参数。
//
// swagger:model RiskDangerParams
type RiskDangerParams struct {
	// 关键字搜索
	//
	// required: true
	// read only: false
	// example: Bitcoin
	Keyword string `json:"keyword" form:"keyword"`

	// 页码
	//
	// required: true
	// read only: false
	// example: 1
	Number int `json:"number" form:"number,default=1"`

	// 页面大小
	//
	// required: true
	// read only: false
	// example: 15
	Size int `json:"size" form:"size,default=15"`

	// 危险风险名称
	//
	// required: true
	// read only: false
	// example: [Bitcoin,HelloWorld]
	Title []string `json:"title" form:"title"`

	// 资产等级
	//
	// required: true
	// read only: false
	// example: [重要,一般]
	AssetLevel []string `json:"asset_level" form:"asset_level"`

	// IP地址段
	//
	// required: true
	// read only: false
	// example: **********-100
	IPRange string `json:"iprange" form:"iprange"`

	// 负责人
	//
	// required: true
	// read only: false
	// example: [张三,李四]
	Username []string `json:"username" form:"username"`

	// 管理单元
	//
	// required: true
	// read only: false
	// example: [华北事业群,华中事业群]
	Company []string `json:"company" form:"company"`

	// 厂商
	//
	// required: true
	// read only: false
	// example: 其他
	CompanyName string `json:"company_name" form:"company_name"`

	// 一级标签
	//
	// required: true
	// read only: false
	// example: 支撑系统
	FirstTag string `json:"first_tag" form:"first_tag"`

	// 二级标签
	//
	// required: true
	// read only: false
	// example: 脚本语言
	SecondTag string `json:"second_tag" form:"second_tag"`

	// 业务系统
	//
	// required: true
	// read only: false
	// example: [财务,CMS]
	BusinessApp []string `json:"business_app" form:"business_app"`

	// 机房信息
	//
	// required: true
	// read only: false
	// example: [北京机房,上海机房]
	ComputerRoom []string `json:"computer_room" form:"computer_room"`

	// 危险资产ID.
	//
	// required: false
	// read only: false
	// example: ["10.10.101.1:90"]
	Ids []string `json:"ids" form:"ids" query:"ids"`

	// 发现时间
	//
	// required: true
	// read only: false
	// example: 2006-11-24,2007-11-24
	CreateTime string `json:"createtime" form:"createtime"`

	// 最近扫描时间
	//
	// required: false
	// read only: false
	// example: 2006-11-24,2007-11-24
	LastTime string `json:"last_time" form:"last_time"`

	// 自定义标签
	//
	// required: false
	// read only: false
	CustomFieldsKey   []string `json:"custom_fields[key]" form:"custom_fields[key]"`
	CustomFieldsValue []string `json:"custom_fields[list]" form:"custom_fields[list]"`

	IsThreat     bool
	ScanType     bool
	PocMap       map[string]interface{}
	TagMap       map[string]string
	SecondMap    map[string]string
	NoStdPorts   map[int]struct{}
	RiskScanPort map[int]string
}

// RiskDangerPortCreate Model.
//
// 数据模型 RuleDesc 添加高危端口。
//
// swagger:model RiskDangerPortCreate
type RiskDangerPortCreate struct {
	// 关键字查询.
	//
	// required: false
	// read only: false
	// example: 22/ftp
	Name string `json:"name" form:"name" query:"name"`

	// 端口添加方式{iport_ip_csv:'导入添加', system:'系统预置', self_add:'自定义添加', flow:"流量分析", scan_add:'扫描添加'}..
	//
	// required: true
	// read only: false
	// example: "self_add"
	AddWay string `json:"add_way" binding:"required"`

	// 上传文件ID（"add_way":"iport_ip_csv"时，此参数必填）.
	//
	// required: false
	// read only: false
	// example: 1
	FileID uint `json:"file_id"`
}

// RiskDangerPortBatch Model.
//
// 数据模型 RuleDesc 删除高危端口。
//
// swagger:model RiskDangerPortBatch
type RiskDangerPortBatch struct {
	// 记录ID数组.
	//
	// required: true
	// read only: false
	// example: [1,2,3]
	Ids []int `json:"ids" form:"ids" query:"ids" binding:"required"`

	// 关键字查询.
	//
	// required: false
	// read only: false
	// example: "22"
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
}

// FixedIdsZeroValue fixed ids zero value.
func (batch *RiskDangerPortBatch) FixedIdsZeroValue() {
	tem := make([]int, 0)
	for _, item := range batch.Ids {
		if item != 0 {
			tem = append(tem, item)
		}
	}
	batch.Ids = tem
}

// ResultRiskDangerPort Model.
//
// 数据模型 RuleDesc 高危端口列表。
//
// swagger:model ResultRiskDangerPort
type ResultRiskDangerPort struct {
	// 记录ID
	//
	// required: true
	// read only: false
	// example: 12
	Id int `json:"id"`

	// 端口/协议.
	//
	// required: true
	// read only: false
	// example: 21/ftp
	Name string `json:"name"`

	// 端口/协议.
	//
	// required: true
	// read only: false
	// example: 2022-11-07 15:49:32
	CreateTime string `json:"create_time"`
}

// ResultRiskDangerList
// Used to return a risk danger list structure information.
// swagger:model
type ResultRiskDangerList struct {
	Number           int                `json:"number"`
	Size             int                `json:"size"`
	Total            int                `json:"total"`
	ResultRiskDanger []ResultRiskDanger `json:"info"`
	CustomFields     map[string]string  `json:"custom_fields"`
}

// ResultRiskDanger
// Used to return a risk danger structure information.
// swagger:model
type ResultRiskDanger struct {
	// 记录ID.
	//
	// required: true
	// read only: false
	// example: **********
	ID string `json:"id"`

	// 危险属性名称
	//
	// required: true
	// read only: false
	// example: Bitcoin
	Title string `json:"title"`

	// IP地址
	//
	// required: true
	// read only: false
	// example: **********
	IP string `json:"ip"`

	// 资产等级
	//
	// required: true
	// read only: false
	// example: 重要
	AssetLevel string `json:"asset_level"`

	// 端口
	//
	// required: true
	// read only: false
	// example: 80
	Port int `json:"port"`

	// 协议
	//
	// required: true
	// read only: false
	// example: http
	Protocol string `json:"protocol"`

	// 组件
	//
	// required: true
	// read only: false
	// example: http
	Rule string `json:"rule"`

	// 发现时间
	//
	// required: true
	// read only: false
	// example: 2022-11-07 15:49:32
	CreateTime string `json:"createtime"`

	// 最近扫描时间
	//
	// required: true
	// read only: false
	// example: 2022-11-07 15:49:32
	LastUpdateTime string `json:"lastupdatetime"`

	// 负责人
	//
	// required: true
	// read only: false
	// example: 张三
	Username string `json:"username"`

	// 邮箱
	//
	// required: true
	// read only: false
	// example: <EMAIL>
	Email string `json:"email"`

	// 联系电话
	//
	// required: true
	// read only: false
	// example: ***********
	Mobile string `json:"mobile"`

	// 管理单元
	//
	// required: true
	// read only: false
	// example: 华北事业群
	Company string `json:"company"`

	// 业务系统
	//
	// required: true
	// read only: false
	// example: 财务
	BusinessApp string `json:"business_app"`

	// 机房信息
	//
	// required: true
	// read only: false
	// example: 北京机房
	ComputerRoom string `json:"computer_room"`

	// 自定义标签.
	//
	// required: true
	// read only: false
	CustomFields map[string]string `json:"custom_fields"`

	// 一级分类
	FirstTag string `json:"-"`

	// 二级分类
	SecondTag string `json:"-"`

	// 厂商规则.
	CompanyName string `json:"-"`
}

// ResultDangerPortFilterData Model.
//
// 数据模型 ResultDangerPortFilterData 危险资产管理-高级筛选。
//
// swagger:model ResultDangerPortFilterData
type ResultDangerPortFilterData struct {
	// 危险属性名称.
	//
	// required: true
	// read only: false
	Title []SystemPreset `json:"title"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: ["重要资产","一般资产"]
	AssetLevel []string `json:"asset_level"`

	// IP地址段.
	//
	// required: true
	// read only: false
	// example: "**********/24"
	IpRange string `json:"ip_range"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-12,2022-02-18"
	FindTime string `json:"find_time"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: "2022-01-12,2022-02-18"
	LastTime string `json:"last_time"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: ["南京分公司","北京分公司"]
	Company []string `json:"company"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: ["客服系统","客服系统"]
	BusinessApp []string `json:"business_app"`

	// 负责人.
	//
	// required: true
	// read only: false
	// example: ["张三","李四"]
	Username []string `json:"username"`

	// 机房信息.
	//
	// required: true
	// read only: false
	// example: "南京机房"
	ComputerRoom []string `json:"computer_room"`

	// 自定义标签.
	//
	// required: true
	// read only: false
	CustomFields []DangerCustomFields `json:"custom_fields"`
}

// DangerCustomFields Model.
//
// 数据模型 DangerCustomFields 高级筛选-自定义标签。
//
// swagger:model DangerCustomFields
type DangerCustomFields struct {
	// 英文名称.
	//
	// required: true
	// read only: false
	// example: "test name"
	Key string `json:"key"`

	// 中文名称.
	//
	// required: true
	// read only: false
	// example: "国家"
	Value string `json:"value"`

	// 数据集合.
	//
	// required: true
	// read only: false
	// example:["中国","美国"]
	List []string `json:"list"`
}

// ResultDangerPortFilterUpData Model.
//
// 数据模型 ResultDangerPortFilterUpData 危险资产管理-高级筛选上面图表。
//
// swagger:model ResultDangerPortFilterUpData
type ResultDangerPortFilterUpData struct {
	// 危险属性名称.
	//
	// required: true
	// example: {"key":"高危端口","value":100}
	Title []SystemBucket `json:"title"`

	// 管理单元.
	//
	// required: true
	// read only: false
	// example: {"key":"南京","value":100}
	Company []SystemBucket `json:"company"`

	// 业务系统.
	//
	// required: true
	// read only: false
	// example: {"key":"南京","value":100}
	BusinessApp []SystemBucket `json:"business_app"`

	// 负责人.
	//
	// required: true
	// read only: false
	// example: {"key":"南京","value":100}
	Username []SystemBucket `json:"username"`

	// 机房信息.
	//
	// required: true
	// read only: false
	// example: {"key":"南京","value":100}
	ComputerRoom []SystemBucket `json:"computer_room"`

	// 资产等级.
	//
	// required: true
	// read only: false
	// example: {"key":"南京","value":100}
	AssetLevel []SystemBucket `json:"asset_level"`

	// 自定义标签.
	//
	// required: true
	// read only: false
	// example: {"key":"A1自定义标签","list":[{"key":"A1","doc_count":13},{"key":"A2","doc_count":10}]}
	CustomFields []interface{} `json:"custom_fields"`
}

// ResultDangerStatisticsData Model.
//
// 数据模型 ResultDangerStatisticsData 危险资产管理列表-左侧聚合。
//
// swagger:model ResultDangerStatisticsData
type ResultDangerStatisticsData struct {
	Data []Data `json:"data"`
}

type Data struct {
	Title    string      `json:"title"`
	Key      string      `json:"key"`
	DocCount int         `json:"doc_count"`
	Children interface{} `json:"children,omitempty"`
}

type SystemBucket struct {
	Key      string `json:"key"`
	DocCount int    `json:"doc_count"`
}

type SystemPreset struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
