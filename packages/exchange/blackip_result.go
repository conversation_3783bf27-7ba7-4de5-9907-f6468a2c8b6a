package exchange

// BlackIpTaskUnFinishedList Model
//
// 数据模型 BlackIpTaskUnFinishedList black task ip问完成任务列表基本数据信息.
//
// swagger:model BlackIpTaskUnFinishedList
type BlackIpTaskUnFinishedList struct {
	// 页码.
	//
	// required: true
	// read only: false
	// example: 1
	Number int `json:"number"`

	// 每页包含的数据.
	//
	// required: true
	// read only: false
	// example: 10
	Size int `json:"size"`

	// 待完成数据总数.
	//
	// required: true
	// read only: false
	// example: 100
	Total int64 `json:"total"`

	// 已完成数据总数.
	//
	// required: true
	// read only: false
	// example: 100
	CompleteTotal int64 `json:"complete_total"`

	// 待完成数据总数.
	//
	// required: true
	// read only: false
	// example: 100
	UnFinishedTotal int64 `json:"un_finished_total"`

	// 检索总数.
	//
	// required: true
	// read only: false
	// example: 100
	SearchTotal int64 `json:"search_total"`

	// 是否开启fofa联动功能.
	//
	// required: true
	// read only: false
	// example: false
	IsLicense bool `json:"is_license"`

	// 是否配置fofa.
	//
	// required: true
	// read only: false
	// example: false
	IsConfig bool `json:"is_config"`

	BlackIpTaskUnFinished []*BlackIpTaskUnFinished `json:"info"`
}

type BlackIpTaskUnFinished struct {
	// 记录ID.
	//
	// required: true
	// read only: false
	// example: 1
	Id int `json:"id"`

	// 任务名称.
	//
	// required: true
	// read only: false
	// example: 测试任务
	Title string `json:"title"`

	// 提交人.
	//
	// required: true
	// read only: false
	// example: 张三
	Username string `json:"username"`

	// 任务状态.
	//
	// required: true
	// read only: false
	// example: 正在执行
	State string `json:"state"`

	// 任务进度.
	//
	// required: true
	// read only: false
	// example: 100
	Progress float64 `json:"progress"`

	// 开始时间.
	//
	// required: true
	// read only: false
	// example: 2006-01-02 16:09:50
	CreateTime string `json:"create_time"`
}

// BlackIpTaskCompleteList Model
//
// 数据模型 BlackIpTaskCompleteList black task ip已完成任务列表基本数据信息.
//
// swagger:model BlackIpTaskCompleteList
type BlackIpTaskCompleteList struct {
	// 页码.
	//
	// required: true
	// read only: false
	// example: 1
	Number int `json:"number"`

	// 每页包含的数据.
	//
	// required: true
	// read only: false
	// example: 10
	Size int `json:"size"`

	// 已完成数据总数.
	//
	// required: true
	// read only: false
	// example: 100
	Total int64 `json:"total"`

	// 已完成数据总数.
	//
	// required: true
	// read only: false
	// example: 100
	CompleteTotal int64 `json:"complete_total"`

	// 待完成数据总数.
	//
	// required: true
	// read only: false
	// example: 100
	UnFinishedTotal int64 `json:"un_finished_total"`

	// 检索总数.
	//
	// required: true
	// read only: false
	// example: 100
	SearchTotal int64 `json:"search_total"`

	// 是否开启fofa联动功能.
	//
	// required: true
	// read only: false
	// example: false
	IsLicense bool `json:"is_license"`

	// 是否配置fofa.
	//
	// required: true
	// read only: false
	// example: false
	IsConfig bool `json:"is_config"`

	BlackIpTaskComplete []*BlackIpTaskComplete `json:"info"`
}

type BlackIpTaskComplete struct {
	// 记录ID.
	//
	// required: true
	// read only: false
	// example: 1
	Id int `json:"id"`

	// 任务名称.
	//
	// required: true
	// read only: false
	// example: 测试任务
	Title string `json:"title"`

	// ip数量.
	//
	// required: true
	// read only: false
	// example: 10
	IpCount int64 `json:"ip_count"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: 2006-01-02 16:09:50
	BeginTime string `json:"begin_time"`

	// 提交人.
	//
	// required: true
	// read only: false
	// example: 张三
	Username string `json:"username"`

	// 封禁时间.
	//
	// required: true
	// read only: false
	// example: 高
	Level string `json:"level"`

	// 任务完成.
	//
	// required: true
	// read only: false
	// example: true
	IsSuccessful bool `json:"is_successful"`

	// IP列表.
	//
	// required: true
	// read only: false
	// example: [**********,**********]
	Ips []SystemPresetKeyInt `json:"ips"`
}

// BlackIpTaskInfo Model
//
// 数据模型 BlackIpTaskInfo black task ip基本数据信息.
//
// swagger:model BlackIpTaskInfo
type BlackIpTaskInfo struct {
	// 记录ID.
	//
	// required: true
	// read only: false
	// example: 1
	Id int `json:"id"`

	// 任务名称.
	//
	// required: true
	// read only: false
	// example: 测试任务
	Title string `json:"title"`

	// 封禁级别 (低：0，中：1，高：2).
	//
	// required: true
	// read only: false
	// example: 0
	Level string `json:"level"`
}

// ResultBlackIpList Model
//
// 数据模型 ResultBlackIpList black task ip列表基本数据信息.
//
// swagger:model ResultBlackIpList
type ResultBlackIpList struct {
	// 页码.
	//
	// required: true
	// read only: false
	// example: 1
	Number int `json:"number"`

	// 每页包含的数据.
	//
	// required: true
	// read only: false
	// example: 10
	Size int `json:"size"`

	// 待完成数据总数.
	//
	// required: true
	// read only: false
	// example: 100
	Total int64 `json:"total"`

	// 记录ID.
	//
	// required: true
	// read only: false
	// example: 1
	Id int `json:"id"`

	// 任务名称.
	//
	// required: true
	// read only: false
	// example: 测试任务
	Title string `json:"title"`

	// 封禁级别.
	//
	// required: true
	// read only: false
	// example: 高
	Level string `json:"level"`

	// 提交人.
	//
	// required: true
	// read only: false
	// example: 张三
	Username string `json:"username"`

	// 发现时间.
	//
	// required: true
	// read only: false
	// example: 2022-12-12 17:22:44
	BeginTime string `json:"begin_time"`

	FoFaList []*FoFaList `json:"info"`
}

type SystemPresetKeyInt struct {
	Pid   int    `json:"pid"`
	Key   int    `json:"key"`
	Value string `json:"value"`
}

// FoFaList fofa数据模型
type FoFaList struct {
	// 记录ID.
	//
	// required: true
	// read only: false
	// example: 1
	Id int `json:"id"`

	// 该IP所在的国家.
	//
	// required: true
	// read only: false
	// example: **********
	Ip string `json:"ip"`

	// 该IP所在的国家.
	//
	// required: true
	// read only: false
	// example: 中国
	Country string `json:"country"`

	// 该IP所在的城市.
	//
	// required: true
	// read only: false
	// example: 北京
	City string `json:"city"`

	// 首次检索时间
	//
	// required: true
	// read only: false
	// example: 2021-09-16T11:20:10+08:00
	CreatedAt string `json:"create_at"`

	// 更新时间
	//
	// required: true
	// read only: false
	// example: 2021-09-16T11:20:10+08:00
	UpdatedAt string `json:"update_at"`

	PortList []*PortList `json:"port_list"`
}

type PortList struct {
	// 协议名称.
	//
	// required: true
	// read only: false
	// example: http
	Protocol string `json:"protocol"`

	// 端口名称.
	//
	// required: true
	// read only: false
	// example: 80
	Port int `json:"port"`

	RuleInfo []*RuleDesc `json:"rule_info"`
}

type RuleDesc struct {
	// 组件图标.
	//
	// required: true
	// read only: false
	// example: "OpenSSH"
	Icon string `json:"icon"`

	// 组件名称.
	//
	// required: true
	// read only: false
	// example: "OpenSSH"
	Name string `json:"name"`
}
