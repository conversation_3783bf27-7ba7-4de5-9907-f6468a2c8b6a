package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestTaskCategorySuite(t *testing.T) {
	suite.Run(t, &TaskCategorySuite{})
}

type TaskCategorySuite struct {
	suite.Suite
	category TaskCategory
}

func (suite *TaskCategorySuite) BeforeTest(suiteName, testName string) {
	suite.category = TaskCategoryOfDCCTask
}

func (suite *TaskCategorySuite) Test_TaskCategory_True() {
	assert.True(suite.T(), suite.category.IsDCCTask())
}

func (suite *TaskCategorySuite) Test_TaskCategory_False() {
	suite.category = ""
	assert.False(suite.T(), suite.category.IsDCCTask())
}
