package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestTaskSuite(t *testing.T) {
	suite.Run(t, &TaskSuite{})
}

type TaskSuite struct {
	suite.Suite
}

func (suite *TaskSuite) Test_TaskScanInfo() {
	assert.NotNil(suite.T(), NewTaskScanInfo(1))
}

func (suite *TaskSuite) Test_TaskScanInfo_ToMap() {
	assert.NotNil(suite.T(), NewTaskScanInfo(1).ToMap())
}
