package exchange

import (
	"encoding/json"
	"sort"
	"time"
)

// CustomFields
// Custom fields.
// swagger: type
// example: {suoshushengfen: "山东省", weizhixinxi: "内容分发"}
type CustomFields map[string]interface{}

func (customFields CustomFields) SortToSlice() []string {
	var slice []string
	var final []string
	for key := range customFields {
		slice = append(slice, key)
	}
	sort.Strings(slice)
	for _, item := range slice {
		if val, ok := customFields[item]; ok {
			final = append(final, val.(string))
		}
	}
	return final
}

// RiskMiningOfSingle
// Used to return a risk mining structure information.
// swagger:model
type RiskMiningOfSingle struct {
	// Id.
	//
	// required: true
	// read only: false
	// example: ***********:8000
	Id string `json:"id"`

	// 挖矿风险名称
	//
	// required: true
	// read only: false
	// example: Bitcoin
	Title string `json:"name"`

	// IP地址
	//
	// required: true
	// read only: false
	// example: **********
	IP string `json:"ip"`

	// 资产等级
	//
	// required: true
	// read only: false
	// example: 重要
	AssetLevel string `json:"asset_level"`

	// 端口
	//
	// required: true
	// read only: false
	// example: 80
	Port int `json:"port"`

	// 协议
	//
	// required: true
	// read only: false
	// example: http
	Protocol string `json:"protocol"`

	// 发现时间
	//
	// required: true
	// read only: false
	// example: 2022-09-28T19:05:59+08:00
	CreateTime *time.Time `json:"createtime"`

	// 最近扫描时间
	//
	// required: true
	// read only: false
	// example: 2022-09-28T19:05:59+08:00
	LastUpdateTime *time.Time `json:"lastupdatetime"`

	// 负责人
	//
	// required: true
	// read only: false
	// example: 张三
	Username string `json:"username"`

	// 邮箱
	//
	// required: true
	// read only: false
	// example: <EMAIL>
	Email string `json:"email"`

	// 联系电话
	//
	// required: true
	// read only: false
	// example: ***********
	Mobile string `json:"mobile"`

	// 管理单元
	//
	// required: true
	// read only: false
	// example: 华北事业群
	Company string `json:"company"`

	// 业务系统
	//
	// required: true
	// read only: false
	// example: 财务
	BusinessApp string `json:"business_app"`

	// 机房信息
	//
	// required: true
	// read only: false
	// example: 北京机房
	ComputerRoom string `json:"computer_room"`

	// 自定义标签
	//
	// required: true
	// read only: false
	// example:
	//   $ref: "#/definitions/CustomFields"
	CustomFields CustomFields `json:"custom_fields"`
}

func (single *RiskMiningOfSingle) String() string {
	data, _ := json.Marshal(&single)
	return string(data)
}
