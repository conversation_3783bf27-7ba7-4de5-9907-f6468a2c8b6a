package exchange

import (
	"encoding/json"
	"time"
)

type DownloadCenterCategory string

func (category DownloadCenterCategory) IsAssetManager() bool {
	return category == DownloadCenterCategoryOfAssetManager
}

func (category DownloadCenterCategory) IsAssetSpaceSearch() bool {
	return category == DownloadCenterCategoryOfAssetSpaceSearch
}

const (
	DownloadCenterCategoryOfAssetManager     DownloadCenterCategory = "asset-manager"
	DownloadCenterCategoryOfAssetSpaceSearch DownloadCenterCategory = "asset-space-search"
)

// DownloadCenterOfSingle
// Used to return a download file structure information.
// swagger:model
type DownloadCenterOfSingle struct {
	// ID
	//
	// required: true
	// read only: false
	// example: 1
	ID int `json:"id"`

	// 文件名称
	//
	// required: true
	// read only: false
	// example: 资产列表_20220623.zip
	Filename string `json:"filename"`

	// 任务类型（asset-manager: 资产数据管理、asset-space-search: 资产数据管理）
	//
	// required: true
	// read only: false
	// example: asset-manager
	Category DownloadCenterCategory `json:"category"`

	// 生成下载文件进度
	//
	// required: true
	// read only: false
	// example: 80
	Progress int `json:"progress"`

	// 开始时间
	//
	// required: true
	// read only: false
	// example: 2022-09-28T19:05:59+08:00
	BeginAt *time.Time `json:"begin_at"`

	// 结束时间
	//
	// required: true
	// read only: false
	// example: 2022-09-28T19:05:59+08:00
	EndAt *time.Time `json:"end_at"`

	// 创建者
	//
	// required: true
	// read only: false
	// example:
	//   $ref: "#/definitions/UserOfSimple"
	User *UserOfSimple `json:"user"`
}

func (single *DownloadCenterOfSingle) String() string {
	data, _ := json.Marshal(&single)
	return string(data)
}
