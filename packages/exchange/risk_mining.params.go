package exchange

import (
	"time"
)

// RiskMiningOfParams
// Used to return a risk mining params structure information.
// swagger:model
type RiskMiningOfParams struct {
	// 关键字搜索
	//
	// required: true
	// read only: false
	// example: Bitcoin
	Keyword string `json:"keyword" form:"keyword"`

	// 页码
	//
	// required: true
	// read only: false
	// example: 1
	Number int `json:"number" form:"number,default=1"`

	// 页面大小
	//
	// required: true
	// read only: false
	// example: 15
	Size int `json:"size" form:"size,default=15"`

	// 挖矿风险名称
	//
	// required: true
	// read only: false
	// example: [Bitcoin,HelloWorld]
	Title []string `json:"title" form:"title"`

	// 资产等级
	//
	// required: true
	// read only: false
	// example: [重要,一般]
	AssetLevel []string `json:"asset_level" form:"asset_level"`

	// IP地址段
	//
	// required: true
	// read only: false
	// example: **********-100
	IPRange string `json:"iprange" form:"iprange"`

	// 发现时间(传递时间对象即可)
	//
	// required: true
	// read only: false
	// example: [2022-09-26T19:05:59+08:00, 2022-09-28T19:05:59+08:00]
	CreateTime []*time.Time `json:"createtime" form:"createtime"`

	// 负责人
	//
	// required: true
	// read only: false
	// example: [张三,李四]
	Username []string `json:"username" form:"username"`

	// 管理单元
	//
	// required: true
	// read only: false
	// example: [华北事业群,华中事业群]
	Company []string `json:"company" form:"company"`

	// 厂商
	//
	// required: true
	// read only: false
	// example: [华北事业群,华中事业群]
	BusinessCompany []string `json:"business_company" form:"business_company"`

	// 业务系统
	//
	// required: true
	// read only: false
	// example: [财务,CMS]
	BusinessApp []string `json:"business_app" form:"business_app"`

	// 机房信息
	//
	// required: true
	// read only: false
	// example: [北京机房,上海机房]
	ComputerRoom []string `json:"computer_room" form:"computer_room"`

	CustomFieldsKey   []string `json:"custom_fields[key]" form:"custom_fields[key]"`
	CustomFieldsValue []string `json:"custom_fields[list]" form:"custom_fields[list]"`
}
