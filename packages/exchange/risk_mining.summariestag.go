package exchange

// RiskMiningSummariesTagItem
// swagger:model
type RiskMiningSummariesTagItem struct {
	// 关联标签数据名称
	//
	// required: true
	// read only: false
	// example: 张三
	Key string `json:"key"`

	// 关联资产总数
	//
	// required: true
	// read only: false
	// example: 55
	Content int `json:"content"`
}

// RiskMiningSummariesTag
// swagger: model
type RiskMiningSummariesTag struct {
	// 关联标签名称
	//
	// required: true
	// read only: false
	// example: 负责人
	Name string `json:"name"`

	// 关联标签数据
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/RiskMiningSummariesTagItem"
	Data []*RiskMiningSummariesTagItem `json:"data"`
}

type RiskMiningSummariesTags []*RiskMiningSummariesTag

func (r *RiskMiningSummariesTags) Increment(name string, key string) {
	if !r.existsWithName(name) {
		*r = append(*r, &RiskMiningSummariesTag{Name: name, Data: []*RiskMiningSummariesTagItem{{Key: key, Content: 1}}})
	} else {
		r.incr(name, key)
	}
}

func (r *RiskMiningSummariesTags) incr(name, key string) {
	for idx, item := range *r {
		if item.Name == name {
			if !r.existsWithKey(item, key) {
				(*r)[idx].Data = append(item.Data, &RiskMiningSummariesTagItem{Key: key, Content: 1})
				break
			} else {
				for i, datum := range item.Data {
					if datum.Key == key {
						item.Data[i].Content++
						break
					}
				}
				(*r)[idx].Data = item.Data
				break
			}
		}
	}
}

func (r *RiskMiningSummariesTags) existsWithKey(item *RiskMiningSummariesTag, key string) bool {
	for _, single := range item.Data {
		if single.Key == key {
			return true
		}
	}
	return false
}

func (r *RiskMiningSummariesTags) existsWithName(name string) bool {
	for _, item := range *r {
		if item.Name == name {
			return true
		}
	}
	return false
}
