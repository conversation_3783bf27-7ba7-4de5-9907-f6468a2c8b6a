package exchange

// RiskOfStatistic Model.
//
// 数据模型 RiskOfStatistic 饼图柱状图变化趋势。
//
// swagger:model RiskOfStatistic
type RiskOfStatistic struct {
	// 名称.
	//
	// required: false
	// read only: false
	// example: 高危
	Key string `json:"key"`

	// 数量.
	//
	// required: false
	// read only: false
	// example: 12
	Count int `json:"count"`
}

// RiskOfQueryParams risk query params.
//
// Common query parameter for risk module.
//
// swagger:model RiskOfQueryParams
type RiskOfQueryParams struct {
	// 资产等级.
	//
	// required: false
	// read only: false
	// example:
	Count int `json:"count"`
}

type ListDateView struct {
	Date  string `json:"key"`
	Value int    `json:"count"`
}
