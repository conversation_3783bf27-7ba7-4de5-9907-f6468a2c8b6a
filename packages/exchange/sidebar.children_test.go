package exchange

import (
	"testing"

	"git.gobies.org/foeye-dependencies/mosso"

	"github.com/stretchr/testify/assert"
)

func TestSidebarOfChildren_Increment(t *testing.T) {
	var children = SidebarOfChildren{}
	children.Increment("重要")
	children.Increment("一般")
	children.Increment("一般")
	children.Increment("一般")
	children.Increment("重要")
	assert.Len(t, children, 2)
	mosso.DebugShowContentWithJSON(children)
}
