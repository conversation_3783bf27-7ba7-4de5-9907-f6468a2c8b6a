package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestTaskCreateOrUpdateSuite(t *testing.T) {
	suite.Run(t, &TaskCreateOrUpdateSuite{})
}

type TaskCreateOrUpdateSuite struct {
	suite.Suite
}

func (suite *TaskCreateOrUpdateSuite) Test_TaskOfOtherConfig_String() {
	var config *TaskOfOtherConfig
	actual := config.String()
	assert.Equal(suite.T(), "null", actual)

	config = &TaskOfOtherConfig{}
	actual = config.String()
	assert.Equal(suite.T(), "{\"is_deep_scan\":false,\"is_treck_recognition\":false,\"is_ping_recognition\":false,\"is_cycle_protocol\":false,\"is_deep_recognition\":false,\"is_crack_dns\":false,\"is_version_recognition\":false,\"is_crawler\":false,\"is_disable_scan_print\":false}", actual)
}

func (suite *TaskCreateOrUpdateSuite) Test_TaskOfCreateOrUpdate_TaskOfSelectedPocs() {
	inst := &TaskOfCreateOrUpdate{SelectedPocs: []string{"1@preset", "2@preset"}}
	actual := inst.TaskOfSelectedPocs()
	assert.NotNil(suite.T(), actual)
}

func (suite *TaskCreateOrUpdateSuite) Test_TaskOfOtherConfig_NewTaskOfOtherConfigWithRawData() {
	data := NewTaskOfOtherConfigWithRawData(nil)
	assert.Nil(suite.T(), data)
}

func (suite *TaskCreateOrUpdateSuite) Test_SelectedPocs_NewSelectedPocsWithRawData() {
	data := NewSelectedPocsWithRawData(nil)
	assert.Nil(suite.T(), data)
}

func (suite *TaskCreateOrUpdateSuite) Test_SelectedPocs_String() {
	data := SelectedPocs{"7758", "7759"}
	assert.Equal(suite.T(), "[\"7758\",\"7759\"]", data.String())
}

func (suite *TaskCreateOrUpdateSuite) Test_SelectedIpRanges_NewSelectedIpRangesWithRawData() {
	data := NewSelectedIpRangesWithRawData(nil)
	assert.Nil(suite.T(), data)
}

func (suite *TaskCreateOrUpdateSuite) Test_SelectedIpRanges_String() {
	data := SelectedIpRanges{"7758", "7759"}
	assert.Equal(suite.T(), "[\"7758\",\"7759\"]", data.String())
}

func (suite *TaskCreateOrUpdateSuite) Test_TaskOfSelectedPoc_IdToInt() {
	poc := &TaskOfSelectedPoc{Id: "1", Category: "custom"}
	assert.Equal(suite.T(), 1, poc.IdToInt())
	poc.Id = ""
	assert.Equal(suite.T(), 0, poc.IdToInt())
	poc.Id = "aa"
	assert.Equal(suite.T(), 0, poc.IdToInt())
}

func (suite *TaskCreateOrUpdateSuite) Test_IPRangeType_String() {
	assert.Equal(suite.T(), "user_input", IPRangeTypeOfUserInput.String())
}

func (suite *TaskCreateOrUpdateSuite) Test_UploadIpFileIds_String() {
	data := UploadIpFileIds{7758, 7759}
	assert.Equal(suite.T(), "[7758,7759]", data.String())
}

func (suite *TaskCreateOrUpdateSuite) Test_UploadIpFileIds_NewUploadIpFileIdsWithRawData() {
	actual := NewUploadIpFileIdsWithRawData([]byte("[7758,7759]"))
	assert.NotNil(suite.T(), actual)
	assert.Equal(suite.T(), "[7758,7759]", actual.String())
}

func (suite *TaskCreateOrUpdateSuite) Test_UploadIpFileIds_IntS() {
	actual := NewUploadIpFileIdsWithRawData([]byte("[7758,7759]"))
	assert.NotNil(suite.T(), actual)
	assert.Equal(suite.T(), []int{7758, 7759}, actual.IntS())
}
