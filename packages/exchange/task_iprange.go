package exchange

import (
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/model/m_task"
)

// GetResultListExchange  Model
//
// 数据模型 GetResultListExchange  添加任务时获取的ip段信息和tags信息的汇总数据
//
// swagger:model GetResultListExchange
type GetResultListExchange struct {
	IpList          []*m_task.GetIpRangeListBase `json:"list"`
	Ipv4List        []*m_task.GetIpRangeListBase `json:"ipv4_list"`
	Ipv6List        []*m_task.GetIpRangeListBase `json:"ipv6_list"`
	Ipv4Tags        []m_tag.TagOptions           `json:"ipv4_tags"`
	Ipv6Tags        []m_tag.TagOptions           `json:"ipv6_tags"`
	TagList         []m_tag.TagOptions           `json:"tags"`
	BaseScanTagList []map[string]string          `json:"base_scan_tag_list"`
}
