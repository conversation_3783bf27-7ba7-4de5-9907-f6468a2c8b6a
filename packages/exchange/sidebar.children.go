package exchange

// SidebarOfChildrenItem
// Used to represent key-value pair types.
// swagger:type
// example: {"key": "value"}
type SidebarOfChildrenItem struct {
	Key   string `json:"key"`
	Value int    `json:"value"`
}

type SidebarOfChildren []SidebarOfChildrenItem

func (r *SidebarOfChildren) Increment(key string) {
	if !r.exists(key) {
		*r = append(*r, SidebarOfChildrenItem{Key: key, Value: 1})
	} else {
		r.incr(key)
	}
}

func (r SidebarOfChildren) incr(key string) {
	for idx, item := range r {
		if item.Key == key {
			item.Value++
			r[idx] = item
		}
	}
}

func (r SidebarOfChildren) exists(key string) bool {
	for _, item := range r {
		if item.Key == key {
			return true
		}
	}

	return false
}
