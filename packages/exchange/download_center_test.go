package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/stretchr/testify/suite"
)

func TestDownloadCenterSuite(t *testing.T) {
	suite.Run(t, &DownloadCenterSuite{})
}

type DownloadCenterSuite struct {
	suite.Suite
}

func (suite *DownloadCenterSuite) Test_DownloadCenterCategory_IsAssetManager() {
	assert.False(suite.T(), DownloadCenterCategoryOfAssetSpaceSearch.IsAssetManager())
	assert.True(suite.T(), DownloadCenterCategoryOfAssetSpaceSearch.IsAssetSpaceSearch())
}
