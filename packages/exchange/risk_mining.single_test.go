package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_RiskMiningOfSingle(t *testing.T) {
	data := &RiskMiningOfSingle{}
	assert.NotEmpty(t, data.String())
}

func Test_CustomFields(t *testing.T) {
	fields := CustomFields{
		"b": "美国",
		"a": "中国",
		"c": "日本",
	}

	expected := []string{"中国", "美国", "日本"}
	actual := fields.SortToSlice()
	assert.Equal(t, expected, actual)
}
