package exchange

import (
	"testing"

	"git.gobies.org/foeye-dependencies/mosso"
)

func TestSidebarOfMultiple_Increment(t *testing.T) {
	var children = SidebarOfMultiple{}
	children.Increment("所属数据区", "custom_fields.weizhixinxi", "账务分账")
	children.Increment("所属数据区", "custom_fields.weizhixinxi", "内容分发")
	children.Increment("所属数据区", "custom_fields.weizhixinxi", "账务分账")
	children.Increment("所属数据区", "custom_fields.weizhixinxi", "账务分账")
	children.Increment("所属数据区", "custom_fields.weizhixinxi", "账务分账")
	children.Increment("所属数据区", "custom_fields.weizhixinxi", "账务分账")
	children.Increment("所属数据区", "custom_fields.weizhixinxi", "内容分发")
	children.Increment("所属数据区", "custom_fields.weizhixinxi", "账务分账")
	mosso.DebugShowContentWithJSON(children)
}

func TestSidebarOfMultiple_Increment2(t *testing.T) {
	var children = SidebarOfMultiple{}
	children.Increment("位置信息", "custom_fields.weizhixinxi", "青岛")
	mosso.DebugShowContentWithJSON(children)
}
