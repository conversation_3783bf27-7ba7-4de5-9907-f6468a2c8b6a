package exchange

// BlackIpCreateParams Model.
//
// 数据模型 RuleDesc 添加黑Ip模型。
//
// swagger:model BlackIpCreateParams
type BlackIpCreateParams struct {
	// 任务名称
	//
	// required: true
	// read only: false
	// example: 任务名称
	TaskName string `json:"task_name" form:"task_name" binding:"required"`

	// 添加方式
	//
	// required: true
	// read only: false
	// example: self_add
	AddWay string `json:"add_way" form:"add_way" binding:"required"`

	// 封禁级别(2：高 1：中 0：低)
	//
	// required: true
	// read only: false
	// example: 2
	Level string `json:"level" form:"level" binding:"required"`

	// 添加方式
	//
	// required: false
	// read only: false
	// example: **********\n10.10.10.2
	BlackIps string `json:"black_ips" form:"black_ips"`

	// 上传文件ID（"add_way":"iport_ip_csv"时，此参数必填）.
	//
	// required: false
	// read only: false
	// example: 1
	FileID int `json:"file_id" form:"black_ips"`
}

// BlackIpBatch Model
//
// 数据模型 BlackIpBatch black ip删除基本数据信息.
//
// swagger:model BlackIpBatch
type BlackIpBatch struct {
	// 记录ID数组.
	//
	// required: true
	// read only: false
	// example: [1,2,3]
	Ids []int `json:"ids" form:"ids" query:"ids" binding:"required"`

	// 关键字查询.
	//
	// required: false
	// read only: false
	// example: "22"
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`

	// 任务id.
	//
	// required: false
	// read only: false
	// example: 11
	TaskId int `json:"task_id" form:"task_id" query:"task_id"`

	// 任务类型.
	//
	// required: false
	// read only: false
	// example: wait
	Type string `json:"type" form:"type" query:"type"`
}

// FixedIdsZeroValue fixed ids zero value.
func (batch *BlackIpBatch) FixedIdsZeroValue() {
	tem := make([]int, 0)
	for _, item := range batch.Ids {
		if item != 0 {
			tem = append(tem, item)
		}
	}
	batch.Ids = tem
}

// BlackIpTaskInfoParams Model
//
// 数据模型 BlackIpTaskInfoParams black task ip基本数据信息.
//
// swagger:model BlackIpTaskInfoParams
type BlackIpTaskInfoParams struct {
	// 记录ID.
	//
	// required: true
	// read only: false
	// example: 1
	Id int `json:"id" form:"id" query:"id" binding:"required"`

	// 页码
	//
	// required: true
	// read only: false
	// example: 1
	Number int `json:"number" form:"number,default=1"`

	// 页面大小
	//
	// required: true
	// read only: false
	// example: 15
	Size int `json:"size" form:"size,default=15"`
}
