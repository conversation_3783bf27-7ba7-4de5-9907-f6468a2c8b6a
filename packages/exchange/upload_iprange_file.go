package exchange

// TaskImportIpRangeResult 任务模块导入IP段模板
type TaskImportIpRangeResult struct {
	Already []uint //已经存在的iprange的id列表
	Newness []uint //新建的iprange的id列表
	Fail    int    //失败数量
}

func (t *TaskImportIpRangeResult) SetAlready(data []uint) {
	t.Already = append(t.Already, data...)
}

func (t *TaskImportIpRangeResult) SetNewness(data []uint) {
	t.Newness = append(t.Newness, data...)
}

func (t *TaskImportIpRangeResult) SetFail(number int) {
	t.Fail += number
}
