package exchange

import "encoding/json"

// UserOfSimple
// Used to return a simple user structure information.
// swagger:model
type UserOfSimple struct {
	// Id
	//
	// required: true
	// read only: false
	// example: 1
	ID int `json:"id"`

	// 用户名
	//
	// required: true
	// read only: false
	// example: admin
	Username string `json:"username"`
}

// NewUserOfSimpleWithMap return a new UserOfSimple structure instance.
func NewUserOfSimpleWithMap(data map[string]interface{}) *UserOfSimple {
	var r *UserOfSimple
	bytes, _ := json.Marshal(&data)
	_ = json.Unmarshal(bytes, &r)
	return r
}
