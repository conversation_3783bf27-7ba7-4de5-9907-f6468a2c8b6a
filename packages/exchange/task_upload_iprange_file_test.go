package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestUploadIPRangeFileSuite(t *testing.T) {
	suite.Run(t, &UploadIPRangeFileSuite{})
}

type UploadIPRangeFileSuite struct {
	suite.Suite

	record *UploadFileCreateIPRangeRecord
}

func (suite *UploadIPRangeFileSuite) BeforeTest(suiteName, testName string) {
	suite.record = &UploadFileCreateIPRangeRecord{
		TaskImportIpRangeResult: &TaskImportIpRangeResult{
			Already: []uint{1, 2, 3, 4, 5},
			Newness: []uint{6, 7, 8, 9, 10, 11, 6},
			Fail:    5,
		},
	}
}

func (suite *UploadIPRangeFileSuite) Test_GetNewness() {
	filter := suite.record.GetNewness()
	assert.Len(suite.T(), filter, 7)

	deduplication := suite.record.NewnessDeduplication()
	assert.Len(suite.T(), deduplication, 6)
}

func (suite *UploadIPRangeFileSuite) Test_Ids() {
	filter := suite.record.GetNewness()
	assert.Len(suite.T(), filter, 7)

	actual := suite.record.Ids()
	assert.Len(suite.T(), actual, 11)
}

func (suite *UploadIPRangeFileSuite) Test_IdsNumber() {
	filter := suite.record.GetNewness()
	assert.Len(suite.T(), filter, 7)

	actual := suite.record.IdsNumber()
	assert.Equal(suite.T(), actual, 11)
}
