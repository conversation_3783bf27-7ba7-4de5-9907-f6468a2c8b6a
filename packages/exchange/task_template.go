package exchange

import (
	"encoding/json"
	"time"
)

// TaskTemplateOfSingle
// Used to return a task template structure information.
// swagger:model
type TaskTemplateOfSingle struct {
	// ID
	//
	// required: true
	// read only: false
	// example: 1
	ID int `json:"id"`

	// 任务场景ID
	//
	// required: true
	// read only: false
	// example: 1
	TemplateId int `json:"template_id,omitempty"`

	// 任务名称
	//
	// required: true
	// read only: false
	// example: 无线局域网C段资产扫描
	Title string `json:"title"`

	// 专业建议
	//
	// required: true
	// read only: false
	// example: 此任务适用于首次或周期常规资产盘查，速度快、资产较全。
	Suggest string `json:"suggest"`

	// 备注信息
	//
	// required: true
	// read only: false
	// example: 常规资产盘查，速度快、资产较全。
	Comment *string `json:"comment"`

	// 任务类型（1：资产扫描、2：漏洞扫描、3：资产及漏洞扫描）
	//
	// required: true
	// read only: false
	// example: 1
	TaskType int `json:"task_type"`

	// IP段类型
	//
	// required: true
	// read only: false
	// example: all
	IPRangeType string `json:"ip_range_type"`

	// 带宽
	//
	// required: true
	// read only: false
	// example: 300
	Bandwidth int `json:"bandwidth"`

	// 扫描端口模板ID
	//
	// required: true
	// read only: false
	// example: 7
	ScanPortTemplateID int `json:"scan_port_template_id"`

	// 扫描端口模板名称
	//
	// required: true
	// read only: false
	// example: 全商品
	ScanPortTemplateDesc string `json:"scan_port_template_desc"`

	// POC扫描类型
	//
	// required: true
	// read only: false
	// example: normal
	PocScanType string `json:"poc_scan_type"`

	// 是否是预置
	//
	// required: true
	// read only: false
	// example: false
	IsPreset bool `json:"is_preset"`

	// 其他配置
	//
	// required: true
	// read only: false
	// example: {"is_deep_scan":false,"is_treck_recognition":false,"is_ping_recognition":false,"is_cycle_protocol":false,"is_deep_recognition":false,"is_crack_dns":false,"is_version_recognition":true,"is_crawler":false}
	OtherConfig *TaskOfOtherConfig `json:"other_cfgs"`

	// 识别并发
	//
	// required: true
	// read only: false
	// example: 0
	Concurrency int `json:"concurrency"`

	// 扫描类型（quick: 极速、common: 深度）
	//
	// required: true
	// read only: false
	// example: quick
	ScanType string `json:"scan_type"`

	// IP类型（1: IPv4 2: IPv6）
	//
	// required: true
	// read only: false
	// example: 300
	IPType int `json:"ip_type"`

	// 选择的IP
	//
	// required: true
	// read only: false
	// example: ["123","567"]
	SelectedIpRanges SelectedIpRanges `json:"selected_ip_ranges"`

	// 自选的poc
	//
	// required: false
	// read only: false
	// example: [ "123","567"]
	SelectedPocs SelectedPocs `json:"selected_pocs"`

	// 上传文件
	//
	// required: false
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/TaskTemplateOfUploadedFile"
	UploadIpFileVisualIds []*TaskTemplateOfUploadedFile `json:"upload_ip_file_visual_ids"`

	// 创建时间
	//
	// required: true
	// read only: false
	// example: 2022-09-28T19:05:59+08:00
	CreatedAt *time.Time `json:"created_at"`

	// 更新时间
	//
	// required: true
	// read only: false
	// example: 2022-09-28T19:05:59+08:00
	UpdatedAt *time.Time `json:"updated_at"`

	// 任务名称（不包含周期任务）
	//
	// required: true
	// read only: false
	// example:
	//   $ref: "#/definitions/UserOfSimple"
	User *UserOfSimple `json:"user"`

	// 漏洞自动告警,(只有包含漏扫时才需要,默认不勾选是false)
	//
	// required: false
	// read only: false
	// example: true
	AutoThreatAnnounce bool `json:"auto_threat_announce"`
}

func (single *TaskTemplateOfSingle) String() string {
	data, _ := json.Marshal(&single)
	return string(data)
}
