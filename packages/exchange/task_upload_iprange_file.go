package exchange

import "sync"

type UploadFileCreateIPRangeRecord struct {
	*TaskImportIpRangeResult
	Error error

	mutex sync.RWMutex
}

func (r *UploadFileCreateIPRangeRecord) SetNewness(values []uint) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if r.Newness == nil {
		r.Newness = make([]uint, 0)
	}

	r.Newness = append(r.Newness, values...)
}

func (r *UploadFileCreateIPRangeRecord) GetNewness() []uint {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	return r.Newness
}

// SetAlready setting the file upload already exists iprange record ids.
func (r *UploadFileCreateIPRangeRecord) SetAlready(values []uint) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if r.Already == nil {
		r.Already = make([]uint, 0)
	}

	r.Already = append(r.Already, values...)
}

// GetAlready returns the upload file already exists iprange record ids.
func (r *UploadFileCreateIPRangeRecord) GetAlready() []uint {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	return r.Already
}

func (r *UploadFileCreateIPRangeRecord) SetFail(value int) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.Fail += value
}

func (r *UploadFileCreateIPRangeRecord) GetFail() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	return r.Fail
}

// NewnessDeduplication return upload file creation newness deduplication iprange ids.
func (r *UploadFileCreateIPRangeRecord) NewnessDeduplication() []uint {
	var set = &sync.Map{}
	for _, id := range r.TaskImportIpRangeResult.Newness {
		set.Store(id, id)
	}

	var list = make([]uint, 0, len(r.TaskImportIpRangeResult.Newness))
	set.Range(func(key, value interface{}) bool {
		list = append(list, key.(uint))
		return true
	})

	return list
}

func (r *UploadFileCreateIPRangeRecord) NewnessNumber() int {
	return len(r.NewnessDeduplication())
}

// Ids return upload file created newness and already uploaded iprange ids.
func (r *UploadFileCreateIPRangeRecord) Ids() []uint {
	var set = &sync.Map{}

	// newness.
	for _, id := range r.TaskImportIpRangeResult.Newness {
		set.Store(id, id)
	}

	// already.
	for _, id := range r.TaskImportIpRangeResult.Already {
		set.Store(id, id)
	}

	var list = make([]uint, 0, len(r.TaskImportIpRangeResult.Newness))
	set.Range(func(key, value interface{}) bool {
		list = append(list, key.(uint))
		return true
	})

	return list
}

// IdsNumber return ids number.
func (r *UploadFileCreateIPRangeRecord) IdsNumber() int {
	return len(r.Ids())
}

func (r *UploadFileCreateIPRangeRecord) IncFailedNumber(val int) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.Fail += val
}

func (r *UploadFileCreateIPRangeRecord) FailedNumber() int {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	return r.Fail
}
