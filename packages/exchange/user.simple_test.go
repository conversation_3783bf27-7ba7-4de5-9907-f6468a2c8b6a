package exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestUserOfSimpleSuite(t *testing.T) {
	suite.Run(t, &UserOfSimpleSuite{})
}

type UserOfSimpleSuite struct {
	suite.Suite
}

func (suite *UserOfSimpleSuite) TestNewUserOfSimpleWithMap_NotNil() {
	assert.NotNil(suite.T(), NewUserOfSimpleWithMap(map[string]interface{}{"id": 1, "username": "kitty"}))
}

func (suite *UserOfSimpleSuite) TestNewUserOfSimpleWithMap_Nil() {
	assert.Nil(suite.T(), NewUserOfSimpleWithMap(nil))
}
