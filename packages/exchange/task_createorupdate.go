package exchange

import (
	"encoding/json"
	"errors"
	"strconv"
	"strings"

	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/packages/statusx"

	"git.gobies.org/foeye-dependencies/logger"
	"git.gobies.org/foeye/foeye3/packages/iprange"
	"git.gobies.org/foeye/foeye3/packages/validation"
)

// TaskOfSelectedPocs tasks the selected pods.
type TaskOfSelectedPocs []*TaskOfSelectedPoc

// TaskOfSelectedPoc task of the selected pod.
type TaskOfSelectedPoc struct {
	Id       string `json:"id"`
	Category string `json:"category"`
}

// IdToInt Convert a TaskOfSelectedPoc id to int.
func (s *TaskOfSelectedPoc) IdToInt() int {
	i, _ := strconv.Atoi(s.Id)
	return i
}

// SelectedIpRanges Selected ip ranges.
type SelectedIpRanges []string

func (s SelectedIpRanges) String() string {
	data, _ := json.Marshal(&s)
	return string(data)
}

func NewSelectedIpRangesWithRawData(raw []byte) SelectedIpRanges {
	var r SelectedIpRanges
	_ = json.Unmarshal(raw, &r)
	return r
}

// SelectedPocs Selected ip ranges.
type SelectedPocs []string

func (s SelectedPocs) String() string {
	data, _ := json.Marshal(&s)
	return string(data)
}

func NewSelectedPocsWithRawData(raw []byte) SelectedPocs {
	var r SelectedPocs
	_ = json.Unmarshal(raw, &r)
	return r
}

// UploadIpFileIds upload iprange files.
type UploadIpFileIds []int

func (s UploadIpFileIds) String() string {
	data, _ := json.Marshal(&s)
	return string(data)
}

func (s UploadIpFileIds) IntS() []int {
	return s
}

// NewUploadIpFileIdsWithRawData return a new upload files instance.
func NewUploadIpFileIdsWithRawData(raw []byte) UploadIpFileIds {
	var r UploadIpFileIds
	_ = json.Unmarshal(raw, &r)
	return r
}

// TaskOfOtherConfig Model.
//
// 数据模型 TaskOfOtherConfig 包含任务高级配置项。
//
// swagger:model TaskOfOtherConfig
type TaskOfOtherConfig struct {
	// 资产精准识别
	//
	// required: false
	// read only: false
	// example: true
	IsDeepScan bool `json:"is_deep_scan"`

	// Treck协议栈指纹检测
	//
	// required: false
	// read only: false
	// example: true
	IsTreckRecognition bool `json:"is_treck_recognition"`

	// ping识别资产
	//
	// required: false
	// read only: false
	// example: true
	IsPingRecognition bool `json:"is_ping_recognition"`

	// 全选协议识别
	//
	// required: false
	// read only: false
	// example: true
	IsCycleProtocol bool ` json:"is_cycle_protocol"`

	// 深度识别操作系统及设备
	//
	// required: false
	// read only: false
	// example: true
	IsDeepRecognition bool `json:"is_deep_recognition"`

	// 域名解析
	//
	// required: false
	// read only: false
	// example: true
	IsCrackDns bool `json:"is_crack_dns"`

	// 版本号识别
	//
	// required: false
	// read only: false
	// example: true
	IsVersionRecognition bool `json:"is_version_recognition"`

	// 是否开启爬虫
	//
	// required: false
	// read only: false
	// example: true
	IsCrawler bool `json:"is_crawler"`

	// 是否禁扫打印机
	//
	// required: false
	// read only: false
	// example: true
	IsDisableScanPrint bool `json:"is_disable_scan_print"`
}

func (config *TaskOfOtherConfig) String() string {
	data, _ := json.Marshal(&config)
	return string(data)
}

func NewTaskOfOtherConfigWithRawData(data []byte) *TaskOfOtherConfig {
	var r *TaskOfOtherConfig
	_ = json.Unmarshal(data, &r)
	return r
}

type IPRangeType string

func (i IPRangeType) String() string {
	return string(i)
}

const (
	IPRangeTypeOfIPRanges      IPRangeType = "ip_ranges"       // 根据IP段筛选
	IPRangeTypeOfUserInput     IPRangeType = "user_input"      // 输入IP段
	IPRangeTypeOfUserInputPort IPRangeType = "user_input_port" // 输入IP段+端口
	IPRangeTypeOfAll           IPRangeType = "all"             // 全部IP
	IPRangeTypeOfUpdateFile    IPRangeType = "upload_file"     // 上传文件
)

// TaskOfUpdate Model.
//
// 数据模型 TaskOfUpdate 编辑任务下发。
//
// swagger:model TaskOfUpdate
type TaskOfUpdate struct {
	// 操作类型(issue:更新并下发;默认可以不传)
	//
	// required: false
	// read only: false
	// example: update
	OperateType string `json:"operate_type" form:"operate_type,default=update"`
}

// TaskOfCreateOrUpdate Model.
//
// 数据模型 TaskOfCreateOrUpdate 添加或编辑任务下发。
//
// swagger:model TaskOfCreateOrUpdate
type TaskOfCreateOrUpdate struct {
	// 记录Id
	//
	// required: true
	// read only: false
	// example: 1
	ID int `json:"id"`

	// 任务名称
	//
	// required: true
	// read only: false
	// example: 标题名称
	Title string `json:"title" form:"title" binding:"required" label:"任务名称"`

	// 任务类型
	//
	// required: true
	// read only: false
	// example: 3
	TaskType int `json:"task_type" form:"task_type" binding:"required" label:"任务类型"`

	// 用户ID
	//
	// required: true
	// read only: false
	// example: 1
	UserID uint `json:"-"`

	// IP类型. 可选项(输入ip信息为: user_input,上传IP信息文件: upload_file,全部ip: all ,根据ip段筛选 :ip_ranges)
	//
	// required: true
	// read only: false
	// example: user_input
	IPRangeType string `json:"ip_range_type" form:"ip_range_type" label:"IP类型"`

	// 带宽.
	//
	// required: true
	// read only: false
	// example: 300
	Bandwidth int `json:"bandwidth" form:"bandwidth" binding:"required" label:"带宽"`

	// 扫描端口模板Id
	//
	// required: true
	// read only: false
	// example: 7
	ScanPortTemplateID int `json:"scan_port_template_id" form:"scan_port_template_id" label:"扫描端口模板Id"`

	// POC扫描类型(普通PoC:normal, 弱口令PoC:week_pwd, 全部PoC:all, 指定PoC:special).
	//
	// required: true
	// read only: false
	// example:
	PocScanType string `json:"poc_scan_type" form:"poc_scan_type" label:"POC扫描类型"`

	// 类别
	//
	// required: true
	// read only: false
	// example: task
	Category string `json:"category"`

	// 识别并发
	//
	// required: false
	// read only: false
	// example: 0
	Concurrency int `json:"concurrency"`

	// 其他配置
	//
	// required: true
	// read only: false
	// example:
	//   $ref: "#/definitions/TaskOfOtherConfig"
	OtherConfig *TaskOfOtherConfig `json:"other_cfgs"`

	// 扫描类型(快速为:quick, 深度为:common)
	//
	// required: true
	// read only: false
	// example:  quick
	ScanType string `json:"scan_type"`

	// IP类型(ipv4:1, ipv6:2)
	//
	// required: true
	// read only: false
	// example: 1
	IPType int `json:"ip_type"` // IP类型: 1.IPv4 2.IPv6

	// 选择的IP
	//
	// required: true
	// read only: false
	// example: ["123","567"]
	SelectedIpRanges SelectedIpRanges `json:"selected_ip_ranges"`

	// 自选的poc
	//
	// required: false
	// read only: false
	// example: ["123","567"]
	SelectedPocs SelectedPocs `json:"selected_pocs"`

	// 上传文件Id列表
	//
	// required: false
	// read only: false
	// example: [ "123","567"]
	UploadIpFileIds UploadIpFileIds `json:"upload_ip_file_ids"`

	// 重复类型（once: 执行一次, weekly: 每周重复, daily: 每日重复, monthly: 每月重复）
	//
	// required: true
	// read only: false
	// example: once
	RepeatType string `json:"repeat_type"`

	// 记录数量
	//
	// required: true
	// read only: false
	// example: 1
	Num string `json:"num"`

	// 开始时间
	//
	// required: true
	// read only: false
	// example: 00:00
	BeginTime string `json:"begin_time"`

	// 备注信息
	//
	// required: true
	// read only: false
	// example: 常规资产盘查，速度快、资产较全。
	Comment string `json:"comment" binding:"max=15"`

	// 漏洞自动告警,(只有包含漏扫时才需要,默认不勾选是false)
	//
	// required: true
	// read only: false
	// example: false
	AutoThreatAnnounce bool `json:"auto_threat_announce"`
}

func (s *TaskOfCreateOrUpdate) ToAdditionTask() *m_task.AdditionTask {
	tmp := &m_task.AdditionTask{
		ID:                 s.ID,
		Title:              s.Title,
		TaskType:           s.TaskType,
		IPRangeType:        s.IPRangeType,
		Bandwidth:          s.Bandwidth,
		ScanPortTemplateID: s.ScanPortTemplateID,
		PocScanType:        s.PocScanType,
		Category:           s.Category,
		OtherConfig:        m_task.OtherConfig{},
		ScanType:           s.ScanType,
		IPType:             s.IPType,
	}
	if s.OtherConfig != nil {
		tmp.OtherConfig = m_task.OtherConfig(*s.OtherConfig)
	}
	return tmp
}

// TrimSpace 去除左右的空格
func (s *TaskOfCreateOrUpdate) TrimSpace() {
	for i, v := range s.SelectedIpRanges {
		s.SelectedIpRanges[i] = strings.TrimSpace(v)
	}
}

// LineValidation 检查扫描目标行数
func (s *TaskOfCreateOrUpdate) LineValidation() (result bool, err error) {
	if s.IPRangeType == IPRangeTypeOfUserInput.String() {
		result = len(s.SelectedIpRanges) > 10000
		err = errors.New(statusx.TaskStatusFailedWithLineValidationTenThousandOfMessage)
	} else if s.IPRangeType == IPRangeTypeOfUserInputPort.String() {
		result = len(s.SelectedIpRanges) > 1000
		err = errors.New(statusx.TaskStatusFailedWithLineValidationThousandOfMessage)
	}
	s.TrimSpace()
	return result, err
}

// InputValidation 检查IP
func (s *TaskOfCreateOrUpdate) InputValidation() iprange.Errors {
	var errs iprange.Errors

	// Verify IPs is invalidation.
	addressType := iprange.IPAddressType(s.IPType)

	// If task type is vulnerability.
	if s.TaskType == 2 {
		addressType = iprange.IPAddressTypeOfAll
	}

	errMessage, err := iprange.ValidationIPs(s.SelectedIpRanges, addressType)
	logger.Infow("[TASK_TEMPLATE@CREATE] iprange.ValidationIPs result", "err_message", errMessage, "error", err)
	if err != nil && errMessage != nil {
		errs = errs.Append(errMessage.Elements()...)
	}

	// Verify Domain
	errDomain, err := iprange.ParseDomainsIgnoreIPv6(s.SelectedIpRanges)
	logger.Infow("[TASK_TEMPLATE@CREATE] iprange.ParseDomains result", "err_message", errDomain, "error", err)
	if err != nil && errDomain != nil {
		errs = errs.Append(errDomain.Elements()...)
	}

	var args = []interface{}{"ips", errMessage, "domains", errDomain, "all", errs}
	logger.Infow("[TASK_TEMPLATE@CREATE] Parse information", args...)
	return errs
}

// InputPortValidation 检查IP+端口
func (s *TaskOfCreateOrUpdate) InputPortValidation() iprange.Errors {
	var errs iprange.Errors
	tmpSelectIpRanges := make([]string, 0)
	for i, selectIpRange := range s.SelectedIpRanges {
		splits := strings.SplitN(selectIpRange, ":", 2)
		if len(splits) != 2 {
			errs = errs.Append(&iprange.Error{
				Line:      i + 1,
				IPAddress: selectIpRange,
			})
			continue
		}
		ports := strings.Split(splits[1], ",")
		if len(ports) == 0 {
			errs = errs.Append(&iprange.Error{
				Line:      i + 1,
				IPAddress: selectIpRange,
			})
			continue
		}
		if len(ports) > 50 {
			errs = errs.Append(&iprange.Error{
				Line:      i + 1,
				IPAddress: selectIpRange,
			})
			continue
		}
		if err := validation.CheckPort(ports); err != nil {
			errs = errs.Append(&iprange.Error{
				Line:      i + 1,
				IPAddress: selectIpRange,
			})
			continue
		}
		tmpSelectIpRanges = append(tmpSelectIpRanges, splits[0])
	}
	tmpAddition := *s
	tmpAddition.SelectedIpRanges = tmpSelectIpRanges

	errMs := tmpAddition.InputValidation()
	if errMs != nil {
		errs = errs.Append(errMs...)
	}
	for _, val := range errs.Elements() {
		if val.Line < len(s.SelectedIpRanges) {
			val.IPAddress = s.SelectedIpRanges[val.Line-1]
		}
	}
	return errs
}

// TaskOfSelectedPocs select poc convert to structure.
func (s *TaskOfCreateOrUpdate) TaskOfSelectedPocs() TaskOfSelectedPocs {
	if s.SelectedPocs != nil {
		var r TaskOfSelectedPocs
		for _, poc := range s.SelectedPocs {
			slice := strings.Split(poc, "@")
			if len(slice) != 2 {
				continue
			}
			if r == nil {
				r = make(TaskOfSelectedPocs, 0, len(slice))
			}
			r = append(r, &TaskOfSelectedPoc{Id: slice[0], Category: slice[1]})
		}

		return r
	}

	return nil
}
