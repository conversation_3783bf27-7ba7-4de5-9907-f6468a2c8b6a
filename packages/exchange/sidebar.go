package exchange

type SidebarOfMultiple []*SidebarOfSingle

func (multiple *SidebarOfMultiple) Filter() SidebarOfMultiple {
	var r []*SidebarOfSingle
	for _, single := range *multiple {
		if single.Value != 0 {
			r = append(r, single)
		}
	}
	return r
}

func (multiple *SidebarOfMultiple) Total() int {
	var summer int
	for _, single := range multiple.Filter() {
		summer += single.Value
	}
	return summer
}

func (multiple *SidebarOfMultiple) Append(item ...*SidebarOfSingle) SidebarOfMultiple {
	*multiple = append(*multiple, item...)
	return *multiple
}

func (multiple *SidebarOfMultiple) Increment(title, key, subKey string) {
	if !multiple.existsWithTitleAndKey(title, key) {
		*multiple = append(*multiple, &SidebarOfSingle{Title: title, Key: key, Value: 1, Children: SidebarOfChildren{SidebarOfChildrenItem{Key: subKey, Value: 1}}})
	} else {
		for _, single := range *multiple {
			if single.Key == key && single.Title == title {
				children := single.Children.(SidebarOfChildren)
				children.Increment(subKey)
				single.Value++
				single.Children = children
			}
		}
	}
}

func (multiple SidebarOfMultiple) existsWithTitleAndKey(title, key string) bool {
	for _, single := range multiple {
		if single.Title == title && single.Key == key {
			return true
		}
	}
	return false
}

// SidebarOfSingle
// Used to return a sidebar of single structure information.
// swagger:model
type SidebarOfSingle struct {
	// 关联资产描述
	//
	// required: true
	// read only: false
	// example: 端口
	Title string `json:"title"`

	// 关联资产键名
	//
	// required: true
	// read only: false
	// example: port
	Key string `json:"key"`

	// 关联资产数
	//
	// required: true
	// read only: 56
	Value int `json:"value"`

	// 子数据集
	//
	// required: true
	// read only: false
	// example:
	//   type: array
	//   items:
	//     $ref: "#/definitions/SidebarOfChildrenItem"
	Children interface{} `json:"children,omitempty"`
}
