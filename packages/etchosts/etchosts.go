package etchosts

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"git.gobies.org/foeye/foeye3/packages/util"

	"git.gobies.org/foeye-dependencies/fsfire"
)

const (
	DefaultHostsFileSavePath = "/etc/hosts"
	DefaultDCCServerHostname = "dcc.hsxa.net"
)

// Hosts operate hosts implement structure
type Hosts struct {
	reader *bytes.Buffer
}

// NewHosts returns a new Hosts
func NewHosts(reader *bytes.Buffer) *Hosts {
	return &Hosts{reader: reader}
}

// AddParse add host parse
func (hosts *Hosts) AddParse(host, domain string) (bool, error) {
	n, err := hosts.reader.WriteString(fmt.Sprintf("\n%s \t%s", host, domain))
	if err != nil {
		return false, err
	}

	return n != 0, nil
}

// AddOrReplaceParse add host parse or replace
func (hosts *Hosts) AddOrReplaceParse(address, domain string) (bool, error) {
	address = strings.TrimSpace(address)
	domain = strings.TrimSpace(domain)

	var err error
	address, _, err = util.GetFixUrlHostname(address, "")
	if err != nil {
		return false, err
	}

	if len(address) == 0 || len(domain) == 0 {
		return false, fmt.Errorf("address or domain invalid")
	}

	lines, err := fsfire.GetFileContentStringSliceWithBuffer(hosts.reader)
	if err != nil {
		return false, err
	}

	for idx, line := range lines {
		slices := strings.Split(line, " ")
		if len(slices) != 2 {
			continue
		}

		space := strings.TrimSpace(string(slices[1]))
		if space == domain {
			fmt.Println("space == domain", space == domain)
			if idx == len(lines) {
				lines = lines[:len(line)-1]
				continue
			}

			lines = append(lines[:idx], lines[idx+1:]...)
		}
	}

	// rewrite
	hosts.reader.Truncate(0)
	for _, line := range lines {
		hosts.reader.WriteString(fmt.Sprintf("%s\n", line))
	}

	n, err := hosts.reader.WriteString(fmt.Sprintf("\n%s \t%s", address, domain))
	if err != nil {
		return false, err
	}

	return n != 0, nil
}

// WriteFile add host parse
func (hosts *Hosts) WriteFile(filename string) error {
	abs, err := filepath.Abs(filename)
	if err != nil {
		return err
	}

	err = fsfire.NotExistsMkdir(filepath.Dir(abs))
	if err != nil {
		return err
	}

	file, err := os.OpenFile(filename, os.O_WRONLY|os.O_APPEND|os.O_CREATE, os.ModePerm)
	if err != nil {
		return err
	}

	err = file.Truncate(0)
	if err != nil {
		return err
	}

	_, err = hosts.reader.WriteTo(file)
	if err != nil {
		return err
	}

	return nil
}
