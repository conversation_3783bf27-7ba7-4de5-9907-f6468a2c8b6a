package etchosts

import (
	"os"
	"os/exec"
	"path/filepath"

	"git.gobies.org/foeye-dependencies/fsfire"
)

const (
	shPath = "tools/scripts/update_etc_hosts.sh"
)

type EtcHosts struct {
	DomainName string
	Host       string
}

func WriteEtcHosts(eh *EtcHosts) ([]byte, error) {
	if err := NotExistsCreateShell(); err != nil {
		return nil, err
	}
	cmd := exec.Command("sudo", "./"+shPath, eh.DomainName, eh.Host)
	cmd.Stderr = os.Stderr
	return cmd.Output()
}

// NotExistsCreateShell  no file with create
func NotExistsCreateShell() error {
	if !fsfire.IsNotExists(shPath) {
		return nil
	}
	abs, err := filepath.Abs(shPath)
	if err != nil {
		return err
	}
	err = fsfire.NotExistsMkdir(filepath.Dir(abs))
	if err != nil {
		return err
	}

	//_, err = fsfire.CreateFile(shPath, []byte(shStr))
	//if err != nil {
	//	return err
	//}

	f, err := os.Create(abs)
	f.Chmod(os.ModePerm)
	defer f.Close()
	_, err = f.Write([]byte(shStr))
	if err != nil {
		return err
	}
	return nil
}

// shell 脚本内容
var shStr = `# !/bin/sh
###################
#@in_url 输入的url
#@in_ip 输入的ip
#@example sudo ./update_etc_hosts.sh test.com 127.0.01
##########################
in_url=${1}
in_ip=${2}
 
#更改host
updateHost()
{
# read 
  inner_host=` + "`cat /etc/hosts | grep ${in_url} | awk '{print $1}'`" + `
  if [ ${inner_host} = ${in_ip} ];then
     echo "${inner_host}  ${in_url} ok"
  else
    #替换
     sed -i "s#${inner_host}#${in_ip}#g" /etc/hosts
     if [ $? = 0 ];then
       echo "change ${inner_host} to ${in_ip} ok"
       else
         inner_ip_map="${in_ip} ${in_url}"
         echo ${inner_ip_map} >> /etc/hosts
         if [ $? = 0 ]; then
           echo "${inner_ip_map} to hosts success host is ` + "`cat /etc/hosts`" + `"
         fi
     fi
  fi
}

main()
{
   updateHost
}
main
`
