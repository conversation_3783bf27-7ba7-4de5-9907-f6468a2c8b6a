package etchosts

import (
	"bytes"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestEtcHostsSuite(t *testing.T) {
	suite.Run(t, &EtcHostsSuite{})
}

type EtcHostsSuite struct {
	suite.Suite
	hosts *Hosts
}

const host = `
127.0.0.1   localhost localhost.localdomain localhost4 localhost4.localdomain4
::1         localhost localhost.localdomain localhost6 localhost6.localdomain6
--ip_host begin--

--ip_host end--
************ 	hello.com`

func (suite *EtcHostsSuite) BeforeTest(suiteName, testName string) {
	suite.hosts = NewHosts(bytes.NewBuffer([]byte(host)))
}

func (suite *EtcHostsSuite) Test_AddParse() {
	suite._AddParse()
}

func (suite *EtcHostsSuite) _AddParse() {
	actual, err := suite.hosts.AddParse("***********", "china.com")
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), actual)

	// baidu.com
	actual, err = suite.hosts.AddParse("**************", "baidu.com")
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), actual)
}

func (suite *EtcHostsSuite) Test_AddOrReplaceParse_Replace() {
	actual, err := suite.hosts.AddOrReplaceParse("***********", "hello.com")
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), actual)

	err = suite.hosts.WriteFile("./etc/hosts")
	assert.NoError(suite.T(), err)
}

func (suite *EtcHostsSuite) Test_AddOrReplaceParse_Add() {
	actual, err := suite.hosts.AddOrReplaceParse("************", "world.com")
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), actual)

	err = suite.hosts.WriteFile("./etc/hosts")
	assert.NoError(suite.T(), err)
}

func (suite *EtcHostsSuite) Test_WriteFile() {
	suite._AddParse()

	err := suite.hosts.WriteFile("./etc/hosts")
	assert.NoError(suite.T(), err)
}
