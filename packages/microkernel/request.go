package microkernel

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"git.gobies.org/foeye-dependencies/logger"
	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/exchange"
)

type Microkernel struct {
	Host    string
	Timeout int
}

func TaskStop(taskId int) (err error) {
	url := fmt.Sprintf("%s%s", config.GetConfigure().Microkernel.Host, "/api/v1/tasks/stop")
	params := fmt.Sprintf(`{"task_id": "%v", "force": true}`, taskId)
	defer func() {
		if err != nil {
			logger.Errorf("[SYSTEM]request microkernel err: url(%+v), params(%+v), resp(%+v)", url, params, err)
		}
	}()

	request := &http.Client{
		Timeout: time.Duration(time.Duration(config.GetConfigure().Microkernel.Timeout) * time.Second),
	}
	resp, err := request.Post(url, "application/json", strings.NewReader(params))
	if err != nil {
		return err
	}

	defer resp.Body.Close()
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	logger.Infof("[SYSTEM]request microkernel info: url(%+v), params(%+v), resp(%+v)", url, params, string(data))

	var r *exchange.SystemInfoResponse
	if err := json.Unmarshal(data, &r); err != nil {
		return err
	}

	if r != nil && r.StatusCode == http.StatusOK && r.Data != nil {
		return nil
	}

	return nil
}
