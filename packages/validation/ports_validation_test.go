package validation

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestPortValidationSuite(t *testing.T) {
	suite.Run(t, new(PortValidationSuite))
}

type PortValidationSuite struct {
	suite.Suite
}

func (suite *PortValidationSuite) Test_ParsePortRawData() {
	grids := []struct {
		str      string
		expected []string
	}{
		{
			str: `80
90
8001-9001
50A
30d-9900`,
			expected: []string{"80", "90", "8001-9001"},
		},
	}

	for _, grid := range grids {
		actual := ParsePortRawDataFilter(grid.str)
		assert.Equal(suite.T(), grid.expected, actual)
	}
}

func (suite *PortValidationSuite) Test_ParsePortSlice() {
	grids := []struct {
		str      []string
		expected []string
		error    error
	}{
		{
			str:      []string{"80", "90", "8001-9001", "50A", "30d-9900", "895\n100"},
			expected: nil,
			error:    fmt.Errorf("第4行端口格式错误"),
		},
		{
			str:      []string{"80", "90", "8001-9001"},
			expected: []string{"80", "90", "8001-9001"},
			error:    nil,
		},
		{
			str:      []string{"0-65535"},
			expected: []string{"0-65535"},
			error:    nil,
		},
		{
			str:      []string{"1-65536"},
			expected: nil,
			error:    fmt.Errorf("第1行端口格式错误"),
		},
		{
			str:      []string{"0-65536"},
			expected: nil,
			error:    fmt.Errorf("第1行端口格式错误"),
		},
		{
			str:      []string{"1-65535"},
			expected: []string{"1-65535"},
			error:    nil,
		},
	}

	for _, grid := range grids {
		actual, err := ParsePortSlice(grid.str)
		if err != nil {
			assert.Equal(suite.T(), grid.error, err)
		}
		assert.Equal(suite.T(), grid.expected, actual)
	}
}

func (suite *PortValidationSuite) Test_ExpansionPortsSlice() {
	grids := []struct {
		str      []string
		expected []string
		error    error
	}{
		{
			str:      []string{"80", "90", "2-5", "50A", "2d-5"},
			expected: []string{"80", "90", "2", "3", "4", "5"},
			error:    nil,
		},
		{
			str:      []string{"80", "90b", "2-5"},
			expected: []string{"80", "2", "3", "4", "5"},
			error:    nil,
		},
	}

	for _, grid := range grids {
		actual, err := ExpansionPortsSlice(grid.str)
		if err != nil {
			assert.Equal(suite.T(), grid.error, err)
		}
		assert.Equal(suite.T(), grid.expected, actual)
	}
}
