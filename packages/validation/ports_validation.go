package validation

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"git.gobies.org/foeye/foeye3/packages/stringsx"
)

const (
	DelimiterForMiddleLine = "-"
	DelimiterForEndLine    = "\n"
)

var ErrOfInvalidPortItem = errors.New("contain invalid port data")

func errOfInvalidPortItemChina(num int) error {
	return errors.New(fmt.Sprintf("第%d行端口格式错误", num))
}

// ParsePortRawDataFilter 将端口原始数据转换为字符串切片.
// 处理过程：
//
// 		Step1: 按行分割.
//		Step2: 如果包含中划线则验证双边是否可以转换为数字.
//
//		注意不符合规则的数据则忽略.
func ParsePortRawDataFilter(ports string) []string {

	sli := strings.Split(ports, DelimiterForEndLine)
	res := make([]string, 0, len(sli))

	for _, item := range sli {
		if strings.Contains(item, DelimiterForMiddleLine) {
			subPorts := strings.Split(item, DelimiterForMiddleLine)
			allowed := true
			for _, subPort := range subPorts {

				if !stringsx.StringIsNumber(subPort) {
					allowed = false
					break
				}

			}

			if allowed {
				res = append(res, item)
			}

		} else {
			if stringsx.StringIsNumber(item) {
				res = append(res, item)
			}
		}
	}

	return res
}

// ParsePortSlice 将端口原始数据转换为字符串切片.
// 处理过程：
//
// 		Step1: 按行分割.
//		Step2: 如果包含中划线则验证双边是否可以转换为数字.
//
//		注意不符合规则的数据则忽略.
func ParsePortSlice(ports []string) ([]string, error) {
	res := make([]string, 0, len(ports))
	for i, item := range ports {
		if strings.Contains(item, DelimiterForMiddleLine) {
			subPorts := strings.Split(item, DelimiterForMiddleLine)
			for _, subPort := range subPorts {
				if !stringsx.StringIsNumber(subPort) {
					return nil, errOfInvalidPortItemChina(i + 1)
				}
				if port, err := strconv.Atoi(subPort); err != nil || port < 0 || port > 65535 {
					return nil, errOfInvalidPortItemChina(i + 1)
				}
			}
			res = append(res, item)
		} else {
			if stringsx.StringIsNumber(item) {
				res = append(res, item)
			} else {
				return nil, errOfInvalidPortItemChina(i + 1)
			}
		}
	}
	return res, nil
}

// ExpansionPortsSlice 展开一个端口范围
// 处理过程：
//
// 		1.按"-"分割,如果分割后返回的数组长度不等于2就忽略这个端口范围
//		2.把"-"左右两边的字符串转成数字，转换失败就忽略这个端口范围
//		3.遍历2中的数字范围
//		注意端口范围应该从小到大,反之会忽略这个端口范围
// 80-85 		["80","81","82","83","84","85"]
// 80a-35		[]
// ["80a","68"]	["80a","68"]
func ExpansionPortsSlice(ports []string) ([]string, error) {
	var data []string
	for _, port := range ports {
		if strings.Contains(port, "-") {
			portRange := strings.Split(port, "-")
			if len(portRange) == 2 {
				portLeft, err := strconv.Atoi(portRange[0])
				if err != nil {
					continue
				}
				portRight, err := strconv.Atoi(portRange[1])
				if err != nil {
					continue
				}
				for i := portLeft; i <= portRight; i++ {
					data = append(data, fmt.Sprintf("%d", i))
				}
			}
		} else {
			_, err := strconv.Atoi(port)
			if err != nil {
				continue
			}
			data = append(data, port)
		}
	}
	return data, nil
}

// CheckPort 简单检查端口格式
func CheckPort(ports []string) error {
	for _, port := range ports {
		p, err := strconv.Atoi(port)
		if err != nil {
			return errors.New(fmt.Sprintf("端口格式不正确:%s", port))
		}
		if p <= 0 || p > 65535 {
			return errors.New(fmt.Sprintf("输入端口不正确:%s", port))
		}
	}
	return nil
}
