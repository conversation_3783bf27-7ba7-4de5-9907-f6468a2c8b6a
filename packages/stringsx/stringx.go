package stringsx

import (
	"regexp"
	"strconv"
	"strings"
)

var number = regexp.MustCompile(`^[0-9]+$`)

// StringIsNumber Check parameter is number.
func StringIsNumber(str string) bool {
	return number.MatchString(str)
}

// RemoveDuplicationSlice 字符串数组切片去重
func RemoveDuplicationSlice(arr []string) []string {
	set := make(map[string]struct{}, len(arr))
	j := 0
	for _, v := range arr {
		_, ok := set[v]
		if ok {
			continue
		}
		set[v] = struct{}{}
		arr[j] = v
		j++
	}
	return arr[:j]
}

// Intersect 两个字符串数组求交集
func Intersect(slice1, slice2 []string) []string {
	m := make(map[string]int)
	nn := make([]string, 0)
	for _, v := range slice1 {
		if _, ok := m[v]; !ok {
			m[v]++
		}
	}
	for _, v := range slice2 {
		times, _ := m[v]
		if times == 1 {
			nn = append(nn, v)
		}
	}
	return nn
}

// UintIntersect
func UintIntersect(slice1, slice2 []uint) []uint {
	m := make(map[uint]int)
	nn := make([]uint, 0)
	for _, v := range slice1 {
		m[v]++
	}
	for _, v := range slice2 {
		times, _ := m[v]
		if times == 1 {
			nn = append(nn, v)
		}
	}
	return nn
}

// Difference 两个字符串数组求差集
func Difference(slice1, slice2 []string) []string {
	m := make(map[string]int)
	nn := make([]string, 0)
	inter := Intersect(slice1, slice2)
	for _, v := range inter {
		m[v]++
	}
	for _, value := range slice1 {
		times, _ := m[value]
		if times == 0 {
			nn = append(nn, value)
		}
	}
	return nn
}

// MapUint 字符串数组转换成uint数组
func MapUint(a []string) (result []uint, err error) {
	for _, v := range a {
		i, err := strconv.Atoi(v)
		if err != nil {
			return result, err
		}

		result = append(result, uint(i))
	}

	return
}

// IsContains 字符串是否在数组中
func IsContains(item string, items []string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

// Union 求并集
func Union(slice1, slice2 []string) []string {
	m := make(map[string]int)
	for _, v := range slice1 {
		m[v]++
	}

	for _, v := range slice2 {
		times, _ := m[v]
		if times == 0 {
			slice1 = append(slice1, v)
		}
	}
	return slice1
}

// UniqueStrings 接收一个字符串切片，按指定分隔符分割每个元素，并返回去重后的字符串切片。
func UniqueStrings(input []string, separator string) []string {
	uniqueSet := make(map[string]struct{})

	for _, item := range input {
		parts := strings.Split(item, separator)
		for _, part := range parts {
			part = strings.TrimSpace(part) // 去除空格
			if part != "" {                // 忽略空字符串
				uniqueSet[part] = struct{}{}
			}
		}
	}

	var result []string
	for key := range uniqueSet {
		result = append(result, key)
	}

	return result
}

// ExtractAndRemoveKeywords 提取并删除指定关键词
func ExtractAndRemoveKeywords(input string, keywords []string) (string, string) {
	// 分割原始字符串为切片
	parts := strings.Split(input, ",")

	// 用于标记是否已提取关键词
	extractedMap := make(map[string]bool)

	// 遍历每个部分
	remainingParts := make([]string, 0)
	extracted := make([]string, 0)
	for _, part := range parts {
		part = strings.TrimSpace(part)
		found := false

		// 检查是否是目标关键词
		for _, keyword := range keywords {
			if part == keyword {
				if !extractedMap[keyword] {
					extracted = append(extracted, keyword)
					extractedMap[keyword] = true
				}
				found = true
				break
			}
		}

		if !found && part != "" {
			remainingParts = append(remainingParts, part)
		}
	}

	return strings.Join(extracted, ","), strings.Join(remainingParts, ",")
}
