package stringsx

import (
	"reflect"
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStringIsNumber(t *testing.T) {
	grids := []struct {
		str      string
		expected bool
	}{
		{
			str:      "4300",
			expected: true,
		},
		{
			str:      "A4300",
			expected: false,
		},
		{
			str:      "430A0",
			expected: false,
		},
		{
			str:      "55",
			expected: true,
		},
	}

	for _, grid := range grids {
		actual := StringIsNumber(grid.str)
		assert.Equal(t, grid.expected, actual)
	}
}

func TestRemoveDuplicationSlice(t *testing.T) {
	origin := []string{"a", "b", "c", "d", "e", "a", "e", "1", "1"}
	expect := []string{"a", "b", "c", "d", "e", "1"}
	result := RemoveDuplicationSlice(origin)
	t1 := reflect.DeepEqual(origin, result)
	assert.Equal(t, false, t1)
	t2 := reflect.DeepEqual(expect, result)
	assert.Equal(t, true, t2)
}

func TestDifference(t *testing.T) {
	a1 := []string{"a", "b", "c"}
	a2 := []string{"b", "d"}
	res := Difference(a1, a2)
	assert.NotNil(t, res)
	assert.Equal(t, 2, len(res))
	assert.Contains(t, res, "a")
	assert.Contains(t, res, "c")
}

func TestIntersect(t *testing.T) {
	a1 := []string{"a", "b", "c"}
	a2 := []string{"b", "d"}
	res := Intersect(a1, a2)
	assert.NotNil(t, res)
	assert.Contains(t, res, "b")
}

func TestUnion(t *testing.T) {
	a1 := []string{"a", "b", "c"}
	a2 := []string{"b", "d"}
	res := Union(a1, a2)
	assert.NotNil(t, res)
	assert.Equal(t, len(res), 4)
}

func TestUniqueStrings(t *testing.T) {
	tests := []struct {
		name      string
		input     []string
		separator string
		expected  []string
	}{
		{
			name:      "Multiple strings with overlapping values",
			input:     []string{"admin,user", "user,editor", "admin,editor,user"},
			separator: ",",
			expected:  []string{"admin", "user", "editor"},
		},
		{
			name:      "Strings with extra spaces",
			input:     []string{" admin , user", "editor , user"},
			separator: ",",
			expected:  []string{"admin", "user", "editor"},
		},
		{
			name:      "Empty strings in input",
			input:     []string{"", "admin,", ",user"},
			separator: ",",
			expected:  []string{"admin", "user"},
		},
		{
			name:      "Different separator",
			input:     []string{"admin|user", "user|editor"},
			separator: "|",
			expected:  []string{"admin", "user", "editor"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := UniqueStrings(tt.input, tt.separator)

			// 排序以确保顺序一致
			sort.Strings(result)
			sort.Strings(tt.expected)

			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestExtractAndRemoveKeywords(t *testing.T) {
	tests := []struct {
		name          string
		input         string
		keywords      []string
		wantExtracted string
		wantRemaining string
	}{
		{
			name:          "empty input",
			input:         "",
			keywords:      []string{"A", "B"},
			wantExtracted: "",
			wantRemaining: "",
		},
		{
			name:          "no keywords match",
			input:         "X, Y, Z",
			keywords:      []string{"A", "B"},
			wantExtracted: "",
			wantRemaining: "X,Y,Z",
		},
		{
			name:          "exact match",
			input:         "A, B, C",
			keywords:      []string{"A", "B"},
			wantExtracted: "A,B",
			wantRemaining: "C",
		},
		{
			name:          "partial match",
			input:         "A, X, B, Y",
			keywords:      []string{"A", "B"},
			wantExtracted: "A,B",
			wantRemaining: "X,Y",
		},
		{
			name:          "duplicate keywords in input",
			input:         "A, A, B, B, C",
			keywords:      []string{"A", "B"},
			wantExtracted: "A,B",
			wantRemaining: "C",
		},
		{
			name:          "with spaces",
			input:         " A ,  B  , C , D ",
			keywords:      []string{"A", "B"},
			wantExtracted: "A,B",
			wantRemaining: "C,D",
		},
		{
			name:          "empty parts",
			input:         "A,,B,,C",
			keywords:      []string{"A", "B"},
			wantExtracted: "A,B",
			wantRemaining: "C",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotExtracted, gotRemaining := ExtractAndRemoveKeywords(tt.input, tt.keywords)
			if gotExtracted != tt.wantExtracted {
				t.Errorf("ExtractAndRemoveKeywords() extracted = %v, want %v", gotExtracted, tt.wantExtracted)
			}
			if gotRemaining != tt.wantRemaining {
				t.Errorf("ExtractAndRemoveKeywords() remaining = %v, want %v", gotRemaining, tt.wantRemaining)
			}
		})
	}
}
