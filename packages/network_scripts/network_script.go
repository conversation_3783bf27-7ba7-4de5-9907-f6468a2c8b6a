package network_scripts

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"

	"git.gobies.org/foeye/foeye3/packages/fsx"
	log "github.com/sirupsen/logrus"

	"github.com/spf13/viper"
)

const (
	NetworkScriptsEth0Path     = "/etc/sysconfig/network-scripts"
	NetworkScriptsEth0FileName = "ifcfg-eth0"
	NetworkScriptsEth1Path     = "/etc/sysconfig/network-scripts"
	NetworkScriptsEth1FileName = "ifcfg-eth1"
)

//NetworkScript 网卡信息
type NetworkScript struct {
	TYPE               string // TYPE 网络类型 Ethernet 以太网
	PROXY_METHOD       string // PROXY_METHOD 代理方式
	BROWSER_ONLY       string // BROWSER_ONLY 只是浏览器：否
	BOOTPROTO          string // BOOTPROTO 网卡协议 获取网卡IP的方式 dhcp：动态主机配置协议 static：手动配置固定IP none：手动配置固定IP
	DEFROUTE           string // DEFROUTE ：默认路由：是
	IPV4_FAILURE_FATAL string // IPV4_FAILURE_FATAL 是否开启IPV4致命错误检测：否
	PV6INIT            string // PV6INIT IPV6初始化: 是
	IPV6_AUTOCONF      string // IPV6_AUTOCONF IPV6是否自动配置：是
	IPV6_DEFROUTE      string // IPV6_DEFROUTE IPV6 默认路由：是
	IPV6_FAILURE_FATAL string // IPV6_FAILURE_FATAL 是否开启IPV6致命错误检测：否
	IPV6_ADDR_GEN_MODE string // IPV6_ADDR_GEN_MODE IPV6地址生成模型  stable-privacy:一种生成IPV6的策略
	NAME               string // NAME 网卡名称
	UUID               string // UUID 唯一标识码
	DEVICE             string // DEVICE 网卡设备名称
	ONBOOT             string // ONBOOT 在开机或重启时是否启动网卡
	IPADDR             string // IPADDR 手动配置静态ip地址
	NETMASK            string // NETMASK 子网掩码
	GATEWAY            string // GATEWAY 默认网关
	DNS1               string // DNS1 dns1
	DNS2               string // DNS2 dns2
	HWADDR             string // HWADDR mac地址
}

func GetNetworkScript(path, fileName string) (*NetworkScript, error) {
	if fsx.IsNotExists(path + "/" + fileName) {
		return nil, fmt.Errorf("file %s not exists", fileName)
	}
	// newFileName := fmt.Sprintf("./%s.env", fileName)
	// err := readFileThenCreate(path, fileName, newFileName)

	fullPath := filepath.Join(path, fileName)
	log.Debugf("network script fullpath  %s", fullPath)

	conf := &NetworkScript{}
	v := viper.New()
	v.SetConfigFile(fullPath)
	v.SetConfigType("env")

	if err := v.ReadInConfig(); err != nil {
		return nil, err
	}

	if err := v.Unmarshal(&conf); err != nil {
		return nil, err
	}
	log.Debugf("fullpath parse data, %+v", conf)
	// os.Remove(newFileName)

	return conf, nil
}

// readFileThenCreate 创建模拟数据文件
func readFileThenCreate(path, fileName, newFileName string) error {
	file, err := os.OpenFile(path+"/"+fileName, os.O_APPEND|os.O_RDWR|os.O_CREATE, os.ModePerm)
	log.Info("read file: ", path+"/"+fileName)
	if err != nil {
		return err
	}
	content, err := ioutil.ReadAll(file)
	if err != nil {
		return err
	}

	f, err := os.Create(newFileName)
	log.Info("create file: ", newFileName)
	if err != nil {
		return err
	}
	defer f.Close()
	defer file.Close()
	_, err = f.Write(content)
	if err != nil {
		return err
	}
	return nil
}
