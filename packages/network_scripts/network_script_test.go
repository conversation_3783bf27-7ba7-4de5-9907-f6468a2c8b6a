package network_scripts

import (
	"fmt"
	"os"
	"testing"

	"git.gobies.org/foeye/foeye3/packages/fsx"
)

func TestGetNetworkScript(t *testing.T) {
	err := fsx.NotExistsMkDir("./network-scripts")
	if err != nil {
		panic(fmt.Errorf("create a folder failed: %v", err))
	}
	// 没有测试数据创建模拟数据
	if fsx.IsNotExists("./network-scripts/ifcfg-eth0") {
		// 创建模拟文件
		err = CreateFileAndWrite("./network-scripts/ifcfg-eth0", []byte(networkScriptsEth0Data))
		if err != nil {
			panic(fmt.Errorf("write Analog Data failed: %v", err))
		}
	}
	res, err := GetNetworkScript("./network-scripts", "ifcfg-eth0")
	if err != nil {
		t.Log(err)
	} else {
		t.Log("network-scripts", res)
		t.Log("network-scripts", res.NAME)
	}
}

// 创建模拟数据文件
func CreateFileAndWrite(fileName string, content []byte) error {
	f, err := os.Create(fileName)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.Write(content)
	if err != nil {
		return err
	}
	return nil
}

var networkScriptsEth0Data = `TYPE=Ethernet   
PROXY_METHOD=none
BROWSER_ONLY=no
BOOTPROTO=dhcp
DEFROUTE=yes
IPV4_FAILURE_FATAL=no
IPV6INIT=yes
IPV6_AUTOCONF=yes
IPV6_DEFROUTE=yes
IPV6_FAILURE_FATAL=no
IPV6_ADDR_GEN_MODE=stable-privacy
NAME=ens34
UUID=************************************
DEVICE=ens34
ONBOOT=no
IPADDR=***************
PREFIX=24
GATEWAY=*************
DNS1=***************
HWADDR=78:2B:CB:57:28:E5`
