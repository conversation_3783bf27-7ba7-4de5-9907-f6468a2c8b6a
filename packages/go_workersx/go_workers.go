package go_workersx

import (
	"strconv"
	"sync"

	"git.gobies.org/foeye/foeye3/config"

	"github.com/jrallison/go-workers"
)

var once sync.Once

// Publish message into the queue.
func Publish(configure *config.Configure, queue string, class string, args interface{}) error {
	Configd(configure)
	_, err := workers.EnqueueWithOptions(
		queue, class, args,
		workers.EnqueueOptions{Retry: true, RetryCount: 3},
	)

	//	logger.Info("WORKER ENQUEUE MESSAGE WITH JOB ID: %s, args: %+v", jid, args)
	if err != nil {
		return err
	}

	return nil
}

func Configd(configure *config.Configure) {
	once.Do(func() {
		c := map[string]string{
			// location of redis instance
			"server": configure.Worker.Host,
			// unique process id for this instance of workers (for proper recovery of inprogress jobs on crash)
			"process": configure.Worker.AssetscanQueue,
		}

		if configure.Worker.Pool != 0 {
			// number of connections to keep open with redis
			c["pool"] = strconv.Itoa(configure.Worker.Pool)
		}

		if configure.Worker.Password != "" {
			// instance of the password
			c["password"] = configure.Worker.Password
		}
		if configure.Worker.Database != 0 {
			// instance of the db
			c["database"] = strconv.Itoa(configure.Worker.Database)
		}

		workers.Configure(c)
	})
}
