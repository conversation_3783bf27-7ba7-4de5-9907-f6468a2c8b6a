package redisclient

import (
	"fmt"
	"sync"
	"time"

	redisc "git.gobies.org/foeye-dependencies/connecter/redis"
	"github.com/go-redis/redis"

	"git.gobies.org/foeye/foeye3/config"
)

var inst *redis.Client
var once sync.Once

var NewDefaultSimpleConfig = redisc.NewDefaultSimpleConfig

// NewRedisClientOr initialize redisclient.Client instance.
func NewRedisClientOr(configure *config.Configure) (*redis.Client, error) {

	configuration := &redisc.Config{
		Host:        configure.Redis.Host,
		Password:    configure.Redis.Password,
		Database:    configure.Redis.Database,
		IdleTimeout: configure.Redis.IdleTimeout * time.Second,
	}

	var err error
	once.Do(func() {
		inst, err = redisc.NewConnection(configuration)
	})

	if err != nil {
		return nil, err
	}

	if inst == nil {
		return nil, fmt.Errorf("initialize redisclient instance to failed")
	}

	return inst, nil
}

// Set redis 存入数据函数
func Set(key string, value interface{}, expiration time.Duration) error {
	return inst.Set(key, value, expiration).Err()
}

// GetString redis 获取数据函数
func GetString(key string) string {
	result, err := inst.Get(key).Result()
	if err != nil {
		return ""
	}

	return result
}

func Del(key string) error {
	return inst.Del(key).Err()
}
