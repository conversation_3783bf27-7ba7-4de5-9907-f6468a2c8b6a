package redisclient

import (
	"testing"
	"time"

	"git.gobies.org/foeye-dependencies/configure"
	"git.gobies.org/foeye/foeye3/config"

	"github.com/go-redis/redis"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestRedisClientSuite(t *testing.T) {
	suite.Run(t, &RedisClientSuite{})
}

type RedisClientSuite struct {
	suite.Suite
	err         error
	redisclient *redis.Client
	configure   *config.Configure
}

func (suite *RedisClientSuite) BeforeTest(suiteName, testName string) {
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath("./../../"),
		configure.WithSpecificConfigName("config.test"),
	)
	assert.NotNil(suite.T(), suite.configure)

	suite.redisclient, suite.err = NewRedisClientOr(suite.configure)
	assert.NoError(suite.T(), suite.err)
	assert.NotNil(suite.T(), suite.redisclient)
}

func (suite *RedisClientSuite) Test_SetGet() {
	// Set key value.
	set := suite.redisclient.Set("username", "<EMAIL>", time.Second*60)
	assert.NotNil(suite.T(), set)
	actual, err := set.Result()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "OK", actual)

	// Get key value.
	actual, err = suite.redisclient.Get("username").Result()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "<EMAIL>", actual)
}

func (suite *RedisClientSuite) Test_Get_NotExists() {
	// Get key value.
	result, err := suite.redisclient.Exists("not_exists_key").Result()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), int64(0), result)
}

func (suite *RedisClientSuite) Test_Set() {
	Set("test_set_key", "1", 0)
	res := GetString("test_set_key")
	assert.Equal(suite.T(), "1", res)

	Del("test_set_key")

	res = GetString("test_set_key")
	assert.NotEqual(suite.T(), "1", res)
}
