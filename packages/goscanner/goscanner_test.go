package goscanner

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/agiledragon/gomonkey/v2"

	"git.gobies.org/foeye/foeye3/packages/systemx"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"git.gobies.org/foeye-dependencies/configure"

	"git.gobies.org/foeye/foeye3/config"
)

func TestGoscannerSuite(t *testing.T) {
	suite.Run(t, &GoscannerSuite{})
}

type GoscannerSuite struct {
	suite.Suite
	configure *config.Configure
}

func (suite *GoscannerSuite) BeforeTest(suiteName, testName string) {

	suite.configure = config.GetConfigure(configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigPath("./../../"),
		configure.WithSpecificConfigName("config.test"),
	)

	assert.NotNil(suite.T(), suite.configure)
	//mosso.DebugShowContentWithJSON(suite.configure)
}

func (suite *GoscannerSuite) Test_HelloWorld() {
	//mosso.DebugShowContentWithJSON(suite.configure)
}

func (suite *GoscannerSuite) Test_WithExploit() {
	g := NewGoScanner(
		"/root/goscanner/goscanner",
		WithExploit(),
	)

	fmt.Println(g.String())
	assert.Contains(suite.T(), g.String(), "-o e")
}

func (suite *GoscannerSuite) Test_WithTarget() {
	g := NewGoScanner(
		"/root/goscanner/goscanner",
		WithTarget("https://www.example.com"),
	)
	fmt.Println(g.String())
	assert.Contains(suite.T(), g.String(), "-t https://www.example.com")
}

func (suite *GoscannerSuite) Test_WithPoc() {
	g := NewGoScanner(
		"/root/goscanner/goscanner",
		WithPoc("gitlab-info.json"),
	)
	fmt.Println(g.String())
	assert.Contains(suite.T(), g.String(), "-m gitlab-info.json")
}

func (suite *GoscannerSuite) Test_WithParams() {
	g := NewGoScanner(
		"/root/goscanner/goscanner",
		WithParams(`{"username": "admin"}`),
	)
	fmt.Println(g.String())
	assert.Contains(suite.T(), g.String(), "{\"username\": \"admin\"}")
}

func (suite *GoscannerSuite) TestGoscanner() {
	g := NewGoScanner(suite.configure.Goscanner.Path)

	if g.String() != "" {
		suite.T().Errorf("expect empty string")
	}

	g = g.Exploit()

	if g.String() != "-o e" {
		suite.T().Errorf("expect exploit operation")
	}

	url := "https://www.example.com"
	g = g.Target(url)

	if g.String() != "-o e -t "+url {
		suite.T().Errorf("expect two parameters")
	}

	cur := g.String()

	filename := "gitlab-info.json"
	g = g.Poc(filename)

	if g.String() != cur+" -m "+filename {
		suite.T().Errorf("expect poc filename parameter")
	}

	cur = g.String()
	g = g.Params(map[string]interface{}{"username": "admin"})

	expected := cur + " -params {\"username\":\"admin\"}"
	actual := g.String()

	if actual != expected {
		suite.T().Errorf("expected=[%s], real=(%s)\n", expected, actual)
	}
}

func (suite *GoscannerSuite) TestFilterHazard() {
	strArr := []map[string]string{
		{"str": "abd", "expect": "abd"},
		{"str": "abd'f", "expect": "abd\\'f"},
		{"str": "abd`f", "expect": "abd\\`f"},
		{"str": "abd;f", "expect": "abd\\;f"},
		{"str": "abd%f", "expect": "abd%f"},
		{"str": "abd\\f", "expect": "abd\\f"},
		{"str": "|\"hello112233\\\"", "expect": "\\|\"hello112233\\\""},
		{"str": "hello&&112233", "expect": "hello\\&\\&112233"},
	}
	for _, v := range strArr {
		assert.Equal(suite.T(), v["expect"], FilterHazard(v["str"]))
	}
}

func (suite *GoscannerSuite) TestRunError() {
	str := "Version:  v0.3.1 (  /  )\njv.VulnerableFunc != nil: (false)\n2024/04/17 23:00:48 {\"Data\":\"\",\"fileNa\":\"redis_unauthorized_access.json\",\"HostInfo\":\"http://************:6379\",\"LastResponse\":\"$3331\\r\\n# Server\\r\\nredis_version:5.0.7\\r\\nredis_git_sha1:00000000\\r\\nredis_git_dirty:0\\r\\nredis_build_id:66bd629f924ac924\\r\\nredis_mode:standalone\\r\\nos:Linux 5.15.0-102-generic x86_64\\r\\narch_bits:64\\r\\nmultiplexing_api:epoll\\r\\natomicvar_api:atomic-builtin\\r\\ngcc_version:9.3.0\\r\\nprocess_id:973\\r\\nrun_id:e65316e8be89ef02f3848fa22c022b04e8e382f6\\r\\ntcp_port:6379\\r\\nuptime_in_seconds:422621\\r\\nuptime_in_days:4\\r\\nhz:10\\r\\nconfigured_hz:10\\r\\nlru_clock:2090016\\r\\nexecutable:/usr/bin/redis-server\\r\\nconfig_file:/etc/redis/redis.conf\\r\\n\\r\\n# Clients\\r\\nconnected_clients:1\\r\\nclient_recent_max_input_buffer:2\\r\\nclient_recent_max_output_buffer:0\\r\\nblocked_clients:0\\r\\n\\r\\n# Memory\\r\\nused_memory:875368\\r\\nused_memory_human:854.85K\\r\\nused_memory_rss:5201920\\r\\nused_memory_rss_human:4.96M\\r\\nused_memory_peak:875368\\r\\nused_memory_peak_human:854.85K\\r\\nused_memory_peak_perc:100.00%\\r\\nused_memory_overhead:846422\\r\\nused_memory_startup:796208\\r\\nused_memory_dataset:28946\\r\\nused_memory_dataset_perc:36.57%\\r\\nallocator_allocated:1011584\\r\\nallocator_active:1368064\\r\\nallocator_resident:4259840\\r\\ntot\",\"Level\":\"3\",\"Method\":\"\",\"Name\":\"Redis Unauthorized Access\",\"Path\":\"\",\"VulURL\":\"************:6379\",\"Vulnerable\":true}"
	g := NewGoScanner(
		"/root/goscanner/goscanner",
		WithParams(`{"username": "admin"}`),
	)
	defer gomonkey.ApplyFuncReturn(systemx.ExecuteSystemCommand, str, nil).Reset()
	_, err := g.Run()
	assert.Equal(suite.T(), "无法处理goscanner返回的结果(格式错误)", err.Error())
}

func (suite *GoscannerSuite) TestRun() {
	str := "Version:  v0.3.1 (  /  )\njv.VulnerableFunc != nil: (false)\n2024/04/17 23:00:48 {\"Data\":\"\",\"FileName\":\"redis_unauthorized_access.json\",\"HostInfo\":\"http://************:6379\",\"LastResponse\":\"$3331\\r\\n# Server\\r\\nredis_version:5.0.7\\r\\nredis_git_sha1:00000000\\r\\nredis_git_dirty:0\\r\\nredis_build_id:66bd629f924ac924\\r\\nredis_mode:standalone\\r\\nos:Linux 5.15.0-102-generic x86_64\\r\\narch_bits:64\\r\\nmultiplexing_api:epoll\\r\\natomicvar_api:atomic-builtin\\r\\ngcc_version:9.3.0\\r\\nprocess_id:973\\r\\nrun_id:e65316e8be89ef02f3848fa22c022b04e8e382f6\\r\\ntcp_port:6379\\r\\nuptime_in_seconds:422621\\r\\nuptime_in_days:4\\r\\nhz:10\\r\\nconfigured_hz:10\\r\\nlru_clock:2090016\\r\\nexecutable:/usr/bin/redis-server\\r\\nconfig_file:/etc/redis/redis.conf\\r\\n\\r\\n# Clients\\r\\nconnected_clients:1\\r\\nclient_recent_max_input_buffer:2\\r\\nclient_recent_max_output_buffer:0\\r\\nblocked_clients:0\\r\\n\\r\\n# Memory\\r\\nused_memory:875368\\r\\nused_memory_human:854.85K\\r\\nused_memory_rss:5201920\\r\\nused_memory_rss_human:4.96M\\r\\nused_memory_peak:875368\\r\\nused_memory_peak_human:854.85K\\r\\nused_memory_peak_perc:100.00%\\r\\nused_memory_overhead:846422\\r\\nused_memory_startup:796208\\r\\nused_memory_dataset:28946\\r\\nused_memory_dataset_perc:36.57%\\r\\nallocator_allocated:1011584\\r\\nallocator_active:1368064\\r\\nallocator_resident:4259840\\r\\ntot\",\"Level\":\"3\",\"Method\":\"\",\"Name\":\"Redis Unauthorized Access\",\"Path\":\"\",\"VulURL\":\"************:6379\",\"Vulnerable\":true}"
	g := NewGoScanner(
		"/root/goscanner/goscanner",
		WithParams(`{"username": "admin"}`),
	)
	defer gomonkey.ApplyFuncReturn(systemx.ExecuteSystemCommand, str, nil).Reset()
	dd, _ := g.Run()
	d1, _ := json.Marshal(dd)
	fmt.Println(string(d1))
	assert.Equal(suite.T(), map[string]interface{}{"Data": "", "FileName": "redis_unauthorized_access.json", "HostInfo": "http://************:6379", "LastResponse": "$3331\r\n# Server\r\nredis_version:5.0.7\r\nredis_git_sha1:00000000\r\nredis_git_dirty:0\r\nredis_build_id:66bd629f924ac924\r\nredis_mode:standalone\r\nos:Linux 5.15.0-102-generic x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:9.3.0\r\nprocess_id:973\r\nrun_id:e65316e8be89ef02f3848fa22c022b04e8e382f6\r\ntcp_port:6379\r\nuptime_in_seconds:422621\r\nuptime_in_days:4\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:2090016\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis/redis.conf\r\n\r\n# Clients\r\nconnected_clients:1\r\nclient_recent_max_input_buffer:2\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:875368\r\nused_memory_human:854.85K\r\nused_memory_rss:5201920\r\nused_memory_rss_human:4.96M\r\nused_memory_peak:875368\r\nused_memory_peak_human:854.85K\r\nused_memory_peak_perc:100.00%\r\nused_memory_overhead:846422\r\nused_memory_startup:796208\r\nused_memory_dataset:28946\r\nused_memory_dataset_perc:36.57%\r\nallocator_allocated:1011584\r\nallocator_active:1368064\r\nallocator_resident:4259840\r\ntot", "Level": "3", "Method": "", "Name": "Redis Unauthorized Access", "Path": "", "VulURL": "************:6379", "Vulnerable": true}, dd)
}

func (suite *GoscannerSuite) TestRunFileName() {
	str := "Version:  v0.3.1 (  /  )\njv.VulnerableFunc != nil: (false)\n2024/04/17 23:00:48 {\"FileName\":\"redis_unauthorized_access.json\",\"HostInfo\":\"http://************:6379\",\"LastResponse\":\"$3331\\r\\n# Server\\r\\nredis_version:5.0.7\\r\\nredis_git_sha1:00000000\\r\\nredis_git_dirty:0\\r\\nredis_build_id:66bd629f924ac924\\r\\nredis_mode:standalone\\r\\nos:Linux 5.15.0-102-generic x86_64\\r\\narch_bits:64\\r\\nmultiplexing_api:epoll\\r\\natomicvar_api:atomic-builtin\\r\\ngcc_version:9.3.0\\r\\nprocess_id:973\\r\\nrun_id:e65316e8be89ef02f3848fa22c022b04e8e382f6\\r\\ntcp_port:6379\\r\\nuptime_in_seconds:422621\\r\\nuptime_in_days:4\\r\\nhz:10\\r\\nconfigured_hz:10\\r\\nlru_clock:2090016\\r\\nexecutable:/usr/bin/redis-server\\r\\nconfig_file:/etc/redis/redis.conf\\r\\n\\r\\n# Clients\\r\\nconnected_clients:1\\r\\nclient_recent_max_input_buffer:2\\r\\nclient_recent_max_output_buffer:0\\r\\nblocked_clients:0\\r\\n\\r\\n# Memory\\r\\nused_memory:875368\\r\\nused_memory_human:854.85K\\r\\nused_memory_rss:5201920\\r\\nused_memory_rss_human:4.96M\\r\\nused_memory_peak:875368\\r\\nused_memory_peak_human:854.85K\\r\\nused_memory_peak_perc:100.00%\\r\\nused_memory_overhead:846422\\r\\nused_memory_startup:796208\\r\\nused_memory_dataset:28946\\r\\nused_memory_dataset_perc:36.57%\\r\\nallocator_allocated:1011584\\r\\nallocator_active:1368064\\r\\nallocator_resident:4259840\\r\\ntot\",\"Level\":\"3\",\"Method\":\"\",\"Name\":\"Redis Unauthorized Access\",\"Path\":\"\",\"VulURL\":\"************:6379\",\"Vulnerable\":true}"
	g := NewGoScanner(
		"/root/goscanner/goscanner",
		WithParams(`{"username": "admin"}`),
	)
	defer gomonkey.ApplyFuncReturn(systemx.ExecuteSystemCommand, str, nil).Reset()
	dd, _ := g.Run()
	d1, _ := json.Marshal(dd)
	fmt.Println(string(d1))
	assert.Equal(suite.T(), map[string]interface{}{"FileName": "redis_unauthorized_access.json", "HostInfo": "http://************:6379", "LastResponse": "$3331\r\n# Server\r\nredis_version:5.0.7\r\nredis_git_sha1:00000000\r\nredis_git_dirty:0\r\nredis_build_id:66bd629f924ac924\r\nredis_mode:standalone\r\nos:Linux 5.15.0-102-generic x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:9.3.0\r\nprocess_id:973\r\nrun_id:e65316e8be89ef02f3848fa22c022b04e8e382f6\r\ntcp_port:6379\r\nuptime_in_seconds:422621\r\nuptime_in_days:4\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:2090016\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis/redis.conf\r\n\r\n# Clients\r\nconnected_clients:1\r\nclient_recent_max_input_buffer:2\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:875368\r\nused_memory_human:854.85K\r\nused_memory_rss:5201920\r\nused_memory_rss_human:4.96M\r\nused_memory_peak:875368\r\nused_memory_peak_human:854.85K\r\nused_memory_peak_perc:100.00%\r\nused_memory_overhead:846422\r\nused_memory_startup:796208\r\nused_memory_dataset:28946\r\nused_memory_dataset_perc:36.57%\r\nallocator_allocated:1011584\r\nallocator_active:1368064\r\nallocator_resident:4259840\r\ntot", "Level": "3", "Method": "", "Name": "Redis Unauthorized Access", "Path": "", "VulURL": "************:6379", "Vulnerable": true}, dd)
}

func (suite *GoscannerSuite) TestDictPath() {
	g := NewGoScanner(suite.configure.Goscanner.Path)
	g = g.DictPath("/data/gobin")
	assert.Equal(suite.T(), "/data/gobin", g.dictPath)
}

func (suite *GoscannerSuite) TestGoscannerRun() {
	str := "Version:  v0.3.1 (  /  )\njv.VulnerableFunc != nil: (false)\n2024/04/17 23:00:48 {\"Data\":\"\",\"FileName\":\"redis_unauthorized_access.json\",\"HostInfo\":\"http://************:6379\",\"LastResponse\":\"$3331\\r\\n# Server\\r\\nredis_version:5.0.7\\r\\nredis_git_sha1:00000000\\r\\nredis_git_dirty:0\\r\\nredis_build_id:66bd629f924ac924\\r\\nredis_mode:standalone\\r\\nos:Linux 5.15.0-102-generic x86_64\\r\\narch_bits:64\\r\\nmultiplexing_api:epoll\\r\\natomicvar_api:atomic-builtin\\r\\ngcc_version:9.3.0\\r\\nprocess_id:973\\r\\nrun_id:e65316e8be89ef02f3848fa22c022b04e8e382f6\\r\\ntcp_port:6379\\r\\nuptime_in_seconds:422621\\r\\nuptime_in_days:4\\r\\nhz:10\\r\\nconfigured_hz:10\\r\\nlru_clock:2090016\\r\\nexecutable:/usr/bin/redis-server\\r\\nconfig_file:/etc/redis/redis.conf\\r\\n\\r\\n# Clients\\r\\nconnected_clients:1\\r\\nclient_recent_max_input_buffer:2\\r\\nclient_recent_max_output_buffer:0\\r\\nblocked_clients:0\\r\\n\\r\\n# Memory\\r\\nused_memory:875368\\r\\nused_memory_human:854.85K\\r\\nused_memory_rss:5201920\\r\\nused_memory_rss_human:4.96M\\r\\nused_memory_peak:875368\\r\\nused_memory_peak_human:854.85K\\r\\nused_memory_peak_perc:100.00%\\r\\nused_memory_overhead:846422\\r\\nused_memory_startup:796208\\r\\nused_memory_dataset:28946\\r\\nused_memory_dataset_perc:36.57%\\r\\nallocator_allocated:1011584\\r\\nallocator_active:1368064\\r\\nallocator_resident:4259840\\r\\ntot\",\"Level\":\"3\",\"Method\":\"\",\"Name\":\"Redis Unauthorized Access\",\"Path\":\"\",\"VulURL\":\"************:6379\",\"Vulnerable\":true}"
	g := NewGoScanner(
		"/root/goscanner/goscanner",
		WithParams(`{"username": "admin"}`),
	)
	defer gomonkey.ApplyFuncReturn(systemx.ExecuteGoscannerCommand, str, nil).Reset()
	dd, _ := g.GoscannerRun()
	d1, _ := json.Marshal(dd)
	fmt.Println(string(d1))
	assert.Equal(suite.T(), map[string]interface{}{"Data": "", "FileName": "redis_unauthorized_access.json", "HostInfo": "http://************:6379", "LastResponse": "$3331\r\n# Server\r\nredis_version:5.0.7\r\nredis_git_sha1:00000000\r\nredis_git_dirty:0\r\nredis_build_id:66bd629f924ac924\r\nredis_mode:standalone\r\nos:Linux 5.15.0-102-generic x86_64\r\narch_bits:64\r\nmultiplexing_api:epoll\r\natomicvar_api:atomic-builtin\r\ngcc_version:9.3.0\r\nprocess_id:973\r\nrun_id:e65316e8be89ef02f3848fa22c022b04e8e382f6\r\ntcp_port:6379\r\nuptime_in_seconds:422621\r\nuptime_in_days:4\r\nhz:10\r\nconfigured_hz:10\r\nlru_clock:2090016\r\nexecutable:/usr/bin/redis-server\r\nconfig_file:/etc/redis/redis.conf\r\n\r\n# Clients\r\nconnected_clients:1\r\nclient_recent_max_input_buffer:2\r\nclient_recent_max_output_buffer:0\r\nblocked_clients:0\r\n\r\n# Memory\r\nused_memory:875368\r\nused_memory_human:854.85K\r\nused_memory_rss:5201920\r\nused_memory_rss_human:4.96M\r\nused_memory_peak:875368\r\nused_memory_peak_human:854.85K\r\nused_memory_peak_perc:100.00%\r\nused_memory_overhead:846422\r\nused_memory_startup:796208\r\nused_memory_dataset:28946\r\nused_memory_dataset_perc:36.57%\r\nallocator_allocated:1011584\r\nallocator_active:1368064\r\nallocator_resident:4259840\r\ntot", "Level": "3", "Method": "", "Name": "Redis Unauthorized Access", "Path": "", "VulURL": "************:6379", "Vulnerable": true}, dd)
}
