package goscanner

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"git.gobies.org/foeye/foeye3/packages/systemx"
)

// Option 参数选项
type Option func(s *goscanner)

type goscanner struct {
	header   string
	path     string
	dictPath string
	cmds     []string
}

// NewGoScanner 创建goscanner
func NewGoScanner(place string, options ...Option) *goscanner {
	tmp := &goscanner{
		path: place,
	}
	for _, opt := range options {
		opt(tmp)
	}
	return tmp
}

// Run
// 结果格式
//
// 验证通过:
//
//	Version:  v0.2 (  /  )
//	2021/10/26 10:41:30 {"FileName":"redis_unauthorized_access.json","HostInfo":"http://************:6379","LastResponse":"","Level":"3","Name":"Redis未授权访问漏洞","Output":"$2272\r\n","Success":true,"VulURL":""}
//
// 验证失败:
//
//	Version:  v0.2 (  /  )
//	2021/10/26 11:10:28 {"FileName":"redis_unauthorized_access.json","HostInfo":"http://************:6379","Name":"Redis未授权访问漏洞","Success":false}
func (g *goscanner) Run() (map[string]interface{}, error) {
	result := make(map[string]interface{})
	cmd := g.path + " " + g.String()
	fmt.Println(time.Now(), "goscanner漏洞核查的命令:", cmd)
	output, err := systemx.ExecuteSystemCommand(cmd)
	fmt.Println(time.Now(), "goscanner漏洞核查的原始结果:", output)
	if err != nil {
		return nil, err
	}

	re := regexp.MustCompile(`({.*?"FileName".+})`)

	match := re.FindStringSubmatch(output)
	if len(match) == 0 {
		return nil, errors.New("无法处理goscanner返回的结果(格式错误)")
	}

	fmt.Println(time.Now(), "match result:", match)

	err = json.Unmarshal([]byte(match[0]), &result)
	fmt.Println(time.Now(), "scan命令解析结果:", result)
	return result, nil
}

// GoscannerRun 使用原始的命令执行goscanner
// 结果格式
//
// 验证通过:
//
//	Version:  v0.2 (  /  )
//	2021/10/26 10:41:30 {"FileName":"redis_unauthorized_access.json","HostInfo":"http://************:6379","LastResponse":"","Level":"3","Name":"Redis未授权访问漏洞","Output":"$2272\r\n","Success":true,"VulURL":""}
//
// 验证失败:
//
//	Version:  v0.2 (  /  )
//	2021/10/26 11:10:28 {"FileName":"redis_unauthorized_access.json","HostInfo":"http://************:6379","Name":"Redis未授权访问漏洞","Success":false}
func (g *goscanner) GoscannerRun() (map[string]interface{}, error) {
	result := make(map[string]interface{})
	cmd := g.path + " " + g.String()
	fmt.Println(time.Now(), "goscanner漏洞核查的命令2:", cmd)
	output, err := systemx.ExecuteGoscannerCommand(g.dictPath, g.path, g.header, g.cmds)
	fmt.Println(time.Now(), "goscanner漏洞核查的原始结果2:", output)
	if err != nil {
		return nil, err
	}

	re := regexp.MustCompile(`({.*?"FileName".+})`)

	match := re.FindStringSubmatch(output)
	if len(match) == 0 {
		return nil, errors.New("无法处理goscanner返回的结果(格式错误)")
	}

	fmt.Println(time.Now(), "match result:", match)

	err = json.Unmarshal([]byte(match[0]), &result)
	fmt.Println(time.Now(), "scan命令解析结果2:", result)
	return result, nil
}

// WithExploit 漏洞利用
func WithExploit() Option {
	return func(s *goscanner) {
		s.cmds = append(s.cmds, "-o", "e")
	}
}

// WithScan 扫描
func WithScan() Option {
	return func(s *goscanner) {
		s.cmds = append(s.cmds, "-o", "s")
	}
}

// WithPoc 指定PoC文件名
func WithPoc(filename string) Option {
	return func(s *goscanner) {
		s.cmds = append(s.cmds, "-m", filename)
	}
}

// WithTarget 指定目标地址
func WithTarget(url string) Option {
	return func(s *goscanner) {
		s.cmds = append(s.cmds, "-t", url)
	}
}

// WithArgs 传递任意参数
func WithArgs(arg string) Option {
	return func(s *goscanner) {
		s.cmds = append(s.cmds, arg)
	}
}

// WithParams 指定参数
func WithParams(params string) Option {
	return func(s *goscanner) {
		//cmd := fmt.Sprintf("-params $'%s'", FilterHazard(params))
		s.cmds = append(s.cmds, "-params", params)
		//s.cmds = app	end(s.cmds, cmd)
	}
}

// WithCapture 追加重定向，否则读不到标准输出
func WithCapture() Option {
	return func(s *goscanner) {
		s.cmds = append(s.cmds, "2>&1")
	}
}

// WithGodServer 配置godServer
func WithGodServer(addr string) Option {
	return func(s *goscanner) {
		//cmd := fmt.Sprintf("-godserver \"%s\"", FilterHazard(addr))
		s.cmds = append(s.cmds, "-godserver", addr)
	}
}

// WithGodServer 配置godServer
func (g *goscanner) DictPath(path string) *goscanner {
	g.dictPath = path
	return g
}

// Reset 清除参数
func (g *goscanner) Reset() *goscanner {
	g.cmds = nil
	return g
}

// String 命令行转string
func (g *goscanner) String() string {
	return strings.Join(g.cmds, " ")
}

// Exploit 漏洞利用
func (g *goscanner) Exploit() *goscanner {
	g.cmds = append(g.cmds, "-o", "e")

	return g
}

// Scan 扫描
func (g *goscanner) Scan() *goscanner {
	g.cmds = append(g.cmds, "-o", "s")

	return g
}

// Poc 指定 PoC 文件名
func (g *goscanner) Poc(filename string) *goscanner {
	g.cmds = append(g.cmds, "-m", filename)

	return g
}

// Poc 指定 PoC 文件名
func (g *goscanner) Header(header string) *goscanner {
	if len(header) > 0 {
		g.header = header
	}

	return g
}

// Target 指定目标地址
func (g *goscanner) Target(url string) *goscanner {
	//url = FilterHazard(url)
	g.cmds = append(g.cmds, "-t", url)

	return g
}

// Args 传递任意参数
func (g *goscanner) Args(arg string) *goscanner {
	g.cmds = append(g.cmds, arg)

	return g
}

// Params 指定参数
func (g *goscanner) Params(data map[string]interface{}) *goscanner {
	buf, err := json.Marshal(data)
	if err != nil {
		panic(err)
	}

	//cmd := fmt.Sprintf("-params $'%s'", FilterHazard(string(buf)))
	g.cmds = append(g.cmds, "-params", string(buf))

	return g
}

// Capture 追加重定向，否则读不到标准输出
func (g *goscanner) Capture() *goscanner {
	g.cmds = append(g.cmds, "2>&1")

	return g
}

// FilterHazard 过滤危害字符
func FilterHazard(str string) (replace string) {
	replace = strings.ReplaceAll(str, "'", "\\'")
	replace = strings.ReplaceAll(replace, "`", "\\`")
	replace = strings.ReplaceAll(replace, ";", "\\;")
	replace = strings.ReplaceAll(replace, "&", "\\&")
	replace = strings.ReplaceAll(replace, "|", "\\|")
	return
}
