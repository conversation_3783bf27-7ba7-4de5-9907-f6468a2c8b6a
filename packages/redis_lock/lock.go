package redis_lock

import (
	"errors"
	"time"

	"github.com/go-redis/redis"
)

const (
	MinExpireTime = 500 * time.Millisecond //最小生命周期
)

type Mutex interface {
	Lock() (bool, error)
	Unlock() error
}
type redisMutex struct {
	isRenewal   bool          // 是否执行续期
	isClose     bool          // 是否已经关闭
	cacheKey    string        // 缓存key
	expire      time.Duration // 生命周期
	timer       *time.Timer   // 续期定时器
	conn        *redis.Client // 连接方法
	stopTimerCh chan struct{} // 通知通道
}

// GetRedisMutex
//* @description: 获取redis锁
//* @param {string} cacheKey，缓存key
//* @param {time.Duration} expire，生命周期
//* @param {bool} isRenewal，是否执行续期
//* @return {*}
func GetRedisMutex(redisClient *redis.Client, cacheKey string, expire time.Duration, isRenewal bool) (mu Mutex, err error) {
	if expire < MinExpireTime {
		err = errors.New("最小生命周期为500ms")
		return
	}
	if cacheKey == "" {
		err = errors.New("cacheKey为空")
		return
	}
	mu = &redisMutex{
		cacheKey:  cacheKey,
		expire:    expire,
		isRenewal: isRenewal,
		conn:      redisClient,
	}
	return
}

// Lock 加锁
func (r *redisMutex) Lock() (ok bool, err error) {
	ok, err = r.conn.SetNX(r.cacheKey, true, r.expire).Result()
	if err != nil {
		return
	}
	r.isClose = false
	if r.isRenewal && ok {
		go r.doRenewal()
	}
	return
}

// 执行续期
func (r *redisMutex) doRenewal() {
	renwalTime := r.expire >> 1
	r.timer = time.NewTimer(renwalTime)
	r.stopTimerCh = make(chan struct{})
	defer func() {
		ok := r.timer.Stop()
		if ok {
			r.isClose = true
		}
	}()
	for {
		select {
		case <-r.timer.C: //执行续期
			ok, err := r.conn.PExpire(r.cacheKey, r.expire).Result()
			if err != nil {
				r.stopTimerCh <- struct{}{}
				return
			}
			if !ok {
				r.stopTimerCh <- struct{}{}
			}
			r.timer.Reset(renwalTime)
		case <-r.stopTimerCh: //停止
			return
		}
	}
}

// Unlock 解锁
func (r *redisMutex) Unlock() (err error) {
	if r.isClose {
		return
	}
	_, err = r.conn.Del(r.cacheKey).Result()
	if err != nil {
		return
	}
	r.stopTimerCh <- struct{}{}
	return
}
