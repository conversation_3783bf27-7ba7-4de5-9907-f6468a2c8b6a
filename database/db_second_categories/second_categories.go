package db_second_categories

import (
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/model/m_second_categories"
	"git.gobies.org/foeye/foeye3/packages/gormx"
)

// GormSecondCategoriesDatabase 组件管理
type GormSecondCategoriesDatabase struct {
	Instance *database.GormDatabase
}

// NewGormSecondCategoriesDatabase initialize Gorm second categories database instance.
func NewGormSecondCategoriesDatabase(gormDatabase *database.GormDatabase) *GormSecondCategoriesDatabase {
	return &GormSecondCategoriesDatabase{Instance: gormDatabase}
}

// CreateTable 通过Gorm自动迁移创建数据表
func (g *GormSecondCategoriesDatabase) CreateTable() error {
	err := g.Instance.DB.Set(
		"gorm:table_options",
		gormx.CommentForDescription(m_second_categories.TableNameOfDescription),
	).AutoMigrate(new(m_second_categories.SecondCategories))

	return err
}

// GetSecondCategoriesList 获取所有类型数据.
func (g *GormSecondCategoriesDatabase) GetSecondCategoriesList(conditions ...interface{}) ([]*m_second_categories.SecondCategories, error) {
	var datums []*m_second_categories.SecondCategories
	var err error
	db := g.Instance.DB.Model(new(m_second_categories.SecondCategories))
	db = gormx.GormConditions(conditions, db)
	err = db.Order("id DESC").Find(&datums).Error
	return datums, err
}

// GetSecondCategoriesList 获取所有类型数据.
func (g *GormSecondCategoriesDatabase) ConvertCategoryLevel(components []*m_second_categories.SecondCategories) []*m_second_categories.SecondCategory {
	return m_second_categories.ConvertCategoryLevel(components)
}

// GetRuleCategoryByRuleIds 规则分类名称
func (g *GormSecondCategoriesDatabase) GetRuleCategoryByRuleIds(ids []uint) map[uint]*m_second_categories.SecondCategory {
	db := g.Instance.DB.Model(new(m_second_categories.SecondCategories))

	selectFields := []string{
		"second_categories.id",
		"second_categories.title",
		"scr.rule_id",
	}
	var res []*m_second_categories.RuleCategory
	err := db.Select(selectFields).
		Joins("RIGHT JOIN second_category_rules scr on scr.second_category_id = second_categories.id").
		Where("scr.rule_id IN(?)", ids).
		Find(&res).Error
	if err != nil {
		return nil
	}
	data := make(map[uint]*m_second_categories.SecondCategory)
	for _, value := range res {
		data[value.RuleID] = &m_second_categories.SecondCategory{
			ID:    value.ID,
			Title: value.Title,
		}
	}
	return data
}

func (g *GormSecondCategoriesDatabase) GetSecondCategoriesIds(category int) ([]int, error) {
	res := make([]int, 0)
	db := g.Instance.DB.Model(new(m_second_categories.SecondCategories)).Select("id")
	err := db.Where("ancestry = ?", category).Find(&res).Error
	return res, err
}

// GetFirstSecondCategory 获取组件一级分类和二级分类
func (g *GormSecondCategoriesDatabase) GetFirstSecondCategory(secondCategoryId uint) (*m_second_categories.FirstSecondCategory, error) {
	tmp := new(m_second_categories.FirstSecondCategory)
	err := g.Instance.DB.Select("s1.id,s1.title as second_title,s1.en_title as second_en_title,s2.title as first_title,s2.en_title as first_en_title").
		Table("second_categories as s1").
		Joins("INNER JOIN second_categories as s2 on s1.ancestry=s2.id").
		Where("s1.id = ?", secondCategoryId).Find(&tmp).Error
	return tmp, err
}

// GetSecondCategoryProducts 获取组件二级分类下所有组件名称
func (g *GormSecondCategoriesDatabase) GetSecondCategoryProducts(secondCategoryName string) []string {
	var products []string
	g.Instance.DB.Table("second_categories as sc").
		Joins("left join second_category_rules as scr on sc.id=scr.second_category_id").
		Joins("left join rules as r on r.id=scr.rule_id").
		Where("sc.title = ?", secondCategoryName).Pluck("r.product", &products)
	return products
}
