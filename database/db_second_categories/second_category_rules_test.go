package db_second_categories

import (
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/model/m_second_categories"
	mysql2 "git.gobies.org/foeye/foeye3/store/mysql"
	"git.gobies.org/foeye/foeye3/store/storage"
)

func TestGormSecondCategoryRuleDatabase_DeleteItemByCondition(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockDb, err := mysql2.InitSqlMock()
	assert.NoError(t, err)
	defer mockDb.Close()
	g := &GormSecondCategoryRuleDatabase{Instance: mockDb.MockDatabase}

	conditions := []any{1, 2}

	mockDb.Mock.ExpectBegin()
	mockDb.Mock.ExpectExec("DELETE FROM `second_category_rules`").
		WithArgs(1, 2).
		WillReturnResult(sqlmock.NewResult(2, 1))

	mockDb.Mock.ExpectCommit()

	err = g.DeleteItemByCondition(conditions)

	assert.Equal(t, nil, err)
}

func TestGormSecondCategoryRuleDatabase_UpdateCategoryIdByRuleId(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockDb, err := mysql2.InitSqlMock()
	assert.NoError(t, err)
	defer mockDb.Close()
	g := &GormSecondCategoryRuleDatabase{Instance: mockDb.MockDatabase}

	mockDb.Mock.ExpectBegin()
	mockDb.Mock.ExpectExec("UPDATE `second_category_rules`").
		WithArgs(100, 50).
		WillReturnResult(sqlmock.NewResult(100, 1))

	mockDb.Mock.ExpectCommit()

	err = g.UpdateCategoryIdByRuleId(100, 50)

	assert.Equal(t, nil, err)
}

func TestGormSecondCategoryRuleDatabase_CreateItemByInstance(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockDb, err := mysql2.InitSqlMock()
	assert.NoError(t, err)
	defer mockDb.Close()
	g := &GormSecondCategoryRuleDatabase{Instance: mockDb.MockDatabase}

	mockDb.Mock.ExpectBegin()
	mockDb.Mock.ExpectExec("INSERT INTO `second_category_rules`").
		WithArgs(2, sqlmock.AnyArg(), 100, 50).
		WillReturnResult(sqlmock.NewResult(100, 1))

	mockDb.Mock.ExpectCommit()

	err = g.CreateItemByInstance(&m_second_categories.SecondCategoryRule{
		SecondCategoryID: 100,
		RuleID:           50,
		AddWay:           2,
	})

	assert.Equal(t, nil, err)
}

func TestGormSecondCategoryRuleDatabase_GetRulesIds(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockDb, err := mysql2.InitSqlMock()
	assert.NoError(t, err)
	defer mockDb.Close()
	g := &GormSecondCategoryRuleDatabase{Instance: mockDb.MockDatabase}

	mockDb.ExpectQuery("SELECT `rule_id` FROM `second_category_rules` WHERE rule_id < ? AND second_category_id IN (?,?,?) ORDER BY rule_id DESC").
		WithArgs(10000000, 1, 2, 3).
		WillReturnRows(sqlmock.NewRows([]string{"rule_id"}).AddRow(101).AddRow(102).AddRow(103))

	ids, err := g.GetRulesIds([]int{1, 2, 3})

	assert.Equal(t, []int{101, 102, 103}, ids)
	assert.Equal(t, nil, err)
}

func TestGormSecondCategoryRuleDatabase_CreateTable(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()

	mockDb, err := mysql2.InitSqlMock()
	assert.NoError(t, err)
	defer mockDb.Close()
	g := &GormSecondCategoryRuleDatabase{Instance: mockDb.MockDatabase}

	mockDb.ExpectQuery("SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE ? ORDER BY SCHEMA_NAME=? DESC,SCHEMA_NAME limit 1").
		WithArgs("%", "").
		WillReturnRows(sqlmock.NewRows([]string{"SCHEMA_NAME"}))

	mockDb.Mock.ExpectExec("CREATE TABLE `second_category_rules`").
		WillReturnResult(sqlmock.NewResult(1, 1))

	err = g.CreateTable()

	assert.Equal(t, nil, err)
}

func TestGormSecondCategoryRuleDatabase_UpdateIsXcByRuleId(t *testing.T) {
	mockDb, err := mysql2.InitSqlMock()
	assert.NoError(t, err)

	g := &GormSecondCategoryRuleDatabase{Instance: mockDb.MockDatabase}

	mockDb.Mock.ExpectBegin()

	mockDb.Mock.ExpectExec("UPDATE `rules` SET `is_xc`=\\? WHERE id = \\?").
		WithArgs("1", 1).
		WillReturnResult(sqlmock.NewResult(1, 1))

	mockDb.Mock.ExpectCommit()

	err = g.UpdateIsXcByRuleId("1", 1)
	if err != nil {
		t.Errorf("Error during UpdateIsXcByRuleId: %v", err)
	}

	assert.Equal(t, nil, err)

	if err := mockDb.Mock.ExpectationsWereMet(); err != nil {
		t.Errorf("Unmet expectations in mock database: %v", err)
	}
}

func TestGormSecondCategoryRuleDatabase_CreateRule(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()
	t.Run("query err", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		g := &GormSecondCategoryRuleDatabase{mockDb.MockDatabase}
		defer gomonkey.ApplyMethodReturn(g, "GetItemByID", nil, fmt.Errorf("mock sql query err")).Reset()

		err = g.CreateSecondCategoryRule()
		assert.Equal(t, fmt.Errorf("mock sql query err"), err)
	})
	t.Run("success", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		g := &GormSecondCategoryRuleDatabase{mockDb.MockDatabase}
		defer gomonkey.ApplyMethodReturn(g, "GetItemByID", nil, nil).Reset()

		mockDb.Mock.ExpectBegin()
		mockDb.Mock.ExpectExec("INSERT INTO `second_category_rules`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mockDb.Mock.ExpectCommit()

		err = g.CreateSecondCategoryRule()
		assert.Equal(t, nil, err)
	})
}

func TestGormSecondCategoryRuleDatabase_GetItemByID(t *testing.T) {
	conf := storage.MockConfigureUniversal()
	defer gomonkey.ApplyFuncReturn(config.GetConfigure, conf).Reset()
	t.Run("record not found", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		g := &GormSecondCategoryRuleDatabase{mockDb.MockDatabase}

		mockDb.ExpectQuery("SELECT * FROM `second_category_rules` WHERE `second_category_rules`.`id` = ? ORDER BY `second_category_rules`.`id` LIMIT 1").
			WithArgs(100000000).
			WillReturnError(gorm.ErrRecordNotFound)

		secondCategoryRule, err := g.GetItemByID(100000000)
		assert.Equal(t, (*m_second_categories.SecondCategoryRule)(nil), secondCategoryRule)
		assert.Equal(t, nil, err)
	})
	t.Run("success case", func(t *testing.T) {
		mockDb, err := mysql2.InitSqlMock()
		assert.NoError(t, err)
		defer mockDb.Close()
		g := &GormSecondCategoryRuleDatabase{mockDb.MockDatabase}

		mockDb.ExpectQuery("SELECT * FROM `second_category_rules` WHERE `second_category_rules`.`id` = ? ORDER BY `second_category_rules`.`id` LIMIT 1").
			WithArgs(100000000).
			WillReturnRows(sqlmock.NewRows([]string{"second_category_id", "rule_id", "add_way"}).AddRow(1, 1, 1))

		secondCategoryRule, err := g.GetItemByID(100000000)
		assert.Equal(t, (*m_second_categories.SecondCategoryRule)(nil), secondCategoryRule)
		assert.Equal(t, nil, err)
	})
}
