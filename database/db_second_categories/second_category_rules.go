package db_second_categories

import (
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/model/m_second_categories"
	"git.gobies.org/foeye/foeye3/packages/gormx"
	"gorm.io/gorm"
)

// GormSecondCategoryRuleDatabaseInter 组件规则关联管理.
type GormSecondCategoryRuleDatabaseInter interface {
	database.GormMigrater
	CreateItemByInstance(rule *m_second_categories.SecondCategoryRule) error
	UpdateCategoryIdByRuleId(secondCategoryId, ruleId uint) error
	DeleteItemByCondition(conditions ...interface{}) error
	GetRulesIds(category []int) ([]int, error)
	UpdateIsXcByRuleId(isXc string, ruleId uint) error
}

// GormSecondCategoryRuleDatabase 组件规则关联管理
type GormSecondCategoryRuleDatabase struct {
	Instance *database.GormDatabase
}

// DeleteItemByCondition 批量删除
func (g *GormSecondCategoryRuleDatabase) DeleteItemByCondition(conditions ...interface{}) error {
	return g.Instance.DB.Delete(new(m_second_categories.SecondCategoryRule), conditions...).Error
}

// UpdateCategoryIdByRuleId ...
func (g *GormSecondCategoryRuleDatabase) UpdateCategoryIdByRuleId(secondCategoryId, ruleId uint) error {
	return g.Instance.DB.Table(m_second_categories.TableOfName).Where("rule_id = ?", ruleId).Update("second_category_id", secondCategoryId).Error
}

// CreateItemByInstance ...
func (g *GormSecondCategoryRuleDatabase) CreateItemByInstance(rule *m_second_categories.SecondCategoryRule) error {
	return g.Instance.DB.Create(rule).Error
}

func (g *GormSecondCategoryRuleDatabase) GetRulesIds(categoriesIds []int) ([]int, error) {
	secondCategoryDb := g.Instance.DB.Model(new(m_second_categories.SecondCategoryRule)).Select("rule_id").Where("rule_id < ?", 10000000)
	secondCategoryDb.Where("second_category_id IN (?)", categoriesIds).Order("rule_id DESC")
	ids := make([]int, 0)
	err := secondCategoryDb.Find(&ids).Error
	return ids, err
}

// CreateTable 通过Gorm自动迁移创建数据表
func (g *GormSecondCategoryRuleDatabase) CreateTable() error {
	err := g.Instance.DB.Set(
		"gorm:table_options",
		gormx.CommentForDescription(m_second_categories.TableOfNameOfDescription),
	).AutoMigrate(new(m_second_categories.SecondCategoryRule))

	return err
}

// UpdateIsXcByRuleId 更新id更新信创类型
func (g *GormSecondCategoryRuleDatabase) UpdateIsXcByRuleId(isXc string, ruleId uint) error {
	return g.Instance.DB.Table("rules").Where("id = ?", ruleId).Update("is_xc", isXc).Error
}

// CreateSecondCategoryRule 通过Gorm自动迁移创建占位规则
func (g *GormSecondCategoryRuleDatabase) CreateSecondCategoryRule() error {
	has, err := g.GetItemByID(100000000)
	if has == nil && err == nil {
		secondCategoryRule := &m_second_categories.SecondCategoryRule{
			ID:               100000000,
			SecondCategoryID: 1,
			RuleID:           1,
			AddWay:           1,
		}
		return g.Instance.DB.Create(secondCategoryRule).Error
	}
	return err
}

func (g *GormSecondCategoryRuleDatabase) GetItemByID(id uint) (*m_second_categories.SecondCategoryRule, error) {
	r := new(m_second_categories.SecondCategoryRule)
	err := g.Instance.DB.First(&r, id).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	if r.ID == id {
		return r, err
	}
	return nil, err
}
