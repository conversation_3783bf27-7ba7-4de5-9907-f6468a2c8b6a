package database

import (
	"git.gobies.org/foeye/foeye3/model/m_visible_column"
)

type GormThreatStore interface {
	CreateTable() error
	AutoMigrateSystemPresetData() error
	GetLastSelectListBySwitch(id uint, state string) (interface{}, error)
	GetCustomFiledList(id uint, state string) (interface{}, error)
	CreateItemByInstance(instance interface{}) error
	GetListByItemType(id uint, state, name string) (interface{}, error)
	DeleteFiledById(id uint) error
	UpdateCustomField(instance interface{}) error
	CreateCustomFieldItem(instance interface{}) error
	GetCustomFiledById(id uint) (interface{}, error)
	UpdateCustomFieldContentByUserIdAndItemType(visibleColumn *m_visible_column.VisibleColumn) error
	GetVisibleColumnsByItemType(itemType string) ([]*m_visible_column.VisibleColumn, error)
}
