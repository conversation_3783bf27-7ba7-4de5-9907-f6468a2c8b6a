package db_user_rules

import (
	"gorm.io/gorm"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/model/m_feature_clustering"
	"git.gobies.org/foeye/foeye3/model/m_second_categories"
	"git.gobies.org/foeye/foeye3/model/m_user_rules"
	"git.gobies.org/foeye/foeye3/packages/gormx"
	"git.gobies.org/foeye/foeye3/responses/r_user_rules"
)

const MyRulesJsonPath = "./static/backup/files/my_rules.json"
const MyRulesSecondCategoryJsonPath = "./static/backup/files/my_rules_second_category.json"

// GormUserRuleDatabaseInter 规则列表操作接口.
type GormUserRuleDatabaseInter interface {
	database.GormMigrater
	GetList(pageNum, pageSize int, conditions ...interface{}) ([]*m_user_rules.UserRule, error)
	CountItems(conditions ...interface{}) (count int64, err error)
	GetItemByID(id uint) (*m_user_rules.UserRule, error)
	GetItemByIds(ids []uint) ([]string, error)
	DeleteItemByID(id uint) error
	CreateItemByInstance(rule *m_user_rules.UserRule) (*m_user_rules.UserRule, error)
	UpdateItemByInstance(rule *m_user_rules.UserRule) error
	DeleteItemByCondition(conditions ...interface{}) error
	GetCompanyByRuleIds(ids []int) []string

	//
	GetSoftwareHardwareListByType(softhardcode string) ([]m_user_rules.SoftwareHardware, error)
	GetMyRuleList(query r_user_rules.QueryListAndKeyword) ([]*m_user_rules.UserRule, error)
	CountMyItems(query r_user_rules.QueryListAndKeyword) (count int64, err error)
	UpdateIsXcByRuleId(isXc string, ruleId uint) error

	GetSecondCategoryRulesByRuleIds(ids []uint) ([]m_second_categories.SecondCategoryRule, error)
	CreateUserRules(rules []m_user_rules.UserRule) error
	CreateUserRuleCategories(rules []m_second_categories.SecondCategoryRule) error
}

// GormUserRuleDatabase Database operation instance of module.
type GormUserRuleDatabase struct {
	Instance *database.GormDatabase
}

// GetCompanyByRuleIds company
func (g *GormUserRuleDatabase) GetCompanyByRuleIds(ids []int) []string {
	db := g.Instance.DB.Model(new(m_user_rules.UserRule))

	selectFields := []string{
		"company",
	}
	var res []string
	db = db.Select(selectFields).
		Where("id < ?", 10000000)
	if len(ids) > 0 {
		db = db.Where("id IN (?)", ids)
	}
	err := db.
		Group("company").
		Find(&res).Error
	if err != nil {
		return nil
	}

	return res
}

// DeleteItemByCondition 批量删除
func (g *GormUserRuleDatabase) DeleteItemByCondition(conditions ...interface{}) error {
	return g.Instance.DB.Delete(new(m_user_rules.UserRule), conditions...).Error
}

// CreateItemByInstance ...
func (g *GormUserRuleDatabase) CreateItemByInstance(rule *m_user_rules.UserRule) (*m_user_rules.UserRule, error) {
	err := g.Instance.DB.Create(rule).Error
	return rule, err
}

// UpdateItemByInstance ...
func (g *GormUserRuleDatabase) UpdateItemByInstance(rule *m_user_rules.UserRule) error {
	return g.Instance.DB.Select(
		"company",
		"product",
		"producturl",
		"rule",
		"level_code",
		"soft_hard_code",
		"is_xc").Updates(rule).Error
}

// GetItemByID ...
func (g *GormUserRuleDatabase) GetItemByID(id uint) (*m_user_rules.UserRule, error) {
	r := new(m_user_rules.UserRule)
	err := g.Instance.DB.First(&r, id).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	if r.ID == id {
		return r, err
	}
	return nil, err
}

func (g *GormUserRuleDatabase) GetItemByIds(ids []uint) ([]string, error) {
	data := make([]string, 0)
	err := g.Instance.DB.Model(new(m_user_rules.UserRule)).Where("id in ?", ids).Select("product").Find(&data).Error
	if err == gorm.ErrRecordNotFound {
		return data, nil
	}
	return data, nil
}

// DeleteItemByID ...
func (g *GormUserRuleDatabase) DeleteItemByID(id uint) error {
	return g.Instance.DB.Where("id = ?", id).Delete(new(m_user_rules.UserRule)).Error
}

// CountItems Count total
func (g *GormUserRuleDatabase) CountItems(conditions ...interface{}) (count int64, err error) {
	db := g.Instance.DB.Model(new(m_user_rules.UserRule))
	db = gormx.GormConditions(conditions, db)
	err = db.Count(&count).Error
	return
}

// GetList 规则列表
func (g *GormUserRuleDatabase) GetList(pageNum, pageSize int, conditions ...interface{}) ([]*m_user_rules.UserRule, error) {
	var datums []*m_user_rules.UserRule
	var err error
	db := g.Instance.DB.Model(new(m_user_rules.UserRule))
	db = gormx.GormConditions(conditions, db)
	db = gormx.GormPaging(pageNum, pageSize, db)
	err = db.Order("id DESC").Find(&datums).Error
	return datums, err
}

// GetMyRuleList 获取我的规则,去除快速添加规则
func (g *GormUserRuleDatabase) GetMyRuleList(query r_user_rules.QueryListAndKeyword) ([]*m_user_rules.UserRule, error) {
	var datums []*m_user_rules.UserRule
	var err error
	db := g.Instance.DB.Model(new(m_user_rules.UserRule))
	db = gormx.GormPaging(query.Number, query.Size, db)
	db.Where("id > ?", "10000000").
		Where("not exists (?)", g.Instance.DB.
			Table(m_feature_clustering.TableName).
			Where("rules.id = feature_clustering.rule_id").
			Where("state = 0"))
	if query.Keyword != "" {
		db.Where("product like ?", "%"+query.Keyword+"%")
	}
	err = db.Order("id DESC").Find(&datums).Error
	return datums, err
}

// CountMyItems 计算我的规则的总数
func (g *GormUserRuleDatabase) CountMyItems(query r_user_rules.QueryListAndKeyword) (count int64, err error) {
	db := g.Instance.DB.Model(new(m_user_rules.UserRule))
	db.Where("id > ?", "10000000").
		Where("not exists (?)", g.Instance.DB.
			Table(m_feature_clustering.TableName).
			Where("rules.id = feature_clustering.rule_id").
			Where("state = 0"))
	if query.Keyword != "" {
		db.Where("product like ?", "%"+query.Keyword+"%")
	}
	err = db.Count(&count).Error
	return
}

// CreateTable 通过Gorm自动迁移创建数据表
func (g *GormUserRuleDatabase) CreateTable() error {
	err := g.Instance.DB.Set(
		"gorm:table_options",
		gormx.CommentForDescription(m_user_rules.TableNameOfDescription),
	).AutoMigrate(new(m_user_rules.UserRule))

	return err
}

// CreateRule 通过Gorm自动迁移创建占位规则
func (g *GormUserRuleDatabase) CreateRule() error {
	has, err := g.GetItemByID(10000000)
	if has == nil && err == nil {
		rule := &m_user_rules.UserRule{
			ID:         10000000,
			Product:    "id占用",
			ProductUrl: "http://z.y.com",
			Company:    "id占用",
			Rule:       "id占用",
			Published:  0,
		}
		return g.Instance.DB.Create(rule).Error
	}
	return err
}

// UpdateIsXcByRuleId 更新id更新信创类型
func (g *GormUserRuleDatabase) UpdateIsXcByRuleId(isXc string, ruleId uint) error {
	return g.Instance.DB.Model(new(m_user_rules.UserRule)).Where("id = ?", ruleId).Update("is_xc", isXc).Error
}

func (g *GormUserRuleDatabase) GetSecondCategoryRulesByRuleIds(ids []uint) ([]m_second_categories.SecondCategoryRule, error) {
	var res []m_second_categories.SecondCategoryRule

	if len(ids) <= 0 {
		return res, nil
	}

	db := g.Instance.DB.Model(new(m_second_categories.SecondCategoryRule))

	err := db.Where("rule_id IN (?)", ids).Find(&res).Error
	if err != nil {
		return res, err
	}

	return res, nil
}

func (g *GormUserRuleDatabase) CreateUserRules(rules []m_user_rules.UserRule) error {
	return g.Instance.DB.Create(rules).Error
}

func (g *GormUserRuleDatabase) CreateUserRuleCategories(ruleCategories []m_second_categories.SecondCategoryRule) error {
	return g.Instance.DB.Model(new(m_second_categories.SecondCategoryRule)).Create(ruleCategories).Error
}

// GetListBySecondCategoryId 根据条件获取某一分类的规则
func (g *GormUserRuleDatabase) GetListBySecondCategoryId(secondCategoryId int, conditions ...interface{}) ([]*m_user_rules.UserRule, error) {
	//根据条件获取某一分类的规则
	//conditions 可能为规则名称等于 mysql,nginx
	return nil, nil
}
