package database

import (
	"git.gobies.org/foeye/foeye3/model/m_user_rules"
	"git.gobies.org/foeye/foeye3/responses/r_user_rules"
)

// GormUserRuleDatabaseInter 规则列表操作接口.
type GormUserRuleDatabaseInter interface {
	GormMigrater
	GetList(pageNum, pageSize int, conditions ...interface{}) ([]*m_user_rules.UserRule, error)
	CountItems(conditions ...interface{}) (count int64, err error)
	GetItemByID(id uint) (*m_user_rules.UserRule, error)
	DeleteItemByID(id uint) error
	CreateItemByInstance(rule *m_user_rules.UserRule) (*m_user_rules.UserRule, error)
	UpdateItemByInstance(rule *m_user_rules.UserRule) error
	DeleteItemByCondition(conditions ...interface{}) error
	GetCompanyByRuleIds(ids []int) []string

	//
	GetSoftwareHardwareListByType(softhardcode string) ([]m_user_rules.SoftwareHardware, error)
	GetMyRuleList(query r_user_rules.QueryListAndKeyword) ([]*m_user_rules.UserRule, error)
	CountMyItems(query r_user_rules.QueryListAndKeyword) (count int64, err error)
	GetListBySecondCategoryName(secondCategoryName string, conditions ...interface{}) ([]*m_user_rules.UserRule, error)
}
