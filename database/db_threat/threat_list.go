package db_threat

import (
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/model/m_visible_column"

	"gorm.io/gorm"
)

func (g *GormThreatDatabase) GetVisibleColumnsByItemType(itemType string) ([]*m_visible_column.VisibleColumn, error) {
	r := make([]*m_visible_column.VisibleColumn, 0)
	err := g.Instance.DB.Where(" `visible_columns`.`item_type` = ? AND `visible_columns`.`last_selected` = ?", itemType, 1).Find(&r).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return r, err
}

// GetListByItemType get list by id.
func (g *GormThreatDatabase) GetListByItemType(id uint, state, name string) (interface{}, error) {
	r := new(m_visible_column.VisibleColumn)
	err := g.Instance.DB.Where("`visible_columns`.`user_id` = ? AND `visible_columns`.`item_type` = ? AND `visible_columns`.`group_name` = ?", id, state, name).First(&r).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	if r.UserId == id && r.ItemType == state && r.GroupName == name {
		return r, err
	}
	return nil, err
}

// GetLastSelectListBySwitch get last select list by state.
func (g *GormThreatDatabase) GetLastSelectListBySwitch(id uint, state string) (interface{}, error) {
	r := new(m_visible_column.VisibleColumn)
	err := g.Instance.DB.Where("`visible_columns`.`user_id` = ? AND `visible_columns`.`item_type` = ? AND `visible_columns`.`last_selected` = ?", id, state, 1).First(&r).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	if r.UserId == id && r.ItemType == state {
		return r, err
	}
	return nil, err
}

// GetCustomFiledList get custom filed list by state.
func (g *GormThreatDatabase) GetCustomFiledList(id uint, state string) (interface{}, error) {
	r := make([]*m_visible_column.VisibleColumn, 0)
	if err := g.Instance.DB.Where("`visible_columns`.`user_id` = ? AND `visible_columns`.`item_type` = ? AND (`visible_columns`.`group_name` != ?)", id, state, m_threat.DefaultGroupName).Find(&r).Error; err != nil {
		return nil, err
	}
	return r, nil
}

// GetCustomFiledById get last select list by id.
func (g *GormThreatDatabase) GetCustomFiledById(id uint) (interface{}, error) {
	r := new(m_visible_column.VisibleColumn)
	err := g.Instance.DB.First(&r, id).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	if r.ID == id {
		return r, err
	}
	return nil, err
}
