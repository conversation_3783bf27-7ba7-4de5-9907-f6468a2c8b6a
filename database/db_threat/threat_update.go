package db_threat

import (
	"errors"

	"git.gobies.org/foeye/foeye3/model/m_visible_column"
	"git.gobies.org/foeye/foeye3/packages/constant"

	"gorm.io/gorm"
)

// UpdateCustomField update a data record based on an instance of the object.
func (g *GormThreatDatabase) UpdateCustomField(instance interface{}) error {
	return g.Instance.DB.Transaction(func(tx *gorm.DB) error {
		item, ok := instance.(*m_visible_column.VisibleColumn)
		if !ok {
			return errors.New(constant.RecordCreateErrorOfTypeNotMatch)
		}
		if item.GroupName != "" {
			if err := tx.Table(m_visible_column.TableName).Where("`item_type` = ? AND `last_selected` = ?", item.ItemType, 1).Update("last_selected", 0).Error; err != nil {
				return err
			}
			if err := tx.Table(m_visible_column.TableName).Where("`group_name` = ?", item.GroupName).Updates(map[string]interface{}{"last_selected": 1, "content": item.Content}).Error; err != nil {
				return err
			}
		} else {
			if err := tx.Table(m_visible_column.TableName).Where("`item_type` = ? AND `last_selected` = ?", item.ItemType, 1).Update("content", item.Content).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

func (g *GormThreatDatabase) UpdateCustomFieldContentByUserIdAndItemType(visibleColumn *m_visible_column.VisibleColumn) error {
	return g.Instance.DB.Table(m_visible_column.TableName).Where("`user_id` = ? AND `item_type` = ? AND `last_selected` = ?", visibleColumn.UserId, visibleColumn.ItemType, 1).Update("content", visibleColumn.Content).Error
}
