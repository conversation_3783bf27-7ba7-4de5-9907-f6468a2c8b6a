package db_threat

import (
	"strings"

	"gorm.io/gorm"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/model/m_system"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/model/m_visible_column"
	"git.gobies.org/foeye/foeye3/packages/gormx"
)

// Database operation instance of module.
type GormThreatDatabase struct {
	Instance *database.GormDatabase
}

// NewGormThreatDatabase initialize Gorm threat password database instance.
func NewGormThreatDatabase(gormDatabase *database.GormDatabase) *GormThreatDatabase {
	return &GormThreatDatabase{Instance: gormDatabase}
}

// CreateTable 通过Gorm自动迁移创建数据表
func (g *GormThreatDatabase) CreateTable() error {
	if err := g.Instance.DB.Set(
		"gorm:table_options",
		gormx.CommentForDescription(m_visible_column.TableNameOfDescription),
	).AutoMigrate(new(m_visible_column.VisibleColumn)); err != nil {
		return err
	}

	return nil
}

// AutoMigrateSystemPresetData 自动迁移系统预置数据.
func (g *GormThreatDatabase) AutoMigrateSystemPresetData() error {
	data := []*m_visible_column.VisibleColumn{
		{
			UserId:       m_threat.DefaultUserId,
			GroupName:    m_threat.DefaultGroupName,
			ItemType:     m_threat.AssetType,
			Content:      m_threat.DefaultAssetFields,
			LastSelected: m_threat.DefaultLastSelected,
		},
		{
			UserId:       m_threat.DefaultUserId,
			GroupName:    m_threat.DefaultGroupName,
			ItemType:     m_threat.PocTypeThreat,
			Content:      m_threat.DefaultFields,
			LastSelected: m_threat.DefaultLastSelected,
		},
	}
	for _, v := range data {
		has, err := g.GetLastSelectListBySwitch(v.UserId, v.ItemType)
		if has == nil && err == nil {
			if err = g.CreateItemByInstance(v); err != nil {
				return err
			}
		}
	}

	system := new(m_system.Systems)
	err := g.Instance.DB.Model(new(m_system.Systems)).Where("key = ? AND category = ?", "is_add", "visible_column_add_database").First(&system).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	if system == nil {

		list, err := g.GetVisibleColumnsByItemType(m_threat.AssetType)

		for _, item := range list {
			if !strings.Contains(item.Content, "database") {
				item.Content = item.Content + ",database"
				err = g.UpdateCustomFieldContentByUserIdAndItemType(item)
				if err != nil {
					return err
				}
			}
		}

		s := new(m_system.Systems)
		s.Key = "is_add"
		s.Value = "yes"
		s.Category = "visible_column_add_database"
		err = g.Instance.DB.Model(new(m_system.Systems)).Create(s).Error
		if err != nil {
			return err
		}
	}
	return nil
}
