package r_asset

import (
	"net"
	"regexp"
	"strings"

	"git.gobies.org/foeye/foeye3/responses/r_common"

	"github.com/gin-gonic/gin"
)

// QueryListAndKeyword 请求参数
type QueryListAndKeyword struct {
	*r_common.QueryList
	Search
	SelectType   string `json:"select_type" form:"select_type" query:"select_type"`
	LatestTaskId uint
	IndexType    string
	LastTaskId   uint
	Fields       []string
}

type QuerySummaryList struct {
	*r_common.QueryList
	SecondCategory string `json:"second_category" form:"second_category" query:"second_category"`
	FirstCategory  string `json:"first_category" form:"first_category" query:"first_category"`
	XcType         int    `json:"xc_type" form:"xc_type" query:"xc_type"`
}

type QueryDomainList struct {
	Number         int      `json:"number" form:"number,default=1"`
	Size           int      `json:"size" form:"size,default=15"`
	All            bool     `json:"all" form:"all,default=false"`           // 是否导出全部
	Keyword        string   `json:"keyword" form:"keyword" query:"keyword"` //关键字，根据 域名+IP模糊查询
	Ids            []string `json:"ids" form:"ids" query:"ids"`
	Tags           map[string][]string
	Domain         string   `json:"domain" form:"domain" query:"domain"`                                        // 域名
	Type           []string `json:"type" form:"type" query:"type"`                                              // 解析类型
	Ip             string   `json:"ip" form:"ip" query:"ip"`                                                    // 解析目标
	ComputerRoom   []string `json:"tags[computer_room]" form:"tags[computer_room]" query:"tags[computer_room]"` // 机房信息
	Company        []string `json:"tags[company]" form:"tags[company]" query:"tags[company]"`                   // 管理单元
	Username       []string `json:"tags[username]" form:"tags[username]" query:"tags[username]"`                // 负责人
	AssetLevel     []string `json:"tags[asset_level]" form:"tags[asset_level]" query:"tags[asset_level]"`       //资产等级
	BusinessApp    []string `json:"tags[business_app]" form:"tags[business_app]" query:"tags[business_app]"`    // 业务系统
	Lastupdatetime string   `json:"lastupdatetime" form:"lastupdatetime" query:"lastupdatetime"`                // 最后更新时间
	Createtime     string   `json:"createtime" form:"createtime" query:"createtime"`                            // 创建时间
}

// Search query 条件
type Search struct {
	Keyword              string   `json:"keyword" form:"keyword" query:"keyword"`
	IpType               string   `json:"ip_type" form:"ip_type" query:"ip_type"`
	XcType               *int     `json:"xc_type" form:"xc_type" query:"xc_type"`
	AddWay               string   `json:"add_way" form:"add_way" query:"add_way"`
	FilterCity           []string `json:"filter_city" form:"filter_city" query:"filter_city"`
	AssetStatus          string   `json:"asset_status" form:"asset_status" query:"asset_status"`
	CreatedTimeRange     string   `json:"createdtime_range" form:"createdtime_range" query:"createdtime_range"`
	IpRanges             string   `json:"ip_ranges" form:"ip_ranges" query:"ip_ranges"`
	OtherIpRanges        string   `json:"other_ip_ranges" form:"other_ip_ranges" query:"other_ip_ranges"`
	Tab                  int      `json:"tab" form:"tab,default=0" query:"tab"`
	SecondCategories     []string `json:"filter_second_categories" form:"filter_second_categories" query:"filter_second_categories"`
	FilterRuleInfoTitles []string `json:"filter_rule_info_titles" form:"filter_rule_info_titles" query:"filter_rule_info_titles"`
	FilterRuleCompanies  []string `json:"filter_rule_companies" form:"filter_rule_companies" query:"filter_rule_companies"`
	FilterPorts          []string `json:"filter_ports" form:"filter_ports" query:"filter_ports"`
	FilterProtocols      []string `json:"filter_protocols" form:"filter_protocols" query:"filter_protocols"`
	RiskFilter           []string `json:"risk_filter" form:"risk_filter" query:"risk_filter"`
	Tags                 map[string][]string
	Ids                  []string          `json:"ids" form:"ids" query:"ids"`
	UpdateFields         map[string]string `json:"update_fields" form:"update_fields" query:"update_fields"`

	TaskID     uint     `json:"task_id" form:"task_id" query:"task_id"`
	Domains    []string `json:"domains" form:"domains" query:"domains"`
	Type       string   `json:"type" form:"type" query:"type"`
	FirstTag   string   `json:"first_tag" form:"first_tag" query:"first_tag"`
	SecondTag  string   `json:"second_tag" form:"second_tag" query:"second_tag"`
	ScanType   bool
	NoStdPorts []string
	AssetLevel []string `json:"asset_level" form:"asset_level" query:"asset_level"`
}

// AssetVulsDetailRequestParams Model.
//
// 数据模型 AssetVulsDetailRequestParams 请求参数。
//
// swagger:model AdditionScanPortTemplate
type AssetVulsDetailRequestParams struct {

	// ip.
	//
	// required: true
	// read only: false
	// example: **********
	Ip string `json:"ip" form:"ip" query:"ip"`

	// 文件名称.
	//
	// required: true
	// read only: false
	// example: redis_unauthorized_access.json
	Filename string `json:"filename" form:"filename"  query:"filename" binding:"required"`
}

// SubdomainBodyRequestParams Model.
//
// 数据模型 SubdomainBodyRequestParams 请求参数。
//
// swagger:model SubdomainBodyRequestParams
type SubdomainBodyRequestParams struct {

	// ip.
	//
	// required: true
	// read only: false
	// example: **********
	Ip string `json:"ip" form:"ip" query:"ip" binding:"required"`

	// 端口号.
	//
	// required: true
	// read only: false
	// example: 80
	Port string `json:"port" form:"port" query:"port" binding:"required"`
}

// CorrectIpV4 过滤非ip
func (q Search) CorrectIpV4() []interface{} {
	if len(q.Ids) == 0 {
		return nil
	}
	ips := make([]interface{}, 0)
	for _, ip := range q.Ids {
		if !IsIp(ip) {
			continue
		}
		ips = append(ips, ip)
	}

	return ips
}

// IsIp 是否是ip地址
func IsIp(ip string) bool {
	ipreg, _ := regexp.Compile(`(^\s+)|(\s+$)`)
	ip = ipreg.ReplaceAllString(ip, "")
	parseIP := net.ParseIP(ip)
	return parseIP.To4() != nil || parseIP.To16() != nil
}

// QueryMapArray ...
func (q *QueryListAndKeyword) QueryMapArray(ctx *gin.Context) {
	query := ctx.Request.URL.Query()
	k := "tags"
	tags := make(map[string][]string)
	for key, value := range query {
		if i := strings.IndexByte(key, '['); i >= 1 && key[0:i] == k && len(value) > 0 {
			j := strings.IndexByte(key, ']')
			tagsKey := key[i+1 : j]
			tags[tagsKey] = value
		}
	}
	if len(tags) > 0 {
		q.Tags = tags
	}
	return
}
