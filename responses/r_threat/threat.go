package r_threat

import (
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

// QueryListAndKeyword 查询列表请求字段
type QueryListAndKeyword struct {
	r_common.QueryList
	SelectType      string   `json:"select_type" form:"select_type" query:"select_type"`
	Keyword         string   `json:"keyword" form:"keyword" query:"keyword"`
	CommonTitleList []string `json:"common_title_list" form:"common_title_list" query:"common_title_list"`
	TemplateId      uint     `json:"template_id" form:"template_id" query:"template_id"`
	Field           string   `json:"field" form:"field" query:"field"`
	Sort            bool     `json:"sort" form:"sort" query:"sort"`
	XcType          *int     `json:"xc_type" form:"xc_type" query:"xc_type"`
	LatestTaskId    uint
	LastTaskId      uint
	m_threat.ThreatBatch
	m_threat.ThreatNotice
}

// QueryListForXcThreat  查询列表请求字段
type QueryListForXcThreat struct {
	r_common.QueryList
	Level   string `json:"level" form:"level" query:"level" `
	Verity  string `json:"verity" form:"verity" query:"verity"`
	VulType string `json:"vul_type" form:"vul_type" query:"vul_type"`
	XcType  string `json:"xc_type" form:"xc_type" query:"xc_type"`
}
