package r_protocol

import (
	"git.gobies.org/foeye/foeye3/model/m_protocol"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

type QueryListAndKeyword struct {
	*r_common.QueryList
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
}

type ProtocolIdsReq struct {
	r_common.IdsReq
}

// ProtocolListResp Model
//
// 数据模型 ProtocolListResp 自定义协议列表响应结构体。
//
// swagger:model ProtocolListResp
type ProtocolListResp struct {
	r_common.Pagination
	// 协议列表数据.
	//
	// required: true
	// read only: false
	Data []*m_protocol.Protocol `json:"data"`
}

type ProtocolInfoResp struct {
	Data *m_protocol.Protocol `json:"data"`
}
