package r_scan_port

import "git.gobies.org/foeye/foeye3/responses/r_common"

type QueryListAndKeyword struct {
	*r_common.QueryList
	Keyword   string   `json:"keyword" form:"keyword" query:"keyword"`
	Protocol  []string `json:"protocol" form:"protocol" query:"protocol"`
	State     []uint   `json:"state" form:"state" query:"state"`
	PortGroup []uint   `json:"port_group" form:"port_group" query:"port_group"`
	AddWays   []string `json:"add_ways" form:"add_ways" query:"add_ways"`
}

type QueryListAndKeywordByRisk struct {
	*r_common.QueryList
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
}
