package r_kafka

// KafkaSettingsFormData  Model
//
// 数据模型 KafkaSettingsFormData 。
//
// swagger:model KafkaSettingsFormData
type KafkaSettingsFormData struct {
	// 是否开启.
	//
	// required: true
	// read only: false
	// example: on|off
	KafkaSwitch string `form:"kafka_switch" json:"kafka_switch"`

	// 服务地址.
	//
	// required: true
	// read only: false
	// example: ************
	KafkaServerIp string `form:"kafka_server_ip" json:"kafka_server_ip"`

	// 端口.
	//
	// required: true
	// read only: false
	// example: 9092
	KafkaServerPort string `form:"kafka_server_port" json:"kafka_server_port"`

	// 端口.
	//
	// required: true
	// read only: false
	// example: aasdasdsad
	KafkaAssetTopic string `form:"kafka_asset_topic" json:"kafka_asset_topic"`

	// 端口.
	//
	// required: true
	// read only: false
	// example: asdsadasdsad
	KafkaThreatTopic string `form:"kafka_threat_topic" json:"kafka_threat_topic"`
}
