package r_history

import (
	"strconv"

	"git.gobies.org/foeye/foeye3/packages/util"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

// QueryListAndKeyword 请求参数
type QueryListAndKeyword struct {
	*r_common.QueryList
	Keyword    string   `json:"keyword" form:"keyword" query:"keyword"`
	Type       string   `json:"type" form:"type" query:"type"`
	Sort       string   `json:"sort" form:"sort" query:"sort"`
	Ip         string   `json:"ip" form:"ip" query:"ip"`
	Ids        []int    `json:"ids" form:"ids" query:"ids"`
	Category   []int    `json:"category" form:"category" query:"category"`
	Operation  []int    `json:"operation" form:"operation" query:"operation"`
	IsFrequent string   `json:"is_frequent" form:"is_frequent" query:"is_frequent"`
	Level      []string `json:"level" form:"level" query:"level"`
	Business   []string `json:"business" form:"business" query:"business"`
	Ips        []string `json:"ips" form:"ips" query:"ips"`
	Switch     string   `json:"switch" form:"ips" query:"ips"`
}

// Config Model
//
// 数据模型 Config 资产动态切换频繁变更.
//
// swagger:model Config
type Config struct {
	Switch string `json:"switch"`

	// 周期(1,3,7,30,60).
	//
	// required: false
	// read only: false
	// example: 1
	Cycle string `json:"cycle"`

	// 频次(1~10).
	//
	// required: false
	// read only: false
	// example: 1
	Frequency string `json:"frequency"`
}

func (c *Config) Check() bool {
	if !util.IsContains(c.Cycle, []string{"1", "3", "7", "30", "90"}) {
		return false
	}
	frequency, err := strconv.Atoi(c.Frequency)
	if err != nil {
		return false
	}
	if frequency < 1 || frequency > 10 {
		return false
	}
	return true
}

// Cancel Model
//
// 数据模型 Cancel 资产动态追踪取消频繁变更.
//
// swagger:model Cancel
type Cancel struct {
	// IP详情.
	//
	// required: false
	// read only: false
	Ip string `json:"ip"`

	// 关键字查询(IP).
	//
	// required: false
	// read only: false
	Keyword string `json:"keyword"`

	// 高级筛选-频繁变更.
	//
	// required: false
	// read only: false
	IsFrequent string `json:"is_frequent"`

	// 是否开启频繁变更.
	//
	// required: false
	// read only: false
	// example: off
	Switch string `json:"switch"`

	// 记录ID.
	//
	// required: false
	// read only: false
	Ids []int `json:"ids"`

	// 高级筛选-变更类型.
	//
	// required: false
	// read only: false
	Category []int `json:"category"`

	// 高级筛选-变更状态.
	//
	// required: false
	// read only: false
	Operation []int `json:"operation"`

	// 记录IP.
	//
	// required: false
	// read only: false
	Ips []string `json:"ips"`

	// 高级筛选-资产等级.
	//
	// required: false
	// read only: false
	Level []string `json:"level"`

	// 高级筛选-资产等级.
	//
	// required: false
	// read only: false
	Business []string `json:"business"`
}
