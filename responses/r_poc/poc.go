package r_poc

import (
	"git.gobies.org/foeye/foeye3/model/m_poc"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

type QueryListAndSearch struct {
	r_common.QueryList
	m_poc.PocSearch
}

// CustomPocBatchOnOFF Model
//
// 数据模型 CustomPocBatchOnOFF 自定义POC未发布/发布基本数据信息。
//
// swagger:model CustomPocBatchOnOFF
type CustomPocBatchOnOFF struct {
	m_poc.PocSearch

	// 未发布:false/发布:true.
	//
	// required: true
	// read only: false
	// example: false
	CustomState bool `json:"custom_state" form:"custom_state"`
}

// CustomPocBatchExecute Model
//
// 数据模型 CustomPocBatchExecute 自定义POC未发布/发布基本数据信息。
//
// swagger:model CustomPocBatchExecute
type CustomPocBatchExecute struct {
	m_poc.PocSearch

	// 未发布:false/发布:true.
	//
	// required: true
	// read only: false
	// example: false
	CustomState bool `json:"custom_state" form:"custom_state"`
}

// IpScanTest Model
//
// 数据模型 IpScanTest poc进行测试基本数据信息。
//
// swagger:model IpScanTest
type IpScanTest struct {
	// 记录id.
	//
	// required: false
	// read only: false
	// example: 2
	Id int `json:"id" form:"id"`

	// 测试地址.
	//
	// required: true
	// read only: false
	// example: "*********:8081"
	Host string `json:"host" form:"host"`
}
