package r_map_ip

type MapIp struct {
	// IP/域名地址.
	//
	// required: true
	// read only: false
	// example: "*********:8081"
	Ip string `json:"ip"`

	// 端口.
	//
	// required: true
	// read only: false
	// example: "8080"
	Port string `json:"port"`
}

// MapIpResp Model
//
// 数据模型 MapIpResp
//
// swagger:model  MapIpResp
type MapIpResp struct {
	IsMap string   `json:"is_map"`
	List  []*MapIp `json:"list"`
}
