package r_common

import (
	"git.gobies.org/foeye-dependencies/logger"
	"git.gobies.org/foeye/foeye3/model/m_common"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"github.com/gin-gonic/gin"
	"net/http"
)

func Error(ctx *gin.Context, err any) {
	code := statusx.ErrInternalServerError
	statusCode := http.StatusInternalServerError
	message := ""
	var err1 error

	// 判断是不是error
	if e, ok := err.(error); ok {
		err1 = e
		message, _ = statusx.GetErrMsgWithCode(code)
	}

	// 判断是不是foeye3error
	if e, ok := err.(*statusx.Foeye3Error); ok {
		code = e.Code
		statusCode = e.StatusCode
		message = e.Error()
		err1 = e.Err
	}

	// 打印日志
	if err1 != nil {
		logger.Errorw("[Response] return error:", "err", err)
	}

	ctx.AbortWithStatusJSON(statusCode, m_common.Successful{
		Code:    code,
		Message: message,
		Data:    map[string]interface{}{},
	})
}

func InternalServer(ctx *gin.Context, err error) {
	InternalServerError(ctx, statusx.InternalServerCode, statusx.InternalServerMessage, err)
}

// InternalServerError
func InternalServerError(ctx *gin.Context, code int, message string, err error) {
	// 打印日志
	if err != nil {
		logger.Errorw("[Response] return error:", "err", err)
	}

	if message == "" {
		message, _ = statusx.GetErrMsgWithCode(http.StatusInternalServerError)
	}

	if code == 0 {
		code = http.StatusInternalServerError
	}

	ReaderJSON(ctx, http.StatusInternalServerError, code, message, map[string]interface{}{})
}
