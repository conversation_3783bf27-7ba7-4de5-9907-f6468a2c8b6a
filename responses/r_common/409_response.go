package r_common

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye-dependencies/logger"
)

// StatusConflict 已经存在的响应
func StatusConflict(ctx *gin.Context, code int, message string, err error) {
	// 打印日志
	if err != nil {
		logger.Errorw("[Response] return error:", "err", err)
	}

	if code == 0 {
		code = http.StatusConflict
	}
	if message == "" {
		message = http.StatusText(http.StatusConflict)
	}

	ReaderJSON(ctx, http.StatusConflict, code, message, map[string]interface{}{})
}
