package r_common

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye/foeye3/packages/statusx"
)

// OK 返回空数据
func OK(ctx *gin.Context) {
	Success(ctx, map[string]string{})
}

// Success 正常情况下, 返回 JSON 数据
func Success(ctx *gin.Context, data interface{}) {
	SuccessReaderJSON(
		ctx,
		http.StatusOK,
		statusx.StatusOkMessage,
		data,
	)
}

// SuccessReaderJSON 成功时渲染JSON数据到HTTP响应
func SuccessReaderJSON(ctx *gin.Context, code int, message string, obj interface{}) {
	ReaderJSON(ctx, http.StatusOK, code, message, obj)
}

// SuccessReaderJSONNoData 成功时渲染JSON数据到HTTP响应
func SuccessReaderJSONNoData(ctx *gin.Context, code int, message string) {
	ReaderJSON(ctx, http.StatusOK, code, message, map[string]string{})
}
