package r_common

import (
	"errors"
	"fmt"

	"github.com/stretchr/testify/assert"
)

const (
	DIYBusinessStatusCode          = 4003
	DIYBusinessStatusCodeOfMessage = "自定义403业务码信息"
)

func (c *CommonSuite) TestForbidden() {
	Forbidden(c.ctx, errors.New("error happened"))
	fmt.Println("Forbidden---", c.rec.Code)
	assert.Equal(c.T(), 403, c.rec.Code)
}

func (c *CommonSuite) TestForbiddenNoData() {

	ForbiddenNoData(c.ctx,
		DIYBusinessStatusCode,
		DIYBusinessStatusCodeOfMessage,
		errors.New("error happened"))
	fmt.Println("Forbidden---", c.rec.Code)
	assert.Equal(c.T(), 403, c.rec.Code)
}

func (c *CommonSuite) TestForbiddenCommon() {

	ForbiddenCommon(c.ctx,
		DIYBusinessStatusCode,
		DIYBusinessStatusCodeOfMessage,
		errors.New("error happened"),
		nil)
	fmt.Println("Forbidden---", c.rec.Code)
	assert.Equal(c.T(), 403, c.rec.Code)
}
