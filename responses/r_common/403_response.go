package r_common

import (
	"errors"
	"net/http"

	"git.gobies.org/foeye-dependencies/logger"
	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/statusx"
)

// Forbidden  err ,
func Forbidden(ctx *gin.Context, err error) {
	ForbiddenCommon(ctx, http.StatusForbidden, "", err, map[string]interface{}{})
}

// ForbiddenNoData  code 业务码，message 自定义403业务码信息，err 真实报错信息
func ForbiddenNoData(ctx *gin.Context, code int, message string, err error) {
	ForbiddenCommon(ctx, code, message, err, map[string]interface{}{})
}

// ForbiddenCommon  code 业务码，message 自定义403业务码报错信息，err 真实报错信息，data 实际需要的数据
func ForbiddenCommon(ctx *gin.Context, code int, message string, err error, data interface{}) {
	// 打印日志
	if err != nil {
		logger.Errorw("[Response] return error:", "err", err)
	}

	if message == "" {
		message, _ = statusx.GetErrMsgWithCode(http.StatusForbidden)
	}

	if code == 0 {
		code = http.StatusForbidden
	}

	if message != "" {
		ctx.Set(constant.CustomStatusCode, code)
		ctx.AbortWithError(http.StatusForbidden, errors.New(message))
	}

	ReaderJSON(ctx, http.StatusForbidden, code, message, data)
}
