package r_common

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestCommonSuite(t *testing.T) {
	suite.Run(t, new(CommonSuite))
}

type CommonSuite struct {
	suite.Suite
	ctx *gin.Context
	rec *httptest.ResponseRecorder
}

func (c *CommonSuite) BeforeTest(suiteName, testName string) {
	//mode.Set(config.GetProjectPath(), mode.Test, true)

	c.rec = httptest.NewRecorder()
	c.ctx, _ = gin.CreateTestContext(c.rec)
}

func (c *CommonSuite) TestSuccessReaderJSON() {
	SuccessReaderJSON(c.ctx, http.StatusOK, "创建成功", gin.H{
		"data": []string{"hello", "world"},
	})

	assert.Equal(c.T(), 200, c.rec.Code)
}
