package r_common

import (
	"errors"
	"net/http"

	"git.gobies.org/foeye-dependencies/logger"
	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/statusx"
)

func BadRequest(ctx *gin.Context, err error) {
	BadRequestCommon(ctx,
		statusx.BadRequestForVerificationParameterCode,
		statusx.BadRequestForVerificationParameterMessage, err,
		map[string]interface{}{},
	)
}

// BadRequestNoData code 业务码，message 自定义报错信息，err 真实报错信息
func BadRequestNoData(ctx *gin.Context, code int, message string, err error) {
	BadRequestCommon(ctx, code, message, err, map[string]interface{}{})
}

// BadRequestCommon code 业务码，message 自定义报错信息，err 真实报错信息，data 实际需要的数据
func BadRequestCommon(ctx *gin.Context, code int, message string, err error, data interface{}) {
	// 打印日志
	if err != nil {
		logger.Errorw("[Response] return error:", "err", err)
	}

	if message == "" {
		message, _ = statusx.GetErrMsgWithCode(http.StatusBadRequest)
	}

	if code == 0 {
		code = http.StatusBadRequest
	}

	if message != "" {
		ctx.Set(constant.CustomStatusCode, code)
		ctx.AbortWithError(http.StatusBadRequest, errors.New(message))
	}

	ReaderJSON(ctx, http.StatusBadRequest, code, message, data)
}
