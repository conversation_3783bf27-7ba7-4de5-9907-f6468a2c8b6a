package r_common

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye-dependencies/logger"
)

// StatusUnauthorized
func StatusUnauthorized(ctx *gin.Context, err error) {
	// 打印日志
	if err != nil {
		logger.Errorw("[Response] return error:", "err", err)
	}

	code := http.StatusUnauthorized
	message := http.StatusText(code)

	ReaderJSON(ctx, http.StatusUnauthorized, code, message, map[string]interface{}{})
}
