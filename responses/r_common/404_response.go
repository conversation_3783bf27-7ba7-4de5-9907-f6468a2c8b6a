package r_common

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye/foeye3/packages/constant"
)

// StatusNotFound code 业务码，message 自定义报错信息
func StatusNotFound(ctx *gin.Context, code int, message string) {
	if message == "" {
		message = http.StatusText(http.StatusNotFound)
	}

	if code == 0 {
		code = http.StatusNotFound
	}

	if message != "" {
		ctx.Set(constant.CustomStatusCode, code)
		ctx.AbortWithError(http.StatusNotFound, errors.New(message))
	}

	ReaderJSON(ctx, http.StatusNotFound, code, message, map[string]interface{}{})
}
