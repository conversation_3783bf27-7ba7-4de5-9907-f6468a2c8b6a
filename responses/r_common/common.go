package r_common

import (
	"git.gobies.org/foeye-dependencies/logger"
	"git.gobies.org/foeye/foeye3/model/m_common"
	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/statusx"

	"github.com/gin-gonic/gin"
)

// Pagination Model
//
// 数据模型 Pagination 分页结构。
//
// swagger:model Pagination
type Pagination struct {
	// 页码.
	//
	// required: true
	// read only: false
	// example: 1
	Number int `json:"number"`
	// 每页数量.
	//
	// required: true
	// read only: false
	// example: 10
	Size int `json:"size"`
	// 数据总数.
	//
	// required: true
	// read only: false
	// example: 100
	Total int64 `json:"total"`
}

// QueryList request paging data，metadata data.
type QueryList struct {
	Number int  `json:"number" form:"number,default=1"`
	Size   int  `json:"size" form:"size,default=15"`
	All    bool `json:"all" form:"all,default=false"`
}

type IdsReq struct {
	Ids []int `json:"ids" query:"ids" form:"ids"`
	All bool  `json:"all" query:"all" form:"all"`
}

type Pager QueryList

// QueryOfKeyword query list parameter of the keyword.
type QueryOfKeyword struct {
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
}

// QueryListToPagination request paging data convert to responses paging data.
func QueryListToPagination(ql *QueryList, total int64) Pagination {
	return Pagination{
		Number: ql.Number,
		Size:   ql.Size,
		Total:  total,
	}
}

// ResponsePaginationList responses pagination list data.
type ResponsePaginationList struct {
	Pagination
	Data           interface{}     `json:"data"`
	SuperLimitWarn *SuperLimitWarn `json:"super_limit_warn,omitempty"`
}

// ResponseList responses list data.
type ResponseList struct {
	Data interface{} `json:"data"`
}

// MapIpResponseList responses list data.
type MapIpResponseList struct {
	Data interface{} `json:"data"`
}

// ResponseDetail responses Data Details.
type ResponseDetail struct {
	Data interface{} `json:"data"`
}

// ResponseToken responses to token data.
type ResponseToken struct {
	Token string `json:"token"`
}

// SuccessOrAbort 成功或失败验证
func SuccessOrAbort(ctx *gin.Context, code int, err error) (success bool) {
	if err != nil {
		ctx.AbortWithError(code, err)
	}
	return err == nil
}

type SuccessOrAbortInfo struct {
	CTX        *gin.Context
	Code       int
	CustomCode int
	Error      error
	Data       interface{}
}

// SuccessOrAbortWithCustomStatusCode 成功或失败验证
// ctx *gin.Context, code int, customcode int, err error
func SuccessOrAbortWithCustomStatusCode(err error, info SuccessOrAbortInfo) {
	if err != nil {
		logger.Errorw("[Gin-Response] gin request failed.", "err", err)
	}

	message := ""
	if info.Error != nil {
		info.CTX.Set(constant.CustomStatusCode, info.CustomCode)
		info.CTX.AbortWithError(info.Code, info.Error)
		message = info.Error.Error()
	}

	if info.CustomCode == 0 {
		info.CustomCode = info.Code
	}

	if info.Data == nil {
		info.Data = map[string]interface{}{}
	}

	ReaderJSON(info.CTX, info.Code, info.CustomCode, message, info.Data)
}

// ReaderJSON 成功时渲染JSON数据到HTTP响应, 最终响应信息。
func ReaderJSON(ctx *gin.Context, httpCode, code int, message string, obj interface{}) {
	ctx.JSON(httpCode, m_common.Successful{
		Code:    code,
		Message: message,
		Data:    &obj,
	})
}

// ErrorWithAbort 出错返回
func ErrorWithAbort(ctx *gin.Context, code int, customCode int, err error) {
	// 打印日志
	if err != nil {
		logger.Errorw("[Response] return error:", "err", err)
	}

	msg, ok := statusx.GetErrMsgWithCode(customCode)
	if !ok {
		msg = err.Error()
	}

	ctx.AbortWithStatusJSON(code, m_common.Successful{
		Code:    customCode,
		Message: msg,
		Data:    map[string]interface{}{},
	})
}

// SuperLimitWarn 数据超量限制提示
type SuperLimitWarn struct {
	IsWarn     bool   `json:"is_warn"`
	SuperLimit int64  `json:"super_limit"`
	Content    string `json:"content"`
}
