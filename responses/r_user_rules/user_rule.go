package r_user_rules

import "git.gobies.org/foeye/foeye3/responses/r_common"

// QueryListAndKeyword 请求参数
type QueryListAndKeyword struct {
	*r_common.QueryList
	r_common.QueryOfKeyword
	Category       int    `form:"category" `
	SecondCategory int    `form:"second_category" `
	Company        string `form:"company" `
	XcType         int    `form:"xc_type" `
}

// UserRuleFormData 自定义规则
type UserRuleFormData struct {
	RuleID         uint   `json:"rule_id" form:"rule_id"`
	Level          int    `json:"level" form:"level"`
	Product        string `json:"product" form:"product"`
	Company        string `json:"company" form:"company"`
	Rule           string `json:"rule" form:"rule"`
	ProductUrl     string `json:"product_url" form:"product_url"`
	SecondCategory uint   `json:"second_category" form:"second_category"`
	IsXc           int    `json:"is_xc" form:"is_xc"`
}
