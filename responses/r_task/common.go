package r_task

import (
	"git.gobies.org/foeye/foeye3/model/m_asset"
	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

// QueryListAndKeyword 查询列表请求字段
type QueryListAndKeyword struct {
	*r_common.QueryList
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
}

type TaskIdsReq struct {
	r_common.IdsReq
	State string `json:"state" form:"state" query:"state"`
}

// QueryListAndKeywordAndFilter 查询列表请求和筛选字段.
type QueryListAndKeywordAndFilter struct {
	*r_common.QueryList
	Keyword           string `json:"keyword" form:"keyword" query:"keyword"`
	TaskType          int    `json:"task_type" form:"task_type" query:"task_type"`
	RealBeginTimeGteq string `json:"real_begin_time_gteq" form:"real_begin_time_gteq" query:"real_begin_time_gteq"`
	RealBeginTimeLteq string `json:"real_begin_time_lteq" form:"real_begin_time_lteq" query:"real_begin_time_lteq"`
	UserId            int    `json:"user_id" form:"user_id" query:"user_id"`
}

// QueryAssetKeywordAndFilter 查询列表请求和筛选字段.
type QueryAssetKeywordAndFilter struct {
	*r_common.QueryList
	Keyword                string   `json:"keyword" form:"keyword" query:"keyword"`
	FilterSecondCategories string   `json:"filter_second_categories" form:"filter_second_categories" query:"filter_second_categories"`
	AssetStatus            string   `json:"asset_status" form:"asset_status" query:"asset_status"`
	FilterPorts            []int    `json:"filter_ports" form:"filter_ports" query:"filter_ports"`
	FilterCity             []string `json:"filter_city" form:"filter_city" query:"filter_city"`
	CreatedTimeRange       string   `json:"createdtime_range" form:"createdtime_range" query:"createdtime_range"`
	IpRanges               string   `json:"ip_ranges" form:"ip_ranges" query:"ip_ranges"`
	IpType                 string   `json:"ip_type" form:"ip_type" query:"ip_type"`
	OtherIpRanges          string   `json:"other_ip_ranges" form:"other_ip_ranges" query:"other_ip_ranges"`
	FilterRuleCompanies    []string `json:"filter_rule_companies" form:"filter_rule_companies" query:"filter_rule_companies"`
	FilterRuleInfoTitles   []string `json:"filter_rule_info_titles" form:"filter_rule_info_titles" query:"filter_rule_info_titles"`
	FilterProtocols        []string `json:"filter_protocols" form:"filter_protocols" query:"filter_protocols"`
	AddWay                 string   `json:"add_way" form:"add_way" query:"add_way"`
}

// TimedTaskQueryList 定时任务开关请求字段
type TimedTaskQueryList struct {
	TaskSwitch string `json:"task_switch" form:"task_switch" query:"task_switch"`
}

// PauseTaskQueryList 暂停任务请求字段
type PauseTaskQueryList struct {
	Type string `json:"type" form:"type" query:"type" binding:"required" enum:"stop,pause,edit_running"`
}

// TimedTaskCountResult Model.
//
// 数据模型 TimedTaskCountResult 定时任务结果参数。
//
// swagger:model TimedTaskCountResult
type TimedTaskCountResult struct {
	// 当前任务任务详情
	//
	// required: true
	// read only: false
	CurrentTask *m_task.TasksBase `json:"current_task"`

	// 定时任务列表
	//
	// required: true
	// read only: false
	TaskList []*m_task.TasksBase `json:"task_list"`

	// 资产数据对比
	//
	// required: true
	// read only: false
	AssetData []*TimedTaskCountLine `json:"asset_data"`

	// 漏洞数据对比
	//
	// required: true
	// read only: false
	ThreatData []*TimedTaskCountLine `json:"threat_data"`
}

func (t *TimedTaskCountResult) Init() {
	t.TaskList = make([]*m_task.TasksBase, 0)
	t.AssetData = make([]*TimedTaskCountLine, 0)
	t.ThreatData = make([]*TimedTaskCountLine, 0)
}

func (t *TimedTaskCountResult) AppendTask(list []*m_task.TasksBase) {
	for _, v := range list {
		tmp := v
		t.TaskList = append(t.TaskList, tmp)
	}
}

// 转换资产数据
func (t *TimedTaskCountResult) AppendAsset(data *m_asset.TaskAssetCountData) {
	// IP数据
	IPCount := new(TimedTaskCountLine)
	IPCount.Title = "IP"
	IPCount.Latest = data.LatestData.IPCount.Value
	IPCount.Last = data.LastData.IPCount.Value
	t.AssetData = append(t.AssetData, IPCount)
	// 端口数据
	PortCount := new(TimedTaskCountLine)
	PortCount.Title = "端口"
	PortCount.Latest = data.LatestData.PortCount.Value
	PortCount.Last = data.LastData.PortCount.Value
	t.AssetData = append(t.AssetData, PortCount)
	// 协议数据
	ProtocolCount := new(TimedTaskCountLine)
	ProtocolCount.Title = "协议"
	ProtocolCount.Latest = data.LatestData.ProtocolCount.Value
	ProtocolCount.Last = data.LastData.ProtocolCount.Value
	t.AssetData = append(t.AssetData, ProtocolCount)
	// 组件数据
	RulesCount := new(TimedTaskCountLine)
	RulesCount.Title = "组件"
	RulesCount.Latest = data.LatestData.RuleInfosCount.Value
	RulesCount.Last = data.LastData.RuleInfosCount.Value
	t.AssetData = append(t.AssetData, RulesCount)
}

// 转换漏洞数据
func (t *TimedTaskCountResult) AppendThreat(data *m_threat.TaskThreatCountData) {
	// 漏洞数据
	ThreatCount := new(TimedTaskCountLine)
	ThreatCount.Title = "漏洞"
	ThreatCount.Latest = data.LatestData.ThreatCount.Value
	ThreatCount.Last = data.LastData.ThreatCount.Value
	t.ThreatData = append(t.ThreatData, ThreatCount)

	// EXP数据
	ExpCount := new(TimedTaskCountLine)
	ExpCount.Title = "EXP"
	ExpCount.Latest = data.LatestData.ExpCount.ExpCountVal.Value
	ExpCount.Last = data.LastData.ExpCount.ExpCountVal.Value
	t.ThreatData = append(t.ThreatData, ExpCount)

	// 弱口令数据
	WeakCommandCount := new(TimedTaskCountLine)
	WeakCommandCount.Title = "弱口令"
	WeakCommandCount.Latest = data.LatestData.WeakCount.WeakCountVal.Value
	WeakCommandCount.Last = data.LastData.WeakCount.WeakCountVal.Value
	t.ThreatData = append(t.ThreatData, WeakCommandCount)

	// 默认口令数据
	DefaultCommandCount := new(TimedTaskCountLine)
	DefaultCommandCount.Title = "默认口令"
	DefaultCommandCount.Latest = data.LatestData.CommandCount.CommandCountVal.Value
	DefaultCommandCount.Last = data.LastData.CommandCount.CommandCountVal.Value
	t.ThreatData = append(t.ThreatData, DefaultCommandCount)
}

// TimedTaskCountLine Model.
//
// 数据模型 TimedTaskCountLine 定时任务结果参数。
//
// swagger:model TimedTaskCountLine
type TimedTaskCountLine struct {
	// 柱状图名称.
	//
	// required: true
	// read only: false
	// example: IP
	Title string `json:"title"`
	// 上次值.
	//
	// required: true
	// read only: false
	// example: 10
	Latest float64 `json:"latest"`
	// 上上次值.
	//
	// required: true
	// read only: false
	// example: 20
	Last float64 `json:"last"`
}
