package r_ip_filter

import "git.gobies.org/foeye/foeye3/model/m_ip_filter"

type IpRequest struct {
	FilterType string   `json:"filter_type" form:"filter_type" query:"filter_type"`
	Ips        []string `json:"ips" form:"ips" query:"ips"`
}

type IpResponse struct {
	FilterType string                 `json:"filter_type" form:"filter_type" query:"filter_type"`
	IpInfo     []m_ip_filter.IpFilter `json:"ip_info" form:"ip_info" query:"ip_info"`
}
