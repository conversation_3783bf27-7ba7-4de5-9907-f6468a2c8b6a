package r_netinfo

import "git.gobies.org/foeye/foeye3/responses/r_common"

// ConfigFormData  Model
//
// 数据模型 ConfigFormData 。
//
// swagger:model ConfigFormData
type ConfigFormData struct {
	// 流量开关.
	//
	// required: true
	// read only: false
	// example:on/off
	FlowSwitch string `form:"flow_switch" json:"flow_switch"`
	// 资产扫描数量.
	//
	// required: true
	// read only: false
	// example:150
	FlowAssetNum int `form:"flow_asset_num" json:"flow_asset_num"`
	// 扫描带宽.
	//
	// required: true
	// read only: false
	// example:100
	FlowBandwidth int `form:"flow_bandwidth" json:"flow_bandwidth"`
	// 任务开关.
	//
	// required: true
	// read only: false
	// example:on/off
	FlowAutoScan string `form:"flow_auto_scan" json:"flow_auto_scan"`
}

// QueryListAndKeyword 请求参数
type QueryListAndKeyword struct {
	*r_common.QueryList
	Keyword      string   `json:"keyword" form:"keyword"`
	Search       bool     `json:"search" form:"search"`
	AssetType    string   `json:"asset_type" form:"asset_type"`
	PortType     string   `json:"port_type" form:"port_type"`
	ProtocolType string   `json:"protocol_type" form:"protocol_type"`
	TimeRange    string   `json:"time" form:"time"`
	IDS          []string `json:"ids" form:"ids"`
}
