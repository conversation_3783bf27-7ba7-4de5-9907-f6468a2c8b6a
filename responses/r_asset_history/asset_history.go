package r_asset_history

import "git.gobies.org/foeye/foeye3/model/m_asset"

// OverviewsInfo  Model
//
// 数据模型 OverviewsInfo 。
//
// swagger:model OverviewsInfo
type OverviewsInfo struct {
	// 头部统计
	HeaderStatistics         HeaderStatistics        `json:"header_statistics"`
	AssetInfo                m_asset.AssetStatistics `json:"asset_info"`
	RepairedThreatInfo       ThreatInfo              `json:"repaired_threat_info"`
	NoRepairedThreatInfo     ThreatInfo              `json:"no_repaired_threat_info"`
	RepairedComplianceInfo   ComplianceInfo          `json:"repaired_compliance_info"`
	NoRepairedComplianceInfo ComplianceInfo          `json:"no_repaired_compliance_info"`
}

// HeaderStatistics  Model
//
// 数据模型 HeaderStatistics  header statistics。
//
// swagger:model HeaderStatistics
type HeaderStatistics struct {
	// IP总数
	//
	// required: false
	// read only: false
	// example: 10
	TotalIP int `json:"total_ip"`

	// IP新增总数
	//
	// required: false
	// read only: false
	// example: 10
	TotalNewIp int `json:"total_new_ip"`

	// 组件总数
	//
	// required: false
	// read only: false
	// example: 10
	TotalComponents int `json:"total_components"`

	// 漏洞总数
	//
	// required: false
	// read only: false
	// example: 10
	TotalThreat int `json:"total_threat"`

	// 端口总数
	//
	// required: false
	// read only: false
	// example: 10
	TotalPorts int `json:"total_ports"`

	// 网站总数
	//
	// required: false
	// read only: false
	// example: 10
	TotalSubDomain int `json:"total_subdomain"`

	// 业务总数
	//
	// required: false
	// read only: false
	// example: 10
	TotalBusinessApp int `json:"total_business_app"`
}

// ThreatInfo  threats statistics
type ThreatInfo struct {
	Total           int64             `json:"total"`
	TodayTotal      int64             `json:"today_total"`
	RepairedPercent float64           `json:"repaired_percent"`
	AvgUseTimeTitle string            `json:"avg_use_time_title"`
	Levels          []*m_asset.Bucket `json:"levels"`
	CommonTitles    []*m_asset.Bucket `json:"common_titles"`
	Usernames       []*m_asset.Bucket `json:"usernames"`
	Histories       []*m_asset.Bucket `json:"children"`
	Tags            interface{}       `json:"tags"`
}

// Level 统计数据
type Level struct {
	Key   int `json:"key"`
	Count int `json:"doc_count"`
}

// Levels 统计数据
type Levels struct {
	Keys []*Level `json:"buckets"`
}

// ComplianceInfo  compliance statistics
type ComplianceInfo struct {
	Total           int64             `json:"total"`
	TodayTotal      int64             `json:"today_total"`
	RepairedPercent float64           `json:"repaired_percent"`
	AvgUseTimeTitle string            `json:"avg_use_time_title"`
	Histories       []*m_asset.Bucket `json:"children"`
	RuleTitles      []*m_asset.Bucket `json:"rule_titles"`
	ViolationType   []*m_asset.Bucket `json:"violation_type"`
	Usernames       []*m_asset.Bucket `json:"usernames"`
	Tags            interface{}       `json:"tags"`
}
