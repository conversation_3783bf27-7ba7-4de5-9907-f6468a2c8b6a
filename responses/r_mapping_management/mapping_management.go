package r_mapping_management

import (
	"git.gobies.org/foeye/foeye3/model/m_mapping_management"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

type QueryListAndKeyword struct {
	*r_common.QueryList
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
}

type QueryListDeleteExtra struct {
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
	m_mapping_management.BatchIDsUint
}
