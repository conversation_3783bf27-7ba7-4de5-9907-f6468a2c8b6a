package r_poc_group

import (
	"git.gobies.org/foeye/foeye3/model/m_poc"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

// QueryListAndSearch
type QueryListAndSearch struct {
	r_common.QueryList
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
}

// GroupPocs Model
//
// GroupPocs 包含成功时的应用数据，data字段，包括已经引用的poc列表(UseList)和未引用的poc列表(NotUseList).
//
// swagger:model GroupPocs
type GroupPocs struct {
	// 响应状态.
	//
	// required: true
	// example: true
	Status bool `json:"status"`

	// 响应状态码.
	//
	// required: true
	// example: 200
	Code int `json:"code"`

	// 响应消息描述.
	//
	// required: true
	// example: 操作成功
	Message string `json:"message"`

	// 响应数据.
	//
	// required: true
	// example: interface{}
	Data PocList `json:"data"`
}

// PocList Model
//
// PocList 包含成功时的应用数据，包括已经引用的poc列表(UseList)和未引用的poc列表(NotUseList).
//
// swagger:model PocList
type PocList struct {

	// 已引用列表.
	//
	// required: true
	// example: [{id:1, name: "aaa"}]
	UseList []*m_poc.PocName `json:"use_list"`

	// 未引用列表.
	//
	// required: true
	// example: [{id:1, name: "aaa"}]
	NotUseList []*m_poc.PocName `json:"not_use_list"`
}
