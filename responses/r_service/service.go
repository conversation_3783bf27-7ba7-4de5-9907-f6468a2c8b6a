package r_service

import (
	"time"
)

// ServicesResponse Model
//
// 数据模型 ServicesResponse 返回给前端的数据模型
//
// swagger:model ServicesResponse
type ServicesResponse struct {
	// 记录ID.
	//
	// required: true
	// read only: false
	// example: 1
	ID uint `json:"id"`

	// 文件生成进度.
	//
	// required: true
	// read only: false
	// example: 10
	Progress int `json:"progress"`

	// 状态(1:开始生成文件,2:生产文件中,3:已完成,4:已失败,5:已下载).
	//
	// required: true
	// read only: false
	// example: 10
	State uint `json:"state"`

	// 用户名.
	//
	// required: true
	// read only: false
	// example:
	Username string `json:"username"`

	// 文件名.
	//
	// required: true
	// read only: false
	// example:
	Filename string `json:"filename"`

	// 下载模块.
	//
	// required: true
	// read only: false
	// example:
	Category string `json:"category"`

	// 添加时间.
	//
	// required: true
	// read only: false
	// example: 2021-03-29 17:55:39
	CreatedAt *time.Time `json:"created_at"`
}
