package r_overview

// AssetTrendStatistics Model
//
// 数据模型 AssetTrendStatistics 资产趋势统计参数
//
// swagger:model AssetTrendStatistics
type AssetTrendStatistics struct {
	// 日期类型(day:日;month:月;custom:自定义时间).
	//
	// required: false
	// read only: false
	// example: day
	DateTime string `json:"date_time" form:"date_time,default=day"`

	// 自定义时间(2022-09-22,2022-09-22).
	//
	// required: false
	// read only: false
	// example: 2022-02-02,2022-03-03
	TimeRange string `json:"time_range" form:"time_range"`
	// 资产类型（信创1/非信创0/全部2）
	//
	// required: false
	// read only: false
	// example: 1
	XcType string `json:"xc_type" form:"xc_type"`
}

// AssetVulnerabilityStatisticsParam Model
//
// 数据模型 AssetVulnerabilityStatisticsParam 资产漏洞环比同比统计参数
//
// swagger:model AssetVulnerabilityStatisticsParam
type AssetVulnerabilityStatisticsParam struct {
	// 当前月份.
	//
	// required: false
	// read only: false
	// example: 2020-02
	CurrentMonth string `json:"current_month" form:"current_month"`
	// 资产类型（信创/非信创）
	//
	// required: false
	// read only: false
	// example: 2020-02
	XcType string `json:"xc_type" form:"xc_type"`
}

// AssetPieParam Model
//
// 数据模型 AssetPieParam 资产总览-资产分类统计-饼图。
//
// swagger:model AssetPieParam
type AssetPieParam struct {

	// 资产类型（信创/非信创）
	//
	// required: false
	// read only: false
	// example: "1"
	XcType string `json:"xc_type" form:"xc_type"`
	// 查询数据类型（（数据排行1/基础软件分布0)。
	//
	// required: false
	// read only: false
	// example: "1"
	DataType string `json:"data_type" form:"data_type"`
}
