package r_compliance_monitor

import (
	"git.gobies.org/foeye/foeye3/model/m_compliance_monitor"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

type QueryListContainExtra struct {
	*r_common.QueryList
	*r_common.QueryOfKeyword
	*m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams

	// 排序字段(ip|createtime).
	//
	// required: false
	// read only: false
	// example: ip
	Field string `json:"field" form:"field" query:"field"`

	// 顺序(asc|desc).
	//
	// required: false
	// read only: false
	// example: asc
	Order string `json:"order" form:"order" query:"order"`
}

type QueryListDeleteExtra struct {
	*r_common.QueryList
	*r_common.QueryOfKeyword
	*m_compliance_monitor.BatchIDsString
	*m_compliance_monitor.ComplianceExamineAdvancedSearchQueryParams
}

type XcComplianceMonitorSummaryParam struct {
	*r_common.QueryList
	ViolationsType string `json:"violations_type" form:"violations_type" query:"violations_type"`
	XcType         string `json:"xc_type" form:"xc_type" query:"xc_type"`
	MenuType       string `json:"menu_type" form:"menu_type" query:"menu_type"`
}

func CheckMonitorTypeForSearch(violationsType string) (valid bool) {
	valid = false
	for _, v := range m_compliance_monitor.ComplianceMonitorItemsIns {
		if violationsType == v.Value {
			valid = true
		}
	}
	return valid
}
