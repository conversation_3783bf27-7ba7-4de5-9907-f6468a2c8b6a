package r_asset_search

import "git.gobies.org/foeye/foeye3/responses/r_common"

// QueryListAndKeyword 请求参数
type QueryListAndKeyword struct {
	*r_common.QueryList
	Asset         map[string]string `form:"asset"`
	Keyword       string            `form:"q"`
	KeywordBase64 string            `form:"qbase64"`
	SearchType    string            `form:"search_type"`
}

func (q *QueryListAndKeyword) RewriteKeywordBase64() {
	// country region map
	countryToRegionMap := map[string]string{
		"cmVnaW9uJTNEJTIySEslMjI=": "Y291bnRyeSUzRCUyMkhLJTIy", // 香港
		"cmVnaW9uJTNEJTIyVFclMjI=": "Y291bnRyeSUzRCUyMlRXJTIy", // 台湾
		"cmVnaW9uJTNEJTIyTU8lMjI=": "Y291bnRyeSUzRCUyMk1PJTIy", // 澳门MO
		"cmVnaW9uJTNEJTIybW8lMjI=": "Y291bnRyeSUzRCUyMm1vJTIy", // 澳门mo
	}

	// rewrite keyword
	if q.KeywordBase64 == "cmVnaW9uJTNEJTIySEslMjI=" ||
		q.KeywordBase64 == "cmVnaW9uJTNEJTIyVFclMjI=" ||
		q.KeywordBase64 == "cmVnaW9uJTNEJTIyTU8lMjI=" ||
		q.KeywordBase64 == "cmVnaW9uJTNEJTIybW8lMjI=" {
		q.KeywordBase64 = countryToRegionMap[q.KeywordBase64]
	}
}

/*-----------资产特征-------------*/

// QueryClusteringListAndKeyword 请求参数
type QueryClusteringListAndKeyword struct {
	*r_common.QueryList
	ClusteringType string `json:"clustering_type" form:"clustering_type"`
	// 排序类型 升序 asc  降序 desc
	//
	// required: true
	// read only: false
	// example: 1
	SortType string `json:"sort_type" form:"sort_type"`
	// 排序字段 update_time 更新时间排序 asset_num 资产数量排序
	//
	// required: true
	// read only: false
	// example: 1
	SortField string `json:"sort_field" form:"sort_field"`
}

// FeatureClusteringRule Model
//
// 数据模型 FeatureClusteringRule 创建资产特征规则的数据模型
//
// swagger:model FeatureClusteringRule
type FeatureClusteringRule struct {
	// 分层是1-5，1是硬件层，以此类推.
	//
	// required: true
	// read only: false
	// example: 1
	Level int `json:"level" form:"level"`

	// 规则名称.
	//
	// required: true
	// read only: false
	// example: f7bb96d1dcd6cfe0e5ce1f03e35f84bf
	Product string `json:"product" form:"product"`

	// 厂商名称.
	//
	// required: true
	// read only: false
	// example: fofa
	Company string `json:"company" form:"company"`

	// 规则内容.
	//
	// required: true
	// read only: false
	// example: fid="asd"
	Rule string `json:"rule" form:"rule"`

	// 应用网站.
	//
	// required: true
	// read only: false
	// example: f7bb96d1dcd6cfe0e5ce1f03e35f84bf
	ProductUrl string `json:"product_url" form:"product_url"`

	// 分类id.
	//
	// required: true
	// read only: false
	// example: 1
	SecondCategory uint `json:"second_category" form:"second_category"`

	// FID.
	//
	// required: true
	// read only: false
	// example: f7bb96d1dcd6cfe0e5ce1f03e35f84bf
	Fid string `json:"fid" form:"fid"`

	// 规则ID.
	//
	// required: false
	// read only: false
	// example: 1
	RuleId uint `json:"rule_id" form:"rule_id"`

	// 规则类型(信创非信创).
	//
	// required: false
	// read only: false
	// example: 1
	IsXc int `json:"is_xc" form:"is_xc"`
}

// FeatureBlacklist Model
//
// 数据模型 FeatureBlacklist 加入特征黑名单的数据模型
//
// swagger:model FeatureBlacklist
type FeatureBlacklist struct {
	// fid.
	//
	// required: true
	// read only: false
	// example: asdasdasd
	Fid string `json:"fid" form:"fid"`

	// 规则ID.
	//
	// required: true
	// read only: false
	// example: 1
	RuleId uint `json:"rule_id" form:"rule_id"`
}

// RemoveInterferenceItems Model
//
// 数据模型 RemoveInterferenceItems 排除干扰状态的数据模型
//
// swagger:model RemoveInterferenceItems
type RemoveInterferenceItems struct {
	// 排除干扰状态(true:开启;false:关闭).
	//
	// required: true
	// read only: false
	// example: true
	State bool `json:"state" form:"state"`
}
