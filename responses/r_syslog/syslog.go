package r_syslog

// SyslogConfigFormData  Model
//
// 数据模型 SyslogConfigFormData 。
//
// swagger:model SyslogConfigFormData
type SyslogConfigFormData struct {
	// 是否开启.
	//
	// required: true
	// read only: false
	// example: on|off
	SyslogAssetSwitch string `form:"switch" json:"switch"`

	// 远程服务地址.
	//
	// required: true
	// read only: false
	// example: ************
	Host string `form:"host" json:"host"`

	// 端口.
	//
	// required: true
	// read only: false
	// example: 22
	Port string `form:"port" json:"port"`
}
