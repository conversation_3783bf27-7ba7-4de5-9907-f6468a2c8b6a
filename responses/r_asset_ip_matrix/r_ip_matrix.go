package r_asset_ip_matrix

import (
	"errors"
	"time"

	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset_ip_matrix"
)

// IPMatrix IP矩阵返回数据
// swagger:model IPMatrix
type IPMatrix struct {
	// IP统计数据
	Statistics es_asset_ip_matrix.IPMatrixStatistics `json:"statistics,omitempty"`
	// IP矩阵列表
	Matrix []es_asset_ip_matrix.IPMatrixItem `json:"matrix,omitempty"`
}

var Cache *CacheNestedData = &CacheNestedData{}

// cacheNestedData cache data for ip matrix
type CacheNestedData struct {
	Data     IPMatrix
	ExpireAt time.Time
}

// GetData get cache data of ip matrix
func (c *CacheNestedData) GetData() (IPMatrix, error) {
	if time.Now().Before(c.ExpireAt) && c.Data.Matrix != nil && len(c.Data.Matrix) > 0 {
		return c.Data, nil
	}
	return c.Data, errors.New("no cache data")
}
