package r_system

import (
	"git.gobies.org/foeye/foeye3/model/m_system"
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

type QueryListAndKeyword struct {
	*r_common.QueryList
	*r_common.QueryOfKeyword
	State bool `json:"state" form:"state" query:"state"`
}

type UpgradeLogQuery struct {
	Type string `json:"type" form:"type" query:"type"`
}

// UpgradeLogResp Model
//
// 数据模型 UpgradeLogResp 升级日志返回结果。
//
// swagger:model UpgradeLogResp
type UpgradeLogResp struct {
	// 日志列表.
	//
	// required: true
	// read only: false
	// example: [{"version": "V3.0.38", "update_time": "2023-01-01 00:00:00", content: "升级内容"}]
	List []*m_system.LogInfo `json:"list"`
}
