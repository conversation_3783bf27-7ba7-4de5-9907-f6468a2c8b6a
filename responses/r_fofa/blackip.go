package r_fofa

import "git.gobies.org/foeye/foeye3/responses/r_common"

// TaskQueryListAndKeyword 请求参数
type TaskQueryListAndKeyword struct {
	*r_common.QueryList
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
	Type    string `json:"type" form:"type" query:"type"`
	Sort    string `json:"sort" form:"sort" query:"sort"`
}

// TaskExportAndKeyword 请求参数
type TaskExportAndKeyword struct {
	Keyword string `json:"keyword" form:"keyword" query:"keyword"`
	TaskIds []int  `json:"task_ids" form:"task_ids" query:"task_ids"`
	Ids     []int  `json:"ids" form:"ids" query:"ids"`
}
