package r_fofa

import (
	"git.gobies.org/foeye/foeye3/responses/r_common"
)

// AccountPermissions Model
//
// 数据模型 AccountPermissions FoFa联动-账户权限请求参数。
//
// swagger:model AccountPermissions
type AccountPermissions struct {
	// 邮箱.
	//
	// required: true
	// read only: false
	// example: <EMAIL>
	Email string `json:"email" form:"email" query:"email" binding:"required"`

	// API_KEY.
	//
	// required: true
	// read only: false
	// example: Ashgd1h2eg781uyg2e78qwdg78qwgdq8w7d
	Key string `json:"key" form:"key" query:"key" binding:"required"`
}

// QueryListAndKeyword 请求参数
type QueryListAndKeyword struct {
	*r_common.QueryList
	Ips     []string `json:"ips" form:"ips" query:"ips"`
	Keyword string   `json:"keyword" form:"keyword" query:"keyword"`
}

type Batch struct {
	Ids   []int `json:"ids" form:"ids" query:"ids" binding:"required"`
	State bool  `json:"state" form:"state" query:"state"`
}

func (batch *Batch) FixedIdsZeroValue() {
	tem := make([]int, 0)
	for _, item := range batch.Ids {
		if item != 0 {
			tem = append(tem, item)
		}
	}
	batch.Ids = tem
}
