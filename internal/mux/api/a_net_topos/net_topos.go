package a_net_topos

import (
	"fmt"
	"net"
	"net/http"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_net_topos"
	"git.gobies.org/foeye/foeye3/database/db_system"
	"git.gobies.org/foeye/foeye3/internal/mux/model/m_net_topos"
	"git.gobies.org/foeye/foeye3/internal/mux/packages/responses/r_common"
	db_topos "git.gobies.org/foeye/foeye3/model/m_net_topos"
	"git.gobies.org/foeye/foeye3/model/m_system"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/elasticx/es_asset"
	"git.gobies.org/foeye/foeye3/packages/elasticx/esi_assets"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"git.gobies.org/foeye/foeye3/responses/r_asset"
	r_common3 "git.gobies.org/foeye/foeye3/responses/r_common"
	"git.gobies.org/foeye/foeye3/responses/r_net_topos"

	"github.com/gin-gonic/gin"
)

// NetToposAPIHandler interface for encapsulating http handles for network topos.
type NetToposAPIHandler interface {
	GetNetToposList(ctx *gin.Context)
	GetSubIPRanges(ctx *gin.Context)
}

// NetToposAPI provides handlers for managing net topos search.
type NetToposAPI struct {
	AssetDB         es_asset.ESAssetDatabaseInter
	DB              db_net_topos.GormNetToposDatabaseInter
	SystemDB        database.GormSystemDatabaseStore
	AssetElasticDB  elasticx.IndexAssetsStore
	databaseFactory database.Factory
}

// NewMuxNetToposAPI creates a new NetToposAPI instance.
func NewMuxNetToposAPI(configure *config.Configure, DB *database.GormDatabase, ES *elasticx.ElasticDatabase, databaseFactory database.Factory) (*NetToposAPI, error) {
	db := new(db_net_topos.GormNetToposDatabase)
	db.Instance = DB

	systemDB := db_system.NewGormSystemDatabase(DB)

	es := es_asset.NewESAssetDatabase(configure, ES, databaseFactory)
	es.Instance = ES
	assetES := esi_assets.NewStore(ES)

	return &NetToposAPI{
		DB:             db,
		SystemDB:       systemDB,
		AssetDB:        es,
		AssetElasticDB: assetES,
	}, nil
}

// GetNetToposList 网络拓扑
// swagger:operation GET /api/v2/net_topos 资产管理 GetNetToposList
//
// 网络拓扑。
//
// ---
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// parameters:
// - name: email
//   in: query
//   description: 用户名.
//   required: true
//   type: string
// - name: key
//   in: query
//   description: 通过用户名密码请求返回的key.
//   required: true
//   type: string
// responses:
//   200:
//     description: Ok
//     schema:
//       type: array
//       items:
//         $ref: "#/definitions/NetToposBase"
//   400:
//     description: Bad Request
//     schema:
//         $ref: "#/definitions/MuxSuccess"
//   401:
//     description: Unauthorized
//     schema:
//         $ref: "#/definitions/MuxSuccess"
//   404:
//     description: Not Found
//     schema:
//         $ref: "#/definitions/MuxSuccess"
func (api *NetToposAPI) GetNetToposList(ctx *gin.Context) {
	list, err := api.DB.GetNetToposList()
	if err != nil {
		r_common.SuccessOrAbortWithCustomStatusCode(r_common.SuccessOrAbortInfo{
			CTX:        ctx,
			Code:       http.StatusBadRequest,
			StatusCode: statusx.AssetListStatusFailedWithNetTopos,
			Msg:        statusx.AssetListStatusFailedWithNetToposOfMessage,
			Status:     "failure",
		})
		return
	}

	system, err := api.SystemDB.GetSystemWorkerIp("addr", "ip_config")
	if err != nil || system == nil {
		r_common.SuccessOrAbortWithCustomStatusCode(r_common.SuccessOrAbortInfo{
			CTX:        ctx,
			Code:       http.StatusInternalServerError,
			StatusCode: statusx.AssetListStatusFailedWithNetToposSystemFailed,
			Msg:        statusx.AssetListStatusFailedWithNetToposSystemFailedOfMessage,
			Status:     "failure",
		})
		return
	}

	if s, ok := system.(*m_system.Systems); ok {
		base := convertAssetNetTopos(list, s.Value)
		assetQuery := &r_asset.QueryListAndKeyword{
			QueryList: &r_common3.QueryList{
				Number: 1,
				Size:   1,
			},
		}
		assetTotal, _, _ := api.AssetDB.GetAssetList(ctx, assetQuery, nil, "", false)
		base.AssetTotal = assetTotal
		ctx.JSON(http.StatusOK, gin.H{
			"status":      "ok",
			"status_code": http.StatusOK,
			"list":        base,
		})
		return
	}

	r_common.SuccessOrAbortWithCustomStatusCode(r_common.SuccessOrAbortInfo{
		CTX:        ctx,
		Code:       http.StatusInternalServerError,
		StatusCode: statusx.AssetListStatusFailedWithNetToposSystemFailed,
		Msg:        statusx.AssetListStatusFailedWithNetToposSystemFailedOfMessage,
		Status:     "failure",
	})
}

// GetSubIPRanges 网络拓扑-当前网段
// swagger:operation GET /api/v2/net_topos/sub_ip_ranges 资产管理 GetSubIPRanges
//
// 网络拓扑-当前网段。
//
// ---
// produces: [application/json]
// security: [clientTokenHeader: [], clientTokenQuery: [], basicAuth: []]
// parameters:
// - name: email
//   in: query
//   description: 用户名.
//   required: true
//   type: string
// - name: key
//   in: query
//   description: 通过用户名密码请求返回的key.
//   required: true
//   type: string
// - name: ip_range
//   in: query
//   description: 网段
//   required: true
//   type: string
// responses:
//   200:
//     description: Ok
//     schema:
//       type: array
//       items:
//         $ref: "#/definitions/SubIpRanges"
//   400:
//     description: Bad Request
//     schema:
//         $ref: "#/definitions/MuxSuccess"
//   401:
//     description: Unauthorized
//     schema:
//         $ref: "#/definitions/MuxSuccess"
//   404:
//     description: Not Found
//     schema:
//         $ref: "#/definitions/MuxSuccess"
func (api *NetToposAPI) GetSubIPRanges(ctx *gin.Context) {
	var query = new(r_net_topos.QueryList)
	if err := ctx.Bind(&query); err != nil {
		r_common.SuccessOrAbortWithCustomStatusCode(r_common.SuccessOrAbortInfo{
			CTX:        ctx,
			Code:       http.StatusBadRequest,
			StatusCode: statusx.AssetListStatusFailedWithNetToposParameterFailed,
			Msg:        statusx.AssetListStatusFailedWithNetToposParameterFailedOfMessage,
			Status:     "failure",
		})
		return
	}

	data, err := api.AssetElasticDB.GetAssetTopo(ctx, query)
	if err != nil {
		r_common.SuccessOrAbortWithCustomStatusCode(r_common.SuccessOrAbortInfo{
			CTX:        ctx,
			Code:       http.StatusInternalServerError,
			StatusCode: statusx.AssetListStatusFailedWithNetTopos,
			Msg:        statusx.AssetListStatusFailedWithNetToposOfMessage,
			Status:     "failure",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status":      "ok",
		"status_code": http.StatusOK,
		"list":        data,
	})
}

// convertAssetNetTopos ...
func convertAssetNetTopos(list []*db_topos.NetTopos, ip string) *m_net_topos.NetToposBase {
	routerMap := make(map[string]string)
	ipRangeMap := make(map[string]string)
	OfficeNetSegmentsMap := make(map[string]string)
	videoNetSegmentsMap := make(map[string]string)
	businessNetSegmentsMap := make(map[string]string)
	dataNetSegmentsMap := make(map[string]string)
	netTopos := make([]*m_net_topos.SubIpRanges, 0)
	for _, topos := range list {
		ipRangeMap[topos.TargetTitle] = topos.TargetTitle
		switch topos.TargetInfo {
		case "普通办公网段":
			OfficeNetSegmentsMap[topos.TargetTitle] = topos.TargetInfo
		case "视频专用网段":
			videoNetSegmentsMap[topos.TargetTitle] = topos.TargetInfo
		case "业务支撑网段":
			businessNetSegmentsMap[topos.TargetTitle] = topos.TargetInfo
		case "数据中心网段":
			dataNetSegmentsMap[topos.TargetTitle] = topos.TargetInfo
		default:
		}
		sub := &m_net_topos.SubIpRanges{
			Source: topos.Source,
			Target: topos.Target,
			Type:   topos.PathCategory,
			Url:    TextToImage[topos.SourceInfo],
			Url1:   TextToImage[topos.TargetInfo],
		}
		if topos.SourceInfo == "路由" {
			routerMap[topos.Source] = topos.Source
			sub.Text = []string{ip}
		} else {
			sub.Text = []string{fmt.Sprintf("%s:%s", topos.SourceInfo, topos.Source)}
		}
		if topos.IsLast > 0 {
			_, _, err := net.ParseCIDR(topos.Target)
			target := topos.Target
			if err != nil {
				target = topos.TargetTitle
			}
			sub.Text1 = []string{fmt.Sprintf("%s:%s", topos.TargetInfo, target)}
			sub.Target = target
		} else {
			sub.Text1 = []string{fmt.Sprintf("%s:%s", topos.TargetInfo, topos.Target)}
		}
		netTopos = append(netTopos, sub)
	}

	base := &m_net_topos.NetToposBase{
		RouteTotal:               len(routerMap),
		IpRangeTotal:             len(ipRangeMap),
		OfficeNetSegmentsTotal:   len(OfficeNetSegmentsMap),
		VideoNetSegmentsTotal:    len(videoNetSegmentsMap),
		BusinessNetSegmentsTotal: len(businessNetSegmentsMap),
		DataNetSegmentsTotal:     len(dataNetSegmentsMap),
		NetTopos:                 netTopos,
	}

	return base
}

var TextToImage = map[string]string{
	"中心":         "/topo/zhongxinIP-F.png",
	"路由":         "/topo/luyou.png",
	"数据中心网段": "/topo/wd-shujuzhongxinwangduan.png",
	"普通办公网段": "/topo/wd-putongbangongwangduan.png",
	"视频专用网段": "/topo/wd-shipinzhuanyongwangduan.png",
	"业务支撑网段": "/topo/wd-yewuzhichengwangduan.png",
}
