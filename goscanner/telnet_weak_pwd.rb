@roots_path = File.expand_path(File.dirname(__FILE__))
Path = @roots_path unless defined? Path

if __FILE__ == $0
  require_relative '../fofascan'
end
require 'fofa_core'
require 'net/telnet'
require 'timeout'
require 'open3'

class FofaExploits < Fofa::Exploit
  def get_info
    {
        "Name": "Telnet弱口令",
        "Description": "Telnet是在因特网或局域网上使用的协议，以使用虚拟终端连接提供双向交互式面向文本的通信设施。如果用户系统存在Telnet弱口令，可能会使攻击者能够轻松进入系统，控制服务器。",
        "Product": "Telnet",
        "Homepage": "https://fofa.so/",
        "DisclosureDate": "2017-08-25",
        "Author": "bmh",
        "FofaQuery": "protocol=\"telnet\" || port=23",
        "Level": "3",
        "Impact": "<p>弱口令会被攻击者轻松破解，获取Telnet服务的权限，造成数据泄露，甚至服务器被控制。</p>",
        "Recommandation": "<p>1、修改弱口令，密码最好包含大小写字母、数字和特殊字符等，且位数大于8位。</p><p>2、通过防火墙等安全设备设置访问策略，设置白名单访问。</p>",
        "References": [
            "https://fofa.so"
        ],
        "HasExp": false,
        "ExpParams": [],
        "is0day": false,
        "ExpTips": {
            "type": "Tips",
            "content": ""
        },
        "ScanSteps": [],
        "Posttime": "2019-09-19 19:04:18",
        "fofacli_version": "3.10.4",
        "fofascan_version": "0.1.16",
        "status": "0"
    }
  end

  def initialize(info = {})
    super(info.merge(get_info()))

    @user_pass_pair = [
        "administrator:_:123",
        "administrator:_:12345",
        "administrator:_:123456",
        "admin:_:admin",
        "root:_:password",
        "root:_:123456",
        "root:_:root",
        "admin:_:admin123",
        "root:_:admin",
        "root:_:000000",
        "www:_:123456",
        "root:_:123",
        "conadmin:_:conadmin",
        "admin:_:1qaz@WSX",
        "root:_:1qaz@WSX",
        "root:_:111111",
        "root:_:123456789",
        "root:_:qwerty",
        "root:_:12345678",
        "root:_:1234567890",
        "root:_:1234567",
        "root:_:123123",
        "root:_:987654321",
        "root:_:123qwe",
        "root:_:zxcvbnm"
    ]
  end

  def vulnerable(hostinfo)
    Timeout.timeout(1800){
      host, port = slice_ip_port(hostinfo)
      if port == "" || port.nil?
        port = 23
      end

      # 读取自定义用户名/密码
      customize_infos = read_customize_userpwd("#{Path}/../data/telnet_customize_info.txt")
      customize_infos = customize_infos + @user_pass_pair
      all_userpwd_infos = customize_infos
      all_userpwd_infos.uniq!
      all_userpwd_infos.each do |values|
        Timeout.timeout(40){
          if values != "" || !values.nil?
            key, value = values.split(':_:')
            puts "\n  telnet-weak: #{host}:#{port}  #{key}---#{value}"
            cmd = "hydra -l #{key} -p #{value} telnet://#{host}:#{port}"
            result = ""
            begin
              cmd_exec(cmd) do |line|
                puts "#{hostinfo}=---1---#{line}"
                result << line
              end
            rescue => e
              puts "Telnet weak pwd cmd_exec error: #{e}"
            end
            #puts "=-----#{result}"
            if result.include?("target successfully completed")
              cmd = "hydra -l whfaiox -p hfcdks7258490 telnet://#{host}:#{port}"
              result = ""
              begin
                cmd_exec(cmd) do |line|
                  puts "#{hostinfo}=---2---#{line}"
                  result << line
                end
              rescue => e
                puts "rdp weak pwd cmd_exec error: #{e}"
              end
              if result.include?("target successfully completed")
                return false
              elsif result.include?("0 valid password found") && !result.include?("The connection failed to establish")
                puts "#{hostinfo}=---3---success"
                yield({"username" => key, "password" => value}) if block_given?
                return true
              end
            end
            # result = `hydra -l #{key} -p #{value} #{host} -s #{port} postgres`

          end
        }
      end
    return false
    }
  end

  def exploit(hostinfo)
  end

  def cmd_exec(cmd)
    time1 = Time.now
    Open3.popen3(cmd) do |stdin, stdout, stderr, wait_thr|
      stdin.close
      stderr.sync = true
      stdout.sync = true

      lines = {stderr => '', stdout => ''}
      finished = false
      loop do
        readers, slurp = IO.select([stderr, stdout], [], [], 1)
        readers.each do |reader|
          need_break = false
          begin
            while c = reader.readchar
              # puts "=--------#{c}"
              if c == "\r" || c == "\n"
                if lines[reader].size > 0
                  line = lines[reader]
                  if reader == stdout
                    # puts "------------line: #{line}"
                    yield(line) if block_given?
                  else #error
                    puts "Telnet weak pwd stderr: #{line}"
                  end
                end
                lines[reader] = ""
                need_break = true
              else
                lines[reader] += c
              end
              break if need_break

              time2 = Time.now
              if time2.to_i-time1.to_i >= 35
                puts "Time: #{Time.now}, command: #{cmd}"
                begin
                  Process.kill(9,wait_thr.pid)
                rescue
                end
                break
              end
            end
          rescue EOFError => e
            finished = true
          rescue => e
            raise e
          end

        end if readers

        if finished
          break
        end
        time2 = Time.now
        if time2.to_i-time1.to_i >= 35
          puts "Time: #{Time.now}, command: #{cmd}"
          begin
            Process.kill(9,wait_thr.pid)
          rescue
          end
          break
        end
      end
    end
  end
end

if __FILE__ == $0
  do_my_scan($0, ARGV)
end