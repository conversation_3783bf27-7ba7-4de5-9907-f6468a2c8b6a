package middleware

import (
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye/foeye3/config"
)

// Channel solution middleware to solve foeye 渠道.
func Channel(configure *config.Configure) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if configure.Server.IsChannel {
			url := ctx.Request.URL.Path
			numberReg, _ := regexp.Compile(`/\d+/?`)
			ipReg, _ := regexp.Compile(`/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})`)
			pdfReg, _ := regexp.Compile(`/v3/public/report/pdf/(.*?)\.pdf$`)
			//替换报告管理里面的查看PDF报告
			url = pdfReg.ReplaceAllString(url, "/v3/public/report/pdf/")
			//必须先替换IP地址,再替换数字,因为IP地址中包含数字
			url = ipReg.ReplaceAllString(url, "/")
			url = numberReg.ReplaceAllString(url, "/")
			//使用手册
			url = strings.ReplaceAll(url, ".", "_")
			if _, exists := configure.Channel.ChannelUrl[url]; !exists {
				ctx.AbortWithStatus(http.StatusNotFound)
				return
			}
		}
		ctx.Next()
	}
}
