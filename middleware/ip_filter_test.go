package middleware

import (
	"git.gobies.org/foeye/foeye3/model/m_ip_filter"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestIsIPInFilter(t *testing.T) {
	// 构造时间（用于初始化 IpFilter 结构体）
	now := time.Now()

	// 测试数据
	ipList := []m_ip_filter.IpFilter{
		{Id: 1, FilterType: "white", Ip: "***********", CreatedAt: &now},
		{Id: 2, FilterType: "white", Ip: "***********/24", CreatedAt: &now},
	}

	tests := []struct {
		userIP   string
		expected bool
	}{
		{"***********", true},   // IP 精确匹配
		{"***********", true},   // IP 在 CIDR 范围内
		{"**********", false},   // IP 不在过滤列表中
		{"************", false}, // IP 不在过滤列表中
		{"invalid-ip", false},   // 非法 IP
	}

	for _, test := range tests {
		t.Run(test.userIP, func(t *testing.T) {
			result := IsIPInFilter(test.userIP, ipList)
			assert.Equal(t, test.expected, result)
		})
	}
}
