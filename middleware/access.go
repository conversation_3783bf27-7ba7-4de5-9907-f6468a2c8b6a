package middleware

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"time"

	"github.com/gin-gonic/gin"
)

type ResponseWriterWrapper struct {
	gin.ResponseWriter
	Body *bytes.Buffer
}

func (w ResponseWriterWrapper) Write(b []byte) (int, error) {
	w.Body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w ResponseWriterWrapper) WriteString(s string) (int, error) {
	w.Body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

func AssetLog() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		begin := time.Now()
		// get request body
		bodyRaw, _ := ctx.GetRawData()
		ctx.Request.Body = ioutil.NopCloser(bytes.NewBuffer(bodyRaw))
		// get response body
		blw := &ResponseWriterWrapper{Body: bytes.NewBufferString(""), ResponseWriter: ctx.Writer}
		ctx.Writer = blw

		ctx.Next()

		respRaw := blw.Body.String()
		// response 最多保留1000字符
		if len(respRaw) > 1000 {
			respRaw = string([]byte(respRaw)[0:1000])
		}

		fmt.Printf("[FOEYE3] %v | %v | %v | %v | %v | %v | %v | %v\n",
			begin.Format("2006/01/02 - 15:04:05"),
			time.Now().Sub(begin).String(),
			ctx.Writer.Status(),
			ctx.ClientIP(),
			ctx.Request.Method,
			ctx.Request.URL.Path,
			string(bodyRaw),
			respRaw,
		)
	}
}
