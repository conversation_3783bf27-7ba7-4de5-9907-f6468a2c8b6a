package middleware

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	log "github.com/sirupsen/logrus"
	"github.com/thoas/go-funk"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/license"
	"git.gobies.org/foeye/foeye3/packages/systemx"
)

// License solution middleware to solve license sharing problems.
func License(configure *config.Configure, redisclient *redis.Client) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if Excluded(ctx.FullPath()) {
			ctx.Next()
			return
		}

		//if !configure.Server.IsCloseLicense {
		isFilter := systemx.IsContain(configure.WhiteUrl.LicenseUrl, ctx.Request.URL.Path)
		if !isFilter {
			isActive, err := license.IsActive(configure, redisclient)
			if err != nil {
				log.Println(err)
				ctx.AbortWithError(http.StatusInternalServerError, errors.New("获取激活状态失败"))
				return
			}
			if !isActive {
				ctx.AbortWithError(http.StatusForbidden, errors.New("未激活"))
				return
			}
		}
		isLicense, err := license.CheckModulePermissions(configure, redisclient, ctx.Request.URL.Path)
		if err != nil {
			log.Println(err)
			ctx.AbortWithError(http.StatusInternalServerError, errors.New("检查模块权限失败"))
			return
		}
		if !isLicense {
			ctx.AbortWithError(http.StatusForbidden, errors.New("未授权"))
			return
		}
		//}
		ctx.Next()
	}
}

// Excluded 排除的路由
func Excluded(path string) bool {
	routes := []string{
		"/v3/microkernels/start_proc",
		"/v3/systems/clear_upgrade_updata_foeye_log",
	}
	if funk.ContainsString(routes, path) {
		return true
	}
	return false
}
