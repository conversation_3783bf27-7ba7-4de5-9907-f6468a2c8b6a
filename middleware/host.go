package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/packages/helpers"
	"git.gobies.org/foeye/foeye3/packages/hosts"
	"git.gobies.org/foeye/foeye3/store"

	"github.com/gin-gonic/gin"

	"git.gobies.org/foeye-dependencies/fireness"
	"git.gobies.org/foeye-dependencies/logger"
)

// Host handler.
func Host(configure *config.Configure, cache store.Factory, table database.Factory) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var host = ctx.Request.Host
		var allows = configure.Server.AllowsHost
		var params = []interface{}{
			"default", allows,
		}

		var loaded = cache.Redis().Global().KeyExistsOfNetworkList()
		if !loaded {
			addr, mapIps := hosts.Addresses(table)
			logger.Infow("[MIDDLEWARE_HOST]Founded all network", "list", addr)
			logger.Infow("[MIDDLEWARE_HOST]Founded all mapIps", "mapIps", mapIps)
			err := cache.Redis().Global().KeySetOfNetworkList(addr, 0)
			if err != nil {
				logger.Warnw("[MIDDLEWARE_HOST]Set founded network list to failed",
					"list", addr,
					"error", err,
				)
			}
			err = cache.Redis().Global().KeySetOfMapIPsList(mapIps, 0)
			if err != nil {
				logger.Warnw("[MIDDLEWARE_HOST]Set founded mapIps list to failed",
					"list", mapIps,
					"error", err,
				)
			}
		}

		list, err := cache.Redis().Global().GetKeyOfNetworkList()
		if err != nil {
			logger.Warnw("[MIDDLEWARE_HOST]Founded all network form cache failure",
				"list", list,
				"error", err,
			)
		}

		if list != nil {
			allows = append(allows, list...)
			params = append(params, "loading", list)
		}

		mapIps, err := cache.Redis().Global().GetKeyOfMapIPsList()
		if err != nil {
			logger.Warnw("[MIDDLEWARE_HOST]Founded all map ip form cache failure",
				"list", mapIps,
				"error", err,
			)
		}
		if mapIps != nil {
			allows = append(allows, mapIps...)
		}

		localhosts, _ := fireness.Localhost(
			fireness.WithLocalhostExclude("127.0.0.1", "::1"),
			fireness.WithLocalhostExcludeIPv6(true),
		)
		for _, localhost := range localhosts {
			if localhost != "" {
				ips := []string{
					localhost,
					fmt.Sprintf("%s:3000", localhost),
					fmt.Sprintf("%s:8000", localhost),
				}

				allows = append(allows, ips...)
				params = append(params, "localhost", ips)
			}
		}

		if !helpers.InArray(allows, host) {
			logger.Infow("[MIDDLEWARE_HOST@ HostHandler]allows", "allows", allows, "host", host)
			allowsStr := fmt.Sprintf("[%s]", strings.Join(allows, ","))
			ctx.AbortWithError(http.StatusForbidden,
				fmt.Errorf("current host %q is deny, allows: %s", host, allowsStr))
			ctx.Abort()
		}

		ctx.Next()
	}
}

func localhost() string {
	list, err := fireness.Localhost(
		fireness.WithLocalhostExclude("127.0.0.1"),
		fireness.WithLocalhostExcludeIPv6(true),
	)

	if err != nil {
		return ""
	}

	first := list.First()
	if first != "" {
		return first
	}

	return ""
}
