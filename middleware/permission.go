package middleware

import (
	"git.gobies.org/foeye/foeye3/config"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// Permission solution middleware to solve permission sharing problems.
func Permission(configure *config.Configure, redisclient *redis.Client) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		//if !configure.Server.IsClosePermission {
		//	ignorePermissionCheck := systemx.IsContainKeyword(configure.WhiteUrl.PermissionUrl, ctx.Request.RequestURI)
		//	// fmt.Printf("[middleware.permission] current request uri: %s\n", ctx.Request.RequestURI)
		//	// fmt.Printf("[middleware.permission] can ignore permission check: %v\n", ignorePermissionCheck)
		//	// fmt.Printf("[middleware.permission] white request uri list: %v\n", configure.WhiteUrl.PermissionUrl)
		//	if !ignorePermissionCheck {
		//		userInfo, err := token.GetUserInfo(ctx, configure, redisclient)
		//		if err != nil {
		//			if err.Error() == "token expired" {
		//				data := map[string]interface{}{
		//					"status":    false,
		//					"code":      http.StatusUnauthorized,
		//					"message":   "[middleware.permission] permission token invalid",
		//					"is_expire": true,
		//				}
		//				ctx.JSON(http.StatusUnauthorized, data)
		//				ctx.Abort()
		//				return
		//			}
		//			if value, ok := ctx.GetQuery(constant.HTTPAccessToken); ok {
		//				if value == "" {
		//					ctx.AbortWithError(http.StatusUnauthorized, errors.New("[middleware.permission] missing Token request path information"))
		//					return
		//				}
		//				userInfo, err = token.Parse(configure, value)
		//				if userInfo == nil || err != nil {
		//					if ve, ok := err.(*jwt.ValidationError); ok {
		//						if ve.Errors == jwt.ValidationErrorExpired {
		//							data := map[string]interface{}{
		//								"status":    false,
		//								"code":      http.StatusUnauthorized,
		//								"message":   "[middleware.permission] permission token invalid",
		//								"is_expire": true,
		//							}
		//							ctx.JSON(http.StatusUnauthorized, data)
		//							ctx.Abort()
		//							return
		//						}
		//					}
		//					ctx.AbortWithError(http.StatusUnauthorized, errors.New("[middleware.permission] permission token invalid"))
		//					return
		//				}
		//
		//			} else {
		//				ctx.AbortWithError(http.StatusUnauthorized, err)
		//				return
		//			}
		//		}
		//
		//		// log.Printf("%+v====%+v====%+v\n", userInfo.Username, ctx.Request.URL.Path, ctx.Request.Method)
		//		if !permission.CheckPermission(userInfo.Username, ctx.Request.URL.Path, ctx.Request.Method) {
		//			ctx.AbortWithError(http.StatusForbidden, errors.New("没有访问权限"))
		//			return
		//		}
		//	}
		//}
		ctx.Next()
	}
}
