package middleware

import (
	"net/http"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_trace"
	"git.gobies.org/foeye/foeye3/database/db_user"
	"git.gobies.org/foeye/foeye3/model/m_trace"
	"git.gobies.org/foeye/foeye3/packages/secure/token"
	"git.gobies.org/foeye/foeye3/packages/tracex"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
)

// Trace solution middleware to solve trace record resource sharing problems.
func Trace(configure *config.Configure, redisclient *redis.Client, gormDatabase *database.GormDatabase) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Next()
		data, exists := ctx.Get(tracex.ContextKey)
		if exists {
			trace := db_trace.NewGormTraceDatabase(gormDatabase)
			user := db_user.NewGormUserDatabase(configure, gormDatabase)
			userInfo, err := token.GetUserInfo(ctx, configure, redisclient)
			if err == nil && userInfo != nil {
				userTmp, err := user.GetUserByID(userInfo.ID)
				if err == nil && userTmp != nil {
					for _, logTmp := range data.([]*m_trace.Trace) {
						logTmp.Username = userTmp.Username
						logTmp.RoleName = userTmp.RoleName
						logTmp.UserID = int(userTmp.ID)
						logTmp.UserEmail = userTmp.Email
						logTmp.Result = ctx.Writer.Status() == http.StatusOK
						trace.CreateTrace(logTmp)
					}
					return
				}
			}
			for _, logTmp := range data.([]*m_trace.Trace) {
				logTmp.Result = ctx.Writer.Status() == http.StatusOK
				trace.CreateTrace(logTmp)
			}
			return
		}
	}
}
