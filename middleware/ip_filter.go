package middleware

import (
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/model/m_ip_filter"
	"git.gobies.org/foeye/foeye3/model/m_task"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"github.com/gin-gonic/gin"
	"net"
	"net/http"
)

// IpFilter 黑白名单过滤
func IpFilter(db database.Factory) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取系统 IP 过滤配置
		systemInfo, err := db.System().GetSystemValueByKey("ip_filter", "ip_filter")
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    http.StatusForbidden,
				"message": statusx.SystemIpFilterOfMessage,
				"error":   err,
			})
			c.Abort()
			return
		}

		// 如果没有开启限制，直接放行
		if systemInfo.Value == "none" {
			c.Next()
			return
		}

		// 获取 IP 列表
		ipInfos, err := db.IpFilter().GetAllIp(systemInfo.Value)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    http.StatusForbidden,
				"message": statusx.SystemIpFilterOfMessage,
				"error":   err,
			})
			c.Abort()
			return
		}

		// 获取用户 IP
		userIp, err := m_task.GetIP(c.Request)
		if err != nil {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    http.StatusForbidden,
				"message": statusx.SystemIpFilterOfMessage,
				"error":   err,
			})
			c.Abort()
			return
		}

		// 检查用户 IP 是否符合过滤规则
		isAllowed := checkIpFilter(systemInfo.Value, userIp, ipInfos)
		if isAllowed {
			c.Next()
			return
		}

		// IP 不符合过滤规则
		c.JSON(http.StatusForbidden, gin.H{
			"code":    http.StatusForbidden,
			"message": statusx.SystemIpFilterOfMessage,
			"error":   nil,
		})
		c.Abort()
		return
	}
}

// 根据过滤规则检查用户 IP 是否允许
func checkIpFilter(filterType, userIp string, ipInfos []m_ip_filter.IpFilter) bool {
	isInFilter := IsIPInFilter(userIp, ipInfos)

	switch filterType {
	case "white": // 白名单：IP 必须在列表中
		return isInFilter
	case "black": // 黑名单：IP 不能在列表中
		return !isInFilter
	default:
		return false // 其他类型默认为禁止
	}
}

// IsIPInFilter 是否在黑白名单中
func IsIPInFilter(userIP string, ipList []m_ip_filter.IpFilter) bool {
	parsedUserIP := net.ParseIP(userIP)
	if parsedUserIP == nil {
		return false
	}

	for _, ipData := range ipList {
		if _, ipNet, err := net.ParseCIDR(ipData.Ip); err == nil {
			if ipNet.Contains(parsedUserIP) {
				return true
			}
		} else {
			parsedIP := net.ParseIP(ipData.Ip)
			if parsedIP != nil && parsedIP.Equal(parsedUserIP) {
				return true
			}
		}
	}

	return false
}
