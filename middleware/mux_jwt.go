package middleware

import (
	"net/http"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/internal/mux/model/m_common"
	"git.gobies.org/foeye/foeye3/internal/mux/packages/exchange/e_common"
	"git.gobies.org/foeye/foeye3/model/m_user"

	"github.com/gin-gonic/gin"
)

// Cross-domain secure solution MuxJWT middleware
func MuxJWT(db *database.GormDatabase) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		params := new(e_common.BaseQuery)
		data := new(m_common.UnauthorizedFail)
		err := ctx.ShouldBindQuery(params)
		if err != nil ||
			params == nil ||
			params.Email == "" ||
			params.Key == "" {
			data.UnauthorizedError = true
			data.ErrMsg = "401 Unauthorized, make sure email and apikey is correct. And only super administrators can use api."
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, data)
			return
		}
		// 判断用户名和key是否匹配,判断该用户是不是管理员
		user := new(m_user.User)
		if err := db.DB.Model(new(m_user.User)).
			Where("(email = ? or username = ?) and `key` = ? and role_name = ?", params.Email, params.Email, params.Key, "administrator").
			First(user).Error; err != nil {
			data.UnauthorizedError = true
			data.ErrMsg = "401 Unauthorized, make sure email and apikey is correct. And only super administrators can use api."
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, data)
			return
		}
		ctx.Next()
	}
}
