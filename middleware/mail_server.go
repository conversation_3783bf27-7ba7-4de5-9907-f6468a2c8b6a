package middleware

import (
	"errors"
	"net/http"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/database/db_mail_server"
	"github.com/gin-gonic/gin"
)

func MailServer(gormDatabase *database.GormDatabase) gin.HandlerFunc {
	return func(c *gin.Context) {
		g := &db_mail_server.GormEmailServerDatabase{Instance: gormDatabase}
		items, err := g.CountItems()
		if err != nil {
			c.AbortWithStatus(http.StatusInternalServerError)
			return
		}

		if items == 0 {
			c.Error(errors.New("还未设置发送邮件服务器"))
			c.AbortWithStatus(http.StatusBadRequest)
			return
		}
		c.Next()
	}
}
