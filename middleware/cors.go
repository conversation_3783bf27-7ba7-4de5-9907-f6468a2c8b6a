package middleware

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/util"

	"github.com/gin-gonic/gin"
)

// HTTP headers.
var headerSli = []string{
	"Content-Type",
	"Content-Length",
	"Accept",
	"Accept-Encoding",
	"Accept-Language",
	"Referer",
	"Connection",
	"X-Access-Token",
	"X-Sign",
	"Authorization",
	"Origin",
	"Cache-Control",
	"X-Requested-With",
	"X-Check-Result",
	"Content-Disposition",
	"Host",
}

// HTTP methods.
var methodSli = []string{
	http.MethodGet,
	http.MethodHead,
	http.MethodPost,
	http.MethodPut,
	http.MethodPatch,
	http.MethodDelete,
	http.MethodConnect,
	http.MethodOptions,
	http.MethodTrace,
}

// CORS solution middleware to solve cross-domain resource sharing problems.
func CORS(configure *config.Configure) gin.HandlerFunc {
	headers := strings.Join(headerSli, constant.SeparatorOfComma)
	methods := strings.Join(methodSli, constant.SeparatorOfComma)
	return func(c *gin.Context) {
		host := c.Request.Host
		url := util.RequestFullUrl(c.Request)
		origin := configure.Cors.AccessControlAllowOrigin

		// 如果不是通配符，则验证当前host是否已在允许跨域的列表中
		if origin != constant.SeparatorOfStar {
			if !checkHostWhetherInOrigins(origin, host) {
				c.AbortWithError(http.StatusForbidden, errors.New(
					fmt.Sprintf("Failed to load %s: "+
						"Redirect from '%s' to '%s' has been blocked by CORS policy: "+
						"No 'Access-Control-Allow-Origin' header is present on the requested resource. "+
						"Origin '%s' is therefore not allowed access.", url, url, url, host)))
				return
			}
		}

		c.Writer.Header().Set("Access-Control-Allow-Origin", configure.Cors.AccessControlAllowOrigin)
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", headers)
		c.Writer.Header().Set("Access-Control-Allow-Methods", methods)
		c.Writer.Header().Set("Access-Control-Expose-Headers", headers)
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		c.Next()
	}
}

// checkHostWhetherInOrigins 检测host地址是否在被允许跨域的列表中
func checkHostWhetherInOrigins(origin, host string) bool {
	sli := strings.Split(origin, ",")
	for index, item := range sli {
		sli[index] = strings.TrimSpace(item)
	}

	return util.InStrSlice(sli, host)
}
