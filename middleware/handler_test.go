package middleware

import (
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"git.gobies.org/foeye/foeye3/model/m_common"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func (suite *MiddlewareSuite) TestDefaultInternalError() {
	// mode.Set(config.GetProjectPath(), mode.Test, false)
	suite.ctx.AbortWithError(http.StatusInternalServerError, errors.New("server has error"))
	Handler()(suite.ctx)
	r := response(suite.ctx, false, http.StatusInternalServerError, "server has error")
	assertResponseJSON(suite.T(), suite.rec, http.StatusInternalServerError, r)
}

func (suite *MiddlewareSuite) TestDefaultBadRequestError() {
	// mode.Set(config.GetProjectPath(), mode.Test, false)
	suite.ctx.AbortWithError(http.StatusBadRequest, errors.New("you request has error, please check params"))
	Handler()(suite.ctx)
	r := response(suite.ctx, false, http.StatusBadRequest, "you request has error, please check params")
	assertResponseJSON(suite.T(), suite.rec, http.StatusBadRequest, r)
}

func (suite *MiddlewareSuite) TestDefaultBindError() {
	suite.ctx.AbortWithError(http.StatusBadRequest, errors.New("you request has error, please check params")).SetType(gin.ErrorTypeBind)
	Handler()(suite.ctx)
	r := response(suite.ctx, false, http.StatusBadRequest, "you request has error, please check params")
	assertResponseJSON(suite.T(), suite.rec, http.StatusBadRequest, r)
}

type validate struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"email"`
	Age      int    `json:"age" binding:"max=100"`
	Limit    int    `json:"limit" binding:"min=20"`
	Size     int    `json:"size" binding:"min=1,max=10"`
}

func assertResponseJSON(t *testing.T, rr *httptest.ResponseRecorder, code int, actual string) {
	data, _ := ioutil.ReadAll(rr.Body)
	assert.Equal(t, code, rr.Code)
	assert.JSONEq(t, string(data), actual)
}

func response(ctx *gin.Context, status bool, code int, message string) string {
	// Calc current status code.
	code = calcCurrentStatusCode(ctx, code)

	marshal, _ := json.Marshal(&m_common.Error{
		Status:  status,
		Code:    code,
		Message: message,
	})
	return string(marshal)
}
