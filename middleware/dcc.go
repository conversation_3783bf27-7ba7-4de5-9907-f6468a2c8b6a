package middleware

import (
	"net/http"

	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/packages/statusx"
	"git.gobies.org/foeye/foeye3/responses/r_common"

	"github.com/gin-gonic/gin"
)

// dcc middleware
func DccCheck(db database.Factory) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 集中管控模式不能直接下发任务
		centralized, err := db.System().IsControlSetManagementPlatform()
		if centralized {
			r_common.ErrorWithAbort(ctx, http.StatusBadRequest, statusx.TaskStatusFailedWithCentralizedForbiddenOperation, err)

			return
		}

		ctx.Next()
	}
}
