package redirect

import (
	"fmt"
	"net/http"
	"sync"

	"github.com/go-redis/redis"

	"git.gobies.org/foeye-dependencies/middleware/redirect"
)

// Redirect alias.
type Redirect = *redirect.Redirect

var inst Redirect
var once sync.Once

func GetRedirectOr(client *redis.Client) (Redirect, error) {
	once.Do(func() {
		emits := redirect.Emits{
			".keep.in.upgrade": &redirect.TriggerEvent{
				StatusCode:            http.StatusFound,
				RedirectURI:           "/v3/systems/upgrade_upload",
				RedirectHeaderMessage: "系统升级中...",
			},
			".keep.in.backup": &redirect.TriggerEvent{
				StatusCode:            http.StatusFound,
				RedirectURI:           "/v3/backups/backup_progress",
				RedirectHeaderMessage: "系统数据备份中...",
			},
			".keep.in.recover": &redirect.TriggerEvent{
				StatusCode:            http.StatusFound,
				RedirectURI:           "/v3/backups/restore_progress",
				RedirectHeaderMessage: "系统数据恢复中...",
			},
			".keep.in.download": &redirect.TriggerEvent{
				StatusCode:            http.StatusFound,
				RedirectURI:           "/v3/systems/download_progress",
				RedirectHeaderMessage: "系统数据下载中...",
			},
		}

		inst = redirect.NewRedirect(
			emits,
			redirect.WatcherOfRedis,
			redirect.WithWatcherOfRedisClient(client),
			redirect.WithRedirectMessageHeader("X-Redirect-Message"),
			redirect.WithSetIgnoreURI("/v3/user_auth/info", true),
			redirect.WithSetIgnoreURI("/v3/user_auth/login", true),
			redirect.WithSetIgnoreURI("/v3/captcha", true),
			redirect.WithSetIgnoreURI("/v3/systems/expiration_times", true),
			redirect.WithSetIgnoreURI("/v3/config", true),
		)
	})

	if inst == nil {
		return nil, fmt.Errorf("initialize redirect instance to failed")
	}

	return inst, nil
}
