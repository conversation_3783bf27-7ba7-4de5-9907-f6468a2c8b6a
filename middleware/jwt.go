package middleware

import (
	"bytes"
	"errors"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/cryptox"
	"git.gobies.org/foeye/foeye3/packages/secure/token"
)

// JWT  secure solution JWT middleware
func JWT(configure *config.Configure, redisclient *redis.Client) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		//if token.IsApiToken(ctx) {
		//	if err := token.CheckFixedUserAuthentication(ctx); err != nil {
		//		logger.Errorw("JWT", "CheckFixedUserAuthentication has error:", err)
		//		data := map[string]interface{}{
		//			"code":    http.StatusUnauthorized,
		//			"message": err.Error(),
		//		}
		//		ctx.JSON(http.StatusUnauthorized, data)
		//		ctx.Abort()
		//		return
		//	}
		//} else {
		//	if _, err := token.CheckUserAuthentication(ctx, configure, redisclient); err != nil {
		//		logger.Errorw("JWT", "CheckUserAuthentication has error:", err)
		//		if err.Error() == "token expired" {
		//			data := map[string]interface{}{
		//				"code":      http.StatusUnauthorized,
		//				"message":   err.Error(),
		//				"is_expire": true,
		//			}
		//			ctx.JSON(http.StatusUnauthorized, data)
		//			ctx.Abort()
		//			return
		//		}
		//		ctx.AbortWithError(http.StatusUnauthorized, err)
		//		return
		//	}
		//}

		ctx.Next()
	}
}

// JWTToken secure solution JWTToken middleware, swagger、file path use it
func JWTToken(configure *config.Configure, redisclient *redis.Client) gin.HandlerFunc {
	return func(ctx *gin.Context) {

		if configure.Server.IsDebug() {
			tk, _ := ctx.GetQuery("Token")
			log.Debugf("app mode: %s tk: %s", configure.Server.Mode, tk)
			if tk == "foeye-debug" {
				ctx.Next()
				return
			}
		}

		if _, err := token.CheckUserAuthenticationByPathToken(ctx, configure, redisclient); err != nil {
			ctx.AbortWithError(http.StatusUnauthorized, err)
			return
		}
		ctx.Next()
	}
}

// JWTSignal JWT middleware for product inspection, 产品巡检,下载资产特征等使用。
func JWTSignal(configure *config.Configure, redisclient *redis.Client) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if _, err := token.CheckUserAuthentication(ctx, configure, redisclient); err != nil {
			ctx.AbortWithError(http.StatusUnauthorized, err)
			return
		}
		if signal, exists := ctx.GetQuery("signal"); exists {
			key, err := cryptox.DePwdCode(signal)
			if err != nil {
				log.Println(err)
				ctx.AbortWithError(http.StatusBadRequest, errors.New(fmt.Sprintf("DePwdCode error %s", err)))
				return
			}
			resKey := bytes.Split(key, []byte("+"))
			if len(resKey) != 2 {
				ctx.AbortWithError(http.StatusBadRequest, errors.New("signal is error"))
				return
			}
			if string(resKey[0]) != cryptox.KeyMd5 {
				ctx.AbortWithError(http.StatusBadRequest, errors.New("check signal error"))
				return
			}
		} else {
			ctx.AbortWithError(http.StatusBadRequest, errors.New("no signal"))
			return
		}
		ctx.Next()
	}
}
