package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/packages/license"
)

// License solution middleware to solve license sharing problems.
func Licenses(configure *config.Configure, redisclient *redis.Client, modules ...license.ModuleName) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// debug
		//if configure.Server.IsCloseLicense {
		//	ctx.Next()
		//	return
		//}

		//// 验证license权限
		//ok := license.CheckModules(configure, redisclient, modules...)
		//logger.Infow("[middleware] Licenses", "moudles", modules, "ok", ok)
		//if !ok {
		//	r_common.Error(ctx, statusx.NewErr(http.StatusForbidden, statusx.ErrModuleNoPermission))
		//	return
		//}

		ctx.Next()
	}
}
