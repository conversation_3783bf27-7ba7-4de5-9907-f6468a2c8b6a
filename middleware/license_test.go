package middleware

import (
	"github.com/stretchr/testify/assert"
)

func (suite *MiddlewareSuite) TestExcluded() {
	expected := []map[string]interface{}{
		{"path": "/v3/microkernels/start_proc", "expect": true},
		{"path": "/v3/systems/aaaaa", "expect": false},
	}
	for _, v := range expected {
		path := v["path"].(string)
		expect := v["expect"].(bool)

		assert.Equal(suite.T(), expect, Excluded(path))
	}
}
