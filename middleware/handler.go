package middleware

import (
	"net/http"
	"reflect"
	"strings"
	"time"

	"git.gobies.org/foeye/foeye3/model/m_common"
	"git.gobies.org/foeye/foeye3/packages/constant"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	translations "github.com/go-playground/validator/translations/zh"
	"gopkg.in/go-playground/validator.v9"
)

// Translator instance.
var trans ut.Translator

// 翻译自定义字典，这些数据是用到验证规则字段相互引用的时候。
// type Booking struct {
//		CheckIn  time.Time `form:"check_in" json:"check_in" binding:"required" time_format:"2006-01-02" label:"输入时间"`
//		CheckOut time.Time `form:"check_out" json:"check_out" binding:"required,gtfield=CheckIn" time_format:"2006-01-02" label:"输出时间"`
// }
var dictionaries = map[string]string{
	"BeginTime": "开始时间",
	"EndTime":   "结束时间",
	"CheckIn":   "输入时间",
	"CheckOut":  "输出时间",
}

// translateTagName translate the structure tag.
func translateTagName(dictionaries, err interface{}) interface{} {
	switch err.(type) {
	case validator.ValidationErrorsTranslations:
		var errs map[string]string
		errs = make(map[string]string, 0)
		for k, v := range err.(validator.ValidationErrorsTranslations) {
			for key, value := range dictionaries.(map[string]string) {
				v = strings.Replace(v, key, value, -1)
			}
			errs[k] = v
		}
		return errs
	case string:
		var errs = err.(string)
		for key, value := range dictionaries.(map[string]string) {
			errs = strings.Replace(errs, key, value, -1)
		}
		return errs
	default:
		return err
	}
}

// hasReferenceTagName 检测是否存在结构体数据绑定验证规则字段相互引用。
func hasReferenceTagName(label string) bool {
	for _, labelName := range dictionaries {
		if labelName == label {
			return true
		}
	}
	return false
}

// bookableDate 自定义验证函数
var bookableDate validator.Func = func(fl validator.FieldLevel) bool {
	date, ok := fl.Field().Interface().(time.Time)
	if ok {
		today := time.Now()
		if today.After(date) {
			return false
		}
	}
	return true
}

func init() {
	uni := ut.New(zh.New())
	trans, _ = uni.GetTranslator("zh")
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		_ = translations.RegisterDefaultTranslations(v, trans)
		v.RegisterTagNameFunc(func(field reflect.StructField) string {
			return field.Tag.Get("label")
		})

		_ = v.RegisterValidation("bookabledate", bookableDate)
		_ = v.RegisterTranslation("bookabledate", trans, func(ut ut.Translator) error {
			return ut.Add("bookabledate", "{0}不能早于当前时间或{1}格式错误!", true)
		}, func(ut ut.Translator, fe validator.FieldError) string {
			t, _ := ut.T("bookabledate", fe.Field(), fe.Field())
			return t
		})
	}
}

// Handler handling errors for gin.
func Handler() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Next()
		if len(ctx.Errors) > 0 {
			for _, e := range ctx.Errors {
				// Existence structure binding validation error.
				if errs, ok := e.Err.(validator.ValidationErrors); ok {
					for _, e := range errs {
						if hasReferenceTagName(e.Field()) {
							writeResponseError(ctx, translateTagName(dictionaries, e.Translate(trans)).(string))
							return
						}
						writeResponseError(ctx, e.Translate(trans))
						return
					}
				}

				// Other errors are output directly.
				writeResponseError(ctx, e.Err.Error())
				return
			}
		}
	}
}

// writeResponseError 写入错误响应信息
func writeResponseError(ctx *gin.Context, message string) {
	code := http.StatusBadRequest
	if cs := ctx.Writer.Status(); cs != http.StatusOK {
		code = cs
	}

	ctx.JSON(code, &m_common.Error{
		Status:  false,
		Code:    calcCurrentStatusCode(ctx, code),
		Message: message,
	})
	ctx.Abort()
}

// calcCurrentStatusCode 计算自定义状态码
func calcCurrentStatusCode(ctx *gin.Context, code int) int {
	cc, exists := ctx.Get(constant.CustomStatusCode)
	if exists {
		return cc.(int)
	}
	return code
}
