package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"git.gobies.org/foeye-dependencies/configure"

	"git.gobies.org/foeye/foeye3/config"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type MiddlewareSuite struct {
	suite.Suite
	ctx *gin.Context
	rec *httptest.ResponseRecorder

	configure *config.Configure
}

func (suite *MiddlewareSuite) BeforeTest(suiteName, testName string) {
	suite.configure = config.GetConfigure(
		configure.WithSpecificConfigure(&config.Configure{}),
		configure.WithSpecificConfigName(config.ConfigureFilenameOfTest.String()),
		configure.WithSpecificConfigPath("./../"),
	)
	assert.NotNil(suite.T(), suite.configure)

	suite.rec = httptest.NewRecorder()
	suite.ctx, _ = gin.CreateTestContext(suite.rec)
}

func (suite *MiddlewareSuite) AfterTest(suiteName, testName string) {
}

func TestMiddlewareSuite(t *testing.T) {
	suite.Run(t, new(MiddlewareSuite))
}

func (suite *MiddlewareSuite) Test_ProductCORS_Invalid() {
	t := suite.T()
	suite.ctx.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	suite.ctx.Request.Host = "www.lide-example.com"
	CORS(suite.configure)(suite.ctx)
	assert.Equal(t, http.StatusForbidden, suite.rec.Code)
}

func (suite *MiddlewareSuite) Test_ProductCORS_valid() {
	t := suite.T()
	suite.ctx.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	suite.ctx.Request.Host = "http://0.0.0.0:8000"
	CORS(suite.configure)(suite.ctx)

	expected := suite.configure.Cors.AccessControlAllowOrigin
	assert.Equal(t, expected, suite.rec.Header().Get("Access-Control-Allow-Origin"))
	assert.Equal(t, http.StatusOK, suite.rec.Code)
}

func (suite *MiddlewareSuite) Test_TestCORS_invalid() {
	t := suite.T()
	suite.ctx.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	suite.ctx.Request.Host = "www.lide-example.com"
	CORS(suite.configure)(suite.ctx)
	assert.Equal(t, http.StatusForbidden, suite.rec.Code)
}

func (suite *MiddlewareSuite) Test_TestCORS() {
	t := suite.T()
	suite.ctx.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	suite.ctx.Request.Host = "127.0.0.1:3000"
	CORS(suite.configure)(suite.ctx)
	assert.Equal(t, http.StatusOK, suite.rec.Code)
}

func (suite *MiddlewareSuite) Test_DevelopCORS_invalid() {
	t := suite.T()
	suite.ctx.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	suite.ctx.Request.Host = "http://lide-example.com"
	CORS(suite.configure)(suite.ctx)
	assert.Equal(t, http.StatusForbidden, suite.rec.Code)
}

func (suite *MiddlewareSuite) Test_DevelopCORS_valid() {
	t := suite.T()
	suite.ctx.Request = httptest.NewRequest(http.MethodGet, "/", nil)
	suite.ctx.Request.Host = "https://lide-cms-develop.com"
	CORS(suite.configure)(suite.ctx)
	assert.Equal(t, http.StatusForbidden, suite.rec.Code)
}

func (suite *MiddlewareSuite) TestCheckHostWhetherInOrigins() {
	origin := "localhost:8000    ,   ************:8000 ,   http://************:8000 ,   http://foeye-develop.com"

	grids := []struct {
		host     string
		expected bool
	}{
		{
			host:     "************:8000",
			expected: true,
		},
		{
			host:     "http://************:8000",
			expected: true,
		},
		{
			host:     "http://************:8000",
			expected: false,
		},
	}

	for _, grid := range grids {
		actual := checkHostWhetherInOrigins(origin, grid.host)
		assert.Equal(suite.T(), grid.expected, actual)
	}
}
