package service

import (
	"bufio"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"sort"
	"strings"
	"sync"
	"unicode"
	"unicode/utf8"

	"git.gobies.org/foeye-dependencies/logger"

	"git.gobies.org/foeye/foeye3/packages/systemx"

	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/model/m_poc_weak_password"
	"git.gobies.org/foeye/foeye3/model/m_system"
	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/fsx"
)

// PocWeakPasswordSrv service interface structure.
type PocWeakPasswordSrv interface {
	ConvertInstanceToSoloList(instance interface{}) []*m_poc_weak_password.PocUserPasswords
	ConvertInstanceToUniList(instance interface{}) []*m_poc_weak_password.PocUniUsernames
	UploadFileDataConvertToDatabase(store database.GormPocWeakPasswordDatabaseStore, filePath string) (interface{}, interface{}, error)
	InstanceConvertToDatabase(store database.GormPocWeakPasswordDatabaseStore, instance interface{}, baseDir string, list interface{}) error
	ConvertInstanceToStringList(instance interface{}) (interface{}, error)
	WritePocWeakFile(filename, password string) error
	SwitchModeToWriteFile(store database.GormPocWeakPasswordDatabaseStore, mode string) error
	UpdateWeakPwdEntryFiles(store database.GormPocWeakPasswordDatabaseStore, state uint, list interface{}) error
}

type pocWeakPasswordSrv struct {
	configure *config.Configure
}

func newPocWeakPasswordSrv(configure *config.Configure) *pocWeakPasswordSrv {
	return &pocWeakPasswordSrv{configure: configure}
}

// ConvertInstanceToSoloList instance convert to solo List
func (s *pocWeakPasswordSrv) ConvertInstanceToSoloList(instance interface{}) []*m_poc_weak_password.PocUserPasswords {
	if body, ok := instance.(*m_poc_weak_password.PocWeakPasswordSoloUpdateAddition); ok {
		ups := make([]*m_poc_weak_password.PocUserPasswords, 0)

		if strings.ContainsAny(body.UserPassword, "\n") {
			list := strings.Split(body.UserPassword, "\n")
			if len(list) > 1 {
				for _, v := range m_poc_weak_password.RemoveDuplication(list) {
					if strings.Contains(v, ":") {
						temp := strings.Split(v, ":")
						if len(temp) == 1 {
							ups = append(ups, &m_poc_weak_password.PocUserPasswords{
								Filename: body.FileName,
								Username: temp[0],
							})
						} else if len(temp) == 2 {
							ups = append(ups, &m_poc_weak_password.PocUserPasswords{
								Filename: body.FileName,
								Username: temp[0],
								Password: temp[1],
							})
						}
					} else {
						ups = append(ups, &m_poc_weak_password.PocUserPasswords{
							Filename: body.FileName,
							Username: v,
						})
					}
				}
			}
		} else {
			if strings.Contains(body.UserPassword, ":") {
				temp := strings.Split(body.UserPassword, ":")
				if len(temp) == 1 {
					ups = append(ups, &m_poc_weak_password.PocUserPasswords{
						Filename: body.FileName,
						Username: temp[0],
					})
				} else if len(temp) == 2 {
					ups = append(ups, &m_poc_weak_password.PocUserPasswords{
						Filename: body.FileName,
						Username: temp[0],
						Password: temp[1],
					})
				}
			} else {
				ups = append(ups, &m_poc_weak_password.PocUserPasswords{
					Filename: body.FileName,
					Username: body.UserPassword,
				})
			}
		}
		return ups
	}
	return nil
}

// ConvertInstanceToUniList instance convert to uni List
func (s *pocWeakPasswordSrv) ConvertInstanceToUniList(instance interface{}) []*m_poc_weak_password.PocUniUsernames {
	if body, ok := instance.(*m_poc_weak_password.PocWeakPasswordUniUpdateAddition); ok {
		unis := make([]*m_poc_weak_password.PocUniUsernames, 0)

		if strings.ContainsAny(body.UserName, "\n") {
			list := strings.Split(body.UserName, "\n")
			if len(list) > 1 {
				for _, v := range m_poc_weak_password.RemoveDuplication(list) {
					if v != "" {
						unis = append(unis, &m_poc_weak_password.PocUniUsernames{
							Filename: body.FileName,
							Username: v,
						})
					}
				}
			}
		} else {
			unis = append(unis, &m_poc_weak_password.PocUniUsernames{
				Filename: body.FileName,
				Username: body.UserName,
			})
		}
		return unis
	}
	return nil
}

// UploadFileDataConvertToDatabase upload file data  convert to uni database
func (s *pocWeakPasswordSrv) UploadFileDataConvertToDatabase(store database.GormPocWeakPasswordDatabaseStore, filePath string) (
	interface{}, interface{}, error) {
	buf, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, nil, err
	}

	lines := strings.Split(string(buf), "\n")
	fileContent := make([]string, 0)
	for i, v := range lines {
		if len(v) > 0 {
			if utf8.RuneCountInString(v) > 50 {
				return nil, nil, fmt.Errorf("第 %d 行的密码过长(超过最大长度50字符)", i)
			}

			for _, c := range v {
				if unicode.Is(unicode.Han, c) {
					return nil, nil, errors.New("上传密码不能包含中文")
				}
			}

			fileContent = append(fileContent, string(v))
		}
	}

	items := make([]*m_poc_weak_password.WeakPwdEntries, 0)
	ids := make([]uint, 0)
	tempList := m_poc_weak_password.RemoveDuplication(fileContent)
	if len(tempList) < 1000 {
		items, ids, err = s.checkPassWordLimit(tempList, store)
		if err != nil {
			return nil, nil, err
		}
	} else {
		items, ids, err = s.checkPassWordAll(tempList, store)
		if err != nil {
			return nil, nil, err
		}
	}

	sort.SliceStable(items, func(i, j int) bool {
		return items[i].Password < items[j].Password
	})

	trace := make(map[string]int)
	trace["export_count"] = len(fileContent)
	trace["repeat_count"] = len(fileContent) - len(tempList)
	trace["exists_count"] = len(ids)

	return trace, lines, nil
}

// checkPassWordAll 验证所有密码
func (s *pocWeakPasswordSrv) checkPassWordAll(tempList []string, store database.GormPocWeakPasswordDatabaseStore) (
	[]*m_poc_weak_password.WeakPwdEntries, []uint, error) {
	items := make([]*m_poc_weak_password.WeakPwdEntries, 0)
	ids := make([]uint, 0)
	passWordList, err := store.GetItemByPasswordAll()
	if err != nil {
		return nil, nil, err
	}
	for _, v := range tempList {
		var weakPwdEntriesId uint
		for _, entries := range passWordList {
			if strings.TrimSpace(v) == entries.Password {
				weakPwdEntriesId = entries.ID
				break
			}
		}

		if weakPwdEntriesId > 0 {
			ids = append(ids, weakPwdEntriesId)
		} else {
			items = append(items, &m_poc_weak_password.WeakPwdEntries{
				Password: strings.TrimSpace(v),
				AddWay:   m_poc_weak_password.WeakPwdEntriesAddWay,
				State:    m_poc_weak_password.WeakPwdEntriesDefaultState,
			})
		}
	}
	return items, ids, nil
}

// checkPassWordAll 验证有条数
func (s *pocWeakPasswordSrv) checkPassWordLimit(tempList []string, store database.GormPocWeakPasswordDatabaseStore) (
	[]*m_poc_weak_password.WeakPwdEntries, []uint, error) {
	items := make([]*m_poc_weak_password.WeakPwdEntries, 0)
	ids := make([]uint, 0)
	for _, v := range tempList {
		item, err := store.GetItemByPassword(strings.TrimSpace(v))
		if err != nil {
			return nil, nil, err
		}
		if item == nil {
			items = append(items, &m_poc_weak_password.WeakPwdEntries{
				Password: strings.TrimSpace(v),
				AddWay:   m_poc_weak_password.WeakPwdEntriesAddWay,
				State:    m_poc_weak_password.WeakPwdEntriesDefaultState,
			})
		} else {
			ids = append(ids, item.ID)
		}
	}
	return items, ids, nil
}

// InstanceConvertToDatabase instance convert to database
func (s *pocWeakPasswordSrv) InstanceConvertToDatabase(store database.GormPocWeakPasswordDatabaseStore, instance interface{}, baseDir string, list interface{}) error {

	has, err := store.GetItemList()
	if err != nil {
		return err
	}

	datums, ok := has.([]*m_poc_weak_password.PocUserPasswords)
	if !ok {
		return err
	}

	if len(datums) < 1 {
		return err
	}

	temp := make(map[string]string)
	for _, v := range datums {
		temp[v.Filename] = ""
	}

	for _, v := range datums {
		if _, ok = temp[v.Filename]; ok {
			temp[v.Filename] += v.Username + ","
		}
	}

	fileData, ok := instance.([]string)
	if !ok {
		return err
	}

	d := fsx.NewTempDir("poc_weak_password")

	// TODO 此处应该改成用Golang去实现 后期调整
	cmd := fmt.Sprintf("%s %s/* %s", "mv", d.Path(), s.configure.Goscanner.DictPath)
	res, err := systemx.ExecuteSystemCommand(cmd)
	log.Infof("res,%v", res)
	if err != nil {
		return err
	}

	if err = d.Clean(); err != nil {
		return err
	}

	items := make([]*m_poc_weak_password.WeakPwdEntries, 0)
	ids := make([]uint, 0)
	tempList := m_poc_weak_password.RemoveDuplication(fileData)
	if len(tempList) < 1000 {
		items, ids, err = s.checkPassWordLimit(tempList, store)
		if err != nil {
			return err
		}
	} else {
		items, ids, err = s.checkPassWordAll(tempList, store)
		if err != nil {
			return err
		}
	}

	sort.SliceStable(items, func(i, j int) bool {
		return items[i].Password < items[j].Password
	})

	if err = store.CreateItemForUploadInstances(items, ids); err != nil {
		return err
	}
	return nil
}

// ConvertInstanceToStringList convert instance to string list
func (s *pocWeakPasswordSrv) ConvertInstanceToStringList(instance interface{}) (interface{}, error) {
	if generate, ok := instance.(*m_poc_weak_password.PocWeakPasswordGenerateAddition); ok {
		prefixInfos := make([]string, 0)
		if strings.Contains(generate.PrefixInfo, "\n") {
			prefixInfos = append(prefixInfos, strings.Split(generate.PrefixInfo, "\n")...)
		} else {
			prefixInfos = append(prefixInfos, generate.PrefixInfo)
		}
		prefixInfos = m_poc_weak_password.RemoveDuplication(prefixInfos)

		SpecialCharacters := make([]string, 0)
		if strings.Contains(generate.SpecialCharacter, "\n") {
			SpecialCharacters = append(SpecialCharacters, strings.Split(generate.SpecialCharacter, "\n")...)
		} else {
			SpecialCharacters = append(SpecialCharacters, generate.SpecialCharacter)
		}
		SpecialCharacters = m_poc_weak_password.RemoveDuplication(SpecialCharacters)

		SuffixInfos := make([]string, 0)
		if strings.Contains(generate.SuffixInfo, "\n") {
			for _, v := range strings.Split(generate.SuffixInfo, "\n") {
				SuffixInfos = append(SuffixInfos, v)
			}
		} else {
			SuffixInfos = append(SuffixInfos, generate.SuffixInfo)
		}
		SuffixInfos = m_poc_weak_password.RemoveDuplication(SuffixInfos)

		passwords := make([]string, 0)
		for _, product := range PermutationAndCombination(prefixInfos, SpecialCharacters, SuffixInfos) {
			max := 0
			for _, password := range product {

				for _, c := range password {
					if unicode.Is(unicode.Han, c) {
						return nil, errors.New("密码中不能包含中文")
					}
				}

				max += utf8.RuneCountInString(password)
				if max > 50 {
					return nil, fmt.Errorf("密码过长(超过最大长度50字符)")
				}
			}
			if len(product) == 3 {
				passwords = append(passwords, fmt.Sprintf("%s%s%s", product[0], product[1], product[2]))
			}
		}
		return passwords, nil
	}

	return nil, errors.New(constant.RecordItemErrorOfTypeNotMatch)
}

// PermutationAndCombination 排列组合
func PermutationAndCombination(sets ...[]string) [][]string {
	lens := func(i int) int { return len(sets[i]) }
	product := make([][]string, 0)
	for ix := make([]int, len(sets)); ix[0] < lens(0); nextIndex(ix, lens) {
		var r []string
		for j, k := range ix {
			r = append(r, sets[j][k])
		}
		product = append(product, r)
	}

	return product
}

// nextIndex
func nextIndex(ix []int, lens func(i int) int) {
	for j := len(ix) - 1; j >= 0; j-- {
		ix[j]++
		if j == 0 || ix[j] < lens(j) {
			return
		}
		ix[j] = 0
	}
}

// WritePocWeakFile write poc weak password.
func (s *pocWeakPasswordSrv) WritePocWeakFile(filename, password string) error {
	var filePath = s.configure.Goscanner.DictPath + "/" + convertToFilename(filename)
	file, err := os.OpenFile(filePath, os.O_WRONLY|os.O_TRUNC|os.O_CREATE, 0644)
	if err != nil {
		return err
	}

	defer file.Close()
	writer := bufio.NewWriter(file)
	if strings.Contains(password, "/n") {
		for _, v := range strings.Split(password, "/n") {
			if filename == "CVD-2023-2638.json" {
				v = strings.TrimPrefix(v, ":")
			}
			if _, err = writer.WriteString(v); err != nil {
				return err
			}
		}
	} else {
		if filename == "CVD-2023-2638.json" {
			password = strings.ReplaceAll(password, ":", "")
		}
		if _, err = writer.WriteString(password); err != nil {
			return err
		}
	}

	if err = writer.Flush(); err != nil {
		return err
	}
	return nil
}

// convertToFilename convert to filename.
func convertToFilename(str string) (filename string) {
	passMap := map[string]string{
		"CVD-2023-2638.json": "vnc_user_pass.dict",
	}

	if strings.Contains(str, "_") {
		filename = fmt.Sprintf("%s_user_pass.dict", str[:strings.Index(str, "_")])
	} else {
		filename = fmt.Sprintf("%s_user_pass.dict", str)
	}

	if tmp, ok := passMap[str]; ok {
		filename = tmp
	}

	return filename
}

// SwitchModeToWriteFile switch mode writer file.
func (s *pocWeakPasswordSrv) SwitchModeToWriteFile(store database.GormPocWeakPasswordDatabaseStore, mode string) error {
	var wg sync.WaitGroup
	ch := make(chan struct{}, 30)

	if mode == m_system.ModeUni {
		items, err := store.GetItemListBySolo()
		if err != nil {
			return err
		}

		for _, v := range items {
			ch <- struct{}{}
			wg.Add(1)

			go func(v *m_poc_weak_password.PocUserPasswords) {
				defer wg.Done()

				list, err := store.GetUserPasswordSoloList(v.Filename)
				if err != nil {
					log.Println(err.Error())
					return
				}

				var fileData string
				for _, temp := range list.([]*m_poc_weak_password.PocUserPasswords) {
					fileData += fmt.Sprintf("%s:%s\n", temp.Username, temp.Password)
				}

				if fileData == "" {
					log.Println(err.Error())
					return
				}

				// Writes the file content according to the file name.
				if err = s.WritePocWeakFile(v.Filename, fileData); err != nil {
					log.Println(err.Error())
					return
				}
				<-ch
			}(v)
		}
	} else if mode == m_system.ModeSolo {
		// all passwords
		instances, err := store.GetList(0, 0, []interface{}{}...)
		if err != nil {
			return err
		}

		passwords := make([]string, 0)
		for _, v := range instances.([]*m_poc_weak_password.WeakPwdEntries) {
			passwords = append(passwords, v.Password)
		}

		items, err := store.GetItemListByUni()
		if err != nil {
			return err
		}

		for _, v := range items {
			ch <- struct{}{}
			wg.Add(1)

			go func(v *m_poc_weak_password.PocUniUsernames) {
				defer wg.Done()

				usernames, err := store.GetUserPasswordUniList(v.Filename)
				if err != nil {
					log.Println(err.Error())
					return
				}

				var fileData string
				users := make([]string, 0)

				for _, username := range usernames.([]*m_poc_weak_password.PocUniUsernames) {
					users = append(users, username.Username)
				}

				for _, data := range PermutationAndCombination(users, passwords) {
					fileData += fmt.Sprintf("%s:%s\n", data[0], data[1])
				}

				if fileData == "" {
					log.Println(err.Error())
					return
				}

				// Writes the file content according to the file name.
				if err = s.WritePocWeakFile(v.Filename, fileData); err != nil {
					log.Println(err.Error())
					return
				}
				<-ch
			}(v)
		}
	}
	wg.Wait()
	return nil
}

// UpdateWeakPwdEntryFiles 更改/data/fofaee/web/goscanner 下的dict 文件内容
func (s *pocWeakPasswordSrv) UpdateWeakPwdEntryFiles(store database.GormPocWeakPasswordDatabaseStore, state uint, list interface{}) error {
	has, err := store.GetItemList()
	if err != nil {
		return err
	}

	datums, ok := has.([]*m_poc_weak_password.PocUserPasswords)
	if !ok {
		return err
	}

	if len(datums) < 1 {
		return err
	}

	temp := make(map[string]string)
	for _, v := range datums {
		temp[v.Filename] = ""
	}

	for _, v := range datums {
		if _, ok = temp[v.Filename]; ok {
			temp[v.Filename] += v.Username + ","
		}
	}

	for i, v := range temp {
		var readFileData string
		filename := convertToFilename(i)
		value := strings.TrimSuffix(v, ",")
		if strings.Contains(value, ",") {
			tempList := m_poc_weak_password.RemoveDuplication(strings.Split(value, ","))
			// go func() {
			// 启用:1 禁用:0
			if state == 1 {
				// 读取字典格式的文件内容
				buf, err := ioutil.ReadFile(fmt.Sprintf("%s/%s", s.configure.Goscanner.DictPath, filename))
				if err != nil {
					logger.Errorw("[UpdateWeakPwdEntryFiles] ReadFile failed", "err", err)
				}
				readFileData = fmt.Sprintf("%s\n", string(buf))

				for _, v := range list.([]*m_poc_weak_password.WeakPwdEntries) {
					for _, user := range tempList {
						readFileData += fmt.Sprintf("%s:%s\n", user, m_poc_weak_password.InternalToWeakPwdEntriesBase(v).Password)
					}
				}
				if err = ioutil.WriteFile(fmt.Sprintf("%s/%s", s.configure.Goscanner.DictPath, filename), []byte(readFileData), 0644); err != nil {
					logger.Errorw("[UpdateWeakPwdEntryFiles] WriteFile failed", "err", err)
				}
			} else {
				// 读取字典格式的文件内容
				buf, err := ioutil.ReadFile(fmt.Sprintf("%s/%s", s.configure.Goscanner.DictPath, filename))
				if err != nil {
					logger.Errorw("[UpdateWeakPwdEntryFiles] ReadFile failed", "err", err)
				}
				// 启用:1 禁用:0
				weakPwdEntries := make(map[string]bool, 0)
				for _, v := range list.([]*m_poc_weak_password.WeakPwdEntries) {
					for _, user := range tempList {
						weakPwdEntries[fmt.Sprintf("%s:%s", user, m_poc_weak_password.InternalToWeakPwdEntriesBase(v).Password)] = true
					}
				}
				fileData := strings.Split(string(buf), "\n")
				// 删除包含在切片中的字符串
				for _, sv := range fileData {
					if !weakPwdEntries[sv] {
						readFileData += fmt.Sprintf("%s\n", sv)
					}
				}

				if err = ioutil.WriteFile(fmt.Sprintf("%s/%s", s.configure.Goscanner.DictPath, filename), []byte(strings.Trim(readFileData, "\n")), 0644); err != nil {
					logger.Errorw("[UpdateWeakPwdEntryFiles] WriteFile failed", "err", err)
				}
			}
			// }()
		}
	}
	return nil
}
