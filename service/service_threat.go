package service

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.gobies.org/foeye-dependencies/logger"

	"git.gobies.org/foeye/foeye3/config"
	"git.gobies.org/foeye/foeye3/database"
	"git.gobies.org/foeye/foeye3/model/m_poc"
	"git.gobies.org/foeye/foeye3/model/m_tag"
	"git.gobies.org/foeye/foeye3/model/m_threat"
	"git.gobies.org/foeye/foeye3/model/m_visible_column"
	"git.gobies.org/foeye/foeye3/packages/constant"
	"git.gobies.org/foeye/foeye3/packages/elasticx"
	"git.gobies.org/foeye/foeye3/packages/excelx"
	"git.gobies.org/foeye/foeye3/packages/fsx"
	"git.gobies.org/foeye/foeye3/packages/stringsx"
	"git.gobies.org/foeye/foeye3/packages/util/inactivated"
	"git.gobies.org/foeye/foeye3/responses/r_threat"

	"github.com/olivere/elastic"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// ThreatSrv service interface structure.
type ThreatSrv interface {
	ConvertInstanceToDefaultList(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, instance interface{}, isLicenseMosaic bool) (interface{}, int64, error)
	ConvertInstanceToIpList(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool) (interface{}, int64, error)
	ConvertInstanceToPocList(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool) (interface{}, int64, error)
	ConvertInstanceToSearchList(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword) (interface{}, error)
	ConvertInstanceToExcel(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, instance interface{}, baseDir string, isLicenseMosaic bool, configure *config.Configure) (string, error)
	//实现下载中心-------->暂存ConvertInstanceToExcel(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, instance interface{}, baseDir string, isLicenseMosaic bool, configure *config.Configure, record *m_down_load_record.DownLoadRecord) (string, int, error)

	ConvertInstanceToNoticeExcel(mysqlStore database.Factory, QL *r_threat.QueryListAndKeyword, instance interface{}, data []map[string]interface{}, baseDir string, configure *config.Configure) (string, error)
	ConvertTaskThreatStatisticsToCountData(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, LatestTaskId int, LastTaskId int) (*m_threat.TaskThreatCountData, error)
	GetThreatOverview(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, ipRangeManager string) (*m_threat.ResultThreatOverview, error)
	GetThreatProductByIps(mysqlStore database.Factory, esStore elasticx.Factory, ips []string, taskId int) (map[string][]string, error)

	UpdateThreatStatus(ctx context.Context, esStore elasticx.Factory, QL *m_threat.RequestUpdateThreatState) error
}

type threatSrv struct {
}

var headerMap = map[string]string{
	"ip":                  "IP地址",
	"online":              "资产状态",
	"is_xc":               "资产类型",
	"asset_level":         "资产等级",
	"common_title":        "漏洞名称",
	"cveId":               "CVE编号",
	"vulType":             "漏洞类型",
	"level":               "漏洞等级",
	"url":                 "漏洞地址",
	"status":              "修复状态",
	"comment":             "修复备注",
	"createtime":          "发现时间",
	"lastupdatetime":      "上次扫描",
	"notice_time":         "通报时间",
	"notice_time_current": "通报时间",
	"name":                "备注信息",
	"mac":                 "MAC地址",
	"province":            "地理位置",
	"company":             "管理单元",
	"business_app":        "业务系统",
	"username":            "负责人",
	"computer_room":       "机房信息",
	"manager_mobile":      "电话",
	"manager_email":       "邮箱",
	"common_description":  "漏洞描述",
	"common_impact":       "漏洞危害",
	"recommandation":      "解决方案",
}

func newThreatSrv() *threatSrv {
	return &threatSrv{}
}

// ConvertInstanceToDefaultList convert instance to default list.
func (s *threatSrv) ConvertInstanceToDefaultList(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, instance interface{}, isLicenseMosaic bool) (interface{}, int64, error) {
	has, err := mysqlStore.Tag().GetAncestryAllByMode()
	if err != nil {
		return nil, 0, err
	}
	tag, ok := has.([]*m_tag.Tag)
	if !ok {
		return nil, 0, err
	}

	// 新增减少漏洞处理
	if QL.SelectType != "" {
		QL.TaskID = QL.LatestTaskId
		number := QL.Number
		size := QL.Size
		QL.Number = 1
		QL.Size = 1000

		if QL.SelectType == "new" {
			QL.TaskID = QL.LastTaskId
		}

		if QL.TaskID != 0 {
			exceptVuls, _, count, err := esStore.Threats().GetThreatByDefault(ctx, QL)
			if err != nil {
				return nil, 0, err
			}

			// 没有数据就返回
			if count < 0 {
				return make([]*m_threat.ResultVulnerabilityDataByDefault, 0), 0, nil
			}

			// 过滤掉对比的数据
			for _, v := range exceptVuls {
				QL.NotInIds = append(QL.NotInIds, v.ID)
			}
		}

		// 再转换回来
		QL.Number = number
		QL.Size = size
		QL.TaskID = QL.LastTaskId
		if QL.SelectType == "new" {
			QL.TaskID = QL.LatestTaskId
		}
	}

	items, item, total, err := esStore.Threats().GetThreatByDefault(ctx, QL)
	if err != nil {
		return nil, 0, err
	}
	data, err := convertInstanceToDefaultList(mysqlStore.Poc(), tag, items, item, instance, QL, isLicenseMosaic)
	if err != nil {
		return nil, 0, err
	}
	return data, total, nil
}

// ConvertInstanceToIpList convert instance to ip list.
func (s *threatSrv) ConvertInstanceToIpList(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool) (interface{}, int64, error) {
	items, item, total, err := esStore.Threats().GetThreatByIp(ctx, QL)
	if err != nil {
		return nil, 0, err
	}
	data, err := convertInstanceToIpList(mysqlStore.Poc(), items, item, QL, isLicenseMosaic)
	if err != nil {
		return nil, 0, err
	}
	return data, total, nil
}

// ConvertInstanceToPocList convert instance to poc list.
func (s *threatSrv) ConvertInstanceToPocList(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool) (interface{}, int64, error) {
	items, item, total, err := esStore.Threats().GetThreatByThreat(ctx, QL)
	if err != nil {
		return nil, 0, err
	}
	data, err := convertInstanceToPocList(mysqlStore.Poc(), items, item, QL, isLicenseMosaic)
	if err != nil {
		return nil, 0, err
	}
	return data, total, nil
}

// ConvertTaskThreatStatisticsToCountData 获取任务漏洞统计数据
func (s *threatSrv) ConvertTaskThreatStatisticsToCountData(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, LatestTaskId int, LastTaskId int) (*m_threat.TaskThreatCountData, error) {
	data, err := esStore.Threats().GetThreatCountDataByTask(ctx, LatestTaskId, LastTaskId)
	if err != nil {
		logger.Errorf("ConvertTaskThreatStatisticsToCountData error: err(%+v)", err)

		return nil, err
	}

	return data, nil
}

// convertInstanceToDefaultList
func convertInstanceToDefaultList(pocStore database.GormPocDatabaseStore, tag []*m_tag.Tag, items []*m_threat.ResultVulnerabilityInfo, item, fields interface{}, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool) (interface{}, error) {
	data := new(m_threat.ResultVulnerabilityDataByDefault)
	var n int
	for _, v := range items {
		poc, err := pocStore.GetPocByName(v.CommonTitle)
		if err != nil {
			return nil, err
		}

		if poc != nil {
			v.Verity = poc.HasExp
			extracted, _ := stringsx.ExtractAndRemoveKeywords(poc.Tags, []string{"信创", "热点", "两高一弱"})
			v.KeywordTags = extracted
		}

		data.ResultVulnerabilityInfo = append(data.ResultVulnerabilityInfo, v)

		if isLicenseMosaic {
			v.IP = inactivated.InactivatedAssetsAddMosaic(v.IP, QL.Number, n)
		}
		n++
	}

	if instance, ok := item.(*m_threat.VulCount); ok {
		data.VulCount = instance
	}

	custom := map[string]string{}
	for _, v := range tag {
		custom[v.Realname] = v.Name
	}
	data.CustomFields = custom

	if temp, ok := fields.(*m_visible_column.VisibleColumn); ok {
		if strings.Contains(temp.Content, ",") {
			list := strings.Split(temp.Content, ",")
			for _, v := range list {
				data.Fields = append(data.Fields, v)
			}
		}
	}

	return data, nil
}

// convertInstanceToIpList
func convertInstanceToIpList(pocStore database.GormPocDatabaseStore, items []*m_threat.IpParentData, item interface{}, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool) (interface{}, error) {
	data := new(m_threat.ResultVulnerabilityDataByIp)
	var n int
	for _, value := range items {
		if value != nil {
			//for _, v := range value.IpChildData {
			//	poc, err := pocStore.GetPocByName(v.CommonTitle)
			//	if err != nil {
			//		return nil, err
			//	}
			//	if poc != nil {
			//		v.Verity = poc.HasExp
			//	}
			//}
			if isLicenseMosaic {
				value.IP = inactivated.InactivatedAssetsAddMosaic(value.IP, QL.Number, n)
			}
			n++
		}

	}
	data.IpParentData = items

	if instance, ok := item.(*m_threat.ResultVulnerabilityTotalData); ok {
		data.ResultVulnerabilityTotalData = instance
	}
	return data, nil
}

// convertInstanceToPocList
func convertInstanceToPocList(pocStore database.GormPocDatabaseStore, items, item interface{}, QL *r_threat.QueryListAndKeyword, isLicenseMosaic bool) (interface{}, error) {
	data := new(m_threat.ResultVulnerabilityDataByPoc)
	//var n int
	if instances, ok := items.([]*m_threat.PocParentData); ok {
		for _, value := range instances {
			if value != nil {
				poc, err := pocStore.GetPocByName(value.CommonTitle)
				if err != nil {
					return nil, err
				}

				if poc != nil {
					//for _, v := range value.PocChildData {
					//	v.Verity = poc.HasExp
					//
					//	if isLicenseMosaic {
					//		v.IP = inactivated.InactivatedAssetsAddMosaic(v.IP, QL.Number, n)
					//	}
					//	n++
					//}
					value.Level = int(poc.Level)
					value.CveID = poc.CveID
				}
			}
		}
		data.PocParentData = instances
	}
	if instance, ok := item.(*m_threat.ResultVulnerabilityTotalData); ok {
		data.ResultVulnerabilityTotalData = instance
	}
	return data, nil
}

// ConvertInstanceToSearchList convert instance to search list.
func (s *threatSrv) ConvertInstanceToSearchList(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword) (interface{}, error) {
	has, err := mysqlStore.Tag().GetAncestryAllByMode()
	if err != nil {
		return nil, err
	}
	tag, ok := has.([]*m_tag.Tag)
	if !ok {
		return nil, err
	}
	items, err := esStore.Threats().GetThreatAdvancedScreen(ctx, tag, QL)
	if err != nil {
		return nil, err
	}
	return items, nil
}

// ConvertInstanceToExcel convert instance to excel list.
func (s *threatSrv) ConvertInstanceToExcel(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, instance interface{}, baseDir string, isLicenseMosaic bool, configure *config.Configure) (string, error) {
	// 新增减少漏洞处理
	if QL.SelectType != "" {
		QL.TaskID = QL.LatestTaskId
		number := QL.Number
		size := QL.Size
		QL.Number = 1
		QL.Size = 1000

		if QL.SelectType == "new" {
			QL.TaskID = QL.LastTaskId
		}

		if QL.TaskID != 0 {
			exceptVuls, _, count, err := esStore.Threats().GetThreatByDefault(ctx, QL)
			if err != nil {
				return "", err
			}

			// 没有数据就返回
			if count < 0 {
				return "", nil
			}

			// 过滤掉对比的数据
			for _, v := range exceptVuls {
				QL.NotInIds = append(QL.NotInIds, v.ID)
			}
		}

		// 再转换回来
		QL.Number = number
		QL.Size = size
		QL.TaskID = QL.LastTaskId
		if QL.SelectType == "new" {
			QL.TaskID = QL.LatestTaskId
		}
	}

	data, err := esStore.Threats().GetThreatByExport(ctx, QL, isLicenseMosaic)
	if err != nil {
		return "", err
	}
	if data == nil {
		return "", nil
	}
	if items, ok := data.([]map[string]interface{}); ok {
		if item, ok := instance.(*m_visible_column.VisibleColumn); ok {
			if strings.Contains(item.Content, ",") {
				headers := strings.Split(item.Content, ",")
				// 漏洞模块 新增资产状态展示字段 - 该字段必展示
				headers = append(headers[:1+1], headers[1:]...)
				headers[1] = "online"
				headers = append(headers, "common_description")
				headers = append(headers, "common_impact")
				headers = append(headers, "recommandation")
				if configure.Server.IsXc() {
					headers = append([]string{"is_xc"}, headers...)
				}
				chineseHeader, err := convertHeadersToChinese(headers, mysqlStore.Tag())
				if err != nil {
					return "", err
				}
				datum := convertInstanceToArrayInterface(items, headers)

				filePath, err := writerSliceDataIntoExcelInLine(baseDir, QL.Switch, chineseHeader, datum)
				if err != nil {
					return "", err
				}
				return filePath, nil
			}
		}
	}
	return "", errors.New(constant.RecordsListErrorOfTypeNotMatch)
}

//实现下载中心-------->暂存
//func (s *threatSrv) ConvertInstanceToExcel(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, QL *r_threat.QueryListAndKeyword, instance interface{}, baseDir string, isLicenseMosaic bool, configure *config.Configure, record *m_down_load_record.DownLoadRecord) (string, int, error) {
//	rand.Seed(time.Now().UnixNano()) //初始化随机数种子
//	_ = mysqlStore.DownLoadRecord().Updates(record.ID, map[string]interface{}{
//		"progress": rand.Intn(10) + 1,
//	})
//	total := 0
//	// 新增减少漏洞处理
//	if QL.SelectType != "" {
//		QL.TaskID = QL.LatestTaskId
//		number := QL.Number
//		size := QL.Size
//		QL.Number = 1
//		QL.Size = 1000
//
//		if QL.SelectType == "new" {
//			QL.TaskID = QL.LastTaskId
//		}
//
//		if QL.TaskID != 0 {
//			exceptVuls, _, count, err := esStore.Threats().GetThreatByDefault(ctx, QL)
//			if err != nil {
//				return "", total, err
//			}
//
//			// 没有数据就返回
//			if count < 0 {
//				return "", total, nil
//			}
//
//			// 过滤掉对比的数据
//			for _, v := range exceptVuls {
//				QL.NotInIds = append(QL.NotInIds, v.ID)
//			}
//		}
//
//		// 再转换回来
//		QL.Number = number
//		QL.Size = size
//		QL.TaskID = QL.LastTaskId
//		if QL.SelectType == "new" {
//			QL.TaskID = QL.LatestTaskId
//		}
//	}
//
//	data, err := esStore.Threats().GetThreatByExport(ctx, QL, isLicenseMosaic)
//	_ = mysqlStore.DownLoadRecord().Updates(record.ID, map[string]interface{}{
//		"progress": rand.Intn(10) + 51,
//	})
//	if err != nil {
//		return "", total, err
//	}
//	if data == nil {
//		return "", total, nil
//	}
//	if items, ok := data.([]map[string]interface{}); ok {
//		if item, ok := instance.(*m_visible_column.VisibleColumn); ok {
//			if strings.Contains(item.Content, ",") {
//				headers := strings.Split(item.Content, ",")
//				headers = append(headers, "common_description")
//				headers = append(headers, "common_impact")
//				headers = append(headers, "recommandation")
//				if configure.Server.IsXc() {
//					headers = append([]string{"is_xc"}, headers...)
//				}
//				chineseHeader, err := convertHeadersToChinese(headers, mysqlStore.Tag())
//				_ = mysqlStore.DownLoadRecord().Updates(record.ID, map[string]interface{}{
//					"progress": rand.Intn(10) + 61,
//				})
//				if err != nil {
//					return "", total, err
//				}
//				datum := convertInstanceToArrayInterface(items, headers)
//				_ = mysqlStore.DownLoadRecord().Updates(record.ID, map[string]interface{}{
//					"progress": rand.Intn(10) + 71,
//				})
//				filePath, err := writerSliceDataIntoExcelInLine(baseDir, QL.Switch, chineseHeader, datum)
//				_ = mysqlStore.DownLoadRecord().Updates(record.ID, map[string]interface{}{
//					"progress": rand.Intn(10) + 81,
//				})
//				if err != nil {
//					return "", total, err
//				}
//				total = len(datum)
//				return filePath, total, nil
//			}
//		}
//	}
//	return "", total, errors.New(constant.RecordsListErrorOfTypeNotMatch)
//}

// convertInstanceToArrayInterface convert instance to array interface.
func convertInstanceToArrayInterface(items []map[string]interface{}, headers []string) [][]interface{} {
	noticeTime := time.Now().Format("2006-01-02 15:04:05")
	datum := make([][]interface{}, 0, len(items))

	for _, v := range items {
		temp := make([]interface{}, 0, len(headers))
		for _, header := range headers {
			if header == "notice_time_current" {
				temp = append(temp, noticeTime)
			} else if _, ok := v[header]; ok {
				switch header {
				case "level":
					if item, ok := v[header].(float64); ok {
						temp = append(temp, getLevelString(item))
					} else {
						temp = append(temp, "")
					}
				case "is_xc":
					if item, ok := v[header].(float64); ok {
						temp = append(temp, getIsXCString(item))
					} else {
						temp = append(temp, "")
					}
				case "online":
					if item, ok := v[header].(float64); ok {
						temp = append(temp, getIsOnline(item))
					} else {
						temp = append(temp, "")
					}
				case "status":
					if item, ok := v[header].(float64); ok {
						temp = append(temp, getThreatStatus(item))
					} else {
						temp = append(temp, "")
					}
				default:
					temp = append(temp, v[header])
				}
			} else {
				val := getCustomFieldValue(v, header)
				temp = append(temp, val)
			}
		}
		datum = append(datum, temp)
	}
	return datum
}
func getCustomFieldValue(v map[string]interface{}, header string) string {
	if cusV, ok := v["custom_fields"]; ok {
		if cus, ok := cusV.(map[string]interface{}); ok {
			if h, ok := cus[header]; ok {
				if hV, ok := h.(string); ok {
					return hV
				}
			}
		}
	}
	return ""
}

func getLevelString(level float64) string {
	switch level {
	case 0:
		return "低危"
	case 1:
		return "中危"
	case 2:
		return "高危"
	case 3:
		return "严重"
	default:
		return ""
	}
}

func getIsXCString(isXC float64) string {
	if isXC == 1 {
		return "信创"
	}
	return "非信创"
}

func getIsOnline(online float64) string {
	if online == 1 {
		return "在线"
	}
	return "离线"
}

func getThreatStatus(status float64) string {
	switch status {
	case 0:
		return "待处理"
	case 1:
		return "待核查"
	case 2:
		return "误报"
	case 3:
		return "接受风险"
	case 4:
		return "其他"
	default:
		return ""
	}
}

// writerSliceDataIntoExcelInLine write data into the excel file. According to the line of writing. Output Excel according to the data.
func writerSliceDataIntoExcelInLine(baseDir, filename string, headers []interface{}, data [][]interface{}) (string, error) {
	ds := "Sheet1"
	ef := excelize.NewFile()

	err := ef.SetColWidth(ds, "A", "Z", 20)

	// Settings the excel table header style.
	style, err := ef.NewStyle(excelx.CellStyleOfDarkGrey)
	if err != nil {
		return "", err
	}
	colStart, _ := excelize.ColumnNumberToName(len(headers))
	if err = ef.SetCellStyle(ds, "A1", fmt.Sprintf("%s1", colStart), style); err != nil {
		return "", err
	}

	// Get save path of the excel file.
	if filename == m_threat.PocTypeFixed {
		filename = fmt.Sprintf("%s_%s_%d.%s", "已修复漏洞", time.Now().Format(constant.UserDefinedFormat1), time.Now().Unix(), "xlsx")
	} else if filename == m_threat.PocTypeUnFixed {
		filename = fmt.Sprintf("%s_%s_%d.%s", "未修复漏洞", time.Now().Format(constant.UserDefinedFormat1), time.Now().Unix(), "xlsx")
	}
	filePath, err := fsx.GetThreatForExcelFilePath(baseDir, filename)
	if err != nil {
		return "", err
	}
	// Generate excel table header.
	err = ef.SetSheetRow(ds, "A1", &headers)
	if err != nil {
		return "", err
	}

	batchSize := 1000 // 根据你的内存和性能要求调整批次大小
	if err := writeDataInBatches(ef, ds, data, batchSize); err != nil {
		return "", err
	}

	// Save excel file.
	if err = ef.SaveAs(filePath); err != nil {
		return "", err
	}
	return filePath, nil
}

func writeDataInBatches(f *excelize.File, sheetName string, data [][]interface{}, batchSize int) error {
	rowIndex := 2 // 跳过表头
	for _, batch := range batchData(data, batchSize) {
		for _, rowData := range batch {
			if err := f.SetSheetRow(sheetName, fmt.Sprintf("A%d", rowIndex), &rowData); err != nil {
				return err
			}
			rowIndex++
		}
	}

	return nil
}

func batchData(data [][]interface{}, batchSize int) [][][]interface{} {
	var batches [][][]interface{}
	for i := 0; i < len(data); i += batchSize {
		end := i + batchSize
		if end > len(data) {
			end = len(data)
		}
		batches = append(batches, data[i:end])
	}
	return batches
}

// convertHeadersToChinese convert header to chinese header.
//func convertHeadersToChinese(headers []string, tagStore database.GormTagDatabaseStore) ([]interface{}, error) {
//	data := make([]interface{}, 0)
//	for _, header := range headers {
//		switch header {
//		case "ip":
//			data = append(data, "IP地址")
//		case "online":
//			data = append(data, "资产状态")
//		case "is_xc":
//			data = append(data, "资产类型")
//		case "asset_level":
//			data = append(data, "资产等级")
//		case "common_title":
//			data = append(data, "漏洞名称")
//		case "cveId":
//			data = append(data, "CVE编号")
//		case "vulType":
//			data = append(data, "漏洞类型")
//		case "level":
//			data = append(data, "漏洞等级")
//		case "url":
//			data = append(data, "漏洞地址")
//		case "status":
//			data = append(data, "修复状态")
//		case "comment":
//			data = append(data, "修复备注")
//		case "createtime":
//			data = append(data, "发现时间")
//		case "lastupdatetime":
//			data = append(data, "上次扫描")
//		case "notice_time":
//			data = append(data, "通报时间")
//		case "notice_time_current":
//			data = append(data, "通报时间")
//		case "name":
//			data = append(data, "备注信息")
//		case "mac":
//			data = append(data, "MAC地址")
//		case "province":
//			data = append(data, "地理位置")
//		case "company":
//			data = append(data, "管理单元")
//		case "business_app":
//			data = append(data, "业务系统")
//		case "username":
//			data = append(data, "负责人")
//		case "computer_room":
//			data = append(data, "机房信息")
//		case "manager_mobile":
//			data = append(data, "电话")
//		case "manager_email":
//			data = append(data, "邮箱")
//		case "common_description":
//			data = append(data, "漏洞描述")
//		case "common_impact":
//			data = append(data, "漏洞危害")
//		case "recommandation":
//			data = append(data, "解决方案")
//		default:
//			has, err := tagStore.GetItemByRealName(header)
//			if err != nil {
//				if err == gorm.ErrRecordNotFound {
//					break
//				}
//				return nil, err
//			}
//			if has == nil {
//				break
//			}
//			if instance, ok := has.(*m_tag.Tag); ok {
//				data = append(data, instance.Name)
//			}
//		}
//	}
//	return data, nil
//}

// convertHeadersToChinese
func convertHeadersToChinese(headers []string, tagStore database.GormTagDatabaseStore) ([]interface{}, error) {
	data := make([]interface{}, 0)
	for _, h := range headers {
		if label, ok := headerMap[h]; ok {
			data = append(data, label)
			continue
		}
		// 动态标签查询
		item, err := tagStore.GetItemByRealName(h)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break
			}
			return nil, err
		}
		if item == nil {
			break
		}
		if tag, ok := item.(*m_tag.Tag); ok {
			data = append(data, tag.Name)
		}
	}
	return data, nil
}

// ConvertInstanceToNoticeExcel convert instance ti notice excel
func (s *threatSrv) ConvertInstanceToNoticeExcel(mysqlStore database.Factory, QL *r_threat.QueryListAndKeyword, instance interface{}, data []map[string]interface{}, baseDir string, configure *config.Configure) (string, error) {
	if item, ok := instance.(*m_visible_column.VisibleColumn); ok {
		// notice_time_current 说明正在进行通报
		item.Content = strings.Replace(item.Content, "notice_time", "notice_time_current", 1)

		if strings.Contains(item.Content, ",") {
			headers := strings.Split(item.Content, ",")
			headers = append(headers, "common_description")
			headers = append(headers, "common_impact")
			headers = append(headers, "recommandation")
			if configure.Server.IsXc() {
				headers = append([]string{"is_xc"}, headers...)
			}
			chineseHeader, err := convertHeadersToChinese(headers, mysqlStore.Tag())
			if err != nil {
				return "", err
			}
			datum := convertInstanceToArrayInterface(data, headers)

			filePath, err := writerSliceDataIntoExcelInLine(baseDir, QL.Switch, chineseHeader, datum)
			if err != nil {
				return "", err
			}
			return filePath, nil
		}
	}
	return "", nil
}

// GetThreatOverview get threat overview.
func (s *threatSrv) GetThreatOverview(ctx context.Context, mysqlStore database.Factory, esStore elasticx.Factory, ipRangeManager string) (*m_threat.ResultThreatOverview, error) {
	has, err := mysqlStore.Tag().GetAncestryAllByMode()
	if err != nil {
		return nil, err
	}
	tag, ok := has.([]*m_tag.Tag)
	if !ok {
		return nil, err
	}

	pocs, err := mysqlStore.Poc().GetListNoSort(0, 0)
	if err != nil {
		return nil, err
	}

	pocMap := map[string]*m_poc.PocPlus{}
	for _, poc := range pocs {
		pocMap[poc.Filename] = poc
	}

	instance, err := esStore.Threats().GetThreatsOverviewByTag(ctx, ipRangeManager, tag)
	if err != nil {
		return nil, err
	}

	if instance.BaseInfo.AddPocThreat, err = esStore.Threats().GetThreatsOverviewCount(ctx, ipRangeManager,
		elastic.NewBoolQuery().Must(elastic.NewTermsQuery("state", 1, 2)).
			Must(elastic.NewRangeQuery("createtime").
				Gte(time.Now().Format(constant.UserDefinedFormat4)).
				Lte(time.Now().Format(constant.FormatChinaDate)+" 23:59:59"))); err != nil {
		return nil, err
	}

	if instance.BaseInfo.UnfixedCount, err = esStore.Threats().GetThreatsOverviewCount(ctx, ipRangeManager,
		elastic.NewBoolQuery().Must(elastic.NewTermsQuery("state", 1, 2))); err != nil {
		return nil, err
	}

	if instance.BaseInfo.FixedCount, err = esStore.Threats().GetThreatsOverviewCount(ctx, ipRangeManager,
		elastic.NewBoolQuery().Must(elastic.NewTermQuery("state", 4))); err != nil {
		return nil, err
	}

	if instance.BaseInfo.ExpCount, err = esStore.Threats().GetThreatsOverviewCount(ctx, ipRangeManager,
		elastic.NewBoolQuery().Must(elastic.NewTermQuery("has_exp", 1)).
			Must(elastic.NewTermsQuery("state", 1, 2))); err != nil {
		return nil, err
	}

	if instance.BaseInfo.WeakPasswordCount, err = esStore.Threats().GetThreatsOverviewCount(ctx, ipRangeManager,
		elastic.NewBoolQuery().Must(elastic.NewTermQuery("vulType", "弱口令")).
			Must(elastic.NewTermsQuery("state", 1, 2))); err != nil {
		return nil, err
	}

	temp, err := esStore.Threats().GetThreatsOverviewVulTypeCount(ctx, ipRangeManager, pocMap,
		elastic.NewBoolQuery())
	if err != nil {
		return nil, err
	}

	for _, v := range temp {
		instance.ThreatRank = append(instance.ThreatRank, m_threat.TagName{
			Key:      v.Name,
			DocCount: v.DocCount,
		})
	}

	if instance.Exp, err = esStore.Threats().GetThreatsOverviewVulTypeCount(ctx, ipRangeManager, pocMap,
		elastic.NewBoolQuery().Must(elastic.NewTermQuery("has_exp", 1))); err != nil {
		return nil, err
	}

	if instance.WeakPassword, err = esStore.Threats().GetThreatsOverviewVulTypeCount(ctx, ipRangeManager, pocMap,
		elastic.NewBoolQuery().Must(elastic.NewTermQuery("vulType", "弱口令"))); err != nil {
		return nil, err
	}

	if instance.ThreatNew, err = esStore.Threats().GetThreatsOverviewByPoc(ctx, ipRangeManager, false,
		elastic.NewBoolQuery()); err != nil {
		return nil, err
	}

	if instance.ThreatOld, err = esStore.Threats().GetThreatsOverviewByPoc(ctx, ipRangeManager, true,
		elastic.NewBoolQuery().Must(elastic.NewRangeQuery("createtime").
			Lte(time.Now().AddDate(0, 0, -7).Format(constant.UserDefinedFormat4)))); err != nil {
		return nil, err
	}

	tagMap := make(map[string]string)
	for _, t := range tag {
		tagMap[t.Name] = t.Realname
	}
	instance.CustomTags = tagMap

	sort.SliceStable(instance.Exp, func(i, j int) bool {
		return instance.Exp[i].FofaRecords > instance.Exp[j].FofaRecords
	})

	sort.SliceStable(instance.WeakPassword, func(i, j int) bool {
		return instance.WeakPassword[i].FofaRecords > instance.WeakPassword[j].FofaRecords
	})

	sort.SliceStable(instance.ThreatOld, func(i, j int) bool {
		return instance.ThreatOld[i].DayCount > instance.ThreatOld[j].DayCount
	})

	return instance, nil
}

// GetThreatProductByIps 获取对应IP下的漏洞product
func (s *threatSrv) GetThreatProductByIps(mysqlStore database.Factory, esStore elasticx.Factory, ips []string, taskId int) (map[string][]string, error) {
	productList := make(map[string][]string, 0)
	vulFileIpMap, err := esStore.Threats().GetThreatVulFileByIps(ips, taskId)
	if err != nil {
		return productList, err
	}
	vulFileList := make([]string, 0)
	for _, v := range vulFileIpMap {
		vulFileList = append(vulFileList, v...)
	}
	vulFileList = stringsx.RemoveDuplicationSlice(vulFileList)
	productFilenameMap, err := mysqlStore.Poc().GetPocProductsByFileNames(vulFileList)
	if err != nil {
		return productList, err
	}
	for ip, vulfiles := range vulFileIpMap {
		for _, vulfile := range vulfiles {
			if tmp, exists := productFilenameMap[vulfile]; exists {
				productList[ip] = append(productList[ip], tmp)
			}
		}
		productList[ip] = stringsx.RemoveDuplicationSlice(productList[ip])
	}
	return productList, nil
}

// UpdateThreatStatus 更新漏洞的状态和备注
func (s *threatSrv) UpdateThreatStatus(ctx context.Context, esStore elasticx.Factory, QL *m_threat.RequestUpdateThreatState) error {
	err := esStore.Threats().UpdateThreatStatus(ctx, QL)
	return err
}
