package ar_router

import (
	"log"

	"git.gobies.org/foeye/foeye3/internal/mux/api/a_net_topos"
	"git.gobies.org/foeye/foeye3/middleware"
	"git.gobies.org/foeye/foeye3/router/ar_engine"
)

// GinMuxNetToposRouter assets routers
type GinMuxNetToposRouter struct {
	Instance *ar_engine.GinEngine
}

// Register net topos router register
func (g *GinMuxNetToposRouter) Register() {
	api, err := a_net_topos.NewMuxNetToposAPI(g.Instance.Configure(), g.Instance.Database, g.Instance.Elastic, g.Instance.DatabaseFactory())
	if err != nil {
		log.Panicf("NewNetToposAPI has error: %s\n", err)
	}
	group := g.Instance.Engine.Group(GetRelativeURIWithVersion("net_topos", WithSpecificVersion("/api/v2")))
	group.Use(middleware.MuxJWT(g.Instance.Database))
	{
		group.GET("", api.GetNetToposList)
		group.GET("/sub_ip_ranges", api.GetSubIPRanges)
	}
}
