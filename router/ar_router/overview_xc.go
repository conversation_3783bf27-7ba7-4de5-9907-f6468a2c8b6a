package ar_router

import (
	log "github.com/sirupsen/logrus"

	"git.gobies.org/foeye/foeye3/api/a_overview_xc"

	"git.gobies.org/foeye/foeye3/middleware"
	"git.gobies.org/foeye/foeye3/router/ar_engine"
)

type GinOverviewXcRouter struct {
	Instance *ar_engine.GinEngine
}

func (g *GinOverviewXcRouter) Register() {
	store := g.Instance.Store()

	api, err := a_overview_xc.NewOverViewXcAPI(g.Instance.Database, g.Instance.Elastic, g.Instance.Configure(), g.Instance.Store(), g.Instance.DatabaseFactory())
	if err != nil {
		log.Panicf("NewOverViewAPI has error: %s\n", err)
	}

	group := g.Instance.Engine.Group(GetRelativeURIWithVersion("/overview_xc"))
	group.Use(middleware.JWT(g.Instance.Configure(), store.Redis().Client()))
	{
		group.GET("header", api.GetHeader)
		group.GET("threat", api.GetBodyThreat)
		group.GET("rule_distribution", api.GetBodyRuleDistribution)
	}
}
