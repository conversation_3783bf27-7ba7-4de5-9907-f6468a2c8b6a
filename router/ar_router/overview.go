package ar_router

import (
	"log"

	"git.gobies.org/foeye/foeye3/api/a_overviews"
	"git.gobies.org/foeye/foeye3/middleware"
	"git.gobies.org/foeye/foeye3/router/ar_engine"
)

type GinOverviewRouter struct {
	Instance *ar_engine.GinEngine
}

func (g *GinOverviewRouter) Register() {
	store := g.Instance.Store()

	api, err := a_overviews.NewOverViewAPI(g.Instance.Database, g.Instance.Elastic, g.Instance.Configure(), g.Instance.Store(), g.Instance.DatabaseFactory())
	if err != nil {
		log.Panicf("NewOverViewAPI has error: %s\n", err)
	}

	group := g.Instance.Engine.Group(GetRelativeURIWithVersion("/overviews"))
	group.Use(middleware.JWT(g.Instance.Configure(), store.Redis().Client()))
	{
		group.GET("", api.GetOverviewsInfo)
		group.GET("asset_vulnerability_statistics", api.GetAssetVulnerabilityStatistics)
		group.GET("asset_trend_statistics", api.GetAssetTrendStatistics)
		group.GET("body", api.GetBody)
		group.GET("header", api.GetHeader)
		group.GET("/assets_pie", api.GetAssetsPie)
	}
}
