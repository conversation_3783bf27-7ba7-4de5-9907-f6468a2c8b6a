
[cors]
  accesscontrolalloworigin = "localhost:3000,127.0.0.1:3000,127.0.0.1,127.0.0.1:80,http://0.0.0.0:8000"

[database]
  defaultpassword = "CS18best"
  defaultstrength = 10
  defaultusername = "admin"
  defaultserviceusername = "sysadmin"# 服务管理账号（三权分立，初始化用户）
  defaultservicepassword = "sysAdmin@123"# 服务管理账号（三权分立，初始化密码）
  host = "localhost"
  iscreatedefaultuser = true
  name = "fofaee"
  password = "root"
  port = 3307
  setmaxidleconns = 10
  setmaxopenconns = 100
  type = "mysql"
  user = "root"

[elastic]
  host = "http://127.0.0.1:9200"
  password = ""
  username = ""

[fofa]
  baseuri = "/api/v1"
  host = "https://fofa.info"
  hosturi = "/host"
  myuri = "/info/my"
  searchuri = "/search/all"
  timeout = 3

[goscanner]
  dictpath = "/Users/<USER>/wwwgo/foeye3/goscanner"
  godserverport = "8080"
  path = "/Users/<USER>/wwwgo/foeye3/goscanner/goscanner"
  pocpath = "/Users/<USER>/wwwgo/foeye3/goscanner/exploits"

[log]
  level = "debug"
  path = "./logs/production.log"

[microkernel]
  host = "http://127.0.0.1:61234"
  systeminfouri = "/api/v1/system/infos"

[nats]
  host = "nats://127.0.0.1:4222"

[redis]
  apiservertaskpause = "task_pause"
  apiservertaskrecover = "task_recover"
  apiservertaskstop = "task_stopping"
  database = 0
  host = "127.0.0.1:6379"
  idletimeout = 200
  password = ""

[rpc]
  address = "127.0.0.1:30000"

[server]
  allowshost = ["Localruby1", "localhost", "127.0.0.1", "127.0.0.1:80", "127.0.0.1:3000", "*************"]
  applicationname = "网络资产测绘及风险分析系统"
  captchatimeout = 60
  host = "0.0.0.0"
  httpsport = 8080
  ignorerisk = true
  ignoresafetypoints = []
  isbackup = false
  ischannel = false
  isclosecaptcha = false
  isclosecompliancemonitor = true
  iscloseiprange = true
  iscloselicense = true
  isclosepermission = true
  isignoresafety = false
  isshowplatform = true
  manualtype = "user_manual"
  mode = "test" # test relase debug
  port = 3000
  productionpath = "./static/settings/production.yml"
  readtimeout = 60
  reportpath = "/var/www/foeye_front/dist/reports"
  scantype = "intranet"
  serverapidocumenttitle = "FOEYE3 API Interface Document."
  servername = "apiservice"
  taskmaximumlimit = 100
  tcpdumperrorlogpath = "./tmp/cap_error_log"
  tcpdumppath = "./tmp/capture_data"
  writetimeout = 60

[sso]
  htmlpath = "/var/www/foeye_front/dist/single-sign-on.html"

[token]
  duration = 20
  iselasticexpires = false
  secret = "application token"
  unit = "minute"

[worker]
  assetscanqueue = "asset_scan_worker"
  database = 0
  host = "localhost:6379"
  pool = 10
  processunique = "handle-xlsx-file-1"
