after_script:
  - rm -rf ~/.netrc
  - |
    if [ "$CI_JOB_STATUS" == "failed" -a $CI_BUILD_REF_NAME == "master" ]; then
       V_TEXT="**CI任务<font color=\\\"#FF3333\\\">执行失败</font>通知**${V_BR}\
          **任务ID**: **${CI_JOB_ID}**${V_BR}\
          **任务名**: **${CI_JOB_NAME}**${V_BR}\
          **项目**: **${CI_PROJECT_PATH}**${V_BR}\
          **分支**: **${CI_BUILD_REF_NAME}**${V_BR}\
          **执行人**: **${GITLAB_USER_NAME}**${V_EXTRA}\
          "

       curl -XPOST "https://oapi.dingtalk.com/robot/send?access_token=${DINGTALK_TOKEN}" -H 'Content-Type: application/json'  -d "{\"actionCard\":{\"title\":\"CI执行失败通知\",\"text\":\"${V_TEXT}\",\"btnOrientation\":\"1\",\"btns\":[{\"title\":\"查看详情\",\"actionURL\":\"dingtalk://dingtalkclient/page/link?url=${CI_PROJECT_URL}/pipelines/${CI_PIPELINE_ID}&pc_slide=false\"}]},\"msgtype\":\"actionCard\"}"
    fi

variables:
  # Disable shallow cloning so that goreleaser can diff between tags to
  # generate a changelog.
  GIT_DEPTH: ''
  # 钉钉markdown换行符 必须\n且前后跟两个空格(shell 转义)
  V_BR: "\ \ \\n\ \ "

# 定义 stages
stages:
  - foeye3-编译
  - foeye3-单元测试
  - foeye3-前端集成测试
  - foeye3-UI集成测试[核心测试用例]
  - foeye3-UI集成测试
#  - foeye3-生成接口覆盖率
#  - foeye3-接口自动化
  - foeye3-自动打docker包

build-job:       # This job runs in the build stage, which runs first.
  stage: foeye3-编译
  script:
    - go mod tidy
    - touch docs/spec.json && touch internal/mux/docs/spec.json
    - go build
  tags:
    - Foeye3CD
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_PIPELINE_SOURCE == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH =~ /^testing.*$/
      when: always
    - when: never

foeye3-unit:
  stage: foeye3-单元测试
  interruptible: false
  coverage: '/\s+\(statements\)\s+(\d+\.\d+)%/'
  script:
    - cp /home/<USER>/data/foeye3.test.toml config.test.toml
    - cp /home/<USER>/data/foeye3.test.toml config.develop.toml
    - cp /home/<USER>/data/database.toml database.toml
    - cp /home/<USER>/data/production.yml ./static/settings/
    - go env -w GOPROXY="https://goproxy.cn,direct"
    - export APPLICATION_MODE=test
    - go mod tidy
#    - swagger generate spec --scan-models -o docs/spec.json
#    - swagger generate spec --work-dir=internal/mux --scan-models -o internal/mux/docs/spec.json
    - touch docs/spec.json && touch internal/mux/docs/spec.json
    - go env
    - ~/delete_es_index.sh
    - make test
    - cp -f source.out ~/source2.out
    - |
      if [ -f "coverage.out" -a $CI_BUILD_REF_NAME == "master" ]; then
          coverage=$(go tool cover -func=coverage.out | awk '/^total:/ {print substr($3, 1, length($3)-1)}')
          if (( $(echo "$coverage < $PROGRESS" | bc -l) )); then
              V_TEXT="**<font color=\\\"#FF3333\\\">单元测试覆盖<$PROGRESS%通知</font>**${V_BR}\
                **任务ID**: **${CI_JOB_ID}**${V_BR}\
                **任务名**: **${CI_JOB_NAME}**${V_BR}\
                **项目**: **${CI_PROJECT_PATH}**${V_BR}\
                **分支**: **${CI_BUILD_REF_NAME}**${V_BR}\
                **覆盖度**: **<font color=\\\"#FF3333\\\">$coverage%</font>**${V_BR}\
                **执行人**: **${GITLAB_USER_NAME}**${V_EXTRA}\
                "

              curl -XPOST "https://oapi.dingtalk.com/robot/send?access_token=${DINGTALK_TOKEN}" -H 'Content-Type: application/json'  -d "{\"actionCard\":{\"title\":\"CI 单元测试覆盖度不达标通知\",\"text\":\"$V_TEXT\",\"btnOrientation\":\"1\",\"btns\":[{\"title\":\"查看详情\",\"actionURL\":\"dingtalk://dingtalkclient/page/link?url=${CI_PROJECT_URL}/pipelines/${CI_PIPELINE_ID}&pc_slide=false\"}]},\"msgtype\":\"actionCard\"}"
          fi
        else
          echo "No coverage report found."
      fi
  artifacts:
    when: always
    reports:
      junit: junit.xml
      coverage_report:
        coverage_format: cobertura
        path: cobertura.xml
  tags:
    - Foeye3CD
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH =~ /^testing.*$/
      when: always
    - when: never

# 把所需要的东西都部署在***********机器上，然后在************机器上运行前端集成测试和UI集成测试（e2e.sh、ui.sh会运行************的脚本），要先运行foeye3-前端集成测试，后运行foeye3-UI集成测试。
foeye3-integration:
  stage: foeye3-前端集成测试
  when: manual
  script:
    - go mod tidy
    - touch docs/spec.json && touch internal/mux/docs/spec.json
    - go build
    - cd cmd/apicoveragectl && go build
    - cd ../../cmd/foeyectl && go build
    - cd ../../
    - cp -f ~/source2.out source.out
    - tar -cvzPf foeye3.tar.gz foeye3 source.out ui-version config.upgrade.toml static etc tools cmd/foeyectl/foeyectl cmd/apicoveragectl/apicoveragectl
    - cp -f foeye3.tar.gz ~/foeye3.tar.gz
    - cd ${FOEYEENGINE_DIR} && ./build_all.sh
    - ~/replace_foeye3_12_38.sh  # 把foeye3和前端等替换到***********机器上
    - ~/e2e.sh
  tags:
    - Foeye3CD
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^testing.*$/
      when: manual
    - when: never

foeye3-integration1-smoke:
  stage: foeye3-UI集成测试[核心测试用例]
  when: manual
  script:
    - ~/ui_smoke.sh
  tags:
    - Foeye3CD
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^testing.*$/
      when: manual
    - when: never


foeye3-integration1:
  stage: foeye3-UI集成测试
  when: manual
  script:
    - ~/ui.sh
  tags:
    - Foeye3CD
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^testing.*$/
      when: manual
    - when: never

#foeye3-xc:
#  stage: foeye3-信创集成测试
#  when: manual
#  script:
#    - go mod tidy
#    - go build
#    - cp -f foeye3 ~/foeye3
#    - cp -f "/home/<USER>/data/config.product.toml.xc" "/data/fofaee/web/foeye3/config.product.toml"
#    - ~/replace_foeye3.sh
#    - ~/replace_foeye3_front_xc.sh
#    - cd ~/foeye3_front && yarn run test:e2e
#    - cd /home/<USER>/FOEYE_autotest
#    - git pull origin main
#    - ./run_case.sh
#  tags:
#    - Foeye3CD
#  rules:
#    - if: $CI_COMMIT_BRANCH == "master"
#    - if: $CI_COMMIT_BRANCH =~ /^testing.*$/
#      when: always
#    - when: never

#foeye3-apicoverage:
#  stage: foeye3-生成接口覆盖率
#  when: manual
#  script:
#    - ~/generate_api_coverage.sh
#  tags:
#    - Foeye3CD
#  only:
#    - /^testing.*$/

foeye3-deploy:
  stage: foeye3-自动打docker包
  when: manual
  script:
#    - ~/replace_foeye3_12_38.sh #本操作不再序，它依赖于 foeye3-前端集成测试
    - ~/auto_package_deploy_12_38.sh
  tags:
    - Foeye3CD
  only:
    - /^testing.*$/


#foeye3-api-automated:
#  stage: foeye3-接口自动化
#  image: api_automation:1.0
#  script:
#    - sudo docker run --network=host -v /data/foeye_apifox:/data/foeye_apifox fd01_apifox:1.0 sh -c 'apifox run https://api.apifox.com/api/v1/projects/3583793/api-test/ci-config/393562/detail?token=x0S_5C68ug7gRjy3-GLefz -k -r html,cli'
#    - sudo docker ps -a | grep "Exited" | awk '{print $1}' | xargs docker rm
#  tags:
#    - foeye3_api_auto
#  when: manual #手动触发
#  only:
#    - /^testing.*$/
